<cfsavecontent variable="local.progressJS">
	<cfoutput>
	<script type="text/javascript">
		<cfif local.hasManageSWODRegResponseRights>
			function viewSWResponse(formID, responseID) {
				const SWResponseURL = '#local.viewResponseLink#&formID=' + formID + '&responseID=' + responseID + '&mode=stream';

				const fnCallBack = function() {
					top.MCModalUtils.buildFooter({
						classlist: 'd-flex',
						showclose: false,
						buttons: [
							{
								class: "btn btn-sm btn-secondary",
								clickhandler: 'MCModalUtils.renderMainPage',
								label: 'Previous',
								iconclass: 'fa-solid fa-arrow-left',
								name: 'btnPrevious',
								id: 'btnPrevious'
							},
							{
								class: "btn btn-sm btn-secondary ml-auto",
								clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.printFormResponse',
								label: 'Print', 
								iconclass: 'fa-solid fa-print',
								name: 'btnPrintResponse',
								id: 'btnPrintResponse'
							}
						]
					});
				};

				top.MCModalUtils.renderInlinePage('View Response',SWResponseURL,'Seminar Progress',{},fnCallBack,'SWEnrollFrmResp');
			}
			function toggleSWActivateExam(rid){
				var activate = $('.response' + rid + ' a.toggleSWActivateExam').data('activate');
				var toggleText = activate?'reactivate':'deactivate';
				var toggleResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true') {
						setToggleSWActivateExamButton(!activate, rid);
					} else { 
						alert('We were unable to '+ toggleText +' this response.');
					}
				};
				let toggleButton = $('.response' + rid + ' a.toggleSWActivateExam');
				mca_initConfirmButton(toggleButton, function(){
					var objParams = { enrollmentID:#local.enrollmentID#, responseID:rid, isActive:activate };
					TS_AJX('ADMINSWCOMMON','toggleActivateExam',objParams,toggleResult,toggleResult,10000,toggleResult);
				},'<i class="fa-solid fa-shuffle"></i>',null,'Toggling...');
			}
			function setToggleSWActivateExamButton(activate,rid){	
				$('.response' + rid +' a.toggleSWActivateExam').data('activate',activate);
				$('.response' + rid +' a.toggleSWActivateExam').html('<i class="fa-solid fa-shuffle"></i>');
				$('.response' + rid +' a.toggleSWActivateExam').removeClass('disabled');
				$('.response' + rid +' a.toggleSWActivateExam').prop('title',(activate?'Activate':'Deactivate') +' this response.').tooltip('dispose').tooltip();
				if(activate) {
					$('.response' + rid).addClass('table-warning');
					$('.response' + rid + ' .d-flex').append('<span class="badge badge-warning ml-auto">Inactive</span>');
				} else {
					$('.response' + rid).removeClass('table-warning');
					$('.response'+rid+' .badge.badge-warning.ml-auto').remove();
				}
			}
		</cfif>
		function completeFile(fid) {
			var completeFileResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					self.location.href = '#local.viewProgressDetailLink#&tab=progressComponent';
				} else { 
					alert('We were unable to mark this enrollment as completed.');
				}
			};

			if (confirm('Are you sure you want to mark this as completed?')) {
				var objParams = { enrollmentID:#local.enrollmentID#, fileID:fid };
				TS_AJX('ADMINSWOD','manuallyCompleteFile',objParams,completeFileResult,completeFileResult,20000,completeFileResult);
			}
		}
		function downloadAccessDetailLogs(fid) {
			self.location.href = '#local.accessDetailsDownloadLink#&fid=' + fid;
		}
		$(function () {
			top.$('##MCModalLabel').text('Seminar Progress');
			top.MCModalUtils.buildFooter({
				classlist: 'text-left',
				showclose: true,
			});
		})
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.progressJS#">


<cfoutput>
<div class="text-right">
	<a href="##" onclick="downloadAccessDetailLogs(0);"><i class="fa-light fa-download"></i> Download Merged Logs</a>
</div>

<table class="table table-sm table-borderless">
</cfoutput>
<cfoutput query="local.qryFiles" group="titleID">
	<tr><td colspan="5"><strong>#local.qryFiles.titleName#</strong></td></tr>
	<tr><td colspan="5"></td></tr>
	
	<cfoutput group="fileID">
		
		<cfif local.qryFiles.filetypeid is 2>
			<cfset local.icon = "document">
			<cfif len(local.strAccessDetails[fileid])>
				<cfset local.pct = floor(len(replacenocase(local.strAccessDetails[fileid],"0","","all")) / len(local.strAccessDetails[fileid]) * 100)>
			<cfelse>
				<cfset local.pct = 0>
			</cfif>
			<cfset local.pctinfo = "(#len(replacenocase(local.strAccessDetails[fileid],"0","","all"))#/#len(local.strAccessDetails[fileid])# pages)">
		<cfelseif local.qryFiles.filetypeid is 3>
			<cfset local.icon = "audio">
			<cfif len(local.strAccessDetails[fileid])>
				<cfset local.pct = floor((len(replacenocase(local.strAccessDetails[fileid],"0","","all")) / len(local.strAccessDetails[fileid])) * 100)>
			<cfelse>
				<cfset local.pct = 0>
			</cfif>
			<cfset local.pctinfo = "">
		<cfelseif local.qryFiles.filetypeid is 4>
			<cfset local.icon = "camera">
			<cfif len(local.strAccessDetails[fileid])>
				<cfset local.pct = floor((len(replacenocase(local.strAccessDetails[fileid],"0","","all")) / len(local.strAccessDetails[fileid])) * 100)>
			<cfelse>
				<cfset local.pct = 0>
			</cfif>
			<cfset local.pctinfo = "">
		</cfif>
		<tr>
			<td class="align-top" width="250">#left(local.qryFiles.fileTitle,56)#<cfif len(local.qryFiles.fileTitle) gt 60>...</cfif></td>
			<td class="align-top">&nbsp; <img src="assets/common/images/seminarweb/icons/#local.icon#Icon.gif" width="20" height="16"></td>
			<td class="align-top" width="200">
				<div class="progress" style="height:20px;">
					<div class="progress-bar" role="progressbar" style="width: #local.pct#%;" aria-valuenow="#local.pct#" aria-valuemin="0" aria-valuemax="100"></div>
				</div>
			</td>
			<td class="align-top">#local.pct#% #local.pctinfo#</td>
			<td class="align-top">
				<cfif local.pct lt 100>
					<a href="##" onClick="completeFile(#local.qryFiles.fileID#);return false;">Complete</a>&nbsp;|
				</cfif>
				<a href="##" onclick="downloadAccessDetailLogs(#local.qryFiles.fileID#);return false;">Log</a>
			</td>
		</tr>
	</cfoutput>
	<tr><td colspan="5">&nbsp;</td></tr>
</cfoutput>
<cfoutput>
</table>

<cfif local.qrySeminarForms.recordcount>
	<h5>Form Responses</h5>
	<table id="SWEnrollFrmResp" class="table table-sm table-borderless mt-2">
		<cfloop query="local.qrySeminarForms">
			<cfset local.qryFormDetails = local.objAdminSWOD.getSeminarFormDetail(seminarFormID=local.qrySeminarForms.seminarFormID, depomemberdataid=local.qryEnrollment.depomemberdataid)>
			<tr>
				<td class="pt-2">
					<strong>#left(local.qrySeminarForms.formTitle,70)#<cfif len(local.qrySeminarForms.formTitle) gt 80>...</cfif></strong><br/>
					#local.qrySeminarForms.loadpoint#
				</td>
			</tr>
			<tr class="border-bottom">
				<td>
					<cfif local.qryFormDetails.recordcount>
						<table class="table table-sm table-striped table-bordered">
						<thead>
						<tr>
							<th width="20%">Response ID</th>
							<th width="20%">Time Started</th>
							<th width="20%">Time Completed</th>
							<th>Score</th>
							<cfif local.hasManageSWODRegResponseRights>
								<th width="15%">Tools</th>
							</cfif>
						</tr>
						</thead>
						<cfloop query="local.qryFormDetails">
							<tr class="response#local.qryFormDetails.responseid#<cfif local.qryFormDetails.isResponseActive is 0> table-warning</cfif>">
								<td class="align-top">
									<div class="d-flex">
										<span>#local.qryFormDetails.responseid#</span>
										<cfif local.qryFormDetails.isResponseActive is 0>
											<span class="badge badge-warning ml-auto">Inactive</span>
										</cfif>
									</div>
								</td>
								<td class="align-top">#dateformat(local.qryFormDetails.datedelivered,"m/d/yy")# #timeformat(local.qryFormDetails.datedelivered,"h:mm tt")#</td>
								<td class="align-top dateCompleted">
									<cfif len(local.qryFormDetails.datecompleted)>
										#dateformat(local.qryFormDetails.datecompleted,"m/d/yy")# #timeformat(local.qryFormDetails.datecompleted,"h:mm tt")# 
									<cfelse>
										Not submitted
									</cfif>
								</td>
								<cfif local.qryFormDetails.formTypeID is 2>
									<td class="align-top">
										<cfif len(local.qryFormDetails.datecompleted)>
											#local.qryFormDetails.passingPct#% (#local.qryFormDetails.correctCount#/#local.qryFormDetails.questionCount#)
										</cfif>
									</td>
								<cfelse>
									<td class="align-top">Not applicable</td>
								</cfif>
								<cfif local.hasManageSWODRegResponseRights>
									<td class="align-top">
										<cfif len(local.qryFormDetails.datecompleted)>
											<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1" onclick="viewSWResponse(#local.qrySeminarForms.formID#, #local.qryFormDetails.responseid#);return false;" title="View Response"><i class="fa-solid fa-eye"></i></a>
										<cfelse>
											<a href="##" class="btn btn-xs btn-outline-primary p-1 m-1 invisible"><i class="fa-solid fa-eye"></i></a>
										</cfif>
										<cfif local.qryFormDetails.isResponseActive is 0>
											<a class="toggleSWActivateExam btn btn-xs btn-outline-primary p-1" data-activate="1" data-confirm="0" title="Activate this Response" href="javascript:toggleSWActivateExam(#local.qryFormDetails.responseid#);"><i class="fa-solid fa-shuffle"></i></a>
										<cfelse>
											<a class="toggleSWActivateExam btn btn-xs btn-outline-primary p-1" data-activate="0" data-confirm="0" title="Inactivate this Response" href="javascript:toggleSWActivateExam(#local.qryFormDetails.responseid#);"><i class="fa-solid fa-shuffle"></i></a>
										</cfif>
									</td>
								</cfif>
							</tr>
						</cfloop>
						</table>
					<cfelse>
						<div class="py-2">No responses</div>
					</cfif>
				</td>
			</tr>
		</cfloop>
	</table>
</cfif>
</cfoutput>