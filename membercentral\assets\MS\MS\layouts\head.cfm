<cfoutput>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>#event.getValue('mc_pageDefinition.pagetitle',event.getValue('mc_siteInfo.sitename'))#</title>	
	
	<link rel="stylesheet" href="/assets/common/javascript/owlCarousel/234/owl.carousel.min.css">
    <link rel="stylesheet" href="/assets/common/javascript/owlCarousel/234/owl.theme.default.min.css">	
    #application.objCMS.getBootstrapHeadHTML()#
    #application.objCMS.getResponsiveHeadHTML()#
   
    <link rel="icon" href="/images/favicon.ico" sizes="16x16">
	#application.objCMS.getFontAwesomeHTML(includeVersion4Support=true)#
	
	<!-- custom css files -->
    <link href="/css/stylesheet.css" rel="stylesheet" type="text/css">
    <link href="/css/responsive.css" rel="stylesheet" type="text/css">
    <link href="/css/main.css" rel="stylesheet" type="text/css">
	#application.objCMS.getSiteCustomCSS(siteID=arguments.event.getValue('mc_siteInfo.siteID'))#
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap" rel="stylesheet">

    <link rel="stylesheet" href="/fonts/adobe-garamond-pro-2-webfont/style.css">
    <link rel="stylesheet" href="/fonts/Carbona/stylesheet.css">
    <!-- 
    font-family: "Lato", sans-serif;

    font-family: 'Carbona Test';
    font-family: 'Adobe Garamond Pro Regular'; 
    -->

</cfoutput>
