<cfinclude template="dsp_manageSubs_commonJS.cfm">

<cfoutput>
<div class="manageSubsApp">
	<form name="frmManageSubs" id="frmManageSubs" action="#local.formlink#" method="post" autocomplete="off" onsubmit="return onSubmitFinalForm();">
		<input type="hidden" name="mid" value="#local.memberID#">
		<input type="hidden" name="rootSubscriptionID" value="#local.rootSubscriptionID#">
		<input type="hidden" id="formStep" name="formStep" value="1">
		<input type="hidden" id="payScheduleRowIDsList" name="payScheduleRowIDsList" value="">
		<input type="hidden" id="skipEmailTemplateNotifications" name="skipEmailTemplateNotifications" value="0">
		<input type="hidden" id="chkBilled" name="chkBilled" value="0">

		<div id="manageSubsContainer">
			<!--- Member Card --->
			<div class="card card-box mb-2">
				<div class="card-body py-2">
					<div>
						<div class="mt-1 font-weight-bold">#local.strMember.mc_combinedName# (#local.strMember.memberNumber#)</div>
						<cfif len(local.strMember.company)><div class="text-dim"><small>#local.strMember.company#</small></div></cfif>
						<cfif len(local.strMember.mc_combinedAddresses) or len(local.strMember.mc_extraInfo) or len(local.strMember.mc_recordType) or len(local.strMember.mc_memberType) or len(local.strMember.mc_lastlogin)>
							<div class="mt-1 p-1">
								<cfif len(local.strMember.mc_combinedAddresses)>#local.strMember.mc_combinedAddresses#</cfif>
								<cfif len(local.strMember.mc_extraInfo)>#local.strMember.mc_extraInfo#</cfif>
								<cfif len(local.strMember.mc_recordType)><div>#local.strMember.mc_recordType#</div></cfif>
								<cfif len(local.strMember.mc_memberType)><div>#local.strMember.mc_memberType#</div></cfif>
								<cfif len(local.strMember.mc_lastlogin)><div>#local.strMember.mc_lastlogin#</div></cfif>
							</div>
						</cfif>
					</div>

					<div class="d-flex flex-sm-column-reverse mt-2">
						<div id="subRegCartTotal" class="font-weight-bold mb-2">
							Total: <span class="grandTotal">$0.00</span>
						</div>
					</div>
				</div>
			</div>

			<!--- Root Sub Card --->
			<div id="rootSubWrapper" class="card card-box mb-1 mainSubCard" 
				data-displaypriceelement="rootSubCardTotalPrice"
				data-linkedsummarycontainer="rootSubSummary"
				data-linkedformcontainer="rootSubForm"
				data-linkedtitlecontainer="rootSubTitleContainer"
				data-linkedtitleerrorcontainer="rootSubTitleAlert"
				data-errfields="">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">
						#local.strRootSub.subscription[1].subscriptionName# <span class="text-danger">*</span>
						<span class="rootSubCardTotalPrice"></span>
					</div>
				</div>
				<div class="card-body py-3">
					<div id="rootSubForm" class="d-none">
						<div class="subsContainer">
							<cfif local.strRootSub.subscription[1].qryRates.recordCount gt 0>
								#manageSubscription_renderSubscriptionsForm(arrSubs=local.strRootSub.subscription, strAddOn={}, parentSubscriptionID=0, strParentFreq=local.strRootSub.strParentFreq, 
									freeRateDisplay=local.freeRateDisplay, strEditSubs={}, recursionLevel=0, listSubscribed="")#
							<cfelse>
								<div class="alert alert-warning mb-1">There are no rates available for this member.<br>Select a different subscription.</div>
							</cfif>
						</div>
						<button type="button" id="rootSubConfirmBtn" class="btn btn-sm btn-primary mt-3 d-none" onclick="confirmRootSubChanges();">Confirm</button>
					</div>
					<div id="rootSubSummary" class="subCardSummary">
						<div id="rootSubRateInfo"></div>
						<cfif local.hasRootSubRatesCount gt 1>
							<button type="button" id="rootSubEditBtn" data-editmode="editrootsub" class="btn btn-sm btn-secondary subCardEditBtn mt-3 d-none">Cancel</button>
						</cfif>
					</div>
				</div>
			</div>

			<!--- Sub Term Dates Card --->
			<div id="rootSubTermDates" class="card card-box mb-1 mainSubCard d-none" 
				data-displaypriceelement=""
				data-linkedsummarycontainer="subTermDatesSummary"
				data-linkedformcontainer="subTermDatesForm"
				data-linkedtitlecontainer=""
				data-linkedtitleerrorcontainer=""
				data-errfields="">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">Term Dates</div>
				</div>
				<div class="card-body py-3">
					<div id="subTermDatesForm" class="d-none">
						<div class="termDatesContainer mb-3">
							<table id="tblCurrentSubTermDates" class="table table-sm table-borderless">
								<thead>
									<tr>
										<th></th>
										<th>Start Date</th>
										<th>End Date</th>
										<th>Grace End Date</th>
									</tr>
								</thead>
								<tbody>
									<tr>
										<td style="width:20%;">Current Subscription:</td>
										<td>
											<div class="input-group input-group-sm">
												<input type="text" name="fTermFrom" id="fTermFrom" value="" class="form-control form-control-sm dateControl dtfield">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermFrom"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</td>
										<td>
											<div class="input-group input-group-sm">
												<input type="text" name="fTermTo" id="fTermTo" value="" class="form-control form-control-sm dateControl dtfield">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="fTermTo"><i class="fa-solid fa-calendar"></i></span>
												</div>
											</div>
										</td>
										<td>
											<div class="d-flex">
												<div class="col px-1">
													<div class="input-group input-group-sm">
														<input type="text" name="fTermGrace" id="fTermGrace" value="" class="form-control form-control-sm dateControl dtfield">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="fTermGrace"><i class="fa-solid fa-calendar"></i></span>
														</div>
													</div>
												</div>
												<div class="flex-shrink-1 d-flex align-items-center">
													<a href="javascript:mca_clearDateRangeField('fTermGrace');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Clear Grace End Date"><i class="fa-regular fa-calendar-minus text-danger font-size-lg"></i></a>
												</div>
											</div>
										</td>
									</tr>
								</tbody>
							</table>
							<table id="tblRecogDates" class="table table-sm table-borderless mt-2">
								<thead>
									<tr>
										<th style="width:20%;"></th>
										<th style="width:25%;">Recognition Start Date</th>
										<th style="width:25%;">Recognition End Date</th>
										<th></th>
									</tr>
								</thead>
								<tbody>
								</tbody>
							</table>
						</div>
						<div class="d-flex no-gutters align-items-center termDatesFooterContainer mt-3 border-lightgray border-top pt-2">
							<div class="col-auto">
								<button type="button" class="btn btn-sm btn-primary subCardConfirmBtn" onclick="confirmTermDatesChanges();return false;">
									Confirm
								</button>
							</div>
							<div class="col-auto pl-2">
								<button type="button" name="btnCancelTermDatesChanges" class="btn btn-sm btn-secondary subCardCancelBtn" onclick="cancelTermDatesForm();">Cancel</button>
							</div>
						</div>
					</div>
					<div id="subTermDatesSummary" class="subCardSummary">
						<div>Subscription Term: <span id="termStart"></span> - <span id="termEnd"></span></div>
						<div class="mt-3">
							<button type="button" id="termDatesEditBtn" data-editmode="edittermdates" class="btn btn-sm btn-secondary subCardEditBtn d-none"></button>
						</div>
					</div>
				</div>
			</div>

			<!--- Sub AddOns --->
			<div id="sub#local.rootSubscriptionID#_addons"></div>

			<!--- Sample Card used by JS for loading --->
			<div id="subLoadingCard" class="d-none">
				<div class="mx-3 my-2 d-flex align-items-center">
					<i class="spinner-border spinner-border-sm"></i><span class="font-weight-bold ml-2">Please Wait...</span>
				</div>
			</div>
		</div>
		<div id="subsSummaryContainer" class="d-none">
			<div id="subSummaryCard" class="card card-box mb-2">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">
						<div>
							<div class="mt-1 font-weight-bold">#local.strMember.mc_combinedName# (#local.strMember.memberNumber#)</div>
							<cfif len(local.strMember.company)><div class="text-dim"><small>#local.strMember.company#</small></div></cfif>
							<cfif len(local.strMember.mc_combinedAddresses) or len(local.strMember.mc_extraInfo) or len(local.strMember.mc_recordType) or len(local.strMember.mc_memberType) or len(local.strMember.mc_lastlogin)>
								<div class="mt-1 p-1">
									<cfif len(local.strMember.mc_combinedAddresses)>#local.strMember.mc_combinedAddresses#</cfif>
									<cfif len(local.strMember.mc_extraInfo)>#local.strMember.mc_extraInfo#</cfif>
									<cfif len(local.strMember.mc_recordType)><div>#local.strMember.mc_recordType#</div></cfif>
									<cfif len(local.strMember.mc_memberType)><div>#local.strMember.mc_memberType#</div></cfif>
									<cfif len(local.strMember.mc_lastlogin)><div>#local.strMember.mc_lastlogin#</div></cfif>
								</div>
							</cfif>
						</div>
					</div>
				</div>
				<div class="card-body py-3">
					<div id="multipleInvProfilesAlert" class="alert alert-info mb-2 d-none">
						This subscription includes multiple Invoice Profiles and will create multiple invoices when finalized.
					</div>
					<div id="subSummaryTable" class="subSummarySection d-none">
						<div class="mb-2"><span class="font-weight-bold">#local.strRootSub.subscription[1].subscriptionName#</span> <span id="termDatesDisp"></span></div>
						<div id="rootSubRateSelection" class="pl-3">
						</div>
						<div id="addOnSubsSelection" class="pl-3">
						</div>
						<div class="mb-2 row no-gutters mt-3">
							<div class="col-7 font-weight-bold">Total Due:</div>
							<div id="totalDueDisplay" class="col text-right font-weight-bold px-3"></div>
							<div class="freqDisplayElm"></div>
						</div>
						<div class="mb-2 row no-gutters mt-2">
							<div class="col-7 font-weight-bold">Amount Due Today:</div>
							<div id="amtDueTodayDisplay" class="col text-right font-weight-bold px-3"></div>
							<div class="freqDisplayElm"></div>
						</div>
						<div id="changePriceFormActions" class="d-none mt-4">
							<button type="button" class="btn btn-sm btn-primary" onclick="confirmPriceChange();">Confirm Price Change</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelPriceChange();">Cancel</button>
						</div>
					</div>
					<div id="paymentScheduleForm" class="subSummarySection d-none">
						<div id="paymentScheduleListing">
							Payment Schedule
						</div>
						<div id="err_schedform" class="alert alert-danger mb-2 d-none"></div>
						<div id="paymentScheduleFormActions" class="d-none mt-4">
							<button type="button" class="btn btn-sm btn-primary" onclick="confirmPaymentSchedule();">Confirm Payment Schedule</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelPaymentSchedule();">Cancel</button>
						</div>
					</div>
					<div id="summaryStepActions" class="mt-4">
						<button type="button" class="btn btn-sm btn-secondary" onclick="changePrice();">Change Price</button>
						<button type="button" class="btn btn-sm btn-secondary" onclick="setPaymentSchedule();">Set Payment Schedule</button>
						<span id="payScheduleResetMsg" style="display:none;"></span>
					</div>
				</div>
			</div>

			<cfset local.stateIDForTax = val(local.qryTaxStateZIP.stateIDForTax)>
			<cfset local.zipForTax = local.qryTaxStateZIP.zipForTax>
			<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
				<cfset local.zipForTax = "">
			</cfif>

			<!--- prompt for missing tax information --->
			<cfif local.stateIDForTax eq 0 OR len(local.zipForTax) eq 0>
				<div class="row no-gutters my-2">
					<div class="col-md-12">
						<div class="card card-box mb-1">
							<div class="card-header bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Additional Billing Information Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="form-group row no-gutters">
									<label for="stateIDforTax" class="col-sm-3 col-form-label-sm font-size-md">Billing State/Province: *</label>
									<div class="col-sm-9">
										<cfset local.qryStates = application.objCommon.getStates()>
										<select id="stateIDforTax" name="stateIDforTax" class="form-control form-control-sm">
											<option value=""></option>
											<cfset local.currentCountryID = 0>
											<cfloop query="local.qryStates">
												<cfif local.qryStates.countryID neq local.currentCountryID>
													<cfset local.currentCountryID = local.qryStates.countryID>
													<cfoutput><optgroup label="#local.qryStates.country#"></cfoutput>
												</cfif>
												<cfoutput><option value="#local.qryStates.stateID#"<cfif local.stateIDForTax eq local.qryStates.stateID> selected</cfif>>#local.qryStates.stateName# (#local.qryStates.stateCode#)</option></cfoutput>
												<cfif local.qryStates.currentrow eq local.qryStates.recordcount or local.qryStates.countryID[local.qryStates.currentrow+1] neq local.currentCountryID>
													</optgroup>
												</cfif>
											</cfloop>
										</select>
									</div>
								</div>
								<div class="form-group row no-gutters">
									<label for="zipForTax" class="col-sm-3 col-form-label-sm font-size-md">Billing Postal Code: *</label>
									<div class="col-sm-9">
										<cfoutput><input type="text" id="zipForTax" name="zipForTax" class="form-control form-control-sm" maxlength="25" value="#local.zipForTax#"></cfoutput>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			<cfelse>
				<cfoutput>
				<input type="hidden" id="stateIDforTax" name="stateIDforTax" value="#local.qryTaxStateZIP.stateIDforTax#">
				<input type="hidden" id="zipForTax" name="zipForTax" value="#local.zipForTax#">
				</cfoutput>
			</cfif>
			<div id="subSaveCard" class="card card-box mb-1">
				<div class="card-header bg-light">
					<div class="card-header--title font-weight-bold font-size-sm">Save Subscription</div>
				</div>
				<div class="card-body py-3">
					<div id="err_finalsubform" class="alert alert-danger mb-2 d-none"></div>
					<div id="finalButtonsContainer">
						<div class="d-flex flex-column">
							<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="allowEmail"
								data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title=""
								data-original-title="Only applies if this subscription or selected addons have been assigned an Email Template to send upon becoming active. Email(s) will not be sent until the subscription activation requirement is met."
								>Finalize and Invoice - Allow Automated Emails *</button>
							<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="skipEmail">Finalize and Invoice - Skip Automated Emails</button>
							<button type="submit" class="btn btn-sm btn-outline-primary font-weight-bold mt-2 btnFinals" value="createAsBilled">Create Subscription as Billed</button>
						</div>
					</div>
					<div id="graceEndDatePromptForm" class="d-none">
						<div class="alert alert-info mb-2">We recommend that all Billed Subscriptions have a Grace End Date. Please confirm the Grace End Date below:</div>
						<table class="table table-sm table-borderless">
							<thead>
								<tr>
									<th></th>
									<th>Start Date</th>
									<th>End Date</th>
									<th>Grace End Date</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td style="width:20%;">Parent Subscription:</td>
									<td>
										<div class="input-group input-group-sm">
											<input type="text" name="fTermFromRO" id="fTermFromRO" value="" class="form-control form-control-sm" readonly disabled>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermFromRO"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</td>
									<td>
										<div class="input-group input-group-sm">
											<input type="text" name="fTermToRO" id="fTermToRO" value="" class="form-control form-control-sm" readonly disabled>
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="fTermToRO"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</td>
									<td>
										<div class="d-flex">
											<div class="col px-1">
												<div class="input-group input-group-sm">
													<input type="text" name="fTermGracePrompt" id="fTermGracePrompt" value="" class="form-control form-control-sm dateControl dtfield">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="fTermGracePrompt"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
										</div>
									</td>
								</tr>
							</tbody>
						</table>
						<div id="err_graceendpromp" class="alert alert-danger mb-2 d-none"></div>
						<div class="mt-4">
							<button type="button" class="btn btn-sm btn-primary font-weight-bold" onclick="saveTermGracePrompt();">Create Subscription as Billed</button>
							<button type="button" class="btn btn-sm btn-secondary" onclick="cancelTermGracePrompt();">Cancel</button>
						</div>
					</div>
					<div id="finalSaveLoadingCard" class="mt-3 d-none">
						<div class="mx-3 my-2 d-flex align-items-center">
							<i class="spinner-border spinner-border-sm"></i><span class="font-weight-bold ml-2">Saving Subscription...</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>
<script id="mc_recogDateRowHTML" type="text/x-handlebars-template">
	<tr id="recogDateRow_{{inputkey}}" class="recogDateRow" data-inputkey="{{inputkey}}">
		<td>{{subname}}</td>
		<td>
			<div class="input-group input-group-sm">
				<input type="text" name="fRecogFrom_{{inputkey}}" id="fRecogFrom_{{inputkey}}" class="form-control form-control-sm dateControl dtfield" value="{{frecogfromdate}}">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="fRecogFrom_{{inputkey}}"><i class="fa-solid fa-calendar"></i></span>
				</div>
			</div>
		</td>
		<td>
			<div class="input-group input-group-sm">
				<input type="text" name="fRecogTo_{{inputkey}}" id="fRecogTo_{{inputkey}}" class="form-control form-control-sm dateControl dtfield" value="{{frecogtodate}}" size="12">
				<div class="input-group-append">
					<span class="input-group-text cursor-pointer calendar-button" data-target="fRecogTo_{{inputkey}}"><i class="fa-solid fa-calendar"></i></span>
				</div>
			</div>
		</td>
		<td></td>
	</tr>
</script>
<script id="mc_subSelectionSummaryHTML" type="text/x-handlebars-template">
	{{##each arraddons}}
		{{##each arrsubs}}
			{{##if @first}}
				{{##compare ../setname '==' name}}
					{{> mc_subRateRowHTML idkey=idkey name=name rate=rate namefwbold=1 subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
					<div class="pl-3">
				{{/compare}}
				{{##compare ../setname '!=' name}}
					<div class="addOnSetName font-weight-bold mb-2">{{../setname}}</div>
					<div class="pl-3">
						{{> mc_subRateRowHTML idkey=idkey name=name rate=rate subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
				{{/compare}}
			{{else}}
				{{> mc_subRateRowHTML idkey=idkey name=name rate=rate subid=subid rfid=rfid rate=rate ratedisplay=ratedisplay freqname=freqname showfreqname=showfreqname }}
			{{/if}}
			{{##if childaddons}}
				{{> mc_subSelectionSummaryHTML arraddons=childaddons showfreqname=showfreqname }}
			{{/if}}
			{{##if @last}}
				</div>
			{{/if}}
		{{/each}}
	{{/each}}
</script>
<script id="mc_subRateRowHTML" type="text/x-handlebars-template">
	<div id="{{idkey}}_rateRow" class="rateRow mb-2 row no-gutters align-items-center">
		<div id="{{idkey}}_nameDisplay" class="col-7 pr-2{{##if namefwbold}} font-weight-bold{{/if}}">{{name}}{{##if isfreeaddonsub}}<span class="badge badge-success ml-2 freeaddonbadge">Free</span>{{/if}}</div>
		<div id="{{idkey}}_rateDisplay" class="col subRateDisplayElm text-right px-3">{{ratedisplay}}</div>
		<div id="{{idkey}}_rateEditable" class="col subRateEditableElm px-3 d-none">
			<div class="d-flex">
				<div class="input-group input-group-sm ml-auto" style="max-width:200px;">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input type="text" name="modifiedRateTotal_{{subid}}_{{rfid}}" id="modifiedRateTotal_{{subid}}_{{rfid}}" class="form-control form-control-sm modifiedRatePriceField" value="{{termratetouse}}" data-originalvalue="{{termratetouse}}" data-linkeddisplayfieldid="{{idkey}}_rateDisplay" onblur="onBlurEditableRateAmt(this);">
				</div>
			</div>
		</div>
		{{##if showfreqname}}
			<div id="{{idkey}}_rateFreq" class="freqDisplayElm col-auto" style="max-width:250px;">{{freqname}}</div>
		{{/if}}
	</div>
</script>
</cfoutput>