/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");

/* Include in Editor: Start */
.HeaderText {
    font-weight: 700;
    font-size: 55px;
    color: #043b56;
    font-family: 'Adobe Garamond Pro';
}
.HeaderTextSmall {
    font-weight: 700;
    font-size: 30px;
    line-height: 1.5;
    color: #023b56;
    font-family: 'Adobe Garamond Pro';
}
.SectionHeader {
    font-size: 45px;
    font-weight: 700;
    line-height: 1.3;
    font-family: 'Adobe Garamond Pro';
    margin-bottom: 30px;
}

.HighlightTitle {
    font-weight: 700;
    font-size: 35px;
    line-height: 1.3;
    color: #043b56;
    margin: 0 0 10px;
    display: block;
}
.quotetitle {
    font-size: 30px;
    margin: 0;
    color: #023b56;
    font-family: 'Adobe Garamond Pro';
}
.ColumnHeader {
    text-transform: uppercase;
    color: #46505d;
    font-size: 30px;
}
.BodyText{
    font-family: 'Carbona Test';
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 1.34;
    color: #0c2c48;
}
.BodyTextLarge {
    font-size: 20px;
}
.InfoText {
    font-size: 14px;
}
.AdobeGaramondProand {font-family: 'Adobe Garamond Proand', serif;}
.CarbonaVariableRegular{font-family: 'Carbona Variable Regular', sans-serif;}


/* Include in Editor: Stop */


/*STYLES NOT IN THE EDITOR: ***********************************************************************************************/