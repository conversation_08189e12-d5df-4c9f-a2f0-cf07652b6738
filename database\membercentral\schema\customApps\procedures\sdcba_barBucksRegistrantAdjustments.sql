ALTER PROC dbo.sdcba_barBucksRegistrantAdjustments
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpBarBucksRates') IS NOT NULL
		DROP TABLE #tmpBarBucksRates;
	IF OBJECT_ID('tempdb..#tmpBarBucksRegistrants') IS NOT NULL
		DROP TABLE #tmpBarBucksRegistrants;
	CREATE TABLE #tmpBarBucksRates (rateID int PRIMARY KEY, eventID int, rateBBAmount decimal(18,2), eventSiteResourceID int);
	CREATE TABLE #tmpBarBucksRegistrants (registrantID int PRIMARY KEY, rateID int, memberID int);

	declare @siteID int, @orgID int, @trashID int, @tr_Sale int, @RTID int, @usageTypeID int;
	declare @registrantID int, @bbAmount decimal(18,2), @rateTransactionID int, @recordedByMemberID int, 
		@nowDate datetime = getdate(), @nowDateOnly date, @invoiceID int, @invoiceProfileID int, @assignedToMemberID int,
		@dateBilled date, @dateDue date, @invoiceNumber varchar(19), @historyDesc varchar(max),
		@xmlSchedule xml, @GLAccountID int, @deferredGLAccountID int, @eventID int, @accrualDate datetime,
		@deferredDate datetime, @deferredDateStr varchar(10), @regMemberID int, @categoryID int, @mpprofileID int, 
		@bbAmountNeg decimal(18,2), @newMemberHistoryID int, @categoryTreeID int, @eventSiteResourceID int,
		@availCreditBalance decimal(18,2), @allocamount decimal(18,2), @paymentTransactionID int, @rowID int,
		@amtLeftToAllocate decimal(18,2);
	declare @tblUnallocatedPayments TABLE (rowID int, transactionID int, unallocatedAmount decimal(18,2));


	-- initially set to snapshot. this allows us to go in and out throughout this request
	-- this is necessary because ams_addMemberHistory cannot be run without starting the request in snapshot
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


	select @siteID = siteID, @orgID = orgID from membercentral.dbo.sites where siteCode = 'SDCBA';
	set @tr_Sale = membercentral.dbo.fn_tr_getTypeID('Sale');
	set @RTID = membercentral.dbo.fn_getResourceTypeID('event');
	select @usageTypeID = usageTypeID from membercentral.dbo.ams_memberHistoryUsageTypes where resourceTypeID = @RTID and usageType = 'registrant' and status = 'A';
	set @recordedByMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
	SET @nowDateOnly = @nowDate;
	SET @itemCount = 0;

	select @categoryTreeID = categoryTreeID 
	from membercentral.dbo.cms_categoryTrees
	where siteID = @siteID
	and categoryTreeName = 'MemberHistoryTypes';

	select @categoryID = categoryID
	from membercentral.dbo.cms_categories
	where categoryTreeID = @categoryTreeID
	and categoryCode = 'BarBucks'
	and isActive = 1;

	select @mpprofileID = profileID
	from membercentral.dbo.mp_profiles
	where siteID = @siteID
	and profileCode = 'BarBucks'
	and [status] = 'A';

	IF @mpprofileID is null
		RAISERROR('BarBucks Pay Profile is not active.',16,1);


	-- get all bar bucks rates. if none, exit.
	INSERT INTO #tmpBarBucksRates (rateID, eventID, rateBBAmount, eventSiteResourceID)
	select r.rateID, e.eventID, abs(membercentral.dbo.fn_regexReplace(r.reportCode,'[^0-9\.]','')) as rateBBAmount, e.siteResourceID
	from membercentral.dbo.ev_rates as r
	inner join membercentral.dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID 
		and sr.siteID = @siteID
		and sr.siteResourceStatusID = 1
	inner join membercentral.dbo.ev_registration as reg on reg.registrationID = r.registrationID
		and reg.siteID = @siteID
		and reg.status = 'A'
	inner join membercentral.dbo.ev_events as e on e.eventID = reg.eventID
		and e.status = 'A'
	where left(replace(r.reportCode,' ',''),3) = 'BB-';
		IF @@ROWCOUNT = 0 goto on_done;

	-- get all registrants tied to bar bucks rates. if none, exit.
	INSERT INTO #tmpBarBucksRegistrants (registrantID, rateID, memberID)
	select r.registrantID, r.rateID, m.activeMemberID
	from membercentral.dbo.ev_registrants as r
	inner join #tmpBarBucksRates as tmp on tmp.rateID = r.rateID
	inner join membercentral.dbo.ams_members as m on m.memberID = r.memberID
	where r.status = 'A'
	and tmp.rateBBAmount > 0;
		IF @@ROWCOUNT = 0 goto on_done;

	-- remove the registrants we have already handled
	delete bbr
	from #tmpBarBucksRegistrants as bbr
	inner join membercentral.dbo.ams_memberHistoryUsages as mhu on mhu.itemID = bbr.registrantID
		and mhu.[status] = 'A'
	inner join membercentral.dbo.ams_memberHistory as mh on mh.historyID = mhu.historyID
		and mh.[status] = 'A'
		and mh.siteID = @siteID
	where mhu.usageTypeID = @usageTypeID;

	declare @regCount int;
	select @regCount = count(*) from #tmpBarBucksRegistrants;
	SET @itemCount = @regCount;
	if @regCount = 0 goto on_done;

	-- process the remaining
	select @registrantID = min(registrantID) from #tmpBarBucksRegistrants;
	while @registrantID is not null begin
		select @bbAmount = null, @rateTransactionID = null, @invoiceProfileID = null, @assignedToMemberID = null,
			@dateBilled = null, @dateDue = null, @invoiceID = null, @invoiceNumber = null, 
			@xmlSchedule = null, @GLAccountID = null, @deferredGLAccountID = null, @eventID = null,
			@accrualDate = null, @deferredDate = null, @deferredDateStr = null, @regMemberID = null, 
			@bbAmountNeg = null, @newMemberHistoryID = null, @eventSiteResourceID = null, @historyDesc = null,
			@availCreditBalance = null, @rowID = null, @amtLeftToAllocate = null;

		select @bbAmount = tmpR.rateBBAmount, @eventID = tmpR.eventID, @regMemberID = tmp.memberID, 
			@bbAmountNeg = tmpR.rateBBAmount * -1, @eventSiteResourceID = tmpR.eventSiteResourceID
		from #tmpBarBucksRegistrants as tmp
		inner join #tmpBarBucksRates as tmpR on tmpR.rateID = tmp.rateID
		where tmp.registrantID = @registrantID;

		select @rateTransactionID = min(rt.transactionID)
		from membercentral.dbo.fn_ev_registrantTransactions(@registrantID) as rt
		inner join membercentral.dbo.tr_applications as a on a.orgID = @orgID and a.transactionID = rt.transactionID 
			and a.itemType = 'Rate' 
			and a.ItemID = @registrantID
			and a.status = 'A'
		where rt.typeID = @tr_Sale
		and rt.statusid = 1;
		
		while @rateTransactionID is not null begin

			select @invoiceProfileID = i.invoiceProfileID, @assignedToMemberID = i.assignedToMemberID,
				@dateBilled = i.dateBilled, @dateDue = i.dateDue, @GLAccountID = t.creditGLAccountID
			from membercentral.dbo.tr_invoiceTransactions as it
			inner join membercentral.dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
			inner join membercentral.dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
			where it.orgID = @orgID
			and it.transactionID = @rateTransactionID;

			SELECT TOP 1 @accrualDate = et.startTime, @historyDesc = 'Bar Bucks Rate increased for ' + cl.contentTitle
			FROM membercentral.dbo.ev_events as e 
			inner join membercentral.dbo.ev_times et on e.eventID = et.eventID   
			INNER JOIN membercentral.dbo.sites s on s.siteID = @siteid AND s.defaultTimeZoneID = et.timeZoneID
			INNER JOIN membercentral.dbo.cms_contentLanguages as cl on cl.siteID = @siteID and cl.contentID = e.eventContentID and cl.languageID = 1
			WHERE e.eventID = @eventID;

			select @deferredGLAccountID = membercentral.dbo.fn_tr_getDeferredGLAccountID(@GLAccountID);
			IF @deferredGLAccountID is not null begin
				set @deferredDate = case when @accrualDate > @nowDate then @accrualDate else @nowDate end;
				set @deferredDateStr = convert(varchar(10),@deferredDate,101);
				set @xmlSchedule = '<rows><row amt="' + cast(@bbAmount as varchar(10)) + '" dt="' + @deferredDateStr + '" /></rows>';
			end

			select @availCreditBalance = balance 
			from membercentral.dbo.tr_creditBalances 
			where orgID = @orgID
			and memberID = @regMemberID 
			and profileID = @mpprofileID;

			IF @availCreditBalance > 0 begin
				delete from @tblUnallocatedPayments;

				insert into @tblUnallocatedPayments (transactionID, unallocatedAmount, rowID)
				select t.transactionID, t.unallocatedAmount, ROW_NUMBER() OVER(order by t.transactionDate, t.transactionID) as rowID
				from membercentral.dbo.fn_tr_paymentsWithAvailableAmounts(@regMemberID,@orgID) as t
				inner join membercentral.dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
				inner join membercentral.dbo.mp_profiles as mp on mp.profileID = tp.profileID
				where mp.profileID = @mpprofileID;
			end

			BEGIN TRAN;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

				-- create invoice for adjustment
				EXEC membercentral.dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
					@assignedToMemberID=@assignedToMemberID, @dateBilled=@dateBilled, @dateDue=@dateDue, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

				-- adjust up rate transaction
				EXEC membercentral.dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, 
					@recordedByMemberID=@recordedByMemberID, @statsSessionID=0, @amount=@bbAmount, 
					@taxAmount=0, @transactionDate=@nowDate, @autoAdjustTransactionDate=1, 
					@saleTransactionID=@rateTransactionID, @invoiceID=@invoiceID, @byPassTax=0, 
					@byPassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@trashID OUTPUT;
			
				-- close invoice
				EXEC membercentral.dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;

				-- add history item tied to registrant
				EXEC membercentral.dbo.ams_addMemberHistory @typeID=1, @memberID=@regMemberID, @categoryID=@categoryID, 
					@subCategoryID=null, @userDate=@nowDateOnly, @userEndDate=null, @qty=null, 
					@dollarAmt=@bbAmountNeg, @description=@historyDesc, @linkMemberID=null, 
					@usageCSRID=@eventSiteResourceID, @usageResourceType='Event', @usageType='registrant', 
					@usageItemID=@registrantID, @enteredByMemberID=@recordedByMemberID, @historyID=@newMemberHistoryID OUTPUT:

				-- attempt to allocate BarBucks monies to the invoice
				IF @availCreditBalance > 0 begin
					set @amtLeftToAllocate = @bbAmount;
					
					select @rowID = min(rowID) from @tblUnallocatedPayments;
					while @rowID is not null begin
						select @allocamount = null, @paymentTransactionID = null;

						select @allocamount = unallocatedAmount, @paymentTransactionID = transactionID
						from @tblUnallocatedPayments 
						where rowID = @rowID;

						IF @amtLeftToAllocate <= @allocamount
							set @allocamount = @amtLeftToAllocate

						EXEC membercentral.dbo.tr_allocateToInvoice @recordedOnSiteID=@siteID, 
							@recordedByMemberID=@recordedByMemberID, @statsSessionID=0, @amount=@allocamount, 
							@transactionDate=@nowDate, @paymentTransactionID=@paymentTransactionID, 
							@invoiceID=@invoiceID;

						set @amtLeftToAllocate = @amtLeftToAllocate - @allocamount;
						IF @amtLeftToAllocate > 0
							select @rowID = min(rowID) from @tblUnallocatedPayments where rowID > @rowID;
						ELSE
							set @rowID = null;
					end
				end
			COMMIT TRAN;

			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select @rateTransactionID = min(rt.transactionID)
			from membercentral.dbo.fn_ev_registrantTransactions(@registrantID) as rt
			inner join membercentral.dbo.tr_applications as a on a.orgID = @orgID and a.transactionID = rt.transactionID 
				and a.itemType = 'Rate' 
				and a.ItemID = @registrantID
				and a.status = 'A'
			where rt.typeID = @tr_Sale
			and rt.statusid = 1
			and rt.transactionID > @rateTransactionID;
		end
						
		select @registrantID = min(registrantID) from #tmpBarBucksRegistrants where registrantID > @registrantID;
	end

	on_done:
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	IF OBJECT_ID('tempdb..#tmpBarBucksRates') IS NOT NULL
		DROP TABLE #tmpBarBucksRates;
	IF OBJECT_ID('tempdb..#tmpBarBucksRegistrants') IS NOT NULL
		DROP TABLE #tmpBarBucksRegistrants;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	IF membercentral.dbo.fn_getCurrentTransactionIsolationLevel() <> 'ReadCommitted'
		SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
