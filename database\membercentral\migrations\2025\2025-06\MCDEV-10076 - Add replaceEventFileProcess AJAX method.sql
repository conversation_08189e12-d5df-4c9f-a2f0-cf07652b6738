-- MCDEV-10076: Add replaceEventFileProcess AJAX method for event file replacement functionality

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @componentID int, @editEventRTFID int;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;
	CREATE TABLE #ajaxComponentMethods (autoid int IDENTITY(1,1), methodName varchar(500), resourceTypeFunctionID int, methodID int);

	SELECT @editEventRTFID = dbo.fn_getResourceTypeFunctionID(dbo.fn_getResourceTypeID('Event'),dbo.fn_getResourceFunctionID('EditEvent',dbo.fn_getResourceTypeID('Event')));

	INSERT INTO #ajaxComponentMethods(methodName, resourceTypeFunctionID)
	VALUES
		('processEventFileUpload', @editEventRTFID);

	EXEC dbo.ajax_addComponentMethodRightsBulk
		@componentName='ADMINEVENT',
		@requestCFC='model.admin.events.event',
		@componentID=@componentID OUTPUT;

	IF OBJECT_ID('tempdb..#ajaxComponentMethods') IS NOT NULL
		DROP TABLE #ajaxComponentMethods;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
END CATCH
