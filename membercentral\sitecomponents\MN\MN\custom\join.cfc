<cfcomponent extends="model.customPage.customPage" output="false">

	<cffunction name="init" access="private" returntype="void" output="false">

		<cfargument name="Event" type="any">
		<!--- variables --->
		<cfset variables.orgID 						= arguments.event.getTrimValue('mc_siteinfo.orgID')>
		<cfset variables.siteID 					= arguments.event.getTrimValue('mc_siteinfo.siteID')>

		<cfset variables.getCategoryStarted 		= application.objCustomPageUtils.mh_getCategory(variables.siteID,'MemAppHistory','Started')>
		<cfset variables.getCategoryCompleted 		= application.objCustomPageUtils.mh_getCategory(variables.siteID,'MemAppHistory','Completed')>
		
		<cfset variables.formNameDisplay 			= "Membership Form">
		<cfset variables.organization 				= arguments.event.getValue('mc_siteInfo.ORGShortName')>

		<cfset variables.profile_1._profileCode 	= "MAJCC">
		<cfset variables.profile_1._profileID 		= application.objCustomPageUtils.acct_getProfileID(siteid=arguments.event.getValue('mc_siteinfo.siteid'),profileCode=variables.profile_1._profileCode)>
		<cfset variables.profile_1._description 	= "#variables.organization# - #variables.formNameDisplay#">
		<cfset variables.qryStates 					= application.objCommon.getStates()>
		<cfset variables.qryCountry 				= application.objCommon.getCountries()>

		<cfset variables.gender						= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Gender") >
		<cfset variables.memberType					= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Contact Type") >
		<cfset variables.lgbt						= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="LGBT") >
		<cfset variables.marital					= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Marital Status") >
		<cfset variables.race						= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Race") >
		<cfset variables.qryPracticeAreas			= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Practice Areas") >
		<cfset variables.sizeOfLawFirm	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Size of Law Firm") >
		<cfset variables.iAmAAAANoFaultArbitrator	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="I am a AAA No-Fault Arbitrator") >
		<cfset variables.iAmACertifiedNeutralArbitrator	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="I am a Certified Neutral Arbitrator") >
		<cfset variables.countiesInWhichIServeAsAAAAArbitrator	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=variables.orgID, columnName="Counties in which I serve as a AAA arbitrator") >
		<cfset variables.requiredMembershipList = "Attorney,Judicial Law Clerk,Legal Service Lawyer,Paralegal,Retired Attorney,Student"/>
		<cfset variables.formName = "frmJoin">

		

		<!--- Email confirmation settings --->
		<cfset variables.emailSubject = "Thank you for joining Minnesota Association for Justice!">
		<cfset variables.strEMailSettings_staff = { 
				from="<EMAIL>", 
				to="<EMAIL>", 
				subject=variables.emailSubject,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		<cfset variables.strEMailSettings_member = { 
				from="<EMAIL>", 
				to=arguments.event.getTrimValue('email',''), 
				subject=variables.emailSubject,
				type="HTML",
				mailerid=arguments.event.getTrimValue('mc_siteinfo.sitename')
			}>
		
		<cfset variables.todayDate = now()>
		<cfif arguments.event.valueExists("testDate")>
			<cfset variables.todayDate = arguments.event.getValue("testDate")>
		</cfif>

		<!--- for form action --->
		<cfset variables.applicationReservedURLParams = "fa,sid">
		<cfset variables.baselink = "/?#getBaseQueryString(false)#">
	</cffunction>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset init(event=arguments.Event)>
		<cfset local.returnHTML = "">
		<cfset local.returnScript = "">

		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>
		<cfscript>
		// CUSTOM FIELD: ------------------------------------------------------------------------------------------------------
			local.arrCustomFields = [];
			local.tmpField = { name="FormInstructions", type="CONTENTOBJ", desc="Content at top of page 1", value="" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorTitle", type="STRING", desc="Account Locator Title", value="Account Lookup / Create New Account" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorButton", type="STRING", desc="Account Locator Button Text", value="Account Lookup" }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AccountLocatorInstructions", type="CONTENTOBJ", desc="Account Locator Instruction Text", value="Click the Account Lookup button to the left. Enter the search criteria and click Continue. If you see your name, please press the Choose button next to your name. If you do not see your name, click the Create an Account link." }; 
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AutoRenewMembership", type="STRING", desc="Auto-Renew Membership Heading", value="Auto-Renew Membership" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="AutoRenewMembershipContent", type="CONTENTOBJ", desc="Auto-Renew Membership Content", value="Content goes here" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="JoinFormTitle", type="STRING", desc="Join Form Title ", value="MAJ Membership Application" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MemberBenefits", type="CONTENTOBJ", desc="Member Benefits content", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipCriteriaAttestation", type="CONTENTOBJ", desc="Membership Criteria and Attestation", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="RequiredNotices", type="CONTENTOBJ", desc="Required Notices", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="CommitteeDescriptions", type="CONTENTOBJ", desc="Committee Descriptions", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MAJLegislativeFund", type="CONTENTOBJ", desc="MAJ Legislative Fund", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MAJJusticeFund", type="CONTENTOBJ", desc="MAJ Justice Fund", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="OptionalContribution", type="CONTENTOBJ", desc="Optional Contribution", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="TaxStatement", type="CONTENTOBJ", desc="Tax Statement", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="FormSubmission", type="CONTENTOBJ", desc="Form Submission", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="MembershipTypeContent", type="CONTENTOBJ", desc="Membership Type Content", value="" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			local.tmpField = { name="emailStaffTo", type="STRING", desc="Email ids of staff to send notices", value="<EMAIL>" };	
				arrayAppend(local.arrCustomFields, local.tmpField);
			
			variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'),siteResourceID=this.siteResourceID,arrCustomFields=local.arrCustomFields);

			StructAppend(variables, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
                formName='frmJoin',
                formNameDisplay=variables.strPageFields.JoinFormTitle,
                orgEmailTo=variables.strEMailSettings_staff.to,
                memberEmailFrom=variables.strEMailSettings_member.from
            ));
		</cfscript>
		<cfif len(trim(variables.strPageFields.emailStaffTo))>
			<cfset variables.strEMailSettings_staff.to = variables.strPageFields.emailStaffTo>
			<cfset variables.orgEmailTo = variables.strPageFields.emailStaffTo>
		</cfif>
		<cfsavecontent variable="local.returnScript">
			<cfinclude template ="commonScript.cfm">
		</cfsavecontent>
		<cfhtmlhead text="#local.returnScript#">

		<cfswitch expression="#arguments.event.getValue('fa','showForm')#">
			
			<cfcase value="processStep1">
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfif NOT structKeyExists(arguments.event.getCollection(), "newSubs") >
					<cfset local.del1 = structDelete(session.fieldArr,"newSubs") >
				</cfif>
				<cfset local.returnHTML = processStep1(event=arguments.event)>
			</cfcase>

			<cfcase value="processStep2">
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfset processStep2(event=arguments.event)>
			</cfcase>

			<cfcase value="complete">
				<cfif NOT isDefined("session.invoice")>
					<cflocation url="#variables.baselink#" addtoken="false">
				</cfif>
				<cfif structKeyExists(session, "historyId") >
					<cfset local.del1 = structDelete(session,"historyId") >
				</cfif>

				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
						<div style="clear:both;"></div>
						<h2>#variables.formNameDisplay#</h2>
						<div class="CPSection">
								<div class="NVTitle CPSectionTitle BB">Form Submission</div>
								 <div class="frmRow1 frmText" >
								 	<table cellspacing="0" cellpadding="2" border="0" width="100%">
										<tr class="frmRow1 BB">
											<td>#variables.strPageFields.FormSubmission#</td>
										</tr>
									</table>
								 </div>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>

			</cfcase>

			<cfcase value="mediator1To2">
				<cfset local.collection = arguments.event.getCollection()>
				
				<cfloop collection="#arguments.event.getCollection()#" item="local.key">
					<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
						and NOT listFindNoCase("fa,btnSubmit",local.key) 
						and left(local.key,9) neq "formfield"
						and left(local.key,4) neq "fld_">
						<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>
					</cfif>
				</cfloop>
				<cfif NOT structKeyExists(arguments.event.getCollection(), "notices") >
					<cfset local.del1 = structDelete(session.fieldArr,"notices") >
				</cfif>
				<cfif NOT structKeyExists(arguments.event.getCollection(), "membership_agreement") >
					<cfset local.del1 = structDelete(session.fieldArr,"membership_agreement") >
				</cfif>
				<cfif NOT structKeyExists(local.collection, "practiceAreas") >
					<cfset local.del1 = structDelete(session.fieldArr,"practiceAreas") >
				</cfif>
			</cfcase>

			<cfcase value="step2">
				<cfset local.saved = saveMember(event=arguments.event)>
				<cfif not structKeyExists(session, "historyId")>
					<cfset session.historyId = application.objCustomPageUtils.mh_addHistory(memberID=arguments.event.getValue("memberID"), categoryID=variables.getCategoryStarted.CATEGORYID,
						subCategoryID=variables.getCategoryStarted.SUBCATEGORYID, description='Join Form Started.', linkMemberID=0, enteredByMemberID=arguments.event.getValue("memberID"),
						newAccountsOnly=false)>
				</cfif>
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
					<div id="customPage">
						
						<h2>#variables.formNameDisplay#</h2>
													
						<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
						
						<div id="formTable">
							<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" 
							onsubmit="return _FB_validateForm(1);">
								<!--- <cfset local.collection = arguments.event.getCollection()> --->
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,amount,btnSubmit",local.key) 
										and left(local.key,7) neq "section"
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">

										<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>

										<cfif NOT listFindNoCase("membership,notices,membership_agreement",local.key) >
											<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
										</cfif>
									</cfif>
								</cfloop>
								<cfinput type="hidden" name="fa" id="fa" value="step3">
								<div class="TitleText" style="padding-bottom:15px;">
									<h3 class="titleHeading"> Step 2 - Membership <button type="button" id="gotoStep2" class="btn pull-right" onClick="getForm2Fields();" >Back</button></h3>
								</div>

								<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates" result="local.membershipRatesResult">
									SET NOCOUNT ON;

									declare @FID int, @scheduleID int, @currentDate datetime, @subscriptionID int;
									set @currentDate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#variables.todayDate#">;
									set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;

									select @scheduleID = scheduleID, @subscriptionID = subscriptionID
									from sub_subscriptions
									where uid IN ('c359fcf8-eb7f-4928-afa0-b42efcb129d7');

									select newRate.rateID, newRate.uid,  newRate.rateName, newRate.termEndDate,
										rs.scheduleName,rf.rateAmt,f.frequencyShortName,f.frequencyName,rf.rfid
									from dbo.sub_subscriptions subs
									inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID
										and rs.status = 'A'
										and subs.subscriptionID = @subscriptionID
									inner join dbo.sub_rates as newRate on newRate.scheduleID = rs.scheduleID 
										and newRate.status = 'A' 
										and newRate.isRenewalRate = 0
										and @currentDate between newRate.rateAFStartDate 
										and dateadd(day, datediff(day, 0, newRate.rateAFEndDate)+1, 0)
									inner join dbo.sub_rateFrequencies rf on  rf.rateID = newRate.rateID 
										and rf.status = 'A'
									inner join dbo.sub_frequencies as f on  f.frequencyID = rf.frequencyID
										and f.status = 'A'
										and f.frequencyShortName IN ('F', 'M', 'Q', 'S')
									inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteResourceID = newRate.siteResourceID
										AND srfrp.functionID = @FID and srfrp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.siteID#"> 
									inner join dbo.cache_perms_groupPrintsRightPrints gprp on srfrp.rightPrintID = gprp.rightPrintID
										and gprp.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#variables.siteID#"> 
									inner join dbo.ams_members m on m.groupPrintID = gprp.groupPrintID
										and m.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('memberID')#"> 
									order by newRate.rateORder;
								</cfquery>
								
									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Membership Type</div>
							 			<div class="frmRow1 frmText" style="padding:10px">
							 				<cfif local.membershipRates.recordCount>
							 					<div class="P">
													<label>Select a Membership Level</label>
												</div>
												<table width="100%" cellpadding="3" border="0" cellspacing="0">	
													<cfloop query="local.membershipRates">
													
														<cfset local.thisAmount = numberFormat(local.membershipRates.rateAmt, "___.__")>
														<cfset local.amountLabel = dollarFormat(local.thisAmount) />
														<tr>
															<td width="3%">
																<label class="radio"><input  <cfif structKeyExists(session.fieldArr, "membership") and session.fieldArr.membership eq local.membershipRates.rfid>checked </cfif> class="mship_check" id="#local.membershipRates.uid#"  type="radio" name="membership" value="#local.membershipRates.rfid#" frequency="#local.membershipRates.frequencyShortName#" amount ="#local.thisAmount#" subname="#local.membershipRates.rateName#"></label>
															</td>
															<td width="35%"><label><cfif local.membershipRates.rateName EQ "Retired Attorney">Retired<cfelse>#local.membershipRates.rateName#</cfif> (#local.membershipRates.frequencyName#)</label></td>
															<td><label>#local.amountLabel#</label></td>
													</cfloop>																
												</table>
											</cfif>
											<br/>
											#variables.strPageFields.MembershipTypeContent#				
										</div>
									</div>
									
									<div class="CPSection" id="renewagreeId">
										<div class="NVTitle CPSectionTitle BB">#variables.strPageFields.AutoRenewMembership#</div>
										<div class="frmRow1 frmText" style="padding:10px;">
											<label class="radio inline">
												<input name="renewAgree" type="radio" value="yes" /> Yes
											</label>
											<label class="radio inline">
												<input name="renewAgree" type="radio" value="no" /> No
											</label>
										</div>
									</div>	

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Member Benefits</div>
										<div class="frmRow1 frmText" style="padding:10px;">
											#variables.strPageFields.MemberBenefits#
										</div>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Membership Criteria and Attestation</div>
										<div class="frmRow1 frmText" style="padding:10px">
											<table cellspacing="0" cellpadding="2" border="0" width="100%">
												<tr class="frmRow1 BB">
													<td>#variables.strPageFields.MembershipCriteriaAttestation#</td>
												</tr>
												<tr class="frmRow1 BB checkboxRow">
													<td>
														<label class="checkbox">
															<input <cfif structKeyExists(session.fieldArr, "membership_agreement") >checked=checked</cfif> type="checkbox" name="membership_agreement"> I agree to the membership terms stated above.&nbsp;&nbsp;<span style="font-size:25px;vertical-align:middle" class="required">*</span>
														</label>
													</td>
												</tr>
											</table>
										</div>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Required Notices</div>
										<div class="frmRow1 frmText" style="padding:10px">
											<table cellspacing="0" cellpadding="2" border="0" width="100%">
												<tr class="frmRow1 BB">
													<td>#variables.strPageFields.RequiredNotices#</td>
												</tr>
												<tr class="frmRow1 BB checkboxRow">
													<td>
														<label class="checkbox">
															<input <cfif structKeyExists(session.fieldArr, "notices") >checked=checked</cfif> type="checkbox" name="notices">Please exclude my name from all rented list provided to selected users.
														</label>
													</td>
												</tr>
											</table>
										</div>
									</div>
									<div class="frmButtons step2">
										<button type="button" id="gotoStep2" class="btn" onClick="getForm2Fields();" >Back</button>
										<button style="float:right;" type="submit" name="btnToStep2" class="btn" >Continue</button>
									</div>
										
								</cfform>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step3">
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
						<div id="customPage">
							<h2>#variables.formNameDisplay#</h2>
							<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
							<div id="formTable">
								<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm(1);">

									<cfset local.collection = arguments.event.getCollection()>
									<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,amount,btnSubmit",local.key) 
										and left(local.key,7) neq "section"
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">

										<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>

										<cfif NOT listFindNoCase("newSubs",local.key) >
											<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
										</cfif>
									</cfif>
									</cfloop>
									
									<cfinput type="hidden" name="fa" id="fa" value="step4">

									<cfif NOT structKeyExists(arguments.event.getCollection(), "newSubs") >
										<cfset local.del1 = structDelete(session.fieldArr,"newSubs") >
									</cfif>

									<cfif NOT structKeyExists(arguments.event.getCollection(), "notices") >
										<cfset local.del1 = structDelete(session.fieldArr,"notices") >
									</cfif>
									<cfif NOT structKeyExists(arguments.event.getCollection(), "membership_agreement") >
										<cfset local.del1 = structDelete(session.fieldArr,"membership_agreement") >
									</cfif>

									<cfif structKeyExists(session.fieldArr, "newSubs")>
										<cfset  local.subscriptions = session.fieldArr.newSubs>
									<cfelse>
										<cfset local.subscriptions = ""> 
									</cfif>

									<div class="TitleText" style="padding-bottom:15px;">
										<h3 class="titleHeading"> Step 3 - Committees & Sections <button type="button" id="gotoStep2" class="btn pull-right" onClick="getFormFields();" >Back</button></h3>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Committee Descriptions</div>
										<div class="frmRow1 frmText" style="padding:10px;">
											#variables.strPageFields.CommitteeDescriptions#
										</div>
									</div>

									<cfset local.qryAvailableSections = application.objCustomPageUtils.getAvailableSections(memberID=arguments.event.getTrimValue('memberid'),subTypeUID='34f7b3d4-8d52-46b0-9668-403978f3045a',siteID= variables.siteID)>
									<cfif local.qryAvailableSections.recordCount>
										<div class="CPSection">
											<div class="NVTitle CPSectionTitle BB">Committee Selections</div>
											<div class="frmRow1 frmText" >
												<div class="P">
													Please select committees that would you like to join
												</div>
												<table cellspacing="0" cellpadding="2" border="0" width="100%">
													<cfloop query="local.qryAvailableSections">
														<tr class="checkboxRow">
															<td>
																<input 
																	<cfif listFindNoCase(local.subscriptions, "#local.qryAvailableSections.SUBSCRIPTIONID#")>checked=checked</cfif>
																	type="checkbox"  class="checkbox" value="#local.qryAvailableSections.SUBSCRIPTIONID#" name="newSubs" id="sectionDivision1_#local.qryAvailableSections.SUBSCRIPTIONID#"><label for="sectionDivision1_#local.qryAvailableSections.SUBSCRIPTIONID#">#local.qryAvailableSections.subscriptionname#</label>
															</td>
														</tr>
													</cfloop>
												</table>
											</div>
										</div>
									</cfif>
									<div class="frmButtons step1">
										<button type="button" id="gotoStep2" class="btn" onClick="getFormFields();" >Back</button>
										<button style="float:right;" type="submit" name="btnToStep2" class="btn" >Continue</button>
									</div>
								</cfform>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="step4">
				<cfsavecontent variable="local.returnHTML">
					<cfoutput>
						<div id="customPage">
							<h2>#variables.formNameDisplay#</h2>
							<div id="issuemsg" style="display:none;margin:6px 0 10px 0;"></div>
							<div id="formTable">
								<cfform name="#variables.formName#" id="#variables.formName#" method="post" action="#variables.baselink#" onsubmit="return _FB_validateForm(1);">

									<cfset local.collection = arguments.event.getCollection()>
									<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,amount,btnSubmit",local.key) 
										and left(local.key,7) neq "section"
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">

										<cfset session.fieldArr[local.key] = arguments.event.getValue(local.key)>

										<cfif NOT listFindNoCase("legislative_fund,other_legislative_fund,oneTime_legislative_fund,justice_fund,other_justice_fund,oneTime_justice_fund",local.key) >
											<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
										</cfif>
									</cfif>
									</cfloop>
									
									<cfinput type="hidden" name="fa" id="fa" value="processStep1">

									<div class="TitleText" style="padding-bottom:15px;">
										<h3 class="titleHeading"> Step 4 - Additional Contributions <button type="button" id="gotoStep2" class="btn pull-right" onClick="getFormFields();" >Back</button></h3>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">MAJ Legislative Fund</div>
										<div class="frmRow1 frmText" >
											<div class="P">
												#variables.strPageFields.MAJLegislativeFund#
											</div>
											<div class="P">
												<label>Monthly Legislative Fund Contributions</label>
											</div>
											<div class="P">
												<table cellspacing="0" cellpadding="2" border="0" width="100%">
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="legislative_fund_10" >
																<input type="radio" <cfif structKeyExists(session.fieldArr, "legislative_fund") and  (session.fieldArr.legislative_fund eq 10) >checked="checked"</cfif>  value="10" name="legislative_fund" id="legislative_fund_10">
																$10/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="legislative_fund_25" >
																<input type="radio" <cfif structKeyExists(session.fieldArr, "legislative_fund") and  (session.fieldArr.legislative_fund eq 25) >checked="checked"</cfif>  value="25" name="legislative_fund" id="legislative_fund_25">$25/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="legislative_fund_50">
																<input type="radio" <cfif structKeyExists(session.fieldArr, "legislative_fund") and  (session.fieldArr.legislative_fund eq 50) >checked="checked"</cfif>  value="50" name="legislative_fund" id="legislative_fund_50">$50/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="legislative_fund_other">
																<input type="radio" <cfif structKeyExists(session.fieldArr, "legislative_fund") and  (session.fieldArr.legislative_fund eq 'other') >checked="checked"</cfif>  value="other" name="legislative_fund" id="legislative_fund_other">Other Monthly Amount
															</label>

															<cfif structKeyExists(session.fieldArr, "other_legislative_fund") and session.fieldArr.other_legislative_fund neq 0  >
																<cfset local.other_legislative_fund = session.fieldArr.other_legislative_fund >
															<cfelse>
																<cfset local.other_legislative_fund = "" >
															</cfif>
															&nbsp;$&nbsp;<input type="text" value="#local.other_legislative_fund#" name="other_legislative_fund" id="other_legislative_fund" value="">
														</td>
													</tr>
													<tr>
														<td colspan="2">
															&nbsp;
														</td>
													</tr>
													<tr>
														<td colspan="2">
															<div class="P">
																<label>One Time Contribution to MAJ Legislative Fund</label>
															</div>
														</td>
													</tr>
													<tr>
														<td colspan="2">
															<cfif structKeyExists(session.fieldArr, "oneTime_legislative_fund") and session.fieldArr.oneTime_legislative_fund neq 0 >
																<cfset local.oneTime_legislative_fund = session.fieldArr.oneTime_legislative_fund >
															<cfelse>
																<cfset local.oneTime_legislative_fund = "" >
															</cfif>
															$&nbsp;<input type="text" value="#local.oneTime_legislative_fund#" name="oneTime_legislative_fund" id="oneTime_legislative_fund">
														</td>
													</tr>
												</table>
											</div>
										</div>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">MAJ Justice Fund</div>
										<div class="frmRow1 frmText" >
											<div class="P">
												#variables.strPageFields.MAJJusticeFund#
											</div>
											<div class="P">
												<label>Monthly Justice Fund Contributions</label>
											</div>
											<div class="P">
												<table cellspacing="0" cellpadding="2" border="0" width="100%">
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="justice_fund_10" >
																<input type="radio" <cfif structKeyExists(session.fieldArr, "justice_fund") and  (session.fieldArr.justice_fund eq 10) >checked="checked"</cfif>  value="10" name="justice_fund" id="justice_fund_10">$10/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="justice_fund_25">
																<input type="radio" <cfif structKeyExists(session.fieldArr, "justice_fund") and  (session.fieldArr.justice_fund eq 25) >checked="checked"</cfif>  value="25" name="justice_fund" id="justice_fund_25">$25/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="justice_fund_50">
																<input type="radio" <cfif structKeyExists(session.fieldArr, "justice_fund") and  (session.fieldArr.justice_fund eq 50) >checked="checked"</cfif>  value="50" name="justice_fund" id="justice_fund_50">$50/month
															</label>
														</td>
													</tr>
													<tr class="checkboxRow">
														<td>
															<label class="radio" for="justice_fund_other">
																<input type="radio" <cfif structKeyExists(session.fieldArr, "justice_fund") and  (session.fieldArr.justice_fund eq 'other') >checked="checked"</cfif>  value="other" name="justice_fund" id="justice_fund_other">Other Monthly Amount
															</label>
															<cfif structKeyExists(session.fieldArr, "other_justice_fund") and session.fieldArr.other_justice_fund neq 0  >
																<cfset local.other_justice_fund = session.fieldArr.other_justice_fund >
															<cfelse>
																<cfset local.other_justice_fund = "" >
															</cfif>
															&nbsp;$&nbsp;<input type="text" value="#local.other_justice_fund#" name="other_justice_fund" id="other_justice_fund">
														</td>
													</tr>
													<tr>
														<td colspan="2">
															&nbsp;
														</td>
													</tr>
													<tr>
														<td colspan="2">
															<div class="P">
																<label>One Time Contribution to MAJ justice Fund</label>
															</div>
														</td>
													</tr>
													<tr>
														<td colspan="2">
															<cfif structKeyExists(session.fieldArr, "oneTime_justice_fund") and session.fieldArr.oneTime_justice_fund neq 0  >
																<cfset local.oneTime_justice_fund = session.fieldArr.oneTime_justice_fund >
															<cfelse>
																<cfset local.oneTime_justice_fund = "" >
															</cfif>
															$&nbsp;<input type="text" value="#local.oneTime_justice_fund#" name="oneTime_justice_fund" id="oneTime_justice_fund">
														</td>
													</tr>
												</table>
											</div>
										</div>
									</div>

									<div class="CPSection">
										<div class="frmRow1 frmText" style="padding:10px;">
											#variables.strPageFields.OptionalContribution#
										</div>
									</div>

									<div class="CPSection">
										<div class="NVTitle CPSectionTitle BB">Tax Statement</div>
										<div class="frmRow1 frmText" style="padding:10px;">
											#variables.strPageFields.TaxStatement#
										</div>
									</div>
										
									<div class="frmButtons step1">
										<button type="button" id="gotoStep2" class="btn" onClick="getFormFields();" >Back</button>
										<button style="float:right;" type="submit" name="btnToStep2" class="btn" >Continue</button>
									</div>
								</cfform>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>

			<cfcase value="backto1">
				<cfset local.memberData.phone = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID)>
				<cfset local.memberData.officeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Work")>
				<cfset local.memberData.homeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Home")>
				<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
				<cfsavecontent variable="local.returnHTML">
					<cfinclude template="join_backto1.cfm">
				</cfsavecontent>
			</cfcase>
			
			<cfdefaultcase>
				<cfif structKeyExists(session, "historyId") >
					<cfset local.del1 = structDelete(session,"historyId") >
				</cfif>
				<cfset local.returnHTML = afterAccountSelection(event=arguments.event)>
			</cfdefaultcase>
		</cfswitch>
		<cfset local.returnHTML = local.returnHTML &''& local.returnScript >
		<cfreturn returnAppStruct(local.returnHTML,"echo")>
	</cffunction>	
	
	<cffunction name="afterAccountSelection" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.strMsgCodes = structNew()>
		<cfset local.strMsgCodes['507D7690-F01F-AF51-C9CCB83F029DD786'] = { err=1, msg="This submission has been flagged as spam and was not submitted." }>
		<cfset local.strMsgCodes['50BA4017-F01F-AF51-C9CCE480104528F0'] = { err=1, msg="This submission is missing information. Ensure you have entered all required fields and the e-mail address is valid." }>
		<!--- setup memberdata struct and prefill with logged in member --->
		<cfset local.memberData = {}>
		<cfset session.fieldarr = {}>
		<cfset local.memberData.phone = application.objMember.getMemberPhones(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID)>
		<cfset local.memberData.officeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Work")>
		<cfset local.memberData.homeAddress = application.objMember.getMemberAddressByAddressType(orgID=variables.orgID, memberID=session.cfcUser.memberData.memberID, addressType="Home")>
		<cfset local.qryOrgProLicenseStatuses = application.objOrgInfo.getOrgProfessionalLicenseStatuses(orgID=variables.orgID)>
		
		<cfsavecontent variable="local.returnHTML">
			<cfinclude template ="join_default.cfm">
		</cfsavecontent>		
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processStep1" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset local.memAmt = 0 />
		<cfset local.legislative_fund = 0 />
		<cfset local.justice_fund = 0 />
		<cfset local.oneTime_legislative_fund = 0 />
		<cfset local.oneTime_justice_fund = 0 />
		

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"memberid") >
			<cfset local.memberid =session.fieldArr.memberid >
		<cfelse>
			<cfset local.memberid =arguments.event.getTrimValue('memberid') >
		</cfif>
		<cfif local.memberid EQ 0 OR NOT IsNumeric(local.memberid)>
			<cfcontent type="text/html; charset=UTF-8"><cfheader statuscode="404" statustext="File Not Found"><cfabort>
		</cfif>
		
		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"membership") >
			<cfset local.membership =session.fieldArr.membership >
		<cfelse>
			<cfset local.membership =event.getValue('membership','') >
		</cfif>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates">
		 	select  r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt,f.frequencyShortName,f.frequencyName,r.termEndDate,rf.rfid
			from dbo.sub_rates as r   
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
				and rs.status = 'A'
				and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
				and r.isRenewalRate = 0
				and rs.uid IN ('2652c419-5da6-4772-8ae4-8f41b91a368f','523328cf-0594-4adc-a9e7-cb41fe32a6e6','01ff53d5-058d-4ab7-99aa-85c14c9b7f26')
				and r.status = 'A'
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
				and rf.status = 'A'
				and rf.rfid = '#local.membership#'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A'
			 ORDER BY r.rateName
		</cfquery>	

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFullRate">
			select  r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt,f.frequencyShortName,f.frequencyName,r.termEndDate,rf.rfid
			from
			dbo.sub_rates as r   
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
				and rs.status = 'A'
				and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
				and  r.isRenewalRate = 0
				and rs.uid IN ('2652c419-5da6-4772-8ae4-8f41b91a368f','523328cf-0594-4adc-a9e7-cb41fe32a6e6','01ff53d5-058d-4ab7-99aa-85c14c9b7f26')
				and r.status = 'A'
			inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
				and rf.status = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.status = 'A'
				and (
					(rs.uid = '2652c419-5da6-4772-8ae4-8f41b91a368f' AND f.frequencyShortName IN ('F', 'M', 'Q', 'S'))
					OR
					(rs.uid <> '2652c419-5da6-4772-8ae4-8f41b91a368f' AND f.frequencyShortName = 'F')
				)
			where
				r.rateID = #local.membershipRates.rateID#
		</cfquery>
		<cfset local.thisMembershipAmt = local.membershipRates.rateAmt>

		<!--- Legislative Amount --->

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"legislative_fund") >
			<cfset local.legislative_fund =session.fieldArr.legislative_fund >
		<cfelseif len(arguments.event.getValue('legislative_fund',''))>
			<cfset local.legislative_fund =arguments.event.getTrimValue('legislative_fund') >
		</cfif>
		
	
		<cfif local.legislative_fund eq 'other'>
			<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"other_legislative_fund") >
				<cfset local.legislative_fund =session.fieldArr.other_legislative_fund >
			<cfelseif len(arguments.event.getValue('other_legislative_fund',''))>
				<cfset local.legislative_fund =arguments.event.getTrimValue('other_legislative_fund') >
			</cfif>
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"oneTime_legislative_fund") >
			<cfset local.oneTime_legislative_fund =session.fieldArr.oneTime_legislative_fund >
		<cfelseif len(arguments.event.getValue('oneTime_legislative_fund',''))>
			<cfset local.oneTime_legislative_fund =arguments.event.getTrimValue('oneTime_legislative_fund') >
		</cfif>

		<!--- Justice Amount --->

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"justice_fund") >
			<cfset local.justice_fund =session.fieldArr.justice_fund >
		<cfelseif len(arguments.event.getValue('justice_fund',''))>
			<cfset local.justice_fund =arguments.event.getTrimValue('justice_fund') >
		</cfif>
		
	
		<cfif local.justice_fund eq 'other'>
			<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"other_justice_fund") >
				<cfset local.justice_fund =session.fieldArr.other_justice_fund >
			<cfelseif len(arguments.event.getValue('other_justice_fund',''))>
				<cfset local.justice_fund =arguments.event.getTrimValue('other_justice_fund') >
			</cfif>
		</cfif>

		<cfif structKeyExists(session, "fieldArr")  && structKeyExists(session.fieldArr,"oneTime_justice_fund") >
			<cfset local.oneTime_justice_fund =session.fieldArr.oneTime_justice_fund >
		<cfelseif len(arguments.event.getValue('oneTime_justice_fund',''))>
			<cfset local.oneTime_justice_fund =arguments.event.getTrimValue('oneTime_justice_fund') >
		</cfif>

		<cfset local.totalAmount = local.thisMembershipAmt>
		<cfset local.totalAmount = local.totalAmount + local.legislative_fund + local.justice_fund + local.oneTime_legislative_fund + local.oneTime_justice_fund >
		<cfset local.formName = "frmPACPay">

		<cfset local.qryMerchantProfile = application.objCustomPageUtils.getMerchantProfileDetails(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profileID=variables.profile_1._profileID)>
		<cfset local.paymentArgs = application.objPayments.getChargeInfoAndPaymentFeatures(
			"amt": local.totalAmount, 
			"strprocessingfee":{ 
				"enable": local.qryMerchantProfile.enableProcessingFeeDonation,
				"feepct": val(local.qryMerchantProfile.processFeeDonationFeePercent),
				"select": val(local.qryMerchantProfile.processFeeDonationDefaultSelect) EQ 1 ? 1 : 0, 
				"label": local.qryMerchantProfile.processFeeOtherPaymentsFELabel,
				"denylabel": local.qryMerchantProfile.processFeeOtherPaymentsFEDenyLabel,
				"title": local.qryMerchantProfile.processFeeDonationFETitle,
				"msg": local.qryMerchantProfile.processFeeDonationFEMsg,
				"gl": local.qryMerchantProfile.processFeeDonationRenevueGLAccountID
			},
			"enableSurcharge": 0,
			"enableApplePay": 0,
			"enableGooglePay": 0,
			"stateIDForTax": arguments.event.getValue('business_stateID',0),
			"zipForTax": arguments.event.getValue('business_zip','')
		)>
		
		<cfset variables.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=variables.profile_1._profileCode,
														pmid = local.memberid, showCOF = local.memberid EQ session.cfcUser.memberData.memberID, usePopup=false, usePopupDIVName='ccForm', autoShowForm=1, 
														paymentFeatures=local.paymentArgs.paymentFeatures, chargeInfo=local.paymentArgs.chargeInfo)>
		<cfif len(variables.profile_1.strPaymentForm.headCode)>
			<cfhtmlhead text="#application.objCommon.minText(variables.profile_1.strPaymentForm.headCode)#">
		</cfif>
		
		<cfsavecontent variable="local.returnHTML">
			<cfoutput>
				<script type="text/javascript"> 
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();

						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();

						if( MethodOfPaymentValue == 'CC' )	{
							#variables.profile_1.strPaymentForm.jsvalidation#	
						}
						
						if (arrReq.length > 0) {
							var msg = 'Please address the following issues with your application:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}					
				</script>
			<div>
				<h2>#variables.formNameDisplay#</h2>
				<div  style="padding-bottom:15px;text-align:right">
					<button type="button" id="gotoStep2" class="btn" onClick="getFormFields();" >Back</button>
				</div>
				<div class="CPSection">
					<div class="CPSectionTitle NVTitle">Membership and Additional Selections</div>
					<div class="frmRow1 frmText" >
						<table cellspacing="0" cellpadding="4" border="0" width="100%">
							<tr>
								<td width="95%"><label>Membership Type - <cfif local.membershipRates.rateName EQ "Retired Attorney">Retired<cfelse>#local.membershipRates.rateName#</cfif> (#local.membershipRates.frequencyName#)</label> </td>
								<td class="r"><label>#dollarFormat(local.thisMembershipAmt)#&nbsp;</label></td>
							</tr>
							
							<cfif local.legislative_fund neq 0>
								<tr>
									<td width="95%"><label>MAJ Legislative Fund - Monthly Contribution</label></td>
									<td class="r"><label>#dollarFormat(local.legislative_fund)#&nbsp;</label></td>
								</tr>
							</cfif>

							<cfif local.oneTime_legislative_fund neq 0>
								<tr>
									<td width="95%"><label>MAJ Legislative Fund - One Time Contribution</label></td>
									<td class="r"><label>#dollarFormat(local.oneTime_legislative_fund)#&nbsp;</label></td>
								</tr>
							</cfif>

							<cfif local.justice_fund neq 0>
								<tr>
									<td width="95%"><label>MAJ Justice Fund - Monthly Contribution</label></td>
									<td class="r"><label>#dollarFormat(local.justice_fund)#&nbsp;</label></td>
								</tr>
							</cfif>

							<cfif local.oneTime_justice_fund neq 0>
								<tr>
									<td width="95%"><label>MAJ Justice Fund - One Time Contribution</label></td>
									<td class="r"><label>#dollarFormat(local.oneTime_justice_fund)#&nbsp;</label></td>
								</tr>
							</cfif>

							<tr>
								<td width="95%"><label><b>Total Charge</b> &nbsp;</label></td><td class="r"><label><b>#numberFormat(local.totalAmount,"_$__.__")#</b>&nbsp;</label>
								</td>
							</tr>
						</table>
					</div>
					<div id="paymentTable">
						<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
						<div class="form">
							<cfform name="#local.formName#" id="#local.formName#" method="post" action="#variables.baselink#" 
							onsubmit="return _FB_validateForm();">
								<cfinput type="hidden" name="fa" id="fa" value="processStep2">
								<cfset local.collection = arguments.event.getCollection()>
								<cfloop collection="#arguments.event.getCollection()#" item="local.key">
									<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
										and NOT listFindNoCase("fa,btnSubmit",local.key) 
										and left(local.key,9) neq "formfield"
										and left(local.key,4) neq "fld_">
										<cfset session.fieldArr[local.key] = 	arguments.event.getValue(local.key)>
										<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
									</cfif>
								</cfloop>
								
								<cfif local.totalAmount gt 0>
								<div class="NVTitle CPSectionTitle BB">Payment Options</div>
								<div class="frmRow1 nvRow">
									<table cellpadding="2" cellspacing="0" width="100%" border="0">
										<tr valign="top">
											<td style="padding-left:10px;" colspan="2"><label><br>Please select your preferred method of payment from the options.</br></label></td>
										</tr>
										<tr>
											<td>
												<div class="P">
													<table cellpadding="2" cellspacing="0" width="100%" border="0">
														<tr>
															<td width="25">
																<label class="radio">
																	<input  value="CC" class="tsAppBodyText optionsRadio" name="payMeth" id="payMethCC" type="radio" onClick="checkPaymentMethod();">
																	Pay by Credit Card
																</label>
															</td>
														</tr>
														<tr>
															<td width="25">
																<label class="radio">
																	<input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="javascript:{checkPaymentMethod();}">Pay by Check
																</label>
															</td>
														</tr>														
													</table>
												</div>
											</td>
										</tr>
									</table>
								</div>
								<div id="CCInfo" class="frmRow1" style="display:none;">
									<div class="CPSectionTitle NVTitle">Credit Card Information</div>
									<div id="ccForm" style="padding:10px;">
										<div>#variables.profile_1.strPaymentForm.inputForm#</div>
										<div><button type="submit" name="btnSubmit" class="btn" data-prevent-text-update="true">SUBMIT</button></div>
									</div>
								</div>
								<div id="CheckInfo" style="display:none;" class="frmRow1">
									<div class="CPSectionTitle BB">Check Payment</div>
									<div class="P add_pad frmRow1">
										<br/><br/>
										<p style="font-size:15px;font-weight:bold;">
										Please mail payment to: <br/><br/> Minnesota Association for Justice<br/> 706 2nd Avenue South<br/> Baker Building - Suite 140<br/> Minneapolis, MN 55402
										<br/><br/>
										</p>
									</div>
									<div class="add_pad"><button type="submit" class="btn" name="btnSubmit">SUBMIT</button></div>
								</div>
								<cfelse>
									<div class="add_pad"><button type="submit" class="btn" name="btnSubmit">SUBMIT</button></div>
								</cfif>
																
								
								<div id="CheckInfo" style="display:none;" class="CPSection">
									<div class=""><button type="submit" class="btn" name="btnSubmit">SUBMIT</button></div>
								</div>
								<div style="" class="frmButtons step1 PL">
									<button type="button" id="gotoStep2" class="btn" onClick="getFormFields();" >Back</button>
								</div>
							</cfform>
						</div>
					</div>
				</div>
			</div>
			</cfoutput>
		</cfsavecontent>
		<cfreturn local.returnHTML>
	</cffunction>
	
	<cffunction name="processStep2" access="private" output="false" returntype="Query">
		<cfargument name="Event" type="any">
		<cfargument name="SectionsNames" type="struct">

		<cfset var local = structNew()>
		<cfset local.myArrayList = arrayNew(1)>		

		<cfif arguments.event.getTrimValue('memberid') EQ 0 OR NOT IsNumeric(arguments.event.getTrimValue('memberid'))>
			<cfcontent type="text/html; charset=UTF-8"><cfheader statuscode="404" statustext="File Not Found"><cfabort>
		</cfif>

		<!--- UPDATE MEMBER RECORD  --->
		<cftry>
			<cfset local.recordUpdated = false>
			<cfset local.newMemIdArr = application.mcCacheManager.sessionGetValue(keyname='newMemIdArr', defaultValue=arrayNew(1))>
			<cfif IsArray(local.newMemIdArr) AND listFind(ArrayToList(local.newMemIdArr),arguments.event.getTrimValue('memberid'))>
				<cfscript>
					local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'));

					local.objSaveMember.setDemo(firstName=arguments.event.getValue('firstName',''),middleName=arguments.event.getValue('middleName',''), lastName=arguments.event.getValue('lastName',''),company=arguments.event.getValue('company_name',''));
					if (len(arguments.event.getValue('email'))) {
						local.objSaveMember.setEmail(type='Primary Email', value=arguments.event.getValue('email'));
					}
					if (len(arguments.event.getValue('website'))) {
						local.objSaveMember.setWebsite(type='Website', value=arguments.event.getValue('website'));
					}
					
					local.objSaveMember.setAddress(type='Work', address1=arguments.event.getValue('business_address',''),address2=arguments.event.getValue('business_address_2',''), city=arguments.event.getValue('business_city',''), stateID=arguments.event.getValue('business_stateID',0), postalCode=arguments.event.getValue('business_zip',''));

					local.objSaveMember.setPhone(addressType='Work',type='Telephone',value=arguments.event.getValue('business_phone',''));
					local.objSaveMember.setPhone(addressType='Work',type='Mobile',value=arguments.event.getValue('business_cell',''));
					local.objSaveMember.setPhone(addressType='Work',type='Fax',value=arguments.event.getValue('business_fax',''));

					local.objSaveMember.setAddress(type='Home', address1=arguments.event.getValue('home_address',''),address2=arguments.event.getValue('home_address_2',''), city=arguments.event.getValue('home_city',''), stateID=arguments.event.getValue('home_stateID',0), postalCode=arguments.event.getValue('home_zip',''));

					local.objSaveMember.setPhone(addressType='Home',type='Telephone',value=arguments.event.getValue('home_phone',''));
					local.objSaveMember.setPhone(addressType='Home',type='Mobile',value=arguments.event.getValue('home_cell',''));
					local.objSaveMember.setPhone(addressType='Home',type='Fax',value=arguments.event.getValue('home_fax',''));

					local.objSaveMember.setRecordType(recordType='Individual');
					local.objSaveMember.setMemberType(memberType='User');
					
					if (len(event.getValue('nickname',''))){
						local.objSaveMember.setCustomField (field='Nickname', value=event.getValue('nickname',''));
					}
					if (len(event.getValue('work',''))){
						local.objSaveMember.setCustomField (field='Member for whom you work', value=event.getValue('work',''));
					}
					if (len(event.getValue('birth_year',''))){
						local.objSaveMember.setCustomField (field='Birth Year', value=event.getValue('birth_year',''));
					}
					if (len(event.getValue('spouse_name',''))){
						local.objSaveMember.setCustomField (field='Spouse', value=event.getValue('spouse_name',''));
					}		
					if (len(event.getValue('sizeOfLawFirm',''))){
						local.objSaveMember.setCustomField (field='Size of Law Firm', value=event.getValue('sizeOfLawFirm',''));
					}				
					
					if (len(event.getValue('gender',''))){
						local.objSaveMember.setCustomField (field='Gender', value=event.getValue('gender',''));
					}
					if (event.getValue('renewAgree','') eq 'yes') {
						local.objSaveMember.setCustomField(field='Allow Automatic Renewal', value=event.getValue('renewAgree',''));
					}
					else{
						local.objSaveMember.setCustomField(field='Allow Automatic Renewal', value=event.getValue('renewAgree',''));
					}
					if (len(event.getValue('lgbt',''))){
						local.objSaveMember.setCustomField (field='LGBT', value=event.getValue('lgbt',''));
					}
					if (len(event.getValue('iAmAAAANoFaultArbitrator',''))){
						local.objSaveMember.setCustomField (field='I am a AAA No-Fault Arbitrator', value=event.getValue('iAmAAAANoFaultArbitrator',''));
					}
					if (len(event.getValue('countiesInWhichIServeAsAAAAArbitrator',''))){
						local.objSaveMember.setCustomField (field='Counties in which I serve as a AAA arbitrator', value=event.getValue('countiesInWhichIServeAsAAAAArbitrator',''));
					}
					if (len(event.getValue('iAmACertifiedNeutralArbitrator',''))){
						local.objSaveMember.setCustomField (field='I am a Certified Neutral Arbitrator', value=event.getValue('iAmACertifiedNeutralArbitrator',''));
					}				
					if (len(event.getValue('marital_status',''))){
						local.objSaveMember.setCustomField (field='Marital Status', value=event.getValue('marital_status',''));
					}
					if (len(event.getValue('race',''))){
						local.objSaveMember.setCustomField (field='Race', value=event.getValue('race',''));
					}
					if (len(event.getValue('practiceAreas',''))){
						local.objSaveMember.setCustomField (field='Practice Areas', valueID=event.getValue('practiceAreas',''));
					}
					if ((len(event.getValue('barDate_new',''))) and (len(event.getValue('attorney_id',''))) and (len(event.getValue('bar_status','')))) {
			     		local.objSaveMember.setProLicense(name='Minnesota',status=event.getValue('bar_status',''),license=event.getValue('attorney_id'), date=event.getValue('barDate_new',''));
					}
					if ((len(event.getValue('barDate_outOfState_new',''))) and (len(event.getValue('attorney_outOfState_id',''))) and (len(event.getValue('bar_outOfState_status','')))) {
			     		local.objSaveMember.setProLicense(name='Out of State',status=event.getValue('bar_outOfState_status',''),license=event.getValue('attorney_outOfState_id'), date=event.getValue('barDate_outOfState_new',''));
					}
				</cfscript>
				
				<cfset local.strResult = local.objSaveMember.saveData()>
				<cfif local.strResult.success>
					<cfset local.recordUpdated = true>	
				<cfelse>					
					<cfset local.recordUpdated = false>	
				</cfif>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.recordUpdated = false>
		</cfcatch>
		</cftry>
		
		<!--- get statecode from stateid --->
		<cfif len(event.getValue('business_stateID',''))>
			<cfquery name="local.qryGetBusinessState" dbtype="query">
				select stateName
				from variables.qryStates
				where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('business_stateID',0)#">
			</cfquery>
		</cfif>

		<!--- get statecode from stateid --->
		<cfif len(event.getValue('home_stateID',''))>
			<cfquery name="local.qryGetHomeState" dbtype="query">
				select stateName
				from variables.qryStates
				where stateID = <cfqueryparam cfsqltype="cf_sql_integer" value="#event.getValue('home_stateID',0)#">
			</cfquery>
		</cfif>

		<!--- get selected commitees names --->
		<cfif len(event.getValue('newSubs',''))>
			<cfset local.qryAvailableSections = application.objCustomPageUtils.getAvailableSections(memberID=arguments.event.getTrimValue('memberid'),subTypeUID='34f7b3d4-8d52-46b0-9668-403978f3045a',siteID= variables.siteID,newSubs=event.getValue('newSubs',''))>
		</cfif>

		<!--- construct email / confirmation --->
		<cfset local.historyId  = application.objCustomPageUtils.mh_updateHistory(arguments.event.getTrimValue('memberid'),session.historyId,variables.getCategoryCompleted.SUBCATEGORYID,'Join form completed.',false)>
		<cfsavecontent variable="local.invoice">
			<cfoutput>
				<style>
					.highlightRow{background-color: yellow;}
				</style>
				<cfsavecontent variable="local.pageCSS">
					<cfoutput>
					<style type="text/css">
						body { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.customPage { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						p { font-size:12pt; font-family: Calibri,Arial,Helvetica; }
						.msgHeader{ background:##224563; color:##fff;font-weight:bold; padding:5px; }
						.frmText{ font-size:12pt; color:##505050; } 
						.b{ font-weight:bold; }
					</style>
					</cfoutput>
				</cfsavecontent>
				<!-- @accResponseMessage@ -->
				<p>#variables.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
				<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage ">			
				<tr class="msgHeader"><td colspan="2" class="b">Member Information:</td></tr>
				
				<tr ><td class="frmText" width="50%">MemberNumber:</td><td class="frmText">#event.getValue('memberNumber','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Member Type:</td><td class="frmText">#event.getValue('memberType','')#&nbsp;</td></tr>
				<tr ><td class="frmText">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Nickname:</td><td class="frmText">#event.getValue('nickname','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Firm Name:</td><td class="frmText">#event.getValue('company_name','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Firm/Practice Contact:</td><td class="frmText">#event.getValue('contact','')#&nbsp;</td></tr><tr ><td class="frmText">Firm Address 1:</td><td class="frmText">#event.getValue('business_address','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Firm Address 2:</td><td class="frmText">#event.getValue('business_address_2','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">City:</td><td class="frmText">#event.getValue('business_city','')#&nbsp;</td></tr>	
				<tr><td class="frmText">State/Province:</td><td class="frmText">
					#local.qryGetBusinessState.stateName#&nbsp;
				</td></tr>
				<tr ><td class="frmText">Zip/Postal Code:</td><td class="frmText">#event.getValue('business_zip','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Phone:</td><td class="frmText">#event.getValue('business_phone','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Cell Phone (optional):</td><td class="frmText">#event.getValue('business_cell','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Fax:</td><td class="frmText">#event.getValue('business_fax','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Home Address 1:</td><td class="frmText">#event.getValue('home_address','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Home Address 2:</td><td class="frmText">#event.getValue('home_address_2','')#&nbsp;</td></tr><tr ><td class="frmText">City:</td><td class="frmText">#event.getValue('home_city','')#&nbsp;</td></tr>	
				<cfif len(event.getValue('home_stateID',''))>
					<tr><td class="frmText">State/Province:</td><td class="frmText">
						#local.qryGetHomeState.stateName#&nbsp;
					</td></tr>
				</cfif>
				<tr ><td class="frmText">Zip/Postal Code:</td><td class="frmText">#event.getValue('home_zip','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Phone:</td><td class="frmText">#event.getValue('home_phone','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Cell Phone (optional):</td><td class="frmText">#event.getValue('home_cell','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">Fax:</td><td class="frmText">#event.getValue('home_fax','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Email:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Website:</td><td class="frmText">#event.getValue('website','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Date admitted to the MN Bar:</td><td class="frmText">#event.getValue('barDate_new','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Bar Status:</td><td class="frmText">#event.getValue('bar_status','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Attorney ID##:</td><td class="frmText">#event.getValue('attorney_id','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Date Admitted to Other State:</td><td class="frmText">#event.getValue('barDate_outOfState_new','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Bar Status:</td><td class="frmText">#event.getValue('bar_outOfState_status','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Attorney ID##:</td><td class="frmText">#event.getValue('attorney_outOfState_id','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Member for whom you work:</td><td class="frmText">#event.getValue('work','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Birth Year:</td><td class="frmText">#event.getValue('birth_year','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Spouse Name:</td><td class="frmText">#event.getValue('spouse_name','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Size of Firm:</td><td class="frmText">#event.getValue('sizeOfLawFirm','')#&nbsp;</td></tr>
				
				<tr class="msgHeader"><td colspan="2" class="b">Areas of Practice:</td></tr>
				<cfset local.areas = replace(application.objCustomPageUtils.mem_getCustomFieldValueFromValueID(orgID=event.getValue('mc_siteInfo.orgID'), columnName='Practice Areas', valueIDList=arguments.event.getValue('practiceAreas','')),'|',', ','ALL')>
				<tr ><td class="frmText">Areas of Practice:</td><td class="frmText">#local.areas#&nbsp;</td></tr>

				<tr class="msgHeader"><td colspan="2" class="b">Qualified Neutral And Include:</td></tr>
				<tr ><td class="frmText">I am a AAA arbitrator:</td><td class="frmText"><cfif event.getValue('iAmAAAANoFaultArbitrator','') eq 0>No<cfelse>Yes</cfif></td></tr>
				<tr ><td class="frmText">Counties in which I serve as a AAA arbitrator:</td><td class="frmText">#event.getValue('countiesInWhichIServeAsAAAAArbitrator','')#&nbsp;</td></tr>
				<tr ><td class="frmText">I am a Certified Neutral Arbitrator:</td><td class="frmText"><cfif event.getValue('iAmACertifiedNeutralArbitrator','') eq 0>No<cfelse>Yes</cfif></td></tr>

				<tr class="msgHeader"><td colspan="2" class="b">Demographic Data:</td></tr>
				<tr ><td class="frmText">Gender:</td><td class="frmText">#event.getValue('gender','')#&nbsp;</td></tr>	
				<tr ><td class="frmText">LGBT:</td><td class="frmText"><cfif event.getValue('lgbt','') eq 0>No<cfelse>Yes</cfif></td></tr>
				<tr ><td class="frmText">Marital Status:</td><td class="frmText">#event.getValue('marital_status','')#&nbsp;</td></tr>
				<tr ><td class="frmText">Race/Ethnicity:</td><td class="frmText">#event.getValue('race','')#&nbsp;</td></tr>		

				<cfset local.sustainingFreq = '' >
				<cfset local.memAmt = 0 />
				<cfset local.memAmt = 0 />
				<cfset local.legislative_fund = 0 />
				<cfset local.justice_fund = 0 />
				<cfset local.oneTime_legislative_fund = 0 />
				<cfset local.oneTime_justice_fund = 0 />

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.membershipRates" result="local.membershipRatesResult">
					select  r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termEndDate,rf.rfid, f.uid as freqUID
					from
					dbo.sub_rates as r   
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
						and rs.status = 'A'
						and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
						and  r.isRenewalRate = 0
						and r.status = 'A'
						and rs.uid IN ('2652c419-5da6-4772-8ae4-8f41b91a368f','523328cf-0594-4adc-a9e7-cb41fe32a6e6','01ff53d5-058d-4ab7-99aa-85c14c9b7f26')
					inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
						and rf.status = 'A'
						and rf.rfid = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('membership')#">
					inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
						and f.status = 'A'
					 ORDER BY r.rateName
				</cfquery>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFullRate">
					select r.uid,r.rateID,r.rateName,rs.scheduleName,rf.rateAmt, f.frequencyShortName,f.frequencyName,r.termEndDate,rf.rfid
					from
					dbo.sub_rates as r   
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID 
						and rs.status = 'A'
						and rs.siteiD = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#event.getValue('mc_siteInfo.siteID')#"> 
						and  r.isRenewalRate = 0
						and rs.uid IN ('2652c419-5da6-4772-8ae4-8f41b91a368f','523328cf-0594-4adc-a9e7-cb41fe32a6e6','01ff53d5-058d-4ab7-99aa-85c14c9b7f26')
						and r.status = 'A'
					inner join dbo.sub_rateFrequencies rf on rf.rateID = r.rateID 
						and rf.status = 'A'
					inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
						and f.status = 'A'
						and (
							(rs.uid = '2652c419-5da6-4772-8ae4-8f41b91a368f' AND f.frequencyShortName IN ('F', 'M', 'Q', 'S'))
							OR
							(rs.uid <> '2652c419-5da6-4772-8ae4-8f41b91a368f' AND f.frequencyShortName = 'F')
						)
					where
						r.rateID = #local.membershipRates.rateID#
				</cfquery>
				
				<cfset local.thisMembershipAmt = local.membershipRates.rateAmt>	

				<!--- Legislative Amount --->
				<cfif len(arguments.event.getValue('legislative_fund',''))>
					<cfset local.legislative_fund =arguments.event.getTrimValue('legislative_fund') >
				
					<cfif local.legislative_fund eq 'other'>
						<cfset local.legislative_fund =arguments.event.getTrimValue('other_legislative_fund') >
					</cfif>
				</cfif>	
				
				<cfif len(arguments.event.getValue('oneTime_legislative_fund',''))>
					<cfset local.oneTime_legislative_fund =arguments.event.getTrimValue('oneTime_legislative_fund') >
				</cfif>

				<!--- Justice Amount --->

				<cfif len(arguments.event.getValue('justice_fund',''))>
					<cfset local.justice_fund =arguments.event.getTrimValue('justice_fund') >
					<cfif local.justice_fund eq 'other'>
						<cfset local.justice_fund =arguments.event.getTrimValue('other_justice_fund') >
					</cfif>
				</cfif>
					
				<cfif len(arguments.event.getValue('oneTime_justice_fund',''))>
					<cfset local.oneTime_justice_fund =arguments.event.getTrimValue('oneTime_justice_fund') >
				</cfif>
				
				<cfset local.totalAmount = local.thisMembershipAmt>
				<cfset local.totalAmount = local.totalAmount + local.legislative_fund + local.justice_fund + local.oneTime_legislative_fund + local.oneTime_justice_fund>
				<cfset local.totalAmountToChargeDsp = numberFormat(local.totalAmount,"_$__.__")>
				
				<cfif event.getValue('payMeth','CC') EQ 'CC'>
					<cfset local.profileID = variables.profile_1._profileID>
					<cfset local.memberPayProfileDetail = "Credit Card">
					<cfset arguments.event.setValue('p_#local.profileID#_mppid',int(val(arguments.event.getValue('p_#local.profileID#_mppid',0))))>
					<cfif arguments.event.getValue('p_#local.profileID#_mppid') gt 0>
						<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.profileID#_mppid'), memberID=val(event.getValue('memberID',0)), profileID=local.profileID)>
						<cfset local.memberPayProfileDetail = "Credit Card - #local.qrySavedInfoOnFile.detail#">

						<cfif arguments.event.getValue('processFeeDonation#local.profileID#',0) EQ 1>
							<cfset local.processingFeesInfo = application.objCustomPageUtils.getPaymentProcessingFeesInfo(siteID=variables.siteID, memberID=val(event.getValue('memberID',0)), 
										mppid=arguments.event.getValue('p_#local.profileID#_mppid'), profileID=local.profileID, amt=local.totalAmount,
										stateIDForTax=val(arguments.event.getValue('business_stateID',0)), zipForTax=arguments.event.getValue('business_zip',''), processingFeeOpted=1)>
						</cfif>
					</cfif>
				<cfelse>
					<cfset local.memberPayProfileDetail = "Check">
				</cfif>

				<tr class="msgHeader"><td colspan="2" class="b">Membership Type:</td></tr>
				<tr ><td class="frmText"><cfif local.membershipRates.rateName EQ "Retired Attorney">Retired<cfelse>#local.membershipRates.rateName#</cfif> (#local.membershipRates.frequencyName#)</td><td class="frmText">#dollarFormat(local.thisMembershipAmt)#</td></tr>
				
				<tr class="msgHeader"><td colspan="2" class="b">Auto-Renew Membership:</td></tr>
				<tr ><td class="frmText">Is Auto-Renew Membership Required</td><td class="frmText">
					<cfif arguments.event.getValue('renewAgree','') EQ  'yes'>
						Yes
					<cfelse>
						No
					</cfif>				
				</td></tr>
				<tr class="msgHeader"><td colspan="2" class="b">Membership Criteria and Attestation:</td></tr>
				
				<tr>
					<td class="frmText">I agree to the membership terms stated above</td>
					<td class="frmText">
						<cfif len(arguments.event.getValue('membership_agreement',''))>
							Yes
						<cfelse>
							No
						</cfif>
					</td>
				</tr>

				<tr class="msgHeader"><td colspan="2" class="b">Required Notices:</td></tr>
				<tr ><td class="frmText">Please exclude my name from all rented list provided to selected users.</td>
					<td class="frmText">
						<cfif len(arguments.event.getValue('notices',''))>
							Yes
						<cfelse>
							No
						</cfif>
					</td>
				</tr>

				<cfset local.strNewSubs = arguments.event.getValue('newSubs','')>
				<cfif len(local.strNewSubs)>
					<tr class="msgHeader"><td colspan="2" class="b">Committee Selections:</td></tr>
					<tr class="checkboxRow">
						<td class="frmText">Committee Selections:</td>
						<td class="frmText">
							<cfloop query="local.qryAvailableSections">
								#local.qryAvailableSections.subscriptionname#<br>
							</cfloop>
						</td>
					</tr>
				</cfif>

				<cfif local.legislative_fund neq 0 or local.oneTime_legislative_fund neq 0 or local.justice_fund neq 0 or local.oneTime_justice_fund neq 0>
					<tr class="msgHeader"><td colspan="2" class="b">Membership and Additional Seelctions:</td></tr>
				</cfif>

				<tr ><td class="frmText"><cfif local.membershipRates.rateName EQ "Retired Attorney">Retired<cfelse>#local.membershipRates.rateName#</cfif> (#local.membershipRates.frequencyName#)</td><td class="frmText">#dollarFormat(local.thisMembershipAmt)#</td></tr>
				<cfif local.legislative_fund neq 0>
					<tr ><td class="frmText">MAJ Legislative Fund - Monthly Contribution</td><td class="frmText">#dollarFormat(local.legislative_fund)#</td></tr>
				</cfif>

				<cfif local.oneTime_legislative_fund neq 0>
					<tr ><td class="frmText">MAJ Legislative Fund - One Time Contribution</td><td class="frmText">#dollarFormat(local.oneTime_legislative_fund)#</td></tr>
				</cfif>

				<cfif local.justice_fund neq 0>
					<tr ><td class="frmText">MAJ Justice Fund - Monthly Contribution</td><td class="frmText">#dollarFormat(local.justice_fund)#</td></tr>
				</cfif>

				<cfif local.oneTime_justice_fund neq 0>
					<tr ><td class="frmText">MAJ Justice Fund - One Time Contribution</td><td class="frmText">#dollarFormat(local.oneTime_justice_fund)#</td></tr>
				</cfif>
				<tr>
					<td><b>Total Charge</b> &nbsp;</td>
					<cfif event.getValue('payMeth','CC') EQ 'CC' AND local.keyExists("processingFeesInfo") AND local.processingFeesInfo.paymentFeeTypeID GT 0>
						<td class="r"><b>#local.totalAmountToChargeDsp# (+ #local.processingFeesInfo.additionalFeesAmtDspLabel# #local.processingFeesInfo.additionalFeesLabel#)</b>&nbsp;</td>
					<cfelse>
						<td class="r"><b>#local.totalAmountToChargeDsp#</b>&nbsp;</td>
					</cfif>
				</tr>
				
				<tr class="msgHeader"><td colspan="2" class="b">MEMBERSHIP PAYMENT METHOD:</td></tr>
				<tr>
					<td class="frmText">Membership Payment Method:</td><td class="frmText">#local.memberPayProfileDetail#</td>
				</tr>
				</table>
			</cfoutput>
		</cfsavecontent>
			<!--- email submitter (no error shown to user) --->

			<cfsavecontent variable="local.mailContent">
					<cfoutput>
							#local.pageCSS#
							<cfif event.getValue('payMeth','CC') neq 'CC'>
								<p>Please mail payment to: Minnesota Association for Justice, 706 2nd Avenue South, Baker Building - Suite 140, Minneapolis, MN 55402</p>
							</cfif>
							<p>Thank you! Please print this page - it is your confirmation.</p><hr/>
							#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#      
					</cfoutput>
			</cfsavecontent>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
                            emailfrom={ name="", email=variables.strEMailSettings_member.from },
                            emailto=[{ name="", email=variables.strEMailSettings_member.to }],
                            emailreplyto=variables.strEMailSettings_staff.to,
                            emailsubject=variables.strEMailSettings_member.SUBJECT,
                            emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #variables.formNameDisplay#",
                            emailhtmlcontent=local.mailContent,
                            siteID=arguments.event.getValue('mc_siteinfo.siteid'),
                            memberID=val(arguments.event.getTrimValue('memberid')),
                            messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
                            sendingSiteResourceID=this.siteResourceID
						  )>
			<cfset local.emailSentToUser = local.responseStruct.success>

			<!--- email staff (no error shown to user) --->

			<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							<p><b>The member was NOT sent an e-mail confirmation of this submission.</b></p>
						</cfif>
						#local.pageCSS#						
						#replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all")#   
					</cfoutput>
			</cfsavecontent>
			<cfscript>
				local.arrEmailTo = [];
				variables.strEMailSettings_staff.to = replace(variables.strEMailSettings_staff.to,",",";","all");
				local.toEmailArr = listToArray(variables.strEMailSettings_staff.to,';');
				for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
					local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
				}
			</cfscript>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name="", email=variables.strEMailSettings_staff.from},
				emailto=local.arrEmailTo,
				emailreplyto=variables.strEMailSettings_staff.from,
				emailsubject=variables.strEMailSettings_staff.subject,
				emailtitle=arguments.event.getTrimValue('mc_siteinfo.sitename') & " - " & variables.formNameDisplay,
				emailhtmlcontent=local.mailContent,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
				messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
				sendingSiteResourceID=this.siteResourceID
			)>

			<!--- relocate to message page --->
			<cfset session.invoice = replaceNoCase(replaceNoCase(replaceNoCase(replace(replace(local.invoice, "*", "", "all"), "highlightRow", "", "all"),"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")>
		
		<cflocation url="#variables.baselink#&fa=complete" addtoken="false">
	</cffunction> 

	<cffunction name="saveMember" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
	
		<cfif arguments.event.getTrimValue('memberid') EQ 0 OR NOT IsNumeric(arguments.event.getTrimValue('memberid'))>
			<cfcontent type="text/html; charset=UTF-8"><cfheader statuscode="404" statustext="File Not Found"><cfabort>
		</cfif>

		<cfset local.myArrayList = arrayNew(1)>		

		<!--- UPDATE MEMBER RECORD  --->
		<cftry>
			<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=arguments.event.getTrimValue('memberid'))>
			<cfset local.strResult = false>	
			<cfset local.memberTypeName = event.getValue('memberType','')>
			<cfif (len(local.memberTypeName)) >
				<cfif local.memberTypeName eq "Retired">
					<cfset local.memberTypeName = "Retired Attorney"/>
				</cfif>
				<cfset local.objSaveMember.setCustomField(field='Contact Type', value=local.memberTypeName )>
			</cfif>

			<cfif len(event.getValue('barDate_new','')) and len(event.getValue('attorney_id','')) and len(event.getValue('bar_status',''))>
	     		<cfset local.objSaveMember.setProLicense(name='Minnesota',status=event.getValue('bar_status',''),license=event.getValue('attorney_id'), date=event.getValue('barDate_new','')) />
			</cfif>
			
			<cfif len(event.getValue('barDate_outOfState_new','')) and len(event.getValue('attorney_outOfState_id','')) and len(event.getValue('bar_outOfState_status',''))>
	     		<cfset local.objSaveMember.setProLicense(name='Out of State',status=event.getValue('bar_outOfState_status',''),license=event.getValue('attorney_outOfState_id'), date=event.getValue('barDate_outOfState_new','')) />
			</cfif>
						
			<cfset local.strResult = local.objSaveMember.saveData(runImmediately=1)>
			
			<cfreturn local.strResult.SUCCESS>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfreturn false>
	</cffunction>
	
	<cffunction name="upperFirst" access="public" returntype="string" output="false" hint="I convert the first letter of a string to upper case, while leaving the rest of the string alone.">
		<cfargument name="name" type="string" required="true">
		<cfreturn uCase(left(arguments.name,1)) & right(arguments.name,len(arguments.name)-1)>
	</cffunction>
</cfcomponent>