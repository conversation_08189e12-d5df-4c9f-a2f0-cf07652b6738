ALTER PROC dbo.tr_getInvoiceProfileConstraintViolations
@orgID int,
@limitAlertsTo int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @SevenDaysAgo datetime = dateadd(d,-7,getdate()), @totalCount int;

	IF OBJECT_ID('tempdb..#tblMPProfiles') IS NOT NULL 
		DROP TABLE #tblMPProfiles;
	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAllocT') IS NOT NULL 
		DROP TABLE #tblAllocT;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;
	IF OBJECT_ID('tempdb..#tblIPIssues') IS NOT NULL 
		DROP TABLE #tblIPIssues;
	CREATE TABLE #tblMPProfiles (merchantProfileID int PRIMARY KEY, payProfileName varchar(100));
	CREATE TABLE #tblProfiles (invoiceProfileID int, merchantProfileID int);
	CREATE TABLE #tblAllocT (transactionID int PRIMARY KEY, recordedByMemberID int, dateRecorded datetime);
	CREATE TABLE #tblAlloc (recordedByMemberID int, amount decimal(18,2), dateRecorded datetime, payTID int, saleTID int, 
		INDEX IX_tblAlloc_saleTID_Includes4 (saleTID) INCLUDE (recordedByMemberID, amount, dateRecorded, payTID),
		INDEX IX_tblAlloc_payTID_Includes4 (payTID) INCLUDE (recordedByMemberID, amount, dateRecorded, saleTID));
	CREATE TABLE #tblIPIssues (recordedByMemberID int, allocAmount decimal(18,2), dateRecorded datetime,
		detail varchar(500), payProfileName varchar(100), invoiceProfileName varchar(50), invoiceNumber varchar(19));

	INSERT INTO #tblMPProfiles (merchantProfileID, payProfileName)
	SELECT mp.profileID, mp.profileName
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID
		and s.orgID = @orgID
	WHERE mp.[status] = 'A';

	IF @@ROWCOUNT = 0 BEGIN
		SET @totalCount = 0;
		GOTO on_end;
	END

	insert into #tblProfiles (invoiceProfileID, merchantProfileID)
	select inp.profileID, mp.merchantProfileID
	from dbo.tr_invoiceProfiles as inp
	inner join dbo.tr_invoiceProfilesMerchantProfiles as ipmp on ipmp.invoiceProfileID = inp.profileID
	inner join #tblMPProfiles as mp on mp.merchantProfileID = ipmp.merchantProfileID
	where inp.orgID = @orgID
	and inp.[status] = 'A';

	IF @@ROWCOUNT = 0 BEGIN
		SET @totalCount = 0;
		GOTO on_end;
	END

	insert into #tblAllocT (transactionID, recordedByMemberID, dateRecorded)
	select transactionID, recordedByMemberID, dateRecorded
	from dbo.tr_transactions
	where ownedByOrgID = @orgID
	and typeID = 5
	and dateRecorded > @SevenDaysAgo
	and statusID = 1;

	IF @@ROWCOUNT = 0 BEGIN
		SET @totalCount = 0;
		GOTO on_end;
	END

	insert into #tblAlloc (recordedByMemberID, amount, dateRecorded, payTID, saleTID)
	select tmpT.recordedByMemberID, alloc.amount_alloc, tmpT.dateRecorded, alloc.transactionID_cash as payTID, alloc.transactionID_rev as saleTID
	from #tblAllocT as tmpT
	inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = tmpT.transactionID;

	IF @@ROWCOUNT = 0 BEGIN
		SET @totalCount = 0;
		GOTO on_end;
	END

	insert into #tblIPIssues (recordedByMemberID, allocAmount, dateRecorded, detail, payProfileName, invoiceProfileName, invoiceNumber)
	select distinct tmpAlloc.recordedByMemberID, tmpAlloc.amount, tmpAlloc.dateRecorded, tPay.detail, mp.payProfileName, ip.profileName, 
		i.fullInvoiceNumber as invoiceNumber
	from #tblAlloc as tmpAlloc
	inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @orgID and tPay.transactionID = tmpAlloc.payTID
	inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = tPay.transactionID
	inner join #tblMPProfiles as mp on mp.merchantProfileID = tp.profileID
	inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.ownedByOrgID = @orgID and tSaleTaxAdj.transactionID = tmpAlloc.saleTID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSaleTaxAdj.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join #tblProfiles as tmp on tmp.invoiceProfileID = i.invoiceProfileID
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
	where not exists (
		select invoiceProfileID from #tblProfiles where invoiceProfileID = i.invoiceProfileID and merchantProfileID = tp.profileID
	);

	set @totalCount = @@ROWCOUNT;

	on_end:
	select TOP (@limitAlertsTo) mActive.lastname + ', ' + mActive.firstname as memberName, mActive.membernumber,
		tmp.allocAmount, tmp.dateRecorded, tmp.detail, tmp.payProfileName, tmp.invoiceProfileName, tmp.invoiceNumber, 
		@totalCount as totalCount
	from #tblIPIssues as tmp
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.recordedByMemberID
	inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
	order by tmp.invoiceProfileName, tmp.invoiceNumber;

	IF OBJECT_ID('tempdb..#tblMPProfiles') IS NOT NULL 
		DROP TABLE #tblMPProfiles;
	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAllocT') IS NOT NULL 
		DROP TABLE #tblAllocT;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;
	IF OBJECT_ID('tempdb..#tblIPIssues') IS NOT NULL 
		DROP TABLE #tblIPIssues;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1
END CATCH
GO
