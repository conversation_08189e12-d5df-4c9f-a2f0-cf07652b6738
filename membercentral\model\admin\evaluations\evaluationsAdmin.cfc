<cfcomponent extends="model.admin.admin" output="no">

	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.list = buildCurrentLink(arguments.event,"list");

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset local.arrTabs = arrayNew(1)>
		<cfset local.objAdmin = CreateObject("component","evaluation")>

		<!--- determine where we are in the navigation: either sw or events --->
		<cfset local.strEvaluationContext = local.objAdmin.getEvaluationContextFromNav(navigationID=arguments.event.getValue('mc_adminNav').currentNavigationItem.NAVID)>
		<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.strEvaluationContext.breadcrumb })>

		<cfset local.formTypes = local.objAdmin.getFormTypes(applicationTypeID=local.strEvaluationContext.applicationTypeID)>

		<cfset local.listFormsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=evaluationsJSON&meth=getForms&mode=stream">
		<cfset local.editFormLink = buildCurrentLink(arguments.event,"editForm")>
		<cfset local.copyFormLink = buildCurrentLink(arguments.event,"copyForm") & "&mode=direct">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_list.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editForm" access="public" returntype="struct" output="false">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","evaluation")>

		<cfset local.formID = int(val(arguments.event.getValue('formID',0)))>
		<cfset local.formTypeAbbr = arguments.event.getValue('formTypeAbbr','')>
		<cfset var formTypeAbbr = local.formTypeAbbr>
		<cfset local.qryGetForm = local.objAdmin.getFormDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=local.formID)>
		<cfset local.siteCode = ucase(arguments.event.getValue('mc_siteInfo.siteCode'))>

		<cfif local.formID GT 0 AND local.qryGetForm.recordcount is not 1>
			<cflocation url="#buildCurrentLink(arguments.event,"list")#" addtoken="no">
		</cfif>

		<cfset local.updateSettingsLink = buildCurrentLink(arguments.event,"updateFormSettings") >
		<cfset local.listQuestionsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=evaluationsJSON&meth=getQuestions&formID=#local.formID#&mode=stream">
		<cfset local.editSectionLink = buildCurrentLink(arguments.event,"editSection") & "&mode=direct&formID=#local.formID#">
		<cfset local.editQuestionLink = buildCurrentLink(arguments.event,"editQuestion") & "&mode=direct&formID=#local.formID#">
		<cfset local.editmemberlink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
		<cfset local.viewResponseLink = buildCurrentLink(arguments.event,"viewResponse") & "&mode=direct&formID=#local.formID#">
		<cfset local.previewFormLink = buildCurrentLink(arguments.event,"previewForm") & "&mode=direct&formID=#local.formID#">

		<cfset local.strEvaluationContext = local.objAdmin.getEvaluationContextFromNav(navigationID=arguments.event.getValue('mc_adminNav').currentNavigationItem.NAVID)>
		<cfset local.formType = local.objAdmin.getFormTypes(applicationTypeID=local.strEvaluationContext.applicationTypeID).filter(function(thisRow) { return arguments.thisRow.formTypeAbbr eq formTypeAbbr; } ).formType>
		<cfset local.previewFormLinkText = "Preview #local.formType#">

		<cfset local.selectedTab = arguments.event.getTrimValue("tab","settings")>
		<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">
		<cfset local.goBackText = 'Return to Evaluations/Exams'>
		<cfif local.formTypeAbbr EQ 'V'>
			<cfset local.goBackText = 'Return to Evaluations'>
			<cfset local.listResponsesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=evaluationsJSON&meth=getEventResponses&formID=#local.formID#&mode=stream">
		<cfelse>
			<cfset local.listResponsesLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=evaluationsJSON&meth=getResponses&formID=#local.formID#&mode=stream">
		</cfif>
		<cfset appendBreadCrumbs(arguments.event,{ link='', text=EncodeForHTML(local.qryGetForm.formtitle) })>

		<cfset local.fbBackListTableObj = application.mcCacheManager.sessionGetValue(keyname='fbBackListTableObj', defaultValue=structNew())>
		<cfif structCount(local.fbBackListTableObj)>
			<cfset application.mcCacheManager.sessionDeleteValue(keyname='fbBackListTableObj')>
		</cfif>
		<cfset local.fbSListTableObj = application.mcCacheManager.sessionGetValue(keyname='fbSListTableObj', defaultValue=structNew())>
		<cfset application.mcCacheManager.sessionSetValue(keyname='fbBackListTableObj', value=local.fbSListTableObj)>

		<cfset local.goBack = this.link.list>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_editForm.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateFormSettings" access="public" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","evaluation")>

		<cfset local.qryGetForm = local.objAdmin.getFormDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=arguments.event.getValue('formID',0))>
		<cfif arguments.event.getValue('formID',0) GT 0 AND local.qryGetForm.recordcount is not 1>
			<cflocation url="#buildCurrentLink(arguments.event,"list")#" addtoken="no">
		</cfif>

		<cfset local.formID = local.objAdmin.saveSettings(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=arguments.event.getValue('formID'), 
			isPublished = int(val(arguments.event.getTrimValue('status',0))), formtitle=arguments.event.getTrimValue('formtitle',''), 
			formIntro=arguments.event.getTrimValue('formIntro',''), formClose=arguments.event.getTrimValue('formClose',''), 
			passingPct=int(val(arguments.event.getTrimValue('passingPct',0))), formTypeAbbr=arguments.event.getTrimValue('formTypeAbbr',''))>

		<cfset local.frmSuccObj = application.mcCacheManager.sessionGetValue(keyname='frmSuccObj', defaultValue=structNew())>
		<cfset structInsert(local.frmSuccObj, 'success', 1, true)>
		<cfset application.mcCacheManager.sessionSetValue(keyname='frmSuccObj', value=local.frmSuccObj)>
		<cfif arguments.event.getValue('formID',0) GT 0>
			<cflocation url="#buildCurrentLink(arguments.event,"editForm")#&formID=#local.formID#&formTypeAbbr=#arguments.event.getValue('formTypeAbbr','')#" addtoken="no">
		<cfelse>
			<cflocation url="#buildCurrentLink(arguments.event,"editForm")#&formID=#local.formID#&formTypeAbbr=#arguments.event.getValue('formTypeAbbr','')#&tab=questions" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="copyForm" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","evaluation")>

		<cfset local.formID = int(val(arguments.event.getValue('formID',0)))>
		<cfset local.qryGetForm = local.objAdmin.getFormDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=local.formID)>
		<cfif arguments.event.getValue('formID',0) GT 0 AND local.qryGetForm.recordcount is not 1>
			<cflocation url="#buildCurrentLink(arguments.event,"list")#" addtoken="no">
		</cfif>

		<cfset local.qryGetFormSites = local.objAdmin.getFormSites()>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_copyForm.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editSection" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.formID = arguments.event.getValue('formID',0) >
		<cfset local.sectionID = arguments.event.getValue('sectionID',0) >
		<cfset local.qryGetSection = CreateObject("component","evaluation").getSection(sectionID=arguments.event.getValue('sectionID',0))>
		<cfset local.updateSectionLink = buildCurrentLink(arguments.event,"updateSection")& "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_section.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateSection" access="public" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>

		<cfset CreateObject("component","evaluation").saveSection(formID=arguments.event.getValue('formID'), sectionID=arguments.event.getValue('sectionID',0), 
			sectionTitle=arguments.event.getTrimValue('sectionTitle',''), sectionDesc=arguments.event.getTrimValue('sectionDesc',''), 
			randomizeQuestions=int(val(arguments.event.getTrimValue('randomizeQuestions',0))))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadTable();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editQuestion" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.objAdmin = CreateObject("component","evaluation")>	
		
		<cfset local.formID = arguments.event.getValue('formID',0) >
		<cfset local.sectionID = arguments.event.getValue('sectionID',0) >
		<cfset local.questionID = arguments.event.getValue('questionID',0) >
		<cfset local.updateQuestionLink = buildCurrentLink(arguments.event,"updateQuestion")& "&mode=stream">
		<cfset local.addNewOptionLink = buildCurrentLink(arguments.event,"addNewOption")& "&mode=stream">	
		<cfset local.qryGetForm = local.objAdmin.getFormDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=local.formID)>
		<cfif local.qryGetForm.recordcount>
			<cfset local.formTypeAbbr = local.qryGetForm.formTypeAbbr>
		</cfif>
		<cfset local.qryGetQuestion = local.objAdmin.getQuestion(questionID=arguments.event.getValue('questionID',0))>
		<cfset local.qryGetOptions = local.objAdmin.getOptions(questionID=arguments.event.getValue('questionID',0))>
		<cfset local.qryGetOptionsx = local.objAdmin.getOptionsX(questionID=arguments.event.getValue('questionID',0))>
		<cfset local.qryGetQuestionTypes = local.objAdmin.getQuestionTypes(ft=local.formTypeAbbr)>
		
		<cfif local.qryGetQuestion.questionID GT 0 AND local.formTypeAbbr EQ 'S'>
			<cfif ListFind("1,2,9,10",local.qryGetQuestion.questionTypeID)>
				<cfquery name="local.qryGetOptionsFaked" dbtype="query">
					select 0 as optionID, 'Option 1' as optionText, 0 as showInput, '' as infoText
					from local.qryGetQuestion
						union
					select 1 as optionID, 'Option 2' as optionText, 0 as showInput, '' as infoText
					from local.qryGetQuestion
				</cfquery>
			</cfif>
			<cfif NOT listfind("7,8,11",local.qryGetQuestion.questionTypeID)>	
				<cfquery name="local.qryGetOptionsxFaked" dbtype="query">
					select 0 as optionID, 'Column A' as optionText
					from local.qryGetQuestion
						union
					select 1 as optionID, 'Column B' as optionText
					from local.qryGetQuestion
				</cfquery>
			</cfif>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_question.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateQuestion" access="public" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","evaluation")>
		<cfset local.frmAction = arguments.event.getValue('frmAction','save')>
		<cfset local.qryGetOptions = local.objAdmin.getOptions(questionID=arguments.event.getValue('questionID',0))>
		<cfset local.qryGetOptionsx = local.objAdmin.getOptionsX(questionID=arguments.event.getValue('questionID',0))>
		<cfscript>
			local.arrOptions = arrayNew(1);
			local.arrOptionsx = arrayNew(1);
			for (local.i=1; local.i<=arguments.event.getValue('optionRowCount',0); local.i++) {
				if (arguments.event.valueExists('isCorrect') AND arguments.event.getTrimValue('isCorrect') EQ local.i) {
					local.isCorrect = 1;
				}
				else {
					local.isCorrect = 0;
				}
				if (arguments.event.valueExists('optionText_#local.i#') AND arguments.event.getTrimValue('optionText_#local.i#') NEQ '') {
					local.tmpStr = {
						optionID = 0,
						optionText = arguments.event.getTrimValue('optionText_#local.i#',''),
						showInput = arguments.event.getTrimValue('showInput_#local.i#',0),
						inputText = arguments.event.getTrimValue('inputText_#local.i#',''),
						isCorrect = local.isCorrect,
						responseText = arguments.event.getTrimValue('responseText_#local.i#',''),
						optionOrder = arguments.event.getTrimValue('optionOrder_#local.i#','')
					};				
					arrayAppend(local.arrOptions, local.tmpStr);
				}
			}
			for (local.i=1; local.i<=arguments.event.getValue('rowCount',0); local.i++) {
				
				if (arguments.event.valueExists('optionText_#local.i#') AND arguments.event.getTrimValue('optionText_#local.i#') NEQ '') {
					local.tmpStr = {
						optionID = 0,
						optionText = arguments.event.getTrimValue('optionText_#local.i#',''),
						showInput = arguments.event.getTrimValue('showInput_#local.i#',0),
						inputText = arguments.event.getTrimValue('inputText_#local.i#',''),
						isCorrect = 0,
						responseText = arguments.event.getTrimValue('responseText_#local.i#',''),
						optionOrder = arguments.event.getTrimValue('optionOrder_#local.i#','')
					};																
					arrayAppend(local.arrOptions, local.tmpStr);	
				}
			}			
			for (local.row in local.qryGetOptions) { 
				if (arguments.event.valueExists('isCorrect') AND arguments.event.getTrimValue('isCorrect') EQ local.row.optionID) {
					local.isCorrect = 1;
				}
				else {
					local.isCorrect = 0;
				}
				if (arguments.event.valueExists('optionText_#local.row.optionID#')) {
				local.tmpStr = {
						optionID = local.row.optionID,
						optionText = arguments.event.getTrimValue('optionText_#local.row.optionID#',''),
						showInput = arguments.event.getTrimValue('showInput_#local.row.optionID#',0),
						inputText = arguments.event.getTrimValue('inputText_#local.row.optionID#',''),
						isCorrect = local.isCorrect,						
						responseText = arguments.event.getTrimValue('responseText_#local.row.optionID#',''),
						optionOrder = arguments.event.getTrimValue('optionOrder_#local.row.optionID#',0)
					};								
					arrayAppend(local.arrOptions, local.tmpStr);
				}	
			}
			for (local.i=1; local.i<=arguments.event.getValue('columnCount',0); local.i++) {
				if (arguments.event.valueExists('optionTextx_#local.i#')) {
					local.tmpStr = {
						optionID = 0,
						optionText = arguments.event.getTrimValue('optionTextx_#local.i#','')						
					};																
					arrayAppend(local.arrOptionsx, local.tmpStr);	
				}
			}
			for (local.row in local.qryGetOptionsx) { 
				if (arguments.event.valueExists('optionTextx_#local.row.optionID#')) {
				local.tmpStr = {
						optionID = local.row.optionID,
						optionText = arguments.event.getTrimValue('optionTextx_#local.row.optionID#','')						
					};								
					arrayAppend(local.arrOptionsx, local.tmpStr);
				}	
			}
			
		</cfscript>
				
		<cfset local.saveQuestionResult = local.objAdmin.saveQuestion(formID=arguments.event.getValue('formID'), sectionID=arguments.event.getValue('sectionID'),questionID=arguments.event.getValue('questionID',0), 		
			questionTypeID=arguments.event.getTrimValue('questionTypeID','0'), questionText=arguments.event.getTrimValue('questionText',''), 
			exportLabel=arguments.event.getTrimValue('exportLabel',''),isRequired=int(val(arguments.event.getTrimValue('isRequired',0))),
			displayQuestionNumber=int(val(arguments.event.getTrimValue('displayQuestionNumber',0))),
			displayQuestionText=int(val(arguments.event.getTrimValue('displayQuestionText',1))),
			howToAnswer=int(val(arguments.event.getTrimValue('howToAnswer',0))),
			optionResponseLimit=int(val(arguments.event.getTrimValue('optionResponseLimit',0))),	
			controlStyle=arguments.event.getTrimValue('controlStyle',''),
			randomizeOptions=int(val(arguments.event.getTrimValue('randomizeOptions',0))) , arrOptions=local.arrOptions, arrOptionsx=local.arrOptionsx)>	
		<cfif saveQuestionResult.questionID GT 0 AND local.frmAction EQ 'prefillData' >
 			<cfif arguments.event.getValue('prefillLocation') eq "columns">
 				<cfset local.objAdmin.prefillOptionsX(questionID = saveQuestionResult.questionID ,prefillType = arguments.event.getValue('prefillContent'))>
 			<cfelse>
 				<cfset local.objAdmin.prefillOptions(questionID = saveQuestionResult.questionID ,prefillType = arguments.event.getValue('prefillContent'))>
 			</cfif>
 			<cfsavecontent variable="local.data">
 				<cfoutput>
 				<script language="javascript">						
					top.reloadEditQuestion(#saveQuestionResult.questionID#,#arguments.event.getValue('sectionID')#);									
 				</script>
 				</cfoutput>
 			</cfsavecontent>
 		<cfelse>
 			<cfsavecontent variable="local.data">
 				<cfoutput>
 				<script language="javascript">
 					top.reloadTable();
 				</script>
 				</cfoutput>
 			</cfsavecontent>
 		</cfif>		
		<cfreturn returnAppStruct(local.data,"echo") />	
	</cffunction>

	<cffunction name="viewResponse" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objAdmin = CreateObject("component","evaluation")>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.siteCode = arguments.event.getValue('mc_siteInfo.siteCode')>
		<cfset local.formID = arguments.event.getValue('formID',0)>
		<cfset local.responseID = arguments.event.getValue('responseID',0)>
		<cfif arguments.event.getValue('from','') EQ 'swod'>
			<cfset local.viewProgressDetailLink = buildLinkToTool(toolType='SeminarWebAdmin',mca_ta='viewProgress') & "&eid=#arguments.event.getValue('eid')#&pid=#arguments.event.getValue('pid')#&from=#arguments.event.getValue('from')#&gridMode=#arguments.event.getValue('gridMode')#&mode=direct&tab=progressComponent" >
		<cfelse>
			<cfset local.viewProgressDetailLink = ''>
		</cfif>


		<cfset local.strResponse = local.objAdmin.getResponse(siteID=local.siteID, formID=local.formID, responseID=local.responseID)>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_viewResponse.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="previewForm" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>
		<cfset local.formID = arguments.event.getValue('formID',0) >
		<cfset local.strFormDetails = CreateObject("component","evaluation").getFormPreviewDetails(siteID=local.siteID, formID=local.formID)>
		
		<cfsavecontent variable="local.data">
			<cfif NOT local.strFormDetails.isValidForm>
				<div class="m-2">
					<div class="alert alert-danger mb-2">Unable to preview these questions.</div>
				</div>
			<cfelse>
				<cfinclude template="frm_previewForm.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>    