ALTER PROC dbo.tr_viewInvoice
@invoiceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	-- invoice info
	SELECT i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, 
		i.fullInvoiceNumber as invoiceNumber, i.expectedPayDate, 
		i.payProcessFee, i.processFeePercent, m2.memberid as assignedToMemberID,
		case when i.payProfileID is null then 0 else 1 end as hasCard, 
		case when len(isnull(mpp.nickname,'')) > 0 then mpp.nickname + ' (' + mpp.detail + ')' else mpp.detail end as cardDetail,
		isnull(m2.lastname,'') + ', ' + isnull(m2.firstname,'') + case when o.hasMiddleName = 1 then isnull(' ' + m2.middlename,'') else '' end + ' (' + m2.membernumber + ')' as memberName,
		m2.company as memberCompany,
		ip.profileID as invoiceProfileID, ip.profileName as invoiceProfile,
		btn_canEdit = case when istat.status = 'Paid' then 0 else 1 end,
		btn_canEmail = case when istat.status in ('Closed','Delinquent','Paid') then 1 else 0 end,
		btn_canPay = case when sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0 then 1 else 0 end,
		inPaymentQueue = cast(case when api.invoiceID is not null then 1 else 0 end as bit),	
		sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
		sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
		dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) as showLink
	from dbo.tr_invoices as i
	inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join dbo.organizations as o on o.orgID = m2.orgID
	left outer join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
	left outer join dbo.tr_invoiceTransactions as it 
		inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
		on it.orgID = i.orgID and it.invoiceID = i.invoiceID
	left outer join (
		select qpid.invoiceID
		from platformQueue.dbo.queue_payInvoicesDetail as qpid
		inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
		inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
		where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
		) api on api.invoiceID = i.invoiceID
	where i.invoiceID = @invoiceID
	group by i.invoiceID, i.dateCreated, i.dateBilled, i.dateDue, istat.status, i.invoiceCode, o.orgcode, 
		i.fullInvoiceNumber, i.expectedPayDate, i.payProcessFee, i.processFeePercent, m2.memberid, 
		i.payProfileID, m2.lastname, m2.firstname, o.hasMiddleName, m2.middlename, 
		m2.membernumber, m2.company, ip.profileID, ip.profileName, api.invoiceID, mpp.nickname, mpp.detail;

	-- invoice history
	select ish.updateDate, 
		istat.status + ' by ' + m2.firstname + ' ' + m2.lastname + ' (' + m2.membernumber + ')' + isnull(', ' + nullif(m2.company,''),'') as detail,
		ROW_NUMBER() OVER(ORDER BY ish.updateDate, ish.statusHistoryID) as row
	from dbo.tr_invoiceStatusHistory as ish
	inner join dbo.tr_invoiceStatuses as istat on istat.statusID = ish.statusID
	inner join dbo.ams_members as m on m.memberid = ish.enteredByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	where ish.invoiceID = @invoiceID
		union all
	select idh.updateDate,
		case 
		when idh.type = 'D' then 'Date Due'
		else 'Date Billed'
		end + ' changed from ' + convert(varchar(10),idh.oldDate,101) + ' to ' + convert(varchar(10),idh.newDate,101) + ' by ' + m2.firstname + ' ' + m2.lastname + ' (' + m2.membernumber + ')' + isnull(', ' + nullif(m2.company,''),'') as detail,
		ROW_NUMBER() OVER(ORDER BY idh.updateDate, idh.dateHistoryID) as row
	from dbo.tr_invoiceDateHistory as idh
	inner join dbo.ams_members as m on m.memberid = idh.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	where idh.invoiceID = @invoiceID
	order by updateDate, row;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
