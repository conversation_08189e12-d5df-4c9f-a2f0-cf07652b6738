<cfscript>
	variables.applicationReservedURLParams = "issubmitted";
	local.customPage.baseURL = "/?#getBaseQueryString(false)#";
	// set page defaults
	StructAppend(local, application.objCustomPageUtils.setFormDefaults(event=arguments.event, 
		formName='frmJoin',
		formNameDisplay='Membership Application Form',
		orgEmailTo='<EMAIL>',
		memberEmailFrom='<EMAIL>'
	));
	// set payment gateways
	local.profile_1 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode='CCDB-CCCIM');
	local.profile_2 = application.objCustomPageUtils.acct_getProfile(siteid=local.siteID, profileCode='CCDB-DRAFT');
	
	local.arrCustomFields = [];
	local.tmpField = { name="chapterAreaOptions",type="STRING",desc="pipe-delimited list of options for the Chapter Area dropdown",value="Option 1 | Option 2 | For Sale | Option 3" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="chapterAreaLabels",type="STRING",desc="pipe-delimited list of labels for the Chapter Area dropdown",value="Label 1 | Label 2 | For Sale | Label 3" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="paraProfDisclaimerContent",type="CONTENTOBJ",desc="Para-Professional Disclaimer",value="<p>New Investigator or Para-Professional member applicants are required to provide two (2) CCDB Attorney Member reference letters to verify that the applicant is engaged in investigative, para-professional, or other work in support of criminal defense. Please send letters and current <NAME_EMAIL>. Call 303.758.2454 if you have any questions.&nbsp;</p>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	local.tmpField = { name="membershipDisclaimerContent",type="CONTENTOBJ",desc="Membership level disclaimer",value="<p>No CCDB Member shall be employed at any time to work on any cases in full-time, part-time, or contract capacity with any law enforcement or prosecution agency, including, but not limited to, United States Attorneys, City Attorneys, District Attorneys or Attorney General, police, sheriffs, correctional, probation, or federal law enforcement and prosecution agencies.</p>" };
		arrayAppend(local.arrCustomFields, local.tmpField);
	variables.strPageFields = application.objCustomPageUtils.setFormCustomFields(siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteResourceID=this.siteResourceID, arrCustomFields=local.arrCustomFields);

	// other functions
	local.qryStates 			= application.objCommon.getStates();
	local.data.fax				= application.objMember.getMemberPhones(orgID=local.orgID, memberID=local.memberID, addressTypeID=449);

	local.xmlAdditionalData 	= application.objCustomPageUtils.mem_getOrgAdditionalDataColumns(orgID=local.orgID);
	local.practiceAreaStruct 	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Areas of Practice", xmlAdditionalData=local.xmlAdditionalData);
	local.countyStruct 			= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="County", xmlAdditionalData=local.xmlAdditionalData);
	local.listStruct 			= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="List Servers", xmlAdditionalData=local.xmlAdditionalData);
	local.ContactTypeStruct	 	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Contact Type", xmlAdditionalData=local.xmlAdditionalData);
	local.JurisdictionsStruct	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Jurisdictions Served", xmlAdditionalData=local.xmlAdditionalData);
	local.EthnicityStruct	 	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Race/Ethnicity", xmlAdditionalData=local.xmlAdditionalData);
	local.GenderStruct	 		= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Gender", xmlAdditionalData=local.xmlAdditionalData);
	local.LGBTCommunityStruct	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="LGBT Community", xmlAdditionalData=local.xmlAdditionalData);
	local.LeadershipStruct	 	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Interested in Leadership/Committee Opportunities", xmlAdditionalData=local.xmlAdditionalData);
	local.FindLawyerTypeStruct	= application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Find a Lawyer", xmlAdditionalData=local.xmlAdditionalData);
	local.MemberDirectoryTypeStruct	 = application.objCustomPageUtils.mem_getCustomFieldData(orgID=local.orgID, columnName="Member Directory", xmlAdditionalData=local.xmlAdditionalData);
	
	local.ContactTypeOrder 		= ["Attorney","Public Defender","ORPC","Life Member","Investigator","Paralegal","Social Worker","Resource Advocate",
		"Other (Legal Asst/Secretary)",	"Law Student","Intern"];
	local.ResultContactTypeStruct = [];
	local.searchStruct = structNew(); 
	local.searchStruct.myArray = local.ContactTypeStruct;
	for (i=1; i <= arrayLen(local.ContactTypeOrder);i++) {		
		local.match = structFindValue(local.searchStruct, local.ContactTypeOrder[i]);
		if(arrayLen(local.match)){
			arrayAppend(local.ResultContactTypeStruct,local.match[1].owner);
		}
	}

	local.listStructOrder 	= ["General","DUI","Juvenile","Arapahoe","Appellate","Associate","Denver Chapter","Colorado Springs Chapter"];
	local.ResultListStruct 	= [];
	local.searchStruct = structNew(); 
	local.searchStruct.myArray = local.listStruct;
	for (i=1; i <= arrayLen(local.listStructOrder);i++) {		
		local.match = structFindValue(local.searchStruct, local.listStructOrder[i]);
		if(arrayLen(local.match)){
			arrayAppend(local.ResultListStruct,local.match[1].owner);
		}
	}

	// SET DUES: -------------------------------------------------------------------------------------------------------------
	local.scheduleNameList	= 'Associate Membership Rates,Attorney Membership Rates';
	local.scheduleUIDList 	= '21A2577F-8851-48FB-97BF-436C5BE575F3,4C597F91-DA61-4186-B2C9-FAF9E882B123';
	local.strDuesTextList	= '1 Year in Practice,2 Years in Practice,3rd Year in Practice,4th Year in Practice,5+ Years in Practice,Sustaining Member,Lifetime Member (One-time fee),Public Defender - 0-6 Years in Practice,Public Defender - 7+ Years in Practice,ORPC - 0-6 Years in Practice,ORPC - 7+ Years in Practice,Associate - Investigator,Associate - Paralegal,Associate - Social Worker,Associate - Resource Advocate,Associate - Other (Legal Asst/Secretary),Associate - Law Student,Associate - Intern';
	local.quarterNameList	= 'Full,Second,Third,Fourth';
	local.rateNameList		= '1 Year,2 Years,3rd,4th,5+,Sustaining Member,Lifetime Member,PD 0-6 Years,PD 7+ Years,ORPC 0-6 Years,ORPC 7+ Years,Investigator,Paralegal,Social Worker - Full,Resource Advocate,Other (Legal Asst/Secretary),Law Student,Intern';
	local.thisQuarter		= Quarter(now());
	
	local.qryRates = application.objCustomPageUtils.sub_getRateSchedule(siteID=local.siteID, scheduleUID=local.scheduleUIDList, activeRatesOnly=true);
	
	local.strDues = arrayNew(1);
	
	for ( x=1; x LTE listLen(local.strDuesTextList); x=x+1 ){
		local.strDues[x] 	= structNew();
		local.strDues[x]["txt"] = listGetAt(local.strDuesTextList,x);
		local.strDues[x]["amt"] = 0;	
	}
	
	for (x=1; x LTE local.qryRates.recordCount; x=x+1){
		for ( y=1; y LTE listLen(local.quarterNameList); y=y+1 ){
			if ( FindNoCase('Full',trim(local.qryRates["rateName"][x])) ){
				for ( z=1; z LTE listLen(local.rateNameList); z=z+1 ){
					if ( FindNoCase(listGetAt(local.rateNameList,z),trim(local.qryRates["rateName"][x])) ){
						local.strDues[z]["amt"] = local.qryRates["rateAmt"][x];
					}
				}
			}
			else if ( FindNoCase(listGetAt(local.quarterNameList,y),trim(local.qryRates["rateName"][x])) ){
				for ( z=1; z LTE listLen(local.rateNameList); z=z+1 ){
					if ( FindNoCase(listGetAt(local.rateNameList,z),trim(local.qryRates["rateName"][x])) ){
						local.strDues[z]["amt"] = local.qryRates["rateAmt"][x];
					}
				}
			}
			else if ( FindNoCase('Half-Year',trim(local.qryRates["rateName"][x])) OR FindNoCase('Half Year',trim(local.qryRates["rateName"][x])) ){
				for ( z=1; z LTE listLen(local.rateNameList); z=z+1 ){
					if ( FindNoCase(listGetAt(local.rateNameList,z),trim(local.qryRates["rateName"][x])) ){
						local.strDues[z]["amt"] = local.qryRates["rateAmt"][x];
					}
				}
			}
		}
		if (trim(local.qryRates["rateName"][x]) == "Lifetime Member"){
			for ( z=1; z LTE listLen(local.rateNameList); z=z+1 ){
				if (FindNoCase(listGetAt(local.rateNameList,z),trim(local.qryRates["rateName"][x])) ){
					local.strDues[z]["amt"] = local.qryRates["rateAmt"][x];
				}
			}
		}
	}
</cfscript>

<cfoutput>
	#local.pageJS#
	#local.pageCSS#
	
	<style>
    *, *:before, *:after { box-sizing:inherit!important; }
    td { vertical-align:inherit; }
    input[type="radio"], input[type="checkbox"] { margin-top:0px; }
    select { height:auto!important; margin-bottom:0px; }
	td.chapterArea { text-align:left;}
	td.demoRow { width:50%; }
	</style>
	<div id="customPage">
		<div class="TitleText" style="padding-bottom:15px;text-align:center;">#local.Organization# <br />#local.formNameDisplay#</div>
		<cfswitch expression="#event.getValue('isSubmitted', 0)#">
			<!--- FORM: ========================================================================================================================================= --->
			<cfcase value="0">
				<script type="text/javascript">
					function _FB_validateForm() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						var lastMtrxErr = '';
						var profStatus = $('select[name="professionalStatus"]');
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['firstName'], 'TEXT')) arrReq[arrReq.length] = 'First Name';
						if (!_FB_hasValue(thisForm['lastName'], 'TEXT')) arrReq[arrReq.length] = 'Last Name';
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['firmName'], 'TEXT')) arrReq[arrReq.length] = 'Firm or School Name';
						if (!_FB_hasValue(thisForm['address'], 'TEXT')) arrReq[arrReq.length] = 'Firm or School Address';
						if (!_FB_hasValue(thisForm['phone'], 'TEXT')) arrReq[arrReq.length] = 'Office Phone';
						if (!_FB_hasValue(thisForm['home_cell'], 'TEXT')) arrReq[arrReq.length] = 'Phone Number';
						if (!_FB_hasValue(thisForm['email'], 'TEXT')) arrReq[arrReq.length] = 'Email Address';
						if (!_FB_hasValue(thisForm['professionalStatus'], 'SELECT')) arrReq[arrReq.length] = 'Professional Status';
						if (!_FB_hasValue(thisForm['adc'], 'RADIO')) arrReq[arrReq.length] = 'ADC Member';
						if (!_FB_hasValue(thisForm['raceEthnicity'], 'SELECT')) arrReq[arrReq.length] = 'Which of the following best describes you? Please select all that apply.';
						if (!_FB_hasValue(thisForm['gender'], 'SELECT')) arrReq[arrReq.length] = 'What is your gender?';
						if (!_FB_hasValue(thisForm['LGBTCommunity'], 'SELECT')) arrReq[arrReq.length] = 'Do you consider yourself a member of the Lesbian, Gay, Bisexual, Transgender and/or Queer (LGBTQ) community?';
						if (!_FB_hasValue(thisForm['leadership'], 'SELECT')) arrReq[arrReq.length] = 'Are you interested in becoming more involved in leadership and committee opportunities?';
						if (!_FB_hasValue(thisForm['paymentSchedule'], 'SELECT')) arrReq[arrReq.length] = 'Payment Schedule';
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['membership'], 'RADIO')) arrReq[arrReq.length] = 'Membership level';
						// -----------------------------------------------------------------------------------------------------------------
						if (profStatus.val() == 'Associate' || profStatus.val() == 'Paralegal') {
							if (!_FB_hasValue(thisForm['eligibilityDate_new'], 'TEXT')) arrReq[arrReq.length] = 'Membership Eligibility Date';
							if (!_FB_hasValue(thisForm['eligibilitySig'], 'TEXT')) arrReq[arrReq.length] = 'Membership Eligibility Signature';
						} else if (profStatus.val() == 'Attorney') {
							if (!_FB_hasValue(thisForm['barDate_new'], 'TEXT')) arrReq[arrReq.length] = 'Date Admitted to Bar';
							if (!_FB_hasValue(thisForm['barNumber'], 'TEXT')) arrReq[arrReq.length] = 'Bar Number';
							if (!_FB_hasValue(thisForm['slidingScale'], 'RADIO')) arrReq[arrReq.length] = 'Sliding Scale';
							if (!_FB_hasValue(thisForm['signature'], 'TEXT')) arrReq[arrReq.length] = 'Member Signature';
							if (!_FB_hasValue(thisForm['signatureDate_new'], 'TEXT')) arrReq[arrReq.length] = 'Membership Signature Date';
						} else if (profStatus.val() == 'Investigator/Para Professional') {
							if (!_FB_hasValue(thisForm['felony'], 'RADIO')) arrReq[arrReq.length] = 'Have you ever been convicted of a felony';
						} else if (profStatus.val() == 'Law Student') {
							if (!_FB_hasValue(thisForm['barDate_new'], 'TEXT')) arrReq[arrReq.length] = 'Expected Graduation Date';
						} else if (profStatus.val() == 'Public Defender') {
							if (!_FB_hasValue(thisForm['barDate_new'], 'TEXT')) arrReq[arrReq.length] = 'Date Admitted to Bar';
							if (!_FB_hasValue(thisForm['barNumber'], 'TEXT')) arrReq[arrReq.length] = 'Bar Number';
						}

						if(!$("input[name='autorenewagree']:checked").val()){
							arrReq[arrReq.length] = "Agree to Membership Auto Renewal terms and conditions";
						}

						if (arrReq.length > 0) {
							var msg = 'The following questions are required:\n\n';
							for (var i = 0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
					function assignMemberData(memObj) {
						var thisForm = document.forms["#local.formName#"];
						var er_change = function(r) {
							var results = r;
							if (results.success) {
								thisForm['memberNumber'].value = results.membernumber;
								thisForm['memberID'].value = results.memberid;
								thisForm['firstName'].value = results.firstname;
								thisForm['middleName'].value = results.middlename;
								thisForm['lastName'].value = results.lastname;
								thisForm['firmName'].value = results.company;
								thisForm['address'].value = results.address1;
								thisForm['address2'].value = results.address2;
								thisForm['city'].value = results.city;
								thisForm['state'].value = results.statecode;
								thisForm['zip'].value = results.postalcode;
								thisForm['county'].value = results.county;
								thisForm['phone'].value = results.phone;
								thisForm['email'].value = results.email;
								thisForm['listEmail'].value = results.email;

								if (results['practice areas'] != null) {
									$("input[value='" + results['practice areas'] + "']").prop('checked', true);
								}
								// un hide form 
								document.getElementById('formToFill').style.display = ''  
							} else { /*alert('not success');*/ }
						};
						
						var arrKeys = ["Areas of Practice"];
						var objParams = {
                            memberNumber: memObj.memberNumber,
                            customfields: arrKeys
                        };
						TS_AJX('MEMBER', 'getMemberDataByMemberNumber', objParams, er_change, er_change, 1000000, er_change);
					}
					function loadMember(memNumber){
						var objParams = { memberNumber:memNumber };
						assignMemberData(objParams);
					}
					function checkFelony(radio){
						var hasFelony = radio.value;
						
						if ( radio.style.display != 'none' ){
							if ( hasFelony == 'Yes' ){
								$('.felonyExplain').show();	
							}	
							else{
								$('.felonyExplain').hide().val('');	
							}
						}
						else{
							$('.felonyExplain').hide().val('');	
						}
					}	
					function showExtraCounty(radioBtn){
						var showExtra = radioBtn.value;
						$('.extraCounty').hide();
						
						if ( showExtra == 'Yes' ){
							$('.extraCounty').show();
						}	
					}
					function checkProfessionalStatus(status){
						if (!status){
							var status = $('input[name="professionalStatus"]');	
						}
						
						var allMemberRadios = $('input[name="membership"]');
						allMemberRadios.prop('disabled',true).prop('checked','');
						
						
						$('.associate').hide();
						$('.paralegal').hide();
						$('.otherStates').hide();
						$('.attorney').hide();
						$('.publicDefender').hide();
						$('.paraProfessional').hide();
						$('.lawStudent').hide();
						checkFelony($('input[name="felony"]')[1]);
						
						switch(status.value){
							case 'Attorney':
								allMemberRadios[1].disabled = false;
								allMemberRadios[2].disabled = false;
								allMemberRadios[3].disabled = false;
								allMemberRadios[4].disabled = false;
								allMemberRadios[5].disabled = false;
								allMemberRadios[0].disabled = false;
								$('.otherStates').show();
								$('.attorney').show();
								checkBarDate();
								break;					
							case 'Public Defender':
								allMemberRadios[7].disabled = false;
								allMemberRadios[8].disabled = false;
								allMemberRadios[5].disabled = false;
								$('.otherStates').show();
								$('.publicDefender').show();
								checkBarDate();
								break;
							case 'ORPC':
								allMemberRadios[9].disabled = false;
								allMemberRadios[10].disabled = false;
								allMemberRadios[5].disabled = false;
								$('.otherStates').show();
								$('.publicDefender').show();
								checkBarDate();
								break;
							case 'Life Member':
								allMemberRadios[6].disabled = false;
								allMemberRadios[6].checked 	= true;
								allMemberRadios[5].disabled = false;
								$('.otherStates').show();
								$('.publicDefender').show();
								checkBarDate();
								break;
							case 'Investigator':
								allMemberRadios[11].disabled = false;
								allMemberRadios[11].checked 	= true;
								break;
							case 'Paralegal':
								allMemberRadios[12].disabled = false;
								allMemberRadios[12].checked 	= true;
								$('.paralegal').show();
								break;
							case 'Social Worker':
								allMemberRadios[13].disabled = false;
								allMemberRadios[13].checked 	= true;
								break;	
							case 'Resource Advocate':
								allMemberRadios[14].disabled = false;
								allMemberRadios[14].checked 	= true;
								break;	
							case 'Other (Legal Asst/Secretary)':
								allMemberRadios[15].disabled = false;
								allMemberRadios[15].checked 	= true;
								break;	
							case 'Law Student':
								allMemberRadios[16].disabled = false;
								allMemberRadios[16].checked 	= true;
								$('.lawStudent').show();
								break;
							case 'Intern':
								allMemberRadios[17].disabled = false;
								allMemberRadios[17].checked  = true;
								break;
						}
						showTotals('membership');
					}
					function checkBarDate(){
						var barDateOld 	= $("##barDate_new").val();
						if(barDateOld == null || barDateOld == ""){
							return;
						}
						var currDate 	 = new Date();
						var currYear   = currDate.getFullYear();
						var currMonth	 = currDate.getMonth()+1;
						var currDay 	 = currDate.getDate();
						var _currDate = currMonth+1+'/'+currDay+'/'+currYear;
						var days = daysdifference(barDateOld ,currMonth+'/'+currDay+'/'+currYear);
						var _profStatus = $('select[name="professionalStatus"]').val();
						if (!_profStatus){
							_profStatus = $('input[name="professionalStatus"]');	
						}
						switch(true){
							case (days <= 729 && _profStatus == 'Attorney'):selectMemberType(0);break;/*1 Year*/
							case (days >= 730 && days <= 1094 && _profStatus == 'Attorney'): selectMemberType(1); break;/*2 Year*/
							case (days >= 1095 && days <= 1459 && _profStatus == 'Attorney'): selectMemberType(2); break;/*3 Year*/
							case (days >= 1460 && days <= 1825 && _profStatus == 'Attorney'): selectMemberType(3); break;/*4 Year*/
							case (days >= 1826 && _profStatus == 'Attorney'):selectMemberType(4); break;/*5 Year*/
							case (days <= 2554 && _profStatus == 'Public Defender'):selectMemberType(7); break;
							case (days >= 2555 && _profStatus == 'Public Defender'):selectMemberType(8); break;
						}
						showTotals('membership');
					}
					function daysdifference(firstDate, secondDate){
						var startDay = new Date(firstDate);
						var endDay = new Date(secondDate);
					
						var millisBetween = startDay.getTime() - endDay.getTime();
						var days = millisBetween / (1000 * 3600 * 24);
					
						return Math.round(Math.abs(days));
					}
					function selectMemberType(toSelect){
						//make sure all radios are disabled and unchecked
						var memberRadios = $('[name="membership"]');
						memberRadios[0].checked  = false;
						memberRadios[1].checked  = false;
						memberRadios[2].checked  = false;
						memberRadios[3].checked  = false;
						memberRadios[4].checked  = false;
						memberRadios[5].checked  = false;
						memberRadios[7].checked  = false;
						memberRadios[8].checked  = false;
						
						memberRadios[1].disabled = true;
						memberRadios[2].disabled = true;
						memberRadios[3].disabled = true;
						memberRadios[4].disabled = true;
						memberRadios[5].disabled = true;
						memberRadios[8].disabled = true;
						memberRadios[7].disabled = true;
						//determine which set of rates to use, 12mo or 6/18mo
					
						switch (toSelect){
							case 5://Sustaining
								memberRadios[5].disabled 	= false;
								memberRadios[5].checked 	= true;
							break;	
							case 0:
								memberRadios[0].disabled 	= false;
								memberRadios[0].checked 	= true;
							break;
							case 1:
								memberRadios[1].disabled 	= false;
								memberRadios[1].checked 	= true;
							break;
							case 2:
								memberRadios[2].disabled 	= false;
								memberRadios[2].checked 	= true;
							break;
							case 3:
								memberRadios[3].disabled 	= false;
								memberRadios[3].checked 	= true;
							break;
							case 4:
								memberRadios[4].disabled 	= false;
								memberRadios[4].checked 	= true;
							break;
							case 7://Public Defender 
								memberRadios[7].disabled 	= false;
								memberRadios[7].checked 	= true;
							break;
							case 8://Public Defender 
								memberRadios[8].disabled 	= false;
								memberRadios[8].checked 	= true;
							break;
						}
					}
					function showTotals(type,amt){
						var membershipTotalSpan 	= $('##membershipTotal');
						var practiceAreasTotalSpan 	= $('##practiceAreasTotal');
						var raptorFundTotalSpan 	= $('##raptorFundTotal');
						var totalDueSpan 			= $('##totalDue');
						var dueNowSpan				= $('##dueNow');
						var practiceAreaCount 		= 0;
						var paymentFreq				= $('##paymentSchedule').val();
						
						switch(type){
							case 'membership':
								if (!amt){
									membershipName = $('input[name="membership"]:checked').val();
									switch (membershipName){
										<cfloop array="#local.strDues#" index="thisMembership">
											case '#thisMembership.txt#':
												amt = #thisMembership.amt#;
											break;
										</cfloop>
										default:
											amt = 0;
										break;
									}	
								}
								$('input[type="hidden"][name="membershipTotalAmt"]').val(amt);
								membershipTotalSpan.text("$" + amt.toFixed(2));
							break;
							
							case 'practiceAreas':
								if (!amt){
									practiceAreaCount = $('select[name="practiceAreas"]').multiselect("getChecked").length;
									if ( practiceAreaCount != 0 ){
										practiceAreaCount = practiceAreaCount - 1
									}	
									amt = practiceAreaCount * 15;
								}
								$('input[type="hidden"][name="practiceAreaTotalAmt"]').val(amt);
								practiceAreasTotalSpan.text("$" + amt.toFixed(2));
							break;
							
							case 'raptor':
								if (!amt){
									amt = parseInt($('input[name="raptorProgram"]').val());	
								}
								if(isNaN(amt)){amt=0};
								$('input[type="hidden"][name="raptorTotalAmt"]').val(amt);
								raptorFundTotalSpan.text("$" + amt.toFixed(2));
							break;
						}
						
						totalTotalAmount = parseFloat($('input[type="hidden"][name="membershipTotalAmt"]').val()) + parseFloat($('input[type="hidden"][name="practiceAreaTotalAmt"]').val()) + parseFloat($('input[type="hidden"][name="raptorTotalAmt"]').val());
						
						switch(paymentFreq){
							case 'Pay in Full':
								dueNowAmount = totalTotalAmount;
							break;
							case '2 Payments':
								dueNowAmount = (parseFloat($('input[type="hidden"][name="membershipTotalAmt"]').val()) / 2) + parseFloat($('input[type="hidden"][name="practiceAreaTotalAmt"]').val()) + parseFloat($('input[type="hidden"][name="raptorTotalAmt"]').val());
							break;
							case '4 Payments':
								dueNowAmount = (parseFloat($('input[type="hidden"][name="membershipTotalAmt"]').val()) / 4) + parseFloat($('input[type="hidden"][name="practiceAreaTotalAmt"]').val()) + parseFloat($('input[type="hidden"][name="raptorTotalAmt"]').val());
							break;
							case '6 Payments':
								dueNowAmount = (parseFloat($('input[type="hidden"][name="membershipTotalAmt"]').val()) / 6) + parseFloat($('input[type="hidden"][name="practiceAreaTotalAmt"]').val()) + parseFloat($('input[type="hidden"][name="raptorTotalAmt"]').val());
							break;
							default:
								dueNowAmount = totalTotalAmount;
							break;
						}
						
						$('input[type="hidden"][name="totalTotalAmt"]').val(totalTotalAmount);
						$('input[type="hidden"][name="dueNowAmt"]').val(dueNowAmount);
						totalDueSpan.text("$" + totalTotalAmount.toFixed(2));
						dueNowSpan.text("$" + dueNowAmount.toFixed(2));
					}

					$(document).ready(function(){
						$("##otherState").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 3,
							minWidth: 200
						});	
						$("##extraCounty").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 3,
							minWidth: 200
						});	
						$("##practiceAreas").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 2,
							minWidth: 400,
						});
						$("##chapterArea").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 2,
							minWidth: 400,
						});
						$("##jurisdictions").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 2,
							minWidth: 400,
						});
						$("##raceEthnicity").multiselect({
							header: false,
							noneSelectedText: ' - Please Select - ',
							selectedList: 2,
							minWidth: 400,
						});
						$("##otherState,##extraCounty,##practiceAreas,##chapterArea,##jurisdictions,##raceEthnicity").multiselect("uncheckAll");
						$("##practiceAreas").on("multiselectclose", function(){
							showTotals('practiceAreas');
						});
						$("##barDate_new").change(function(){
							checkBarDate();
						});
					});
				</script>
				<div class="r i frmText">*Denotes required field</div>
				<cfform name="#local.formName#"  id="#local.formName#" method="post" action="#local.customPage.baseURL#" onsubmit="return _FB_validateForm();">
					<input type="hidden" name="isSubmitted" value="1" />
					<input type="hidden" name="memberID" value="#session.cfcUser.memberData.memberID#" />
					<input type="hidden" name="memberNumber" value="#session.cfcUser.memberData.memberNumber#" />
					<!--- =============================================================================================================================================== --->
					<!--- ACCOUNT LOCATOR: ============================================================================================================================== --->
					<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Account Lookup / Create New Account</div>
							<div class="frmRow1" style="padding:10px;">
								<table cellspacing="0" cellpadding="2" border="0" width="100%">
									<tr>
										<td width="175" class="c">
											<div id="associatedMemberIDSelect" style="display: inline; margin-right: 5px;">
												<button name="btnAddAssoc" type="button" onClick="selectMember()">Account Lookup</button>
											</div>
										</td>
										<td>
											<span class="frmText">
												<span class="block" style="padding-bottom:5px;">Click the <span class="b">Account Lookup</span> button to the left.</span>
												<span class="block" style="padding-bottom:5px;">Enter the search criteria and click <span class="b">Continue</span>.</span>
												<span class="block" style="padding-bottom:5px;">If you see your name, please press the <span class="b">Choose</span> button next to your name.</span>
												<span class="block" style="padding-bottom:5px;">If you do not see your name, click the <span class="b">Create an Account</span> link.</span>
											</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
					</cfif>
					
					<div id="formToFill" style="display:none;">
						<!--- PERSONAL INFORMATION: ========================================================================================================================= --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Contact Information</div>
							<div class=" frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="P r"><span class="required">*</span>First Name:</td>
										<td><input maxlength="75" size="40" name="firstName" type="text" value="#session.cfcUser.memberData.firstname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Middle Name:</td>
										<td><input maxlength="75" size="40" name="middleName" type="text" value="#session.cfcUser.memberData.middlename#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Last Name:</td>
										<td><input maxlength="75" size="40" name="lastName" type="text" value="#session.cfcUser.memberData.lastname#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Firm or School/University Name:</td>
										<td><input maxlength="100" size="60" name="firmName" type="text" value="#session.cfcUser.memberData.Company#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Firm or School/University Address:</td>
										<td><input maxlength="100" size="60" name="address" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">&nbsp;</td>
										<td><input maxlength="100" size="60" name="address2" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">City:</td>
										<td><input maxlength="75" size="60" name="city" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">State:</td>
										<td>
											<cfselect class="tsAppBodyText largeBox" name="state" id="state">
												<option value=""> - Please Select - </option>
												<cfloop query="local.qryStates">
													<cfif local.qryStates.country eq "United States">
														<option value="#local.qryStates.stateCode#">#local.qryStates.stateName#</option>
													</cfif>
												</cfloop>
											</cfselect>
										</td>
									</tr>
									<tr>
										<td class="P r">Zip:</td>
										<td><input maxlength="25" size="10" name="zip" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">County:</td>
										<td><input maxlength="60" size="60" name="county" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Office Phone:</td>
										<td><input size="13" maxlength="13" name="phone" id="phone" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);" /></td>
									</tr>
									<tr id="phone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter Office Phone in the format: ************</div></td></tr>
									<tr>
										<td class="P r">Office Fax:</td>
										<td><input size="13" maxlength="13" name="fax" id="fax" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);" /></td>
									</tr>
									<tr id="fax_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter Office Fax in the format: ************</div></td></tr>
									<tr>
										<td class="P r">Home Address:</td>
										<td><input maxlength="100" size="60" name="home_address1" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">&nbsp;</td>
										<td><input maxlength="100" size="60" name="home_address2" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Home City:</td>
										<td><input maxlength="75" size="60" name="home_city" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Home State:</td>
										<td>
											<cfselect class="tsAppBodyText largeBox" name="home_state" id="home_state">
												<option value=""> - Please Select - </option>
												<cfloop query="local.qryStates">
													<option value="#local.qryStates.stateCode#">#local.qryStates.stateName#</option>
												</cfloop>
											</cfselect>
										</td>
									</tr>
									<tr>
										<td class="P r">Home Zip:</td>
										<td><input maxlength="25" size="10" name="home_zip" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Home Country:</td>
										<td><input maxlength="60" size="60" name="home_country" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Home Phone:</td>
										<td><input size="13" maxlength="13" name="home_phone" id="home_phone" type="text" value="" class="tsAppBodyText" onchange="checkPhFormat(this);"/></td>
									</tr>
									<tr id="home_phone_message" style="display:none;"><td colspan="3"><div style="text-align:center;color:red;">Please enter Home Phone in the format: ************</div></td></tr>
									<tr>
										<td class="P r"><span class="required">*</span>Cell:</td>
										<td><input maxlength="100" size="20" name="home_cell" type="text" value="" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Email Address:</td>
										<td><input size="60" name="email" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
									</tr>
									<tr>
										<td class="P r">Website:</td>
										<td><cfinput size="60" name="website" id="website" type="text" value="" message="The website entered failed validation.  Please verify you have included http:// in website address." validate="regular_expression" pattern="#application.regEx.url#" /></td>
									</tr>										
									<tr>
										<td class="P r"><span class="required">*</span>Professional Status:</td>
										<td>
											<select name="professionalStatus" id="professionalStatus" class="tsAppBodyText" onChange="checkProfessionalStatus(this);">
												<option value=""> - Please Select - </option>
												<cfloop array="#local.ResultContactTypeStruct#" index="thisStatus">
													<option value="#thisStatus.columnValueString#">#thisStatus.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="paraProfessional" style="display:none;">
										<td class="P c" colspan="2">#variables.strPageFields.paraProfDisclaimerContent#</td>
									</tr>
									<tr class="attorney publicDefender lawStudent" style="display:none;">
										<td class="P r"><span class="required">*</span><span class="attorney publicDefender" style="display:none;">Date Admitted to Bar</span><span class="lawStudent" style="display:none;">Expected Graduation Date</span>:</td>
										<td>
											<cfset local.columnName = 'barDate' />
											<cfset local.showClear = true />
											<cfset local.clearOnSameLine = true />
											<!--- DATE PICKER: ================================================================================ --->
											<cfinput type="hidden" name="#local.columnName#_old"  id="#local.columnName#_old" value="">
											<cfif local.showClear AND NOT local.clearOnSameLine>
												<nobr>
											</cfif>
											<cfinput class="tsAppBodyText" type="text" name="#local.columnName#_new" id="#local.columnName#_new" value="" autocomplete="off" size="16">
											<cfif local.showClear>
												<cfinput type="button" class="tsAppBodyButton" name="btnClear#local.columnName#"  id="btnClear#local.columnName#" value="clear">
											</cfif>
											<cfif local.showClear AND NOT local.clearOnSameLine>
												</nobr>
											</cfif>
											<cfsavecontent variable="local.datejs">
												<script language="javascript">
													$(function() { 
														mca_setupDatePickerField('#local.columnName#_new'); 
														$("##btnClear#local.columnName#").click( function(e) { mca_clearDateRangeField('#local.columnName#_new');return false; } );
													});
												</script>
												<style type="text/css">
												###local.columnName#_new { 
													background-image:url("/assets/common/images/calendar/monthView.gif"); 
													background-position:right center; background-repeat:no-repeat; }
												</style>
											</cfsavecontent>
											<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
										</td>
									</tr>
									<tr class="attorney publicDefender" style="display:none;">
										<td class="P r"><span class="required">*</span>Bar Number:</td>
										<td><input size="40" name="barNumber" id="barNumber" type="text" value="" class="tsAppBodyText" /></td>
									</tr>									
									<tr class="attorney publicDefender" style="display:none;">
										<td class="P r">&nbsp;</td>
										<td><span class="info">(Date originally admitted to the bar in any state) </span></td>
									</tr>
									<tr class="paraProfessional" style="display:none;">
										<td class="P r"><span class="required">*</span>Have you ever been convicted of a felony in any court/state/US district</td>
										<td>
											<input type="radio" name="felony" value="Yes" onClick="checkFelony(this);" />Yes&nbsp;&nbsp;
											<input type="radio" name="felony" value="No" onClick="checkFelony(this);" />No&nbsp;&nbsp;
										</td>
									</tr>
									<tr class="felonyExplain" style="display:none;">
										<td class="P r">Please explain in detail:</td>
										<td>
											<textarea name="felonyExplain" value="" /></textarea>
										</td>
									</tr>
									<tr class="otherStates" style="display:none;">
										<td class="P r">Other State(s) Licensed:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="otherState" id="otherState" multiple="multiple">
												<cfloop query="local.qryStates">
													<cfif local.qryStates.country eq "United States">
														<option value="#local.qryStates.stateName#">#local.qryStates.stateName#</option>
													</cfif>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr class="attorney" style="display:none;">
										<td class="P r"><span class="required">*</span>Include me on the Sliding Scale list:</td>
										<td>
											<input type="radio" name="slidingScale" value="Yes" onClick="showExtraCounty(this);" />Yes&nbsp;&nbsp;
											<input type="radio" name="slidingScale" value="No" onClick="showExtraCounty(this);" />No&nbsp;&nbsp;
											<span class="small">(low-pay, slow-pay)</span>
										</td>
									</tr>
									<tr class="extraCounty" style="display:none;">
										<td class="P r">Please select all applicable counties:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="extraCounty" id="extraCounty" multiple="multiple">
												<cfloop array="#local.countyStruct.columnValueArr#" index="thisCounty">
													<option value="#thisCounty.columnValueString#">#thisCounty.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="P r">Jurisdictions Served:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="jurisdictions" id="jurisdictions" multiple="multiple">
												<cfloop array="#local.JurisdictionsStruct.columnValueArr#" index="thisJurisdiction">
													<option value="#thisJurisdiction.valueID#">#thisJurisdiction.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									
									<tr>
										<td class="P r"><span class="required">*</span>ADC Member:</td>
										<td>
											<input type="radio" name="adc" value="Yes" />Yes&nbsp;&nbsp;
											<input type="radio" name="adc" value="No" />No&nbsp;&nbsp;
										</td>
									</tr>
								</table>
							</div>
						</div>

						<div class="CPSection">
							<div class="CPSectionTitle BB">Demographic Information</div>
							<div class="subCPSectionArea1 BB frmText">
								<strong>NOTE: the following demographic questions are for internal purposes only to assist CCDB with our diversity and inclusion efforts. This information will not be available to the public. Your responses will be kept private and secure, you can always change the information at any time.</strong>
							</div>
							<div class=" frmRow1 frmText" style="padding:10px;">
								<table cellspacing="0" cellpadding="4" width="100%" border="0" align="center">
									<tr>
										<td class="P r demoRow"><span class="required">*</span>Which of the following best describes you? Please select all that apply:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="raceEthnicity" id="raceEthnicity" multiple="multiple">
												<cfloop array="#local.EthnicityStruct.columnValueArr#" index="thisEthnicity">
													<option value="#thisEthnicity.valueID#">#thisEthnicity.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>What is your gender?:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="gender" id="gender">
												<cfloop array="#local.GenderStruct.columnValueArr#" index="thisGender">
													<option value="#thisGender.columnValueString#">#thisGender.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Do you consider yourself a member of the Lesbian, Gay, Bisexual, Transgender and/or Queer (LGBTQ) community?:</td>
										<td>
											<select class="tsAppBodyText largeBox" name="LGBTCommunity" id="LGBTCommunity">
												<cfloop array="#local.LGBTCommunityStruct.columnValueArr#" index="thisLGBT">
													<option value="#thisLGBT.columnValueString#">#thisLGBT.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
									<tr>
										<td class="P r"><span class="required">*</span>Are you interested in becoming more involved in leadership and committee opportunities?</td>
										<td>
											<select class="tsAppBodyText largeBox" name="leadership" id="leadership">
												<cfloop array="#local.LeadershipStruct.columnValueArr#" index="thisLeadership">
													<option value="#thisLeadership.columnValueString#">#thisLeadership.columnValueString#</option>
												</cfloop>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB"><span class="required">*</span>Membership Level</div>
							<div class="subCPSectionArea1 BB frmText">
								#variables.strPageFields.membershipDisclaimerContent#
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<cfset local.rowCount = 1 />
									<cfloop array="#local.strDues#" index="thisMembership">
										<tr <cfif local.rowCount MOD 2 gte 1>class="frmRow1 <cfif local.rowCount neq ArrayLen(local.strDues)>BB</cfif>"<cfelse>class="frmRow2 <cfif local.rowCount neq ArrayLen(local.strDues)>BB</cfif>"</cfif>>
											<td class="P r" width="5px"><input type="radio" value="#thisMembership.txt#" name="membership" onClick="showTotals('membership',#thisMembership.amt#)" /></td>
											<td class="P l">#thisMembership.txt#</td>
											<td class="P r">#dollarFormat(thisMembership.amt)#</td>
										</tr>
										<cfset local.rowCount = local.rowCount + 1 />
									</cfloop>
								</table>
							</div>
						</div>
						<div class="CPSection">
							<div class="CPSectionTitle BB">List Servers</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<cfset local.listCount = 1 />
									<cfloop array="#local.ResultListStruct#" index="thisList">
										<tr <cfif local.listCount MOD 2 gte 1>class="frmRow1 BB"<cfelse>class="frmRow2 BB"</cfif>>
											<td class="P r" width="5px"><input type="checkbox" value="#thisList.columnValueString#" name="listServers" /></td>
											<td class="P l"><cfif thisList.columnValueString neq "Associate">#thisList.columnValueString#<cfelse>Investigator/Paralegal</cfif></td>
										</tr>
										<cfset local.listCount = local.listCount + 1 />
									</cfloop>
									<tr <cfif local.listCount MOD 2 gte 1>class="frmRow1"<cfelse>class="frmRow2"</cfif>>
										<td colspan="2">
											<table border="0" cellpadding="0" cellspacing="0" width="100%">
												<tr>
													<td class="P r" width="300">Preferred List Server Email Address:</td>
													<td class="P"><input size="60" name="listEmail" type="text" value="#session.cfcUser.memberData.email#" class="tsAppBodyText" /></td>
												</tr>
											</table>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Areas of Practice and Knowledge</div>
							<div class="subCPSectionArea1 BB frmText">
								<strong>You are entitled to be listed in <span style="text-decoraction:underline;">one field-of-practice category free of charge</span>. 
								Any additional categories will be at a cost of $15 per category.</strong> Each selected listing in the "Areas of Practice and Knowledge" section are also listed on the CCDB website for future clients seeking representation.
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c">
											<select class="tsAppBodyText largeBox" width="500px" name="practiceAreas" id="practiceAreas" multiple="multiple">
												<cfloop array="#local.practiceAreaStruct.columnValueArr#" index="thisArea">
													<option value="#thisArea.columnValueString#">#thisArea.columnValueString# &nbsp;</option>
												</cfloop>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
						
						<!--- Chapter Area  --->
						<div class="CPSection">
							<div class="CPSectionTitle BB">Chapter Area</div>
							<div class="subCPSectionArea1 BB frmText">
								Select Chapter Area that best suits your location.
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P r" width="300">Chapter Area</td>
										<td class="P c chapterArea">
											<select class="tsAppBodyText largeBox" name="chapterArea" id="chapterArea" multiple="multiple">
												<cfset local.chapterAreaCount =  1>
												<cfloop list="#variables.strPageFields.chapterAreaLabels#" index="chapterAreaLabels" delimiters="|">
													<option value="#trim(listGetAt(variables.strPageFields.chapterAreaOptions,local.chapterAreaCount,"|"))#">#chapterAreaLabels#</option>
													<cfset local.chapterAreaCount = local.chapterAreaCount + 1>
												</cfloop>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">Member Directory Preferences</div>
							<div class="subCPSectionArea1 BB frmText">
								Please choose your member directory preferences below.  Yes, will top you into that directory.  No, will opt you out of that directory.
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c">
											<b>Member Directory</b>&nbsp;&nbsp;&nbsp;&nbsp;
											<select class="tsAppBodyText largeBox" name="memberDirectory" id="memberDirectory" style="width:120px;">
												<cfloop array="#local.MemberDirectoryTypeStruct.columnValueArr#" index="thisArea">
													<option value="#thisArea.columnValueString#" <cfif #thisArea.columnValueString# eq 'Yes'>selected="true"</cfif>>#thisArea.columnValueString# &nbsp;</option>
												</cfloop>
											</select>
										</td>
										<td class="P c">
											<b>Find a Lawyer Directory</b>&nbsp;&nbsp;&nbsp;&nbsp;
											<select class="tsAppBodyText largeBox" name="findALawyer" id="findALawyer" style="width:120px;">
												<cfloop array="#local.FindLawyerTypeStruct.columnValueArr#" index="thisArea">
													<option value="#thisArea.columnValueString#" <cfif #thisArea.columnValueString# eq 'Yes'>selected="true"</cfif>>#thisArea.columnValueString# &nbsp;</option>
												</cfloop>
											</select>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="CPSection">
							<div class="CPSectionTitle BB">Raptor Program</div>
							<div class="subCPSectionArea1 BB frmText">
								Use this opportunity to make your #year(now())# - #year(now())+1# Raptor Fund contribution. Remember this is vital to continued policy work on behalf of you and your clients!<br />
								<br />
								*  The Colorado Criminal Defense Bar is a 501(c)(6) non-profit organization under the Internal Revenue Service Code. Contributions, gifts and dues to the CCDB are not tax deductible 
								   as charitable contributions; however, contributions may be deductible as ordinary and necessary business expenses subject to restrictions imposed as a result of lobbying activities. <br />
								<br />
								Please consult your tax advisor for any questions.<br />
								<br />
								** Please note: Raptor Funds contributions are lobbying expenses, and are NOT tax deductible.
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c">
											I would like to donate $&nbsp;<input class="tsAppBodyText largeBox" type="text"  name="raptorProgram" id="raptorProgram" onChange="checkNaN(this);showTotals('raptor');" /> to the Raptor Program.
											<br />
											<span class="info">(e.g. 15.00 or 15)</span>
										</td>
									</tr>
									<tr class="frmRow1" id="raptorProgram_message" style="display:none;"><td colspan="2" align="center"><span style="color:red;font-weight:bold;text-align:center;">Please enter a valid number.</span></td></tr>
								</table>
							</div>
						</div>
						<div class="CPSection">
							<div class="CPSectionTitle BB">MEMBERSHIP AUTO RENEWAL METHOD</div>
							<div class="subCPSectionArea1 BB frmText">
								Memberships Renew Automatically until you contact the CCDB to cancel. We will send you a reminder each year so you may update your contact profile, practice areas, change membership level, or cancel. 
								When you choose your payment method on the next screen, we will automatically renew your membership at the beginning of the new fiscal year using the same method. 
								Our fiscal year begins June 1st, renewals are completed by the 15th.
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P c">
											<input type="checkbox" class="tsAppBodyText largeBox"  name="autorenewagree" id="autorenewagree" value="Yes" />&nbsp; I agree to the terms and conditions above
										</td>
										<td class="P">
											<input type="checkbox" class="tsAppBodyText largeBox"  name="optoutautorenewal" id="optoutautorenewal" value="Yes" />&nbsp; I prefer to "opt-out" of auto-renewal and will make other arrangements with CCDB.
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="CPSection associate paralegal" style="display:none;">
							<!--- Same Content, different titles. Only one should ever display. --->
							<div class="CPSectionTitle BB associate" style="display:none;">Eligibility for Associate Membership</div>
							<div class="CPSectionTitle BB paralegal" style="display:none;">Eligibility for Paralegal Membership</div>
							<div class="subCPSectionArea1 BB frmText">
								A person of professional competence, integrity and good moral character who is actively
								engaged in assisting persons licensed to practice law in the defense of criminal cases. However, as a CCDB Member, no such
								person shall be employed at any time to work on any cases in a full-time, part-time, or contract capacity with any law
								enforcement or prosecution agency, including, but not limited to, United States Attorneys, City Attorneys, District Attorneys or
								Attorneys General, police, sheriffs, correctional, probation, or federal law enforcement and prosecution agencies. 
							</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<!--- As with the title, only one of these should show --->
										<td class="P c associate" colspan="2" style="display:none;">
											With my typed/signed signature, I hereby attest that I am eligible for CCDB membership. I understand I will go
											through a vetting process by the CCDB Associate Review Committee before membership is officially approved. 
										</td>
										<td class="P c paralegal" colspan="2" style="display:none;">
											With my typed/signed signature, I hereby attest that I am eligible for CCDB membership. I understand I will go
											through a vetting process by the CCDB Paralegal Review Committee before membership is officially approved. 
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="P c">
											<span class="required">*</span>Date:
											<cfset local.columnName = 'eligibilityDate' />
											<cfset local.showClear = true />
											<cfset local.clearOnSameLine = true />
											<!--- DATE PICKER: ================================================================================ --->
											<cfinput type="hidden" name="#local.columnName#_old"  id="#local.columnName#_old" value="">
											<cfif local.showClear AND NOT local.clearOnSameLine>
												<nobr>
											</cfif>
											<cfinput class="tsAppBodyText" type="text" name="#local.columnName#_new" id="#local.columnName#_new" value="" autocomplete="off" size="16">
											<cfif local.showClear>
												<cfinput type="button" class="tsAppBodyButton" name="btnClear#local.columnName#"  id="btnClear#local.columnName#" value="clear">
											</cfif>
											<cfif local.showClear AND NOT local.clearOnSameLine>
												</nobr>
											</cfif>
											<cfsavecontent variable="local.datejs">
												<script language="javascript">
													$(function() { 
														mca_setupDatePickerField('#local.columnName#_new'); 
														$("##btnClear#local.columnName#").click( function(e) { mca_clearDateRangeField('#local.columnName#_new');return false; } );
													});
												</script>
												<style type="text/css">
												###local.columnName#_new { 
													background-image:url("/assets/common/images/calendar/monthView.gif"); 
													background-position:right center; background-repeat:no-repeat; }
												</style>
											</cfsavecontent>
											<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">
										</td>
										<td class="P c">
											<span class="required">*</span>Signature: <input type="text" class="tsAppBodyText largeBox"  name="eligibilitySig" id="eligibilitySig" value="" /> 
										</td>
									</tr>
								</table>
							</div>
						</div>
						<div class="CPSection attorney">
							<div class="CPSectionTitle BB">Please read and sign</div>
							<div class="frmText">
								<div class="P subCPSectionArea1 BB frmText">
									With typed signature, I hereby attest that I am a criminal defense lawyer, paralegal or investigator engaged in the practice of the defense of the accused. 
									I am neither a full-time judge nor a part-time prosecutor, nor law enforcement. 
									I agree that the CCDB may contact me about CCDB events, business matters, and seminars by mail/email.
								</div>
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1">
										<td class="P"><span class="required">*</span>Authorization: <input type="text" value="" name="signature" /></td>
										<td class="P">
											<span class="required">*</span>Date:
											<cfset local.columnName = 'signatureDate' />
											<cfset local.showClear = true />
											<cfset local.clearOnSameLine = true />
											<!--- DATE PICKER: ================================================================================ --->
											<cfinput type="hidden" name="#local.columnName#_old"  id="#local.columnName#_old" value="">
											<cfif local.showClear AND NOT local.clearOnSameLine>
												<nobr>
											</cfif>
											<cfinput class="tsAppBodyText" type="text" name="#local.columnName#_new" id="#local.columnName#_new" value="" autocomplete="off" size="16">
											<cfif local.showClear>
												<cfinput type="button" class="tsAppBodyButton" name="btnClear#local.columnName#"  id="btnClear#local.columnName#" value="clear">
											</cfif>
											<cfif local.showClear AND NOT local.clearOnSameLine>
												</nobr>
											</cfif>
											<cfsavecontent variable="local.datejs">
												<script language="javascript">
													$(function() { 
														mca_setupDatePickerField('#local.columnName#_new'); 
														$("##btnClear#local.columnName#").click( function(e) { mca_clearDateRangeField('#local.columnName#_new');return false; } );
													});
												</script>
												<style type="text/css">
												###local.columnName#_new { 
													background-image:url("/assets/common/images/calendar/monthView.gif"); 
													background-position:right center; background-repeat:no-repeat; }
												</style>
											</cfsavecontent>
											<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">	
										</td>
										<td class="P">
											Bar Date: 
											
											<cfset local.columnName = 'signatureBarDate' />
											<cfset local.showClear = true />
											<cfset local.clearOnSameLine = true />
											<!--- DATE PICKER: ================================================================================ --->
											<cfinput type="hidden" name="#local.columnName#_old"  id="#local.columnName#_old" value="">
											<cfif local.showClear AND NOT local.clearOnSameLine>
												<nobr>
											</cfif>
											<cfinput class="tsAppBodyText" type="text" name="#local.columnName#_new" id="#local.columnName#_new" value="" autocomplete="off" size="16">
											<cfif local.showClear>
												<cfinput type="button" class="tsAppBodyButton" name="btnClear#local.columnName#"  id="btnClear#local.columnName#" value="clear">
											</cfif>
											<cfif local.showClear AND NOT local.clearOnSameLine>
												</nobr>
											</cfif>
											<cfsavecontent variable="local.datejs">
												<script language="javascript">
													$(function() { 
														mca_setupDatePickerField('#local.columnName#_new'); 
														$("##btnClear#local.columnName#").click( function(e) { mca_clearDateRangeField('#local.columnName#_new');return false; } );
													});
												</script>
												<style type="text/css">
												###local.columnName#_new { 
													background-image:url("/assets/common/images/calendar/monthView.gif"); 
													background-position:right center; background-repeat:no-repeat; }
												</style>
											</cfsavecontent>
											<cfhtmlhead text="#application.objCommon.minText(local.datejs)#">	
											<!--- ============================================================================================= --->
											<span class="info">(if applicable)</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
						<input type="hidden" name="membershipTotalAmt" value="0" />
						<input type="hidden" name="practiceAreaTotalAmt" value="0" />
						<input type="hidden" name="raptorTotalAmt" value="0" />
						<input type="hidden" name="totalTotalAmt" value="0" />
						<input type="hidden" name="dueNowAmt" value="0" />
						<div class="CPSection">
							<div class="CPSectionTitle BB">TOTAL DUE</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr class="frmRow1 BB">
										<td class="P l b">
											Membership Dues
										</td>
										<td class="P r b">
											<span id="membershipTotal">#dollarFormat(0)#</span>
										</td>
									</tr>
									<tr class="frmRow2 BB">
										<td class="P l b">
											Areas of Practice Subtotal
										</td>
										<td class="P r b">
											<span id="practiceAreasTotal">#dollarFormat(0)#</span>
										</td>
									</tr>
									<tr class="frmRow1 BB">
										<td class="P l b">
											Raptor Fund Contribution
										</td>
										<td class="P r b">
											<span id="raptorFundTotal">#dollarFormat(0)#</span>
										</td>
									</tr>
									<tr class="frmRow2"><td colspan="2" class="BB"><img src="/assets/common/images/spacer.gif"></td></tr>
									<tr class="frmRow2 BB">
										<td class="P l b BB">
											Total
										</td>
										<td class="P r b BB">
											<span id="totalDue">#dollarFormat(0)#</span>
										</td>
									</tr>
									<tr class="frmRow1">
										<td class="P l b">
											Total Due Now
										</td>
										<td class="P r b">
											<span id="dueNow">#dollarFormat(0)#</span>
										</td>
									</tr>
								</table>
							</div>
						</div>
						
						<div class="CPSection">
							<div class="CPSectionTitle BB">Payment Schedule</div>
							<div class="frmText">
								<table cellspacing="0" cellpadding="0" width="100%" border="0">
									<tr>
										<td class="P r" width="50%"><span class="required">*</span>I would like to pay my membership dues:</td>
										<td class="P l">
											<select name="paymentSchedule" id="paymentSchedule" class="tsAppBodyText" onChange="showTotals(this);">
												<option value="Pay in Full">Pay in Full</option>
												<option value="2 Payments">2 Payments</option>
												<option value="4 Payments">4 Payments</option>
												<option value="6 Payments">6 Payments</option>
												<option value="8 Payments">8 Payments</option>
												<option value="12 Payments">12 Payments</option>
											</select>
										</td>
									</tr>
									<tr>
										<td class="P c" colspan="2">
											<span class="info c red">All Payment plans are consecutive and MUST be paid in full by May 15th of the current fiscal year. Payment plans may ONLY be arranged via ACH and Credit Cards (NO Checks).</span>
										</td>
									</tr>
								</table>
							</div>
						</div>

						<!--- BUTTONS: ====================================================================================================================================== --->					
						<div id="formButtons">
							<div style="padding:10px;">
								<div align="center" class="frmButtons">
									<input type="submit" value="Continue" name="submit"> &nbsp;&nbsp; <input type="button" onClick="history.go(-1);" value="Cancel" name="cancel"/>
								</div>
							</div>
						</div>
						<!--- =============================================================================================================================================== --->					
					</div>
				</cfform>

				<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
					<script>loadMember('#session.cfcUser.memberData.memberNumber#');</script>
				</cfif>
				
			</cfcase>
			
			<!--- PAYMENT INFO: ================================================================================================================================= --->
			<cfcase value="1">
				<cfscript>
					// GATEWAY INFORMATION: --------------------------------------------------------------------------------------------------
					local.profile_1.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_1._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='ccForm',
																			autoShowForm=1
																		);
																		
																		
																		
					local.profile_2.strPaymentForm = application.objPayments.showGatewayInputForm(
																			siteid=local.siteID,
																			profilecode=local.profile_2._profileCode,
																			pmid = local.useMID,
																			showCOF = local.useMID EQ session.cfcUser.memberData.memberID,
																			usePopup=false,
																			usePopupDIVName='bdForm',
																			autoShowForm=1
																		);
				</cfscript>
				<script type="text/javascript">
					function checkPaymentMethod() {
						var rdo = document.forms["#local.formName#"].payMeth;
						if (rdo[0].checked) {
							$('##CCInfo').show();
							$('##BDInfo').hide();
							$('##CheckInfo').hide();
						} else if (rdo[1].checked) {
							$('##CCInfo').hide();
							$('##BDInfo').show();
							$('##CheckInfo').hide();
						}  else if (rdo[2].checked) {							
							$('##CCInfo').hide();
							$('##BDInfo').hide();
							$('##CheckInfo').show();
						}  
					}

					function getMethodOfPayment() {
						var btnGrp = document.forms['#local.formName#'].payMeth;
						var i = getSelectedRadio(btnGrp);
						if (i == -1) return "";
						else {
							if (btnGrp[i]) return btnGrp[i].value;
							else return btnGrp.value;
						}
					}

					function _validate() {
						var thisForm = document.forms["#local.formName#"];
						var arrReq = new Array();
						// -----------------------------------------------------------------------------------------------------------------
						if (!_FB_hasValue(thisForm['payMeth'], 'RADIO')) arrReq[arrReq.length] 	= 'Method of Payment';
						var MethodOfPaymentValue = getMethodOfPayment();
						
						if( MethodOfPaymentValue == 'CC' )	{
							#local.profile_1.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatementCC'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						} else if ( MethodOfPaymentValue == 'BD' )	{
							#local.profile_2.strPaymentForm.jsvalidation#
							var confirmation 	= 0;
							var statement			= thisForm['confirmationStatementBD'];
							if(statement.length == undefined){ if (statement.checked == 1) confirmation++; }
							if(confirmation == 0) arrReq[arrReq.length] = 'Confirmation Statement';
						}
						
						if (arrReq.length > 0) {
							var msg = 'The following fields are required:\n\n';
							for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '\n';
							alert(msg);
							return false;
						}
						return true;
					}
				
					function showAlert(msg){ $('##payerrDIV').html(msg).attr('class','alert').show();  };
				</script>
				<cfif len(local.profile_1.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_1.strPaymentForm.headCode)#">
				</cfif>
				<cfif len(local.profile_2.strPaymentForm.headCode)>
					<cfhtmlhead text="#application.objCommon.minText(local.profile_2.strPaymentForm.headCode)#">
				</cfif>
				
				<div id="paymentTable">
					<div id="payerrDIV" style="display:none;margin:6px 0;"></div>
					<div class="form">
						<cfform name="#local.formName#"  id="#local.formName#" method="POST" action="#local.customPage.baseURL#" onSubmit="return _validate();">
						<cfinput type="hidden" name="isSubmitted"  id="isSubmitted" value="#event.getValue('isSubmitted') + 1#">
						<cfloop collection="#arguments.event.getCollection()#" item="local.key">
							<cfif listFindNoCase(event.getValue('fieldnames',''),local.key)
								and NOT listFindNoCase("isSubmitted,btnSubmit",local.key) 
								and left(local.key,4) neq "fld_">
								<cfinput type="hidden" name="#local.key#"  id="#local.key#" value="#arguments.event.getValue(local.key)#">
							</cfif>
						</cfloop>
						<div>
							<div class="CPSection">
								<div class="CPSectionTitle">*Method of Payment</div>
								<div class="P">
									<table cellpadding="2" cellspacing="0" width="100%" border="0">
										<tr valign="top">
											<td colspan="2">Please select your preferred method of payment from the options below.</td>
										</tr>
										<tr>
											<td>
												<table cellpadding="2" cellspacing="0" width="100%" border="0">
													<tr>
														<td width="25"><input value="CC" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Credit Card</td>
													</tr>
													<tr>
														<td width="25"><input value="BD" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Checking Account</td>
													</tr>
													<tr>
														<td width="25"><input value="Check" class="tsAppBodyText optionsRadio" name="payMeth" type="radio" onClick="checkPaymentMethod();"></td>
														<td>Check by Mail</td>
													</tr>
												</table>
											</td>
										</tr>
									</table>
								</div>
							</div>
								
							<div id="CCInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Credit Card Information</div>
								<div class="PL PR frmText paymentGateway BT BB">
									<cfif len(local.profile_1.strPaymentForm.inputForm)>
										<div id="ccForm">#local.profile_1.strPaymentForm.inputForm#</div>
									</cfif>
								</div>
								<div class="P">
									<div class="PB">* Please confirm the statement below:</div>
									<table width="100%">
									<tr>
										<td width="25"><input name="confirmationStatementCC" id="confirmationStatementCC"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
										<td>I confirm that I have full authority to make payment from the above credit card account for my contribution.</td>
									</tr>
									</table>
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">SUBMIT</button></div>
							</div>
								
							<div id="BDInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Checking Account Information</div>
								<div class="PL PR frmText paymentGateway BT BB">
									<cfif len(local.profile_2.strPaymentForm.inputForm)>
										<div id="bdForm">#local.profile_2.strPaymentForm.inputForm#</div>
									</cfif>
								</div>
								<div class="P">
									<div class="PB">* Please confirm the statement below:</div>
									<table width="100%">
									<tr>
										<td width="25"><input name="confirmationStatementBD" id="confirmationStatementBD"  type="checkbox" value="I confirm." class="tsAppBodyText optionsCheckbox"  /></td>
										<td>I confirm that I have full authority to make payment from the above checking account account for my contribution.</td>
									</tr>
									</table>
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">SUBMIT</button></div>
							</div>

							<div id="CheckInfo" style="display:none;" class="CPSection">
								<div class="CPSectionTitle">Check Information</div>
								<div class="P">
									Please <strong>print</strong> the confirmation and <strong>send</strong> it with your check to the following address:<br /><br />
									<strong>Colorado Criminal Defense Bar</strong><br />
									955 Bannock St, Ste 200<br />
									Denver, CO 80204
								</div>
								<div class="P"><button type="submit" class="tsAppBodyButton" name="btnSubmit">CONTINUE</button></div>
							</div>
						</div>
						<cfinclude template="/model/cfformprotect/cffp.cfm" />
						</cfform>
					</div>
				</div>
			</cfcase>
		
			<!--- PROCESS: ====================================================================================================================================== --->
			<cfcase value="2">
				
				<!--- CHECK FOR SPAM SUBMISSION: -------------------------------------------------------------------------------- --->
				<cfif NOT local.objCffp.testSubmission(form)>
					<cflocation url="#local.customPage.baseURL#&isSubmitted=100" addtoken="no">
				</cfif>
				<cfset local.qryStates = application.objCommon.getStates()>
				<cfset local.thisOfficeStateID = 0>
				<cfset local.thisOfficeStateName = "">
				<cfif (len(trim(event.getValue('state',''))))>
					<cfquery name="local.getStateOffice" dbtype="query">
						select stateID, StateName from [local].qryStates where StateCode = <cfqueryparam  value="#event.getTrimValue('state','')#" cfsqltype="cf_sql_varchar">
					</cfquery>
					<cfset local.thisOfficeStateID = val(local.getStateOffice.stateID)>
					<cfset local.thisOfficeStateName = local.getStateOffice.StateName>
				</cfif>
				<cfset local.thisHomeStateID = 0>
				<cfset local.thisHomeStateName = "">
				<cfif (len(trim(event.getValue('home_state',''))))>
					<cfquery name="local.getHomeState" dbtype="query">
						select stateID, StateName from [local].qryStates where StateCode = <cfqueryparam  value="#event.getTrimValue('home_state','')#" cfsqltype="cf_sql_varchar">
					</cfquery>
					<cfset local.thisHomeStateID = val(local.getHomeState.stateID)>
					<cfset local.thisHomeStateName = local.getHomeState.StateName>
				</cfif>

				<cfsavecontent variable="local.invoice">
					
					#local.pageCSS#
					<style>table.customPage {border:1px solid black!important;}</style>
					
					<p>#local.formNameDisplay# submitted on #dateformat(now(),"dddd, m/d/yyyy")# #timeformat(now(),"h:mm tt")#.</p>
					<table cellpadding="2" cellspacing="0" width="100%" border="1" class="customPage">
			
					<tr class="msgHeader BB"><td colspan="2" class="b">CONTACT INFORMATION</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">First Name:</td><td class="frmText">#event.getValue('firstName','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Middle Name:</td><td class="frmText">#event.getValue('middleName','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Last Name:</td><td class="frmText">#event.getValue('lastName','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Firm/School Name:</td><td class="frmText">#event.getValue('firmName','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Firm/School Address:</td><td class="frmText">#event.getValue('address','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">&nbsp;</td><td class="frmText">#event.getValue('address2','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">City:</td><td class="frmText">#event.getValue('city','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">State:</td><td class="frmText">#local.thisOfficeStateName#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Zip:</td><td class="frmText">#event.getValue('zip','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">County:</td><td class="frmText">#event.getValue('county','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Office Phone:</td><td class="frmText">#event.getValue('phone','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Office Fax:</td><td class="frmText">#event.getValue('fax','')#&nbsp;</td></tr>					
					<tr class="frmRow2 BB"><td class="frmText b">Home Address:</td><td class="frmText">#event.getValue('home_address1','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">&nbsp;</td><td class="frmText">#event.getValue('home_address2','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Home City:</td><td class="frmText">#event.getValue('home_city','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Home State:</td><td class="frmText">#local.thisHomeStateName#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Home Zip:</td><td class="frmText">#event.getValue('home_zip','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Home County:</td><td class="frmText">#event.getValue('home_country','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Home Phone:</td><td class="frmText">#event.getValue('home_phone','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Cell:</td><td class="frmText">#event.getValue('home_cell','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Email Address:</td><td class="frmText">#event.getValue('email','')#&nbsp;</td></tr>
					<tr class="frmRow1 BB"><td class="frmText b">Website:</td><td class="frmText">#event.getValue('website','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Professional Status:</td><td class="frmText">#event.getValue('professionalStatus','')#&nbsp;</td></tr>
					<cfif event.getValue('professionalStatus','') eq 'Attorney'>
						<tr class="frmRow1 BB"><td class="frmText b">Date Admitted to Bar:</td><td class="frmText">#event.getValue('barDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Bar Number:</td><td class="frmText">#event.getValue('barNumber','')#&nbsp;</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Other States Licensed:</td><td class="frmText">#event.getValue('otherState','')#&nbsp;</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Sliding Scale:</td><td class="frmText">#event.getValue('slidingScale','')#&nbsp;</td></tr>
						<cfif event.getValue('slidingScale') eq 'Yes'>
						<tr class="frmRow1 BB"><td class="frmText b">Other Counties:</td><td class="frmText">#event.getValue('extraCounty','')#&nbsp;</td></tr>
						</cfif>
					<cfelseif event.getValue('professionalStatus','') eq 'Investigator/Para Professional'>
						<tr class="frmRow2 BB"><td class="frmText b">Have you ever been convicted of a felony?</td><td class="frmText">#event.getValue('felony','')#&nbsp;</td></tr>
						<cfif event.getValue('felony','No') eq 'Yes'>
							<tr class="frmRow1 BB"><td class="frmText b">Please explain:</td><td class="frmText">#event.getValue('felonyExplain','')#&nbsp;</td></tr>
						</cfif>
					<cfelseif event.getValue('professionalStatus','') eq 'Law Student'>
						<tr class="frmRow2 BB"><td class="frmText b">Expected Graduation Date:</td><td class="frmText">#event.getValue('barDate_new','')#&nbsp;</td></tr>
					<cfelseif event.getValue('professionalStatus','') eq 'Public Defender' OR event.getValue('professionalStatus','') eq 'Life Member' OR event.getValue('professionalStatus','') eq 'ORPC'>
						<tr class="frmRow2 BB"><td class="frmText b">Date Admitted to Bar:</td><td class="frmText">#event.getValue('barDate_new','')#&nbsp;</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Bar Number:</td><td class="frmText">#event.getValue('barNumber','')#&nbsp;</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Other States Licensed:</td><td class="frmText">#event.getValue('otherState','')#&nbsp;</td></tr>
					</cfif>
					
					<cfif listLen(event.getValue('jurisdictions',''))>
						<cfset local.jurisdictionlist = "">
						<cfloop array="#local.JurisdictionsStruct.columnValueArr#" index="local.thisOption">
							<cfif listFindNoCase(event.getValue('jurisdictions',''), local.thisOption.valueid)>
								<cfset local.jurisdictionlist = listAppend(local.jurisdictionlist,local.thisOption.columnValueString)>
							</cfif>
						</cfloop>
						<tr class="frmRow2 BB"><td class="frmText b">Jurisdictions Served:</td><td class="frmText">#local.jurisdictionlist#&nbsp;</td></tr>
					</cfif>

					<tr class="frmRow2 BB"><td class="frmText b">ADC Member:</td><td class="frmText">#event.getValue('adc','')#&nbsp;</td></tr>
					<tr><td colspan="2">&nbsp;</td></tr>
					<tr class="msgHeader BB"><td colspan="2" class="b">Demographic Information</td></tr>

					<cfif listLen(event.getValue('raceEthnicity',''))>
						<cfset local.raceEthnicitylist = "">
						<cfloop array="#local.EthnicityStruct.columnValueArr#" index="local.thisOption">
							<cfif listFindNoCase(event.getValue('raceEthnicity',''), local.thisOption.valueid)>
								<cfset local.raceEthnicitylist = listAppend(local.raceEthnicitylist,local.thisOption.columnValueString)>
							</cfif>
						</cfloop>
						<tr class="frmRow2 BB"><td class="frmText b">Which of the following best describes you? Please select all that apply:</td><td class="frmText">#local.raceEthnicitylist#&nbsp;</td></tr>
					</cfif>
					<tr class="frmRow2 BB"><td class="frmText b">What is your gender?:</td><td class="frmText">#event.getValue('gender','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Do you consider yourself a member of the Lesbian, Gay, Bisexual, Transgender and/or Queer (LGBTQ) community?:</td><td class="frmText">#event.getValue('LGBTCommunity','')#&nbsp;</td></tr>
					<tr class="frmRow2 BB"><td class="frmText b">Are you interested in becoming more involved in leadership and committee opportunities?</td><td class="frmText">#event.getValue('leadership','')#&nbsp;</td></tr>
					<tr><td colspan="2">&nbsp;</td></tr>
					<cfset local.listServers = event.getValue('listServers','None Selected')>	
					<cfset local.listServers = replaceNoCase(local.listServers,"Associate","Investigator/Paralegal")>
					<tr class="msgHeader BB"><td colspan="2" class="b">LIST SERVERS</td></tr>
						<tr class="frmRow1 BB"><td colspan="2" class="frmText">#local.listServers#</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Preferred Email Address:</td><td class="frmText">#event.getValue('listEmail','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">AREAS OF PRACTICE</td></tr>
						<tr class="frmRow1 BB"><td colspan="2" class="frmText">#event.getValue('practiceAreas','None Selected')#</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">Chapter Area</td></tr>
						<tr class="frmRow1 BB"><td colspan="2" class="frmText">#event.getValue('chapterArea','None Selected')#</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>

					<tr class="msgHeader BB"><td colspan="2" class="b">MEMBER DIRECTORY PREFERENCES</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Member Directory:</td><td class="frmText">#event.getValue('memberDirectory','')#&nbsp;</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Find a Lawyer Directory:</td><td class="frmText">#event.getValue('findALawyer','')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">RAPTOR PROGRAM</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Raptor Program:</td><td class="frmText">#dollarFormat(event.getValue('raptorProgram',0))#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">MEMBERSHIP AUTO RENEWAL METHOD</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">I prefer to "opt-out" of auto-renewal and will make other arrangements with CCDB:</td><td class="frmText">#event.getValue('optoutautorenewal','No')#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
						<cfif event.getValue('professionalStatus','') eq 'Attorney'>
							<tr class="msgHeader BB"><td colspan="2" class="b">MEMBERSHIP AGREEMENT</td></tr>
								<tr class="frmRow1 BB"><td class="frmText b">Authorization:</td><td class="frmText">#event.getValue('signature','')#&nbsp;</td></tr>
								<tr class="frmRow2 BB"><td class="frmText b">Date:</td><td class="frmText">#event.getValue('signatureDate_new','')#&nbsp;</td></tr>
								<tr class="frmRow1 BB"><td class="frmText b">Bar Date:</td><td class="frmText">#event.getValue('signatureBarDate_new','')#&nbsp;</td></tr>
								<tr><td colspan="2">&nbsp;</td></tr>
						<cfelseif event.getValue('professionalStatus','') eq 'Associate' OR event.getValue('professionalStatus','') eq 'Paralegal'>
							<tr class="msgHeader BB"><td colspan="2" class="b">MEMBERSHIP AGREEMENT</td></tr>
								<tr class="frmRow1 BB"><td class="frmText b">Authorization:</td><td class="frmText">#event.getValue('signature','')#&nbsp;</td></tr>
								<tr class="frmRow2 BB"><td class="frmText b">Date:</td><td class="frmText">#event.getValue('signatureDate_new','')#&nbsp;</td></tr>
								<tr><td colspan="2">&nbsp;</td></tr>
						</cfif>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">MEMBERSHIP LEVEL</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">#event.getValue('membership','')#:</td><td class="frmText">#dollarFormat(event.getValue('membershipTotalAmt',0))#&nbsp;</td></tr>
						<tr><td colspan="2">&nbsp;</td></tr>
						
					<tr class="msgHeader BB"><td colspan="2" class="b">TOTAL DUE</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Total Amount:</td><td class="frmText">#dollarFormat(event.getValue('totalTotalAmt',0))#&nbsp;</td></tr>
						<tr class="frmRow2 BB"><td class="frmText b">Payment Schedule:</td><td class="frmText">#event.getValue('paymentSchedule','Annual')#&nbsp;</td></tr>
						<tr class="frmRow1 BB"><td class="frmText b">Amount Due Now:</td><td class="frmText">#dollarFormat(event.getValue('dueNowAmt',0))#&nbsp;</td></tr>
					</table>					
				</cfsavecontent>

				<!--- UPDATE CUSTOM FIELDS / PROF LICENSE --->
				
				<cfset local.objSaveMember = application.objCustomPageUtils.mem_objSaveMember(memberID=local.useMID)>
				<cfset local.objSaveMember.setAddress(
					type='Office Address', 
					address1=event.getValue('address',''),
					address2=event.getValue('address2',''), 
					city=event.getValue('city',''), 
					stateID=local.thisOfficeStateID, 
					postalCode=event.getValue('zip',''), 
					county = event.getValue('county','')
					)/>
			    <cfset local.objSaveMember.setPhone(addressType='Office Address',type='Phone',value=event.getValue('phone',''))/>
			    <cfset local.objSaveMember.setPhone(addressType='Office Address',type='Fax',value=event.getValue('fax',''))/>

				<cfset local.objSaveMember.setAddress(
					type='Home Address', 
					address1=event.getValue('home_address1',''),
					address2=event.getValue('home_address2',''), 
					city=event.getValue('home_city',''), 
					stateID=local.thisHomeStateID, 
					postalCode=event.getValue('home_zip',''), 
					county = event.getValue('home_country','')
					)/>
			    <cfset local.objSaveMember.setPhone(addressType='Home Address',type='Phone',value=event.getValue('home_phone',''))/>
			    <cfset local.objSaveMember.setPhone(addressType='Home Address',type='Cell', value=event.getValue('home_cell',''))/>

				<cfset local.licenseDate = arguments.event.getTrimValue('barDate_new','')>
				<cfset local.licenseNumber = arguments.event.getTrimValue('barNumber','')>
				<cfif (event.getTrimValue('professionalStatus','') eq 'Attorney' OR event.getTrimValue('professionalStatus','') eq 'Public Defender') AND Len(local.licenseDate)>
					<cfset local.qryOrgProLicenses = application.objOrgInfo.getOrgProfessionalLicenseTypes(orgID=local.orgID)>
					<cfif event.getTrimValue('otherState','') eq ''>
						<cfset local.objSaveMember.setProLicense(name='Colorado', status='Active', license=local.licenseNumber, date=local.licenseDate)>
					<cfelse>
						<cfset local.otherStates = listAppend(event.getTrimValue('otherState',''), "Colorado")>
						<cfloop query="local.qryOrgProLicenses">
							<cfif ListFindNoCase(local.otherStates, local.qryOrgProLicenses.PLName)>
								<cfset local.objSaveMember.setProLicense(name=local.qryOrgProLicenses.PLName, status='Active', license=local.licenseNumber, date=local.licenseDate)>
							</cfif>
						</cfloop>
					</cfif>
				</cfif>

				<cfif event.getTrimValue('professionalStatus','') eq 'Law Student' AND local.licenseDate neq ''>
					<cfset local.objSaveMember.setCustomField(field='Expected Graduation', value=local.licenseDate)>
				</cfif>
				<cfif event.getTrimValue('practiceAreas','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Areas of Practice', value=event.getTrimValue('practiceAreas',''))>
				</cfif>
				<cfif event.getTrimValue('otherState','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Other States Licensed', value=event.getTrimValue('otherState',''))>
				</cfif>
				<cfif event.getTrimValue('memberDirectory','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Member Directory', value=event.getTrimValue('memberDirectory',''))>
				</cfif>
				<cfif event.getTrimValue('findALawyer','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Find a Lawyer', value=event.getTrimValue('findALawyer',''))>
				</cfif>
				<cfif event.getTrimValue('listServers','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='List Servers', value=event.getTrimValue('listServers',''))>
				</cfif>
				<cfif event.getTrimValue('chapterArea','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Chapter Area', value=event.getTrimValue('chapterArea',''))>
				</cfif>
				<cfset local.OptOutAutoRenew = 0 />
				<cfif event.getTrimValue('optoutautorenewal','') eq 'Yes'>
					<cfset local.OptOutAutoRenew = 1 />
				</cfif>
				<cfset local.objSaveMember.setCustomField(field='OPT Out of Auto-Renew', value=local.OptOutAutoRenew)>

				<cfset local.autoRenewAgree = 0 />
				<cfif event.getTrimValue('autorenewagree','') eq 'Yes'>
					<cfset local.autoRenewAgree = 1 />
				</cfif>
				<cfset local.objSaveMember.setCustomField(field='Auto-Renew Agreement Signed', value=local.autoRenewAgree)>

				<cfif isValid("regex",event.getTrimValue('website',''),application.regEx.url)>
					<cfset local.objSaveMember.setWebsite(type='Website', value=event.getTrimValue('website'))>
				</cfif>
				<cfset local.objSaveMember.setCustomField(field='Contact Type', value=event.getValue('professionalStatus',''))>
				<cfif event.getTrimValue('jurisdictions','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Jurisdictions Served', valueID=event.getValue('jurisdictions',''))>
				</cfif>

				<cfif event.getTrimValue('raceEthnicity','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Race/Ethnicity', valueID=event.getValue('raceEthnicity',''))>
				</cfif>
				<cfif event.getTrimValue('gender','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Gender', value=event.getValue('gender',''))>
				</cfif>
				<cfif event.getTrimValue('LGBTCommunity','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='LGBT Community', value=event.getValue('LGBTCommunity',''))>
				</cfif>
				<cfif event.getTrimValue('leadership','') neq ''>
					<cfset local.objSaveMember.setCustomField(field='Interested in Leadership/Committee Opportunities', value=event.getValue('leadership',''))>
				</cfif>
				<cfset local.objSaveMember.setMemberType(memberType='User')>
				<cfset local.strResult = local.objSaveMember.saveData()>

				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<p>Thank you for submitting your application! Please print this page - it is your receipt.</p>	
						<hr />
						#local.invoice#		
					</cfoutput>
				</cfsavecontent>

				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.memberEmail.from},
					emailto=[{ name="", email=local.memberEmail.to}],
					emailreplyto= local.ORGEmail.to,
					emailsubject=local.memberEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=local.siteID,
					memberID=val( local.useMID),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>
				<cfset local.emailSentToUser = local.responseStruct.success>
				
				<cfsavecontent variable="local.mailContent">
					<cfoutput>
						<cfif NOT local.emailSentToUser>
							Member was not sent email confirmation due to bad Data.<br />
							Please contact, and let them know.
							<hr />
						</cfif>
						#local.invoice#
					</cfoutput>
				</cfsavecontent>
				
				<cfscript>
					local.arrEmailTo = [];
					local.ORGEmail.to = replace(local.ORGEmail.to,",",";","all");
					local.toEmailArr = listToArray(local.ORGEmail.to,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>
				<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
					emailfrom={ name="", email=local.ORGEmail.from},
					emailto=local.arrEmailTo,
					emailreplyto=local.ORGEmail.from,
					emailsubject=local.ORGEmail.SUBJECT,
					emailtitle="#arguments.event.getValue('mc_siteInfo.sitename')# - #local.formNameDisplay#",
					emailhtmlcontent=local.mailContent,
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
					messageTypeID=application.objCustomPageUtils.getMessageTypeID(),
					sendingSiteResourceID=this.siteResourceID
				)>

				<cfset session.invoice = local.invoice>
				<cflocation url="#local.customPage.baseURL#&isSubmitted=99" addtoken="no">
			</cfcase>
			<cfcase value="99">
				<div class="HeaderText">Thank you for submitting your application!</div>
				<br/>
				<div>This page has been emailed to the email address on file. If you would like, you could also print the page out as a receipt.</div>
				<br />
				<cfif isDefined("session.invoice")>
					<div class="BodyText">
						#replaceNoCase(replaceNoCase(replaceNoCase(session.invoice,"html>","div>","ALL"),"body>","div>","ALL"),"head>","div>","ALL")#
					</div>
					<cfset session.invoice = "" />
				</cfif>
			</cfcase>
			<cfcase value="100">
				<div>
					Error! you Can't Post Here.
				</div>
			</cfcase>
		</cfswitch>
	</div>
</cfoutput>
