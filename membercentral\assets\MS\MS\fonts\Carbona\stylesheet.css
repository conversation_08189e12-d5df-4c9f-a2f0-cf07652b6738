@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-Black.woff2') format('woff2'),
        url('Carbona-Black.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-ExtraBoldSlanted.woff2') format('woff2'),
        url('Carbona-ExtraBoldSlanted.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-ExtraLightSlanted.woff2') format('woff2'),
        url('Carbona-ExtraLightSlanted.woff') format('woff');
    font-weight: 200;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-Bold.woff2') format('woff2'),
        url('Carbona-Bold.woff') format('woff');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-BoldSlanted.woff2') format('woff2'),
        url('Carbona-BoldSlanted.woff') format('woff');
    font-weight: 700;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-BlackSlanted.woff2') format('woff2'),
        url('Carbona-BlackSlanted.woff') format('woff');
    font-weight: 900;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-ExtraBold.woff2') format('woff2'),
        url('Carbona-ExtraBold.woff') format('woff');
    font-weight: 900;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-ExtraLight.woff2') format('woff2'),
        url('Carbona-ExtraLight.woff') format('woff');
    font-weight: 200;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-Light.woff2') format('woff2'),
        url('Carbona-Light.woff') format('woff');
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-LightSlanted.woff2') format('woff2'),
        url('Carbona-LightSlanted.woff') format('woff');
    font-weight: 300;
    font-style: italic;
    font-display: swap;
}
@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-Medium.woff2') format('woff2'),
        url('Carbona-Medium.woff') format('woff');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}
@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-MediumSlanted.woff2') format('woff2'),
        url('Carbona-MediumSlanted.woff') format('woff');
    font-weight: 500;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-SemiBold.woff2') format('woff2'),
        url('Carbona-SemiBold.woff') format('woff');
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-RegularSlanted.woff2') format('woff2'),
        url('Carbona-RegularSlanted.woff') format('woff');
    font-weight: 400;
    font-style: italic;
    font-display: swap;
}

@font-face {
    font-family: 'Carbona Test';
    src: url('Carbona-SemiBoldSlanted.woff2') format('woff2'),
        url('Carbona-SemiBoldSlanted.woff') format('woff');
    font-weight: 600;
    font-style: italic;
    font-display: swap;
}

