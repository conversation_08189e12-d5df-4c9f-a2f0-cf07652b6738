<cfsavecontent variable="local.allocatePaymentJS">
	<cfoutput>
	<script language="javascript">
		function hideAlert() { mca_hideAlert('err_allocate_payment'); };
		function showAlert(msg) { mca_showAlert('err_allocate_payment', msg); };

		<cfswitch expression="#local.adt#">
			<!--- sales --->
			<cfcase value="s">
				function clearInvAmts() {
					$('##allocatesales :input').each(function() {
						$(this).val('0.00');
					});
				}
				function checkAllocForm() {
					sumSalesAlloc();
					if (top.$('##btnSaveAllocation').is(":disabled") == true) return false;
					else {
						top.$('##btnSaveAllocation').attr('disabled',true);
						return true;
					}
				}
				function sumSalesAlloc() {
					var AmtCanAllocate = parseFloat(#local.qryPayment.AmtCanAllocate#);
					var salesum = 0;
					hideAlert();
					top.$('##btnSaveAllocation').attr('disabled',false);

					var overAllMsg = '';
					<cfloop query="local.qrySalesDue">
						if (parseFloat($('##alloc_s_#local.qrySalesDue.transactionid#_amount').val().toString().replace(/\$|\,/g,'')) > parseFloat(#local.qrySalesDue.SalesUnAlloc#)) {
							overAllMsg += 'The maximum amount you may allocate to #JSStringFormat(local.qrySalesDue.detail)# is #dollarFormat(local.qrySalesDue.SalesUnAlloc)#.<br/>';
						}
					</cfloop>
					if (overAllMsg.length > 0) {
						showAlert(overAllMsg);
						top.$('##btnSaveAllocation').attr('disabled',true);
					} else {
						$('##allocatesales :input').each(function() {
							salesum += parseFloat($(this).val().toString().replace(/\$|\,/g,''));
						});
						if (salesum > AmtCanAllocate) {
							showAlert('The maximum amount you may allocate from this payment is $' + formatCurrency(AmtCanAllocate) + '.');
							top.$('##btnSaveAllocation').attr('disabled',true);
						} else if (salesum == 0) {
							top.$('##btnSaveAllocation').attr('disabled',true);
						}
					}
				}
				$(function() {
					$('##allocatesales :input').blur(function() {
						$(this).val(formatCurrency($(this).val()));
						sumSalesAlloc();
					});
				});
			</cfcase>
			<!--- invoice / default --->
			<cfdefaultcase>
				<cfif local.adt eq "m">
					function toggleInvoiceFilters(f) {
						var show = f && $('##divFilterForm').hasClass('d-none');
						$('##divFilterForm').toggleClass('d-none', !show);
						$('##divFilterFormLink').toggleClass('d-none', show);
					}
					function filterInvoiceAllocations() {
						hideAlert();
						if (!checkInvoiceFilterForm()) return false;

						setTotalInvDue('');
						top.$('##btnSaveAllocation').attr('disabled',true);
						var loadHTML = mca_getLoadingHTML('Loading..');
						$('##divAllocationFormContainer').html(loadHTML).load('#local.allocatePaymentInvLink#&' + $('##frmInvoiceFilter').serialize(), function() {  });
					}
					function checkInvoiceFilterForm() {
						var das = $('##dueAmtStart');
						var dae = $('##dueAmtEnd');
						if (das.val().length > 0) das.val(formatCurrency(das.val()));
						if (dae.val().length > 0) dae.val(formatCurrency(dae.val()));
						var la = formatCurrency(das.val()).replace(/\,/g,'');
						var ha = formatCurrency(dae.val()).replace(/\,/g,'');
						if ($('##dueAmtEnd').val() != '' && parseFloat(la) > parseFloat(ha)) {
							$('##dueAmtEnd').addClass('bg-palered').focus();
							return false;
						}
						$('##dueAmtEnd').removeClass('bg-palered');
						return true;
					}
					function clearFilterInvoiceAllocations() {
						$('##frmInvoiceFilter')[0].reset();
						$('##frmInvoiceFilter [data-toggle="custom-select2"]').trigger('change');
						filterInvoiceAllocations();
					}
					function setTotalInvDue(total){
						$('##divTotalInvDue').toggleClass('d-none', total.length ? false : true);
						$('span##totalInvDueAmt').text(total);
					}
				</cfif>
				function clearInvAmts() {
					$('##allocateinvoice :input').each(function() {
						$(this).val('0.00');
					});
				}
				function checkAllocForm() {
					sumInvAlloc();

					if (top.$('##btnSaveAllocation').is(":disabled") == true) return false;
					else {
						top.$('##btnSaveAllocation').attr('disabled',true);
						return true;
					}
				}
				invoicesDueArr = [];
				function sumInvAlloc() {
					var AmtCanAllocate = parseFloat(#local.qryPayment.AmtCanAllocate#);
					var invsum = 0;
					hideAlert();
					top.$('##btnSaveAllocation').attr('disabled',false);

					var overAllMsg = '';

					$.each(invoicesDueArr , function(index, thisInvoice) {
						if (parseFloat($('##alloc_i_' + thisInvoice.invoiceid + '_amount').val().toString().replace(/\$|\,/g,'')) > parseFloat(thisInvoice.InvUnAll)) {
							overAllMsg += 'The maximum amount you may allocate to invoice ##' + thisInvoice.invoicenumber + ' is $'+ formatCurrency(thisInvoice.InvUnAll) +'.<br/>';
						}
					});
					
					if (overAllMsg.length > 0) {
						showAlert(overAllMsg);
						top.$('##btnSaveAllocation').attr('disabled',true);
					} else {
						$('##allocateinvoice :input').each(function() {
							invsum += parseFloat($(this).val().toString().replace(/\$|\,/g,'')).toFixed(2)*1;
							invsum = invsum.toFixed(2)*1;
						});
						if (invsum > AmtCanAllocate) {
							showAlert('The maximum amount you may allocate from this payment is $' + formatCurrency(AmtCanAllocate) + '.');
							top.$('##btnSaveAllocation').attr('disabled',true);
						} else if (invsum == 0) {
							top.$('##btnSaveAllocation').attr('disabled',true);
						}
					}
				}
				$(function() {
					mca_setupDatePickerRangeFields('duedateStart','duedateEnd');
					mca_setupDatePickerRangeFields('billeddateStart','billeddateEnd');
					mca_setupCalendarIcons('frmInvoiceFilter');
					mca_setupSelect2($('##frmInvoiceFilter'));
				});
			</cfdefaultcase>
		</cfswitch>
		$(function() {
			top.MCModalUtils.setTitle($('##allocPmtTitle').html());
			top.MCModalUtils.buildFooter({
				classlist: 'd-flex',
				buttons: [ 
					{ class: "btn-primary py-1 ml-auto", clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmAllocation :submit").click', label: 'Save Allocations', name: 'btnSaveAllocation', id: 'btnSaveAllocation' }
				]
			});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.allocatePaymentJS)#">

<cfoutput>
<h4 id="allocPmtTitle" class="d-none">Allocate #dollarFormat(local.qryPayment.amount)# #local.qryPayment.detail#<cfif len(local.strPayObj.t)> to #local.strPayObj.t#</cfif></h4>
<div id="paystep2DIV" class="p-3">
	<cfif local.strPayObj.ta gt 0>
		<h6 id="addPayAmount" class="text-primary font-weight-bold mb-3">#dollarFormat(local.strPayObj.ta)# Due</h6>
	</cfif>

	<div class="section py-2">
		<div class="card card-box shadow-none">
			<div class="card-title pl-2 pt-2">
				<i class="fa-regular fa-user mr-1"></i> Payer
			</div>
			<div class="card-body py-0">
				<div>#local.qryPayee.firstName# #local.qryPayee.lastName# (#local.qryPayee.memberNumber#)</div>
				<cfif len(local.qryPayee.company)><div>#local.qryPayee.company#</div></cfif>
			</div>
		</div>
	</div>

	<div class="section py-2">
		<div class="card card-box shadow-none">
			<div class="card-title pl-2 pt-2">
				<i class="fa-regular fa-money-bill-1 mr-1"></i> Payment
			</div>
			<div class="card-body py-0">
				<div>#dollarFormat(local.qryPayment.amount)# #local.qryPayment.detail# on #dateformat(local.qryPayment.transactionDate,"m/d/yyyy")# #timeformat(local.qryPayment.transactionDate,"h:mm tt")#</div>
				<div>#dateformat(local.qryPayment.depositDate,"m/d/yyyy")# #local.qryPayment.batchStatus# batch <i>#local.qryPayment.batchName#</i></div>
				<cfif local.qryPayment.cache_allocatedAmountOfPayment gt 0>
					<div><span class="font-weight-bold text-green">#dollarformat(local.qryPayment.cache_allocatedAmountOfPayment)#</span> of this payment is already allocated.</div>
				</cfif>
				<div><span class="font-weight-bold text-green">#dollarformat(local.qryPayment.AmtCanAllocate)#</span> of this payment is available to allocate.</div>
			</div>
		</div>
	</div>

	<cfif local.adt eq "m">
		<div id="divFilterFormLink" class="border-bottom mt-3 mb-2 pb-2">
			<a href="javascript:toggleInvoiceFilters(true);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter invoices.">
				<i class="fa fa-filter"></i> Filter Invoices associated with #local.qryPayee.firstName# #local.qryPayee.lastName#
			</a>
		</div>

		<div id="divFilterForm" class="d-none mb-2">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">Filter Invoices associated with #local.qryPayee.firstName# #local.qryPayee.lastName#</div>
					<button type="button" class="close" aria-label="Close" onclick="toggleInvoiceFilters(false);">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="card-body pb-2">
					<form name="frmInvoiceFilter" id="frmInvoiceFilter">
						<input type="hidden" name="amtCanAllocateForPrefill" id="amtCanAllocateForPrefill" value="#local.amtCanAllocateForPrefill#">
						<div class="form-group row no-gutters">
							<div class="col-md-6 col-sm-12 pr-md-1">
								<select id="statusID" name="statusID" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Any Invoice Status">
									<cfloop query="local.qryStatus">
										<option value="#local.qryStatus.statusID#">#local.qryStatus.status#</option>
									</cfloop>
								</select>
							</div>
							<div class="col-md-6 col-sm-12">
								<div class="row no-gutters">
									<div class="col-sm-6 pr-1">
										<div class="input-group input-group-sm">
											<input type="text" name="billeddateStart" id="billeddateStart" value="" class="form-control form-control-sm dateControl" placeholder="Billed from">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateStart"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</div>
									<div class="col-sm-6">
										<div class="input-group input-group-sm">
											<input type="text" name="billeddateEnd" id="billeddateEnd" value="" class="form-control form-control-sm dateControl" placeholder="Billed To">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateEnd"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row no-gutters">
							<div class="col-md-6 col-sm-12 pr-md-1">
								<div class="input-group input-group-sm">
									<div class="input-group-prepend">
										<span class="input-group-text">#local.qryOrgData.invoiceNumPrefix#<cfif len(local.qryOrgData.invoiceNumPrefix)>-</cfif></span>
									</div>
									<input type="text" name="invoiceNumber" id="invoiceNumber" value="" class="form-control form-control-sm" placeholder="Invoice number" onblur="mca_sanitizeInvNumInput('#arguments.event.getValue('mc_siteinfo.orgCode')#',this)">
								</div>
							</div>
							<div class="col-md-6 col-sm-12">
								<div class="row no-gutters">
									<div class="col-sm-6 pr-1">
										<div class="input-group input-group-sm">
											<input type="text" name="duedateStart" id="duedateStart" value="" class="form-control form-control-sm dateControl" placeholder="Due from">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="duedateStart"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</div>
									<div class="col-sm-6">
										<div class="input-group input-group-sm">
											<input type="text" name="duedateEnd" id="duedateEnd" value="" class="form-control form-control-sm dateControl" placeholder="Due To">
											<div class="input-group-append">
												<span class="input-group-text cursor-pointer calendar-button" data-target="duedateEnd"><i class="fa-solid fa-calendar"></i></span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row no-gutters">
							<div class="col-md-6 col-sm-12 pr-md-1">
								<select id="cardOnFile" name="cardOnFile" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="With or Without Pay Method">
									<option value="0">No Pay Method</option>
									<cfloop query="local.qryPayProfiles">
										<option value="#local.qryPayProfiles.profileID#">#local.qryPayProfiles.profileName#</option>
									</cfloop>
								</select>
							</div>
							<div class="col-md-6 col-sm-12">
								<select id="invProfile" name="invProfile" class="form-control form-control-sm" multiple="true" data-toggle="custom-select2" placeholder="Any Invoice Profile">
									<cfloop query="local.qryInvoiceProfiles">
										<option value="#local.qryInvoiceProfiles.profileID#">#local.qryInvoiceProfiles.profileName#</option>
									</cfloop>
								</select>
							</div>
						</div>
						<div class="form-group row no-gutters">
							<div class="col-md-3 col-sm-6 pr-md-1">
								<div class="input-group input-group-sm">
									<div class="input-group-prepend">
										<span class="input-group-text">$</span>
									</div>
									<input type="text" name="dueAmtStart" id="dueAmtStart" value="" size="15" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control form-control-sm" placeholder="Amt Due From">
								</div>
							</div>
							<div class="col-md-3 col-sm-6 pr-md-1">
								<div class="input-group input-group-sm">
									<div class="input-group-prepend">
										<span class="input-group-text">$</span>
									</div>
									<input type="text" name="dueAmtEnd" id="dueAmtEnd" value="" size="15" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control form-control-sm" placeholder="Amt Due To">
								</div>
							</div>
							<div class="col-md-6 col-sm-12 text-right">
								<button type="button" name="filterInvBtn" id="filterInvBtn" class="btn btn-sm btn-secondary" onclick="filterInvoiceAllocations()"><i class="fa-light fa-filter"></i> Filter Invoices</button>
								<button type="button" name="clearInvBtn" id="clearInvBtn" onclick="clearFilterInvoiceAllocations()" class="btn btn-sm btn-secondary">Clear Filters</button>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</cfif>

	<form name="frmAllocation" id="frmAllocation" action="#local.saveAllocationLink#" method="post" onsubmit="return checkAllocForm();">
	<input type="hidden" name="po" id="po" value="#arguments.event.getValue('po')#">

	<div id="err_allocate_payment" class="alert alert-danger my-2 d-none"></div>
	<div class="d-flex align-items-center">
		<cfif local.adt eq "m">
			<div id="divTotalInvDue" class="d-none">Total Amount Due: <span id="totalInvDueAmt" class="font-weight-bold"></span></div>
		</cfif>
		<button type="submit" class="d-none">Save Allocations</button>
	</div>

	<div id="divAllocationFormContainer" class="my-3">
		<cfswitch expression="#local.adt#">
			<cfcase value="s">
				<cfinclude template="frm_allocatePayment_sale.cfm">
			</cfcase>
			<cfdefaultcase>
				<cfinclude template="frm_allocatePayment_inv.cfm">
			</cfdefaultcase>
		</cfswitch>
	</div>
	</form>
</div>
</cfoutput>