ALTER PROC dbo.tr_createTransaction_writeoff_sale
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@status varchar(20),
@amount decimal(18,2),
@transactionDate datetime,
@saleTransactionID int, -- this can be a sale or adjustment TID
@transactionID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @assignedToMemberID int, @ownedByOrgID int, @detail varchar(500), @debitGLAccountID int, @ARGLAID int, 
		@creditGLAccountID int, @invoiceID int, @invStatusID int, @invstatus varchar(10), @stTypeID int, @stID int, 
		@activePaymentAllocatedAmount decimal(18,2), @pendingPaymentAllocatedAmount decimal(18,2), @amtDueNoPendingOnInvoice decimal(18,2),
		@tr_AdjustTrans int, @tr_WriteOffSaleTrans int;

	set @transactionID = 0;

	-- no zero or negative dollar writeoffs
	if @amount <= 0
		RAISERROR('amount is not greater than 0', 16, 1);

	-- ensure amount is 2 decimals
	select @amount = cast(@amount as decimal(18,2));

	-- get assignedToMemberID from sale transaction
	select @assignedToMemberID = assignedToMemberID, @ownedByOrgID = ownedByOrgID, @detail = detail
	from dbo.tr_transactions 
	where transactionID = @saleTransactionID;

	EXEC dbo.tr_getGLAccountByGLCode @orgID=@ownedByOrgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;
	EXEC dbo.tr_getGLAccountByGLCode @orgID=@ownedByOrgID, @GLCode='WRITEOFF', @GLAccountID=@debitGLAccountID OUTPUT;
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_WriteOffSaleTrans = dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans');

	-- ensure @saleTransactionID is a non-voided sale, sales tax, or positive adjustment
	IF NOT EXISTS (
		select transactionID
		from dbo.tr_transactions
		where transactionID = @saleTransactionID
		and debitGLAccountID = @ARGLAID
		and typeID in (1,3,7)
		and statusID = 1
	)
		RAISERROR('saleTransactionID is not a non-voided sale, sales tax, or positive adjustment', 16, 1);

	-- dont assume memberid is the active one. get the active one.
	select @assignedToMemberID = activeMemberID from dbo.ams_members where memberID = @assignedToMemberID;
	select @recordedByMemberID = activeMemberID from dbo.ams_members where memberID = @recordedByMemberID;

	-- WO is debit acct, AR is credit acct
	set @creditGLAccountID = @ARGLAID;

	-- ensure we have valid debit/credit accts. Accounts do not need to be active.
	IF @debitGLAccountID is null 
		RAISERROR('Debit GL account not found. Must be an active system account with GLCode = WRITEOFF', 16, 1);
	IF @creditGLAccountID is null 
		RAISERROR('Credit GL account not found. Must be an active system account with GLCode = ACCOUNTSRECEIVABLE', 16, 1);

	-- if writing off a sale on an open/pending invoice, close it. Only closed/delinq inv can write offs.
	select @invoiceID=i.invoiceID, @invstatus=ins.status
	from dbo.tr_invoices as i
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @ownedByOrgID and it.invoiceID = i.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	where i.orgID = @ownedByOrgID
	and it.transactionID = @saleTransactionID;

	BEGIN TRAN;
		IF @invstatus not in ('Closed','Delinquent')
			EXEC dbo.tr_closeInvoice @orgID=@ownedByOrgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceID;
	
		-- ensure amount is abs
		INSERT INTO dbo.tr_transactions (ownedByOrgID, recordedOnSiteID, statusID, detail, parentTransactionID, 
			amount, dateRecorded, transactionDate, assignedToMemberID, recordedByMemberID, statsSessionID, 
			typeID, debitGLAccountID, creditGLAccountID)
		VALUES (@ownedByOrgID, @recordedOnSiteID, dbo.fn_tr_getStatusID(@status), @detail, null, 
			abs(@amount), getdate(), @transactionDate, @assignedToMemberID, @recordedByMemberID, @statsSessionID, 
			6, @debitGLAccountID, @creditGLAccountID);
		select @transactionID = SCOPE_IDENTITY();

		INSERT INTO dbo.tr_relationships (typeID, transactionID, appliedToTransactionID, orgID)
		VALUES (@tr_WriteOffSaleTrans, @transactionID, @saleTransactionID, @ownedByOrgID);

		-- update sales cache
		-- if writing off an adjustment, update cache of sale or tax it is adjusting
		select @stTypeID = typeID from dbo.tr_transactions where transactionID = @saleTransactionID;
		IF @stTypeID in (1,7)
			set @stID = @saleTransactionID;
		ELSE
			select @stID = tSale.transactionID
			from dbo.tr_transactions as tSale
			inner join dbo.tr_relationships as tR on tR.orgID = @ownedByOrgID
				and tR.typeID = @tr_AdjustTrans 
				and tR.appliedToTransactionID = tSale.transactionID 
				and tR.transactionID = @saleTransactionID
			where tSale.ownedByOrgID = @ownedByOrgID;

		select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
			@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSale(@stID);

		UPDATE dbo.tr_transactionSales
		SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
			cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
		WHERE orgID = @ownedByOrgID
		and transactionID = @stID;

		-- update invoiceTransactions cache
		select @activePaymentAllocatedAmount = null, @pendingPaymentAllocatedAmount = null;
		select @activePaymentAllocatedAmount = activePaymentAllocatedAmount,
			@pendingPaymentAllocatedAmount = pendingPaymentAllocatedAmount
		from dbo.fn_tr_getAllocatedAmountofSaleOrAdj(@ownedByOrgID,@saleTransactionID);

		UPDATE dbo.tr_invoiceTransactions
		SET cache_activePaymentAllocatedAmount = @activePaymentAllocatedAmount,
			cache_pendingPaymentAllocatedAmount = @pendingPaymentAllocatedAmount
		WHERE transactionID = @saleTransactionID;

		-- check the in-bound rules.
		-- sale - new cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount must be between 0 and cache_amountAfterAdjustment
		IF NOT EXISTS (select saleID from dbo.tr_transactionSales where orgID = @ownedByOrgID and transactionID = @stID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_amountAfterAdjustment)
			OR NOT EXISTS (select itID from dbo.tr_invoiceTransactions where orgID = @ownedByOrgID and transactionID = @saleTransactionID and cache_activePaymentAllocatedAmount+cache_pendingPaymentAllocatedAmount between 0 and cache_invoiceAmountAfterAdjustment)
			RAISERROR('in-bound checks failed', 16, 1);

		-- cleanup invoice
		-- if invoice is closed/delinq and is now fully paid with active payments, mark it as paid
		select @invoiceID=i.invoiceID, @invStatusID=ins.statusID, @invstatus=ins.status
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @ownedByOrgID and it.invoiceID = i.invoiceID
			inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
			where it.transactionID = @saleTransactionID;
		select @amtDueNoPendingOnInvoice = sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount)
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.transactionID = it.transactionID
			where it.orgID = @ownedByOrgID
			and it.invoiceID = @invoiceID
			and t.statusID <> 2;
		IF @invstatus in ('Closed','Delinquent') and @amtDueNoPendingOnInvoice = 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"INV",
				"ORGID":' + cast(s.orgID as varchar(10)) + ',
				"SITEID":' + cast(s.siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('Pay Profile ' + mpp.detail + ' removed from Invoice ' + i.fullInvoiceNumber),'"','\"') + '" } }'
			FROM dbo.tr_invoices as i
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
			INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = i.MPProfileID
			INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
			WHERE i.invoiceID = @invoiceID;

			update dbo.tr_invoices
			set statusID = 4, 
				payProfileID = null,
				MPProfileID = null
			where invoiceID = @invoiceID;
			
			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			values (@invoiceID, getdate(), 4, @invStatusID, @recordedByMemberID);
		END
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
