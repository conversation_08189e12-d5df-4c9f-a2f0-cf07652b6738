ALTER PROC dbo.sub_updatePaymentProfile
@siteID int,
@subscriberID int,
@MPProfileID int,
@payProfileID int,
@payProcessFee bit,
@retainCurrentFeePercent bit,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;
	CREATE TABLE #tmpAuditLog (auditCode varchar(10), msg varchar(max));
	CREATE TABLE #tmpUpdateInvoices (invoiceID int);

	DECLARE @orgID int, @existingSubPayProfileID int, @existingMerchantProfileID int, @existingSubPayProcessFee bit, 
		@processFeePercent decimal(5,2), @payProfileIDList varchar(100);
	SET @MPProfileID = NULLIF(@MPProfileID,0);
	SET @payProfileID = NULLIF(@payProfileID,0);
	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	SELECT @existingSubPayProfileID = payProfileID, @existingMerchantProfileID = MPProfileID, @existingSubPayProcessFee = payProcessFee
	FROM dbo.sub_subscribers
	WHERE subscriberID = @subscriberID;

	SET @payProcessFee = ISNULL(@payProcessFee,0);

	IF @payProcessFee = 1 BEGIN
		IF @retainCurrentFeePercent = 1
			SELECT @processFeePercent = processFeePercent
			FROM dbo.sub_subscribers
			WHERE subscriberID = @subscriberID;
		ELSE
			SELECT @processFeePercent = processFeeDonationFeePercent
			FROM dbo.mp_profiles
			WHERE profileID = @MPProfileID
			AND enableProcessingFeeDonation = 1;
	END

	IF ISNULL(@existingSubPayProfileID,0) <> ISNULL(@payProfileID,0)
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'SUBS', 'Pay Profile ' +
				CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
					WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
					WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
				END + ' Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(@subscriberID AS varchar(10)) + ')'
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = s.payProfileID
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
		WHERE s.subscriberID = @subscriberID;

	IF @existingSubPayProcessFee <> @payProcessFee BEGIN
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'SUBS', 'Pay Processing Fees changed from ' + CASE WHEN @existingSubPayProcessFee = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @payProcessFee = 1 THEN 'Yes' ELSE 'No' END + ' for Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(@subscriberID AS varchar(10)) + ')'
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
		WHERE s.subscriberID = @subscriberID;

		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'SUBS', 'Processing Fee Percentage changed from ' + CAST(ISNULL(s.processFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(@subscriberID AS varchar(10)) + ')'
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
		WHERE s.subscriberID = @subscriberID;
	END
	
	-- inv
	INSERT INTO #tmpUpdateInvoices (invoiceID)
	SELECT DISTINCT i.invoiceID
	FROM dbo.sub_subscribers AS s
	INNER JOIN dbo.tr_invoiceItems as ii on ii.orgID = @orgID
		AND ii.itemType = 'Dues'
		AND ii.itemID = s.subscriberID
	INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID
		AND i.invoiceID = ii.invoiceID
		AND i.statusID <> 4
		AND (
			ISNULL(i.payProfileID,0) <> ISNULL(@payProfileID,0)
			OR ISNULL(i.MPProfileID,0) <> ISNULL(@MPProfileID,0)
			OR i.payProcessFee <> @payProcessFee
		)
	INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID
		AND it.invoiceID = i.invoiceID
		AND it.cache_invoiceAmountAfterAdjustment > 0
	WHERE s.orgID = @orgID
	AND s.rootSubscriberID = @subscriberID;

	INSERT INTO #tmpAuditLog (auditCode, msg)
	SELECT 'INV', 'Pay Profile ' + 
			CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
				WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
				WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
			END + ' Invoice ' + i.fullInvoiceNumber
	FROM dbo.tr_invoices as i
	INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
	INNER JOIN dbo.organizations as o on o.orgID = i.orgID
	LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
	LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
	WHERE i.orgID = @orgID
	AND ISNULL(i.payProfileID,0) <> ISNULL(@payProfileID,0);

	INSERT INTO #tmpAuditLog (auditCode, msg)
	SELECT 'INV', 'Pay Processing Fees changed from ' + CASE WHEN i.payProcessFee = 1 THEN 'Yes' ELSE 'No' END  + ' to ' + CASE WHEN @payProcessFee = 1 THEN 'Yes' ELSE 'No' END + ' for Invoice ' + i.fullInvoiceNumber
	FROM dbo.tr_invoices as i
	INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
	INNER JOIN dbo.organizations as o on o.orgID = i.orgID
	WHERE i.orgID = @orgID
	AND i.payProcessFee <> @payProcessFee;

	INSERT INTO #tmpAuditLog (auditCode, msg)
	SELECT 'INV', 'Processing Fee Percentage changed from ' + CAST(ISNULL(i.processFeePercent ,0) AS varchar(10)) + '% to ' + CAST(ISNULL(@processFeePercent ,0) AS varchar(10)) + '% for Invoice ' + i.fullInvoiceNumber
	FROM dbo.tr_invoices as i
	INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
	INNER JOIN dbo.organizations as o on o.orgID = i.orgID
	WHERE i.orgID = @orgID
	AND ISNULL(i.processFeePercent,0) <> ISNULL(@processFeePercent,0);
		
	BEGIN TRAN;
		IF ISNULL(@existingSubPayProfileID,0) <> ISNULL(@payProfileID,0) 
		OR ISNULL(@existingMerchantProfileID,0) <> ISNULL(@MPProfileID,0) 
		OR @existingSubPayProcessFee <> @payProcessFee
			UPDATE dbo.sub_subscribers
			SET payProfileID = @payProfileID,
				MPProfileID = @MPProfileID,
				payProcessFee = @payProcessFee,
				processFeePercent = @processFeePercent
			WHERE subscriberID = @subscriberID;
			
		UPDATE tri
		SET tri.payProfileID = @payProfileID,
			tri.MPProfileID = @MPProfileID,
			tri.payProcessFee = @payProcessFee,
			tri.processFeePercent = @processFeePercent
		FROM dbo.tr_invoices AS tri
		INNER JOIN #tmpUpdateInvoices AS tmp ON tmp.invoiceID = tri.invoiceID
		WHERE tri.orgID = @orgID;

		-- trigger reprocessing of credit card expiration conditions (if limiting to subscriptions)
		IF ISNULL(@existingSubPayProfileID,0) <> ISNULL(@payProfileID,0) BEGIN
			SET @payProfileIDList = CAST(ISNULL(@existingSubPayProfileID,0) as varchar(20)) + ',' + CAST(ISNULL(@payProfileID,0) as varchar(20));
			EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittosub';
		END

		IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"' + auditCode + '",
				"ORGID":' + cast(@orgID as varchar(10)) + ',
				"SITEID":' + cast(@siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
			FROM #tmpAuditLog;
		END
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
