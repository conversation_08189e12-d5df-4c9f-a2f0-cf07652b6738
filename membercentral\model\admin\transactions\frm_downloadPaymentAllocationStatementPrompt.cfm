<cfsavecontent variable="local.pageJS">
	<cfoutput>
	<script language="javascript">	
		function showDownloadingPaymentAllocationMessage() { 
			$('##downloadingMessage').removeClass('d-none');
			$('##frmDownloadPaymentAllocationStatement').addClass('d-none');
			top.$('##btnMCModalSave').remove();
			window.setTimeout("top.MCModalUtils.hideModal();",3000);
			return true;
		}

		$(function() {
			top.MCModalUtils.buildFooter({
				classlist: 'd-flex justify-content-between',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmDownloadPaymentAllocationStatement :submit").click',
				extrabuttonlabel: 'Continue'
			});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<cfoutput>
<form name="frmDownloadPaymentAllocationStatement" id="frmDownloadPaymentAllocationStatement" class="p-3" action="#this.link.downloadPaymentAllocationStatement#&tid=#arguments.event.getValue('tid',0)#" method="post" onsubmit="return showDownloadingPaymentAllocationMessage();">
	<div class="mt-2">
		<div class="form-group row no-gutters mt-2">
			<label class="col-12 col-form-label-sm font-size-md">Optionally enter a payment allocation description to include on the statement.</label>
			<div class="col-12">
				<textarea name="paymentAllocationDesc" id="paymentAllocationDesc" class="form-control form-control-sm" rows="4"></textarea>
			</div>
		</div>
		<!--- hidden submit triggered from parent --->
		<button type="submit" class="d-none"></button>
	</div>
</form>
<div id="downloadingMessage" class="p-3 d-none">
	<h4><i class="fa-light fa-circle-notch fa-spin fa-lg"></i> We're preparing your Payment Allocation Statement for download.</h4>
	You may close this message when the download appears.
</div>
</cfoutput>

