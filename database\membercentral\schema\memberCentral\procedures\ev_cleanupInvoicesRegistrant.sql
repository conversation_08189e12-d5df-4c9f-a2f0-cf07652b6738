ALTER PROC dbo.ev_cleanupInvoicesRegistrant
@registrantID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpRegTrans') IS NOT NULL 
		DROP TABLE #tmpRegTrans;

	DECLARE @orgID int, @ApplicationTypeID int, @minTID int, @assignedToMemberID int, @invoiceProfileID int, @trashID int, 
		@invoiceID int, @GLAccountID int, @invoiceNumber varchar(19), @adjAmount decimal(18,2), @nowdate datetime;
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);
	DECLARE @tblOrigPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
	DECLARE @tblCurrentPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
	DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));
	DECLARE @tblRegistrants TABLE (registrantID int);
	DECLARE @tblInstances TABLE (instanceID int);
	DECLARE @tblSeats TABLE (seatID int);

	IF @AROption NOT IN ('A','B')
		GOTO on_done;

	select @nowdate = getdate();
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');

	select @orgID = m.orgID, @assignedToMemberID = m.activeMemberID
	from dbo.ev_registrants as r
	inner join dbo.ams_members as m on m.memberID = r.memberID
	inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID
	inner join dbo.ev_events as e on e.eventID = reg.eventID and reg.siteID = e.siteID
	cross apply dbo.fn_getContent(e.eventcontentID,1) as eventcontent
	where r.registrantID = @registrantID;

	-- get registrant and any linked subevent registrants
	INSERT INTO @tblRegistrants (registrantID) 
	select registrantID
	from dbo.ev_registrants
	where recordedOnSiteID = @recordedOnSiteID
	and [status] = 'D'
	and registrantID = @registrantID;

	INSERT INTO @tblRegistrants (registrantID) 
	select registrantID
	from dbo.ev_registrants
	where recordedOnSiteID = @recordedOnSiteID
	and [status] = 'D'
	and parentRegistrantID = @registrantID;

	-- get all reg transactions
	select rt.transactionID, rt.typeID
	into #tmpRegTrans
	from @tblRegistrants as r
	cross apply dbo.fn_ev_registrantTransactions(r.registrantID) as rt
	OPTION(RECOMPILE);

	-- put all open invoices used for registrants into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from #tmpRegTrans as rt
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = rt.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
	where invs.status = 'Open';

	-- payment allocations
	INSERT INTO @tblOrigPaymentTransactions (paymentTransactionID, allocatedAmountToReg)
	select rt.transactionID, sum(atop.allocAmount)
	from #tmpRegTrans as rt
	cross apply dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,rt.transactionID) as atop
	inner join #tmpRegTrans as tmpRev on tmpRev.transactionID = atop.transactionID
	where rt.typeID = 2
	group by rt.transactionID;

	-- ticket instances
	INSERT INTO @tblInstances (instanceID)
	select rpi.instanceID
	from @tblRegistrants as tmp
	inner join dbo.ev_registrantPackageInstances as rpi on rpi.registrantID = tmp.registrantID
	where rpi.status <> 'D';

	INSERT INTO @tblSeats (seatID)
	select s.seatID
	from @tblInstances as tblI
	inner join dbo.ev_registrantPackageInstanceSeats as s on s.instanceID = tblI.instanceID
	and s.status <> 'D';
	
	-- get all registrant-related sales transactions we need to adjust
	IF @AROption = 'A' 
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select rt.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
		from #tmpRegTrans as rt
		inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
		where tsFull.cache_amountAfterAdjustment > 0
		and rt.typeID = 1
		OPTION(RECOMPILE);
	IF @AROption = 'B' 
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select rt.transactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount, t.creditGLAccountID
		from #tmpRegTrans as rt
		inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
		where rt.typeID = 1
		and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
		OPTION(RECOMPILE);

	BEGIN TRAN;		
		IF @AROption IN ('A','B') BEGIN
			UPDATE dbo.tr_applications
			SET [status] = 'D'
			WHERE itemID in (select registrantID from @tblRegistrants)
			AND itemType = 'Rate'
			AND applicationTypeID = @ApplicationTypeID
			AND [status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.dataID = tr.itemID and fd.itemType = 'EventRegCustom'
			INNER JOIN @tblRegistrants as tmp on tmp.registrantID = fd.itemID 
			WHERE tr.itemType = 'Custom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.dataID = tr.itemID and fd.itemType = 'ticketPackInstCustom'
			INNER JOIN @tblInstances as tmp on tmp.instanceID = fd.itemID 
			WHERE tr.itemType = 'ticketPackInstCustom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.itemID = tr.itemID and fd.itemType = 'ticketPackSeatCustom'
			INNER JOIN @tblSeats as tmp on tmp.seatID = fd.itemID 
			WHERE tr.itemType = 'ticketPackInstCustom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';
		END

		-- if there are adjustments to make
		IF EXISTS (select transactionID from @tblAdjust) BEGIN
			SELECT @minTID = min(transactionID) from @tblAdjust;
			WHILE @minTID IS NOT NULL BEGIN
				select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

				select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where orgID = @orgID and glAccountID = @GLAccountID;
				select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
				-- if necessary, create invoice assigned to registrant based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
						@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);
				END	

				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdate,
					@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, @byPassAccrual=0,
					@xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
				SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
			END
		END
	COMMIT TRAN;

	on_done:

	IF OBJECT_ID('tempdb..#tmpRegTrans') IS NOT NULL 
		DROP TABLE #tmpRegTrans;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
