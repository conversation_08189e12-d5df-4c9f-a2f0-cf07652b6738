USE membercentral;
GO

CREATE PROC dbo.tr_getPaymentAllocationStatementData
@orgID int,
@paymentTransactionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- Get payment transaction details
	SELECT 
		t.transactionID,
		t.amount as originalAmount,
		t.detail as paymentDetail,
		t.transactionDate as paymentDate,
		t.dateRecorded,
		t.statusID,
		tp.profileID,
		tp.cache_refundableAmountOfPayment as refundableAmount,
		tp.cache_allocatedAmountOfPayment as allocatedAmount,
		tp.cache_refundableAmountOfPayment - tp.cache_allocatedAmountOfPayment as unallocatedAmount,
		-- Member details
		m2.memberID,
		m2.memberNumber,
		m2.prefix,
		m2.firstName,
		m2.middleName,
		m2.lastName,
		m2.suffix,
		m2.company,
		-- Address details (billing address)
		ma.address1,
		ma.address2,
		ma.address3,
		ma.city,
		ma.attn,
		s.Name,
		ma.postalCode,
		c.countryID,
		c.country
	FROM dbo.tr_transactions t
	INNER JOIN dbo.tr_transactionPayments tp ON tp.transactionID = t.transactionID
	INNER JOIN dbo.ams_members m ON m.orgID = @orgID AND m.memberID = t.assignedToMemberID
	INNER JOIN dbo.ams_members m2 ON m2.orgID = @orgID AND m2.memberID = m.activeMemberID
	LEFT JOIN dbo.ams_memberAddresses ma ON ma.orgID = @orgID AND ma.memberID = m2.memberID
	LEFT JOIN dbo.ams_states s ON s.stateID = ma.stateID
	LEFT JOIN dbo.ams_countries c ON c.countryID = s.countryID
	WHERE t.ownedByOrgID = @orgID 
	AND t.transactionID = @paymentTransactionID;

	-- Get void/refund/writeoff history for this payment
	SELECT 
		t.transactionID,
		t.amount,
		t.detail,
		t.transactionDate,
		tt.type as transactionType,
		CASE 
			WHEN tt.type = 'VoidOffset' THEN 'Void'
			WHEN tt.type = 'Refund' THEN 'Refund'
			WHEN tt.type = 'Write Off' AND t.amount < 0 THEN 'Write Off'
			ELSE tt.type
		END as displayType
	FROM dbo.tr_transactions t
	INNER JOIN dbo.tr_types tt ON tt.typeID = t.typeID
	INNER JOIN dbo.tr_relationships r ON r.orgID = @orgID 
		AND r.appliedToTransactionID = @paymentTransactionID
		AND r.transactionID = t.transactionID
	WHERE t.ownedByOrgID = @orgID
	AND t.statusID IN (1,3)
	AND tt.type IN ('VoidOffset', 'Refund', 'Write Off', 'NSF')
	ORDER BY t.transactionDate, t.transactionID;

	-- Get current allocations rolled up by member/sale (excluding adjustments)
	-- Only include direct sales, not adjustments or other transaction types
	SELECT 
		saleT.transactionID as saleTransactionID,
		SUM(alloc.allocatedAmount) as totalAllocatedToSale,
		saleT.amount as saleAmount,
		saleT.detail as saleDetail,
		saleT.transactionDate as saleDate,
		-- Member details for the sale
		saleM2.memberID,
		saleM2.memberNumber,
		saleM2.firstName + ISNULL(' ' + NULLIF(saleM2.middleName,''),'') + ' ' + saleM2.lastName + ISNULL(' ' + NULLIF(saleM2.suffix,''),'') as memberName
	FROM (
		-- Get allocations from this payment to sales (excluding adjustments)
		SELECT 
			alloc.transactionID_rev as saleTransactionID,
			SUM(CASE WHEN glAllocDeb.GLCode = 'ACCOUNTSRECEIVABLE' THEN alloc.amount_alloc*-1 ELSE alloc.amount_alloc END) AS allocatedAmount
		FROM dbo.cache_tr_allocations alloc
		INNER JOIN dbo.tr_transactions allocT ON allocT.ownedByOrgID = @orgID 
			AND allocT.transactionID = alloc.transactionID_alloc 
			AND allocT.statusID = 1
		INNER JOIN dbo.tr_transactions payT ON payT.ownedByOrgID = @orgID 
			AND payT.transactionID = alloc.transactionID_cash 
			AND payT.statusID IN (1,3)
			AND payT.transactionID = @paymentTransactionID
		INNER JOIN dbo.tr_GLAccounts glAllocDeb ON glAllocDeb.orgID = @orgID 
			AND glAllocDeb.GLAccountID = allocT.debitGLAccountID
		-- Only include sales, exclude adjustments and other types
		INNER JOIN dbo.tr_transactions saleT ON saleT.ownedByOrgID = @orgID 
			AND saleT.transactionID = alloc.transactionID_rev
			AND saleT.typeID = dbo.fn_tr_getTypeID('Sale')
		WHERE alloc.orgID = @orgID
		GROUP BY alloc.transactionID_rev
	) alloc
	INNER JOIN dbo.tr_transactions saleT ON saleT.ownedByOrgID = @orgID 
		AND saleT.transactionID = alloc.saleTransactionID
	INNER JOIN dbo.ams_members saleM ON saleM.orgID = @orgID 
		AND saleM.memberID = saleT.assignedToMemberID
	INNER JOIN dbo.ams_members saleM2 ON saleM2.orgID = @orgID 
		AND saleM2.memberID = saleM.activeMemberID
	GROUP BY 
		saleT.transactionID,
		saleT.amount,
		saleT.detail,
		saleT.transactionDate,
		saleM2.memberID,
		saleM2.memberNumber,
		saleM2.firstName,
		saleM2.middleName,
		saleM2.lastName,
		saleM2.suffix,
		saleM2.company
	ORDER BY 
		saleT.transactionDate,
		SUM(alloc.allocatedAmount) DESC,
		saleM2.lastName,
		saleM2.firstName,
		saleM2.memberNumber;

	RETURN 0;

END TRY
BEGIN CATCH
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
