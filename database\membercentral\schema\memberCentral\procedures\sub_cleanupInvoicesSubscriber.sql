ALTER PROC dbo.sub_cleanupInvoicesSubscriber
@subscriberID int,
@memberID int,
@siteID int,
@enteredByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @nowdate datetime, @minSubID int, @invoiceID int, @invoiceNumber varchar(19), @minTID int, 
		@adjAmount decimal(18,2), @invoiceIDList varchar(max), @invoiceProfileID int, @trashID int, @orgID int;
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust decimal(18,2));
	DECLARE @tblSubs TABLE (subscriberID int, thePathExpanded varchar(max));

	IF @AROption NOT IN ('A','B')
		GOTO on_done;
	
	select @nowdate = getdate();
	select @orgID = orgID from dbo.sites where siteID = @siteID;

	INSERT INTO @tblSubs (subscriberID, thePathExpanded)
	select subscriberID, thePathExpanded
	from dbo.fn_getRecursiveMemberSubscriptions(@memberID,@siteID,@subscriberID);

	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;
	CREATE TABLE #mcSubscribersForAcct (subscriberID int PRIMARY KEY);
	CREATE TABLE #mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
		invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
		amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
		assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int,
		creditGLAccountID int, INDEX IDX_mcSubscriberTransactions_transactionID_mainTransactionID (transactionID, mainTransactionID));

	INSERT INTO #mcSubscribersForAcct (subscriberID)
	SELECT distinct subscriberID
	FROM @tblSubs;

	-- populate mcSubscriberTransactions
	EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;

	IF @AROption = 'A' 
		INSERT INTO @tblAdjust (transactionID, amountToAdjust)
		select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment
		from #mcSubscriberTransactions as rt
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,rt.transactionID) as tsFull
		where rt.typeID = 1
		and tsFull.cache_amountAfterAdjustment > 0;

	IF @AROption = 'B'
		INSERT INTO @tblAdjust (transactionID, amountToAdjust)
		select rt.mainTransactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount
		from #mcSubscriberTransactions as rt
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,rt.transactionID) as tsFull
		where rt.typeID = 1
		and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0;

	IF OBJECT_ID('tempdb..#mcSubscribersForAcct') IS NOT NULL
		DROP TABLE #mcSubscribersForAcct;
	IF OBJECT_ID('tempdb..#mcSubscriberTransactions') IS NOT NULL
		DROP TABLE #mcSubscriberTransactions;

	-- if there are adjustments to make
	IF EXISTS (select transactionID from @tblAdjust) BEGIN
		BEGIN TRAN;
			-- if any transactions are on a open or pending invoice, grab it for all adjustments. 
			-- otherwise, we need to create one to hold these adjustments
			select top 1 @invoiceID = i.invoiceID 
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
				where i.orgID = @orgID
				and i.statusID in (1,2)
				order by i.invoiceID;
			IF @invoiceID is null BEGIN
				select top 1 @invoiceProfileID=i.invoiceProfileID
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
					inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
					where i.orgID = @orgID;
				EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
					@assignedToMemberID=@memberID, @dateBilled=@nowdate, @dateDue=@nowdate, 
					@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
				
				set @invoiceIDList = cast(@invoiceID as varchar(10));
			END
			ELSE 
				select @invoiceIDList = dbo.sortedIntList(i.invoiceID)
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = i.invoiceID
				inner join @tblAdjust as tbl on tbl.transactionID = it.transactionID
				where i.orgID = @orgID
				and i.statusID in (1,2);

			-- record adjustments
			SELECT @minTID = min(transactionID) from @tblAdjust;
			WHILE @minTID IS NOT NULL BEGIN
				SELECT @adjAmount = amountToAdjust*-1 from @tblAdjust where transactionID = @minTID;
				
				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@siteID, @recordedByMemberID=@enteredByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdate,
					@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, 
					@byPassAccrual=0, @xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
				SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
			END

			IF @invoiceIDList IS NOT NULL
				EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceIDList;	
		COMMIT TRAN;
	END

	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
