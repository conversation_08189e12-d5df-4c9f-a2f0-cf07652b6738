<cfoutput>
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
		   <body>
				<!-- wrapper start -->
				<div id="wrapper" class="wrapper">
					<!--Header Start-->
					<cfinclude template="header.cfm">
					<!--Header End--> 
					<!-- slider start -->
					<div class="slider sliderWrap">
						<div class="owl-carousel owl-theme sliderBanner">
							
						</div>
					</div> 
					<!-- slider End -->
			   
					<div class="pd_70 quick-links-sec quickLinksHomeWrap hideNone">
						<div class="container">
						   <div class="row">
							  <div class="span12">
								 <div class="sbm-icon-card-wrap">
									<ul class="quickLinksHome">
										
									</ul>
								 </div>
							  </div>
						   </div>
						</div>
					 </div>
			   
					<div class="about-sec magazineHomeWrap  hideNone">
						<div class="inner-sec-wrap">
							<div class="container">
								<div class="about-tflf-box">
									<div class="left-box">
										<div class="border-card-box zoneEmainCnt">
											
										</div>
									</div>
										
									<div class="right-box">
										<div class="img-slider">
											
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					
					<div class="latest-blogs-sec bg-dotted pdb_70 blogHomeWrap hideNone">
						<div class="container">
							<div class="flex-row blogHomeWrapCnt">
								<div class="span12">
									<h2 class="SectionHeader text-center"></h2>
								</div>
								
								
							</div>
						</div>
					</div>
					
					<div class="sponsors-sec pd_50 CLPHomeWrap hideNone">
						<div class="container">
							<div class="row flex-row">
							<div class="span12">
								<h2 class="SectionHeader text-center"></h2>
							</div>
							<div class="span12">
								<div class="sponsorSlider">
									<ul>
										
									</ul>
								</div>
							</div>
							</div>
						</div>
					</div>

					<div class="stay-conected-sec zoneHWrap hideNone">
						<div class="container">
							<div class="row zoneHWrapCnt">
								<div class="span12">
									<h2 class="SectionHeader"></h2>
									<p></p>
								</div>	
							</div>
						</div>
					</div>

					<div class="contact-sec pd_50 zoneIWrap hideNone">
						<div class="container">
							<div class="row row-flex">
								<div class="span12">
									<h2 class="SectionHeader text-center"></h2>
								</div>
								<div class="span12">
									<iframe src="/?pg=homePageContact&mode=direct" id="contactIframe" frameborder="0" onload="resizeContactIframe()" height="0px" width="100%" scrolling="no"></iframe>
								</div>
							</div>
						</div>
					</div>
				  
					<!--Footer Start-->
					<cfinclude template="footer.cfm">				
					<!--Footer End-->                
					<cfinclude template="toolBar.cfm">
				</div>
				<!-- wrapper end -->	
				
	
				<!--slide-->
				<div id="zoneMainObj" class="hide">
					<cfif application.objCMS.getZoneItemCount(zone='Main',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Main'], 1) AND event.getValue("mc_pageDefinition").pageZones['Main'][1].view EQ "echo">
						<cfset local.carouselContent = event.getValue("mc_pageDefinition").pageZones['Main']>
						<cfloop from="1" to="#arrayLen(local.carouselContent)#" index="local.thisItem" >
							<cfif lcase(trim(local.carouselContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.carouselContent[local.thisItem].data))>
								#trim(REReplace(REReplace(local.carouselContent[local.thisItem].data,"<p>","","All"),"</p>","","All"))#			
							</cfif>
						</cfloop>
					</cfif> 
				</div>
				<cfset local.zone = "D"><!--Quick Links -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneDObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
	
				<cfset local.zone = "E"><!--Magazine -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneEObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
	
				<cfset local.zone = "F">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneFObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
	
				<cfset local.zone = "G">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneGObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
	
				<cfset local.zone = "H">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneHObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
	
				<cfset local.zone = "I">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneIObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

				<cfset local.zone = "J"><!--SMCBA Diversity -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneJObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
			</body>
		<cfelse>
			<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
				#application.objCMS.renderZone(zone='Main',event=event)#  
			</body>		
		</cfif>
		<cfinclude template="foot.cfm">   
	</html>
	</cfoutput>