<cfset local.filterTypeID = val(arguments.event.getValue('filterTypeID',0))>
<cfset local.availableSubscriptionsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAvailableSubscriptionsList&mid=#arguments.event.getValue('mid')#&mode=stream">
<cfset local.renewalSubscriptionListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscriptionsList&mode=stream&isRenew=1&mid=#arguments.event.getValue('mid', '0')#">
<cfset local.subConfirmRenewLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='SubscriptionAdmin',mca_ta='confirmGenerateRenewals') & "&mode=direct">

<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script language="javascript">
		var #ToScript(local.filterTypeID,'ftid')#		
		let availableSubscriptionsTable,memberSubTable, selSubID = 0;
		function initAvailableSubscriptionsTable(){
			availableSubscriptionsTable = $('##availableSubscriptionsTable').DataTable({
				"processing": true,
				"serverSide": false,
				"paging": false,
				"scrollY": "400px",
				"scrollCollapse": true,
				"info": false,
				"language": {
					"emptyTable": "There are no subscriptions available at this time."
				},
				"ajax": { 
					"url": "#local.availableSubscriptionsListLink#"
				},
				"autoWidth": false,
				"columns": [
					{
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display')	{
								let thisRowID = data['DT_RowId'];
								let cellPadding = data.level > 1 ? 24 * (data.level-1) : 0;
								renderData += '<div style="padding-left:'+cellPadding+'px;">';
								if(data.rowType == 'subListType'){
									if (data.hasChildren == 0){
										renderData += '<span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+ data.displayName+'</span>';
									}else{
										renderData += '<a href="javascript:toggleParentDisplay(\''+thisRowID+'\');" id="subListType_'+thisRowID+'"><i class="fas fa-folder-plus fa-fw rowToggleBtn pr-2"></i> '+ data.displayName +'</a>';
									}
								}else{
									renderData += '<span><a id="subadd_'+data.subscriptionID+'" href="javascript:void(0);" onclick="doBtnContinueSelSub('+data.subscriptionID+')" title="Add subscription" class="btn btn-xs text-primary p-0"><i class="fa-regular fa-circle-plus fa-md"></i></a></span><span><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i> '+ data.displayName+'</span>';
								}
								renderData += '</div>';
							}
							return type === 'display' ? renderData : data;
						},
						"orderable":false
					}

				],
				"searching": false,
				"ordering": false
			});
		}
		function toggleParentDisplay(rowID) {
			let rowToggleBtn = $('##availableSubscriptionsTable ##subListType_'+rowID+' i.rowToggleBtn');
			rowToggleBtn.toggleClass('fa-folder-plus fa-folder-minus');
			let isExpanded = rowToggleBtn.hasClass('fa-folder-minus');
			$('##availableSubscriptionsTable tr.child-of-'+rowID).toggleClass('d-none',!isExpanded)
		}
		
		function renderSubscriptionName ( data, type, row, meta ) {
			let renderData = '';
			if (type === 'display')	{
				let arrSubPathExpanded = data.thePath.split('.');
				let thePathNoPeriods = data.thePath.replace(/\./gm,'');
				let grpPadding = arrSubPathExpanded.length > 2 ? 10 * (arrSubPathExpanded.length-1) : 0;
				renderData += '<div style="padding-left:'+grpPadding+'px;">';
				if (data.hasChildSubs) {
					renderData += '<div><a data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'" href="javascript:toggleParentSubDisplay(`'+thePathNoPeriods+'`);" id="grpLabel_'+thePathNoPeriods+'"><i class="fas '+(data.expandSub ? 'fa-folder-minus' : 'fa-folder-plus')+' fa-fw subToggleBtn pr-2"></i> '+data.subscriptionName+'</a> <small data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'">('+data.rateName+')</small></div>';
				} else {
					renderData += '<div><ul class="pl-1 m-0"><li style="list-style:none;"><span data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'">'+data.subscriptionName+' <small>('+data.rateName+')</small></span></li></ul></div>';
				}
				renderData += '</div>';
			}
			return type === 'display' ? renderData : data;
		}

		function renderSubscriptionActions( data, type, row, meta){
			let renderData = '';
			if (type === 'display')	{	
				renderData += '<div class="actionTool">';						
				if ('ROPAIE'.split('').includes(data.status)) {
					if(data.subCanRenew == 1 && data.parentSubscriberIDLen == 0){
						renderData += '<button type="button" class="btn btn-sm btn-success" onclick="renewMemberSub('+data.subscriberID+',#arguments.event.getValue('mid')#);return false;" data-toggle="tooltip" title="Renew Subscription">Renew Subscription</button>';
					}else{
						renderData += '<a href="##" class="btn btn-xs  p-1 mx-1 mb-1 text-muted disabled"><i class="fa-solid fa-rotate-right"></i></a>';
					}
				}

				renderData += '</div>';
			}
			return type === 'display' ? renderData : data;

		}
		function toggleParentSubDisplay(sid) {
			let subToggleBtn = $('##grpLabel_'+sid+' i.subToggleBtn');		
			subToggleBtn.toggleClass('fa-folder-plus fa-folder-minus');

			if (subToggleBtn.first().hasClass('fa-folder-minus')) {
				showChildSubs(sid);
			} else {
				hideChildSubs(sid);
			}
		}
		function showChildSubs(sid) {
			$('tr.childSubOf'+sid).removeClass('d-none');
			$('tr.childSubOf'+sid).each(function(i,grpRow) {
				if ($(this).find('i.subToggleBtn').hasClass('fa-folder-minus')) showChildSubs($(this).attr('data-thePathNoPeriods'));
			});
		}
		function hideChildSubs(sid) {
			$('tr.childSubOf'+sid).addClass('d-none');
			$('tr.childSubOf'+sid).each(function(i,grpRow) {
				if ($(this).find('i.subToggleBtn').hasClass('fa-folder-minus')) hideChildSubs($(this).attr('data-thePathNoPeriods'));
			});
		}
		function initMemberSubscriptions(){
			memberSubTable = $('##memberSubTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"language": {
					"emptyTable": "There are no subscriptions eligible for renewal at this time."
				},
				"ajax": { 
					"url": '#local.renewalSubscriptionListLink#',
					"type": "post",
					"data": function(d) {
						$.each($('##frmFilter').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": renderSubscriptionName,
						"className": "align-top"
					},
					{ "data": "subStartDate", "width": "1", "className": "align-top text-nowrap px-2" },
					{ "data": "subEndDate", "width": "1", "className": "align-top text-nowrap px-2" },
					{ "data": "billedAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
					{ "data": "dueAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
					{ "data": "statusName", "width": "1px", "className": "align-top text-nowrap px-2" },
					{ "data": null,
						"render": renderSubscriptionActions,
						"width": "300px",
						"className": "align-top"
					}
				],
				"searching": false,
				"ordering": false,				
				"createdRow": function (row, data, index) {
					let thePathNoPeriods = data.thePath.replace(/\./gm,'');
					$(row).attr('data-subscriberID',data.subscriberID); /* adding data-subscriberID here instead of DT_RowData due to the row data being lost while moving up/down grp */
					$(row).attr('data-thePathNoPeriods',thePathNoPeriods);
					if(data.status == 'D'){
						$(row).addClass('table-warning');
					}else if(data.paymentStatus == 'N' && (data.status == 'A' || data.status == 'P')){
						$(row).addClass('row-nonactivated');
					}
					$(row).addClass('rowPointer');
				}
			});
		}
		function renewMemberSub(sid,mid) {
			top.MCModalUtils.setTitle('Confirm Renewal Generation');
			top.$('##MCModal').find('.modal-dialog').removeClass('modal-xl').addClass('modal-lg');
					top.MCModalUtils.buildFooter({
						classlist: 'd-flex',
						showclose: false,
						buttons: [ 
							{ class: "btn-success py-1", clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSubmit").click', label: 'Generate Renewals', name: 'btnGenerateRenewals', id: 'btnGenerateRenewals' },
						]
					});
			self.location.href = '#local.subConfirmRenewLink#&sid='+sid+'&mid='+mid+'&chkAll=0&fSubscribers='+ sid +'&fNotSubscribers=""';
		}
		
		function doBtnContinueSelSub(sid) {
			$('##subadd_'+sid).attr('disabled', true).css('pointer-events', 'none').addClass('disabled').html('<i class="fa-solid fa-spinner fa-spin"></i>');
			var doBtnContinueSelSubResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') { 
					if (r.subscribed == true) {
						alert('This member already has that subscription.\r\nIf you wish to edit or remove the subscription, use the actions on the member record.');
					} else {
						top.MCModalUtils.showLoading('Add Subscription');
						<cfif arguments.event.getValue('subsV2',0) EQ 1>
							self.location.href = '#arguments.event.getValue('mainsuburl')#&subAction=showManageSubs&subid=' + sid;
						<cfelse>
							self.location.href = '#arguments.event.getValue('mainsuburl')#&subAction=chooseSub&puid=#local.puid#&subid=' + sid;
						</cfif>
					}
				} else {
					$('##subadd_'+sid).attr('disabled', false).css('pointer-events', 'unset').removeClass('disabled').html('<i class="fa-regular fa-circle-plus fa-md"></i>');
					alert('Unable to add subscription.');
				}
			};
			var objParams = { subID:sid, memberID:#arguments.event.getValue('mid')# };
			TS_AJX('ADMSUBS','checkMemberSub',objParams,doBtnContinueSelSubResult,doBtnContinueSelSubResult,20000,doBtnContinueSelSubResult);
			
			return false;
		}
		
		$(function() {
			initAvailableSubscriptionsTable();	
			initMemberSubscriptions();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.gridJS#">

<cfoutput>
<cfif arguments.event.getValue('subsV2',0) EQ 1>
	<div class="card card-box mb-2">
		<div class="card-body py-2">
			<div>
				<div class="mt-1 font-weight-bold">#local.strMember.mc_combinedName# (#local.strMember.memberNumber#)</div>
				<cfif len(local.strMember.company)><div class="text-dim"><small>#local.strMember.company#</small></div></cfif>
				<div class="mt-1 p-1">
					<cfif len(local.strMember.mc_combinedAddresses)>#local.strMember.mc_combinedAddresses#</cfif>
					<cfif len(local.strMember.mc_extraInfo)>#local.strMember.mc_extraInfo#</cfif>
					<cfif len(local.strMember.mc_recordType)><div>#local.strMember.mc_recordType#</div></cfif>
					<cfif len(local.strMember.mc_memberType)><div>#local.strMember.mc_memberType#</div></cfif>
					<cfif len(local.strMember.mc_lastlogin)><div>#local.strMember.mc_lastlogin#</div></cfif>
				</div>
			</div>
		</div>
	</div>
</cfif>
<div class="row no-gutters mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title">
					<b>Choose Subscription</b>
					<div class="text-dark">Choose an available subscription from the information presented below.</div>
				</div>
			</div>
			<div class="card-body pb-3">
				<div>
					<table id="availableSubscriptionsTable" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Subscription Type/ Subscription</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="row no-gutters mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title">
					<b>Subscriptions Eligible for Renewal</b>
					<div class="text-dark">If you are unable to add a subscription above, it may be because the subscription is already on the member's record and needs to be renewed rather than added.</div>
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="my-2">
					<table id="memberSubTable" class="table table-sm table-bordered table-hover" style="width:100%;">
						<thead>
							<tr>
								<th>Subscription Name</th>
								<th>Start</th>
								<th>End</th>
								<th>Billed</th>
								<th>Due</th>
								<th>Status</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>
</cfoutput>