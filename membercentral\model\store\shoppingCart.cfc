<cfcomponent extends="model.store.store" output="no">
	
	<cffunction name="paramOrderNumber" access="package" output="FALSE" returntype="void" hint="Create Unique Order Number">
		<cfargument name="appInstanceID" required="yes" type="numeric">
		<cfargument name="orderNumber" required="no" type="string" default="#createUUID()#">

		<cfscript>
			var local = {}
			local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});

			if (not local.store.keyExists(arguments.appInstanceID)) {
				local.tmpStr = { orderNumber=arguments.orderNumber, shippingOption='', currentStep=1};
				StructAppend(local.tmpStr,initCartVars());
			
				local.formData = {
					"#arguments.appInstanceID#":local.tmpStr
				}		
				application.mcCacheManager.sessionSetValue(keyname='store', value=local.formData);
			}
	
		</cfscript>
	</cffunction>

	<cffunction name="initCartVars" access="package" output="false" returntype="struct" hint="set up default Cart Variables">
		<cfscript>
		var tmpStr = { 	CrtItemID = "",
						CrtProductID = "",
						CrtProductName = "",
						CrtQuantity = "",
						CrtPrice = "",
						CrtRate = "",
						CrtFormat = "",
						CrtShipping = {},
						CrtThumbNails = "",
						CrtMemberID = "",
						CrtCoupon = {}
					};

		return tmpStr;
		</cfscript>
	</cffunction>

	<cffunction name="getCartMerchantProfiles" access="private" output="FALSE" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="qryItems" type="query" required="true">

		<cfset var local = structNew()>

		<cfquery name="local.qryRateGLs" dbtype="query">
			SELECT DISTINCT rateGLAccountID
			FROM arguments.qryItems
		</cfquery>
		<cfquery name="local.qryShippingGLs" dbtype="query">
			SELECT DISTINCT shippingGLAccountID
			FROM arguments.qryItems
			WHERE shipmentAmt > 0
		</cfquery>
		<cfset local.glIDs = valueList(local.qryRateGLs.rateGLAccountID)>
		<cfset local.glIDs = local.qryShippingGLs.recordCount ? listAppend(local.glIDs,valueList(local.qryShippingGLs.shippingGLAccountID)) : local.glIDs>
		<cfset local.glIDs = listRemoveDuplicates(local.glIDs)>

		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="local.qryProfiles">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@procFeeSupportedGatewayIDs varchar(10) = '10';	-- AuthorizeCCCIM
			DECLARE @tmpInvoiceProfiles TABLE (invoiceProfileID int PRIMARY KEY);
			DECLARE @tmpInvoiceProfileProcFeeOverrides TABLE (invoiceProfileID int, gatewayID int, enableProcessingFeeDonation bit, 
				processFeeDonationDefaultSelect bit, processFeeDonationFETitle varchar(100), processFeeDonationFEMsg varchar(800));

			<cfif listLen(local.glIDs)>
				INSERT INTO @tmpInvoiceProfiles (invoiceProfileID)
				SELECT DISTINCT gl.invoiceProfileID
				FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.glIDs#">,',') AS tmp
				INNER JOIN dbo.tr_glAccounts AS gl ON gl.orgID = @orgID
					AND gl.GLAccountID = tmp.listitem;

				-- invoice profile processing fee override settings
				IF @@ROWCOUNT = 1
					INSERT INTO @tmpInvoiceProfileProcFeeOverrides (invoiceProfileID, gatewayID, enableProcessingFeeDonation, processFeeDonationDefaultSelect, processFeeDonationFETitle, processFeeDonationFEMsg)
					SELECT ip.profileID, psg.listitem, ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, pfm.title, pfm.message
					FROM dbo.tr_invoiceProfiles AS ip 
					INNER JOIN @tmpInvoiceProfiles AS tmp ON tmp.invoiceProfileID = ip.profileID
					LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = ip.solicitationMessageID
					CROSS APPLY dbo.fn_intListToTableInline(@procFeeSupportedGatewayIDs,',') AS psg
					WHERE ip.orgID = @orgID;
			</cfif>

			SELECT mp.profileID, mp.profileCode, g.gatewayID, g.gatewayType, g.gatewayClass, s.emailRecipient, mp.tabTitle,
				CASE WHEN mp.enableProcessingFeeDonation = 1 AND ISNULL(tmp.enableProcessingFeeDonation,1) = 1 THEN 1 ELSE 0 END AS enableProcessingFeeDonation,
				mp.processFeeDonationFeePercent, ISNULL(tmp.processFeeDonationDefaultSelect,mp.processFeeDonationDefaultSelect) as processFeeDonationDefaultSelect,
				mp.processFeeDonationRenevueGLAccountID, mp.processFeeDonationRevTransDesc, mp.processFeeOtherPaymentsFELabel, mp.processFeeOtherPaymentsFEDenyLabel,
				ISNULL(tmp.processFeeDonationFETitle,pfm.title) as processFeeDonationFETitle, 
				ISNULL(tmp.processFeeDonationFEMsg,pfm.message) as processFeeDonationFEMsg, mp.processingFeeLabel,
				mp.enableApplePay, mp.enableGooglePay, mp.enableSurcharge, mp.surchargePercent, mp.surchargeRevenueGLAccountID
			FROM dbo.store_merchantProfiles as smp
			INNER JOIN dbo.mp_profiles as mp on mp.profileID = smp.merchantProfileID
			INNER JOIN dbo.mp_gateways as g on g.gatewayID = mp.gatewayID
			INNER JOIN dbo.store as s on s.storeID = smp.storeID
			LEFT OUTER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.messageID = mp.solicitationMessageID
			LEFT OUTER JOIN @tmpInvoiceProfileProcFeeOverrides AS tmp ON tmp.gatewayID = g.gatewayID
			WHERE smp.storeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">
			AND mp.status = 'A'
			AND g.isActive = 1
			AND g.gatewayID not in (2,13,14)
			AND mp.allowPayments = 1
			ORDER BY mp.frontEndOrderBy;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryProfiles>
	</cffunction>

	<cffunction name="getCartData" access="public" output="FALSE" returntype="query" hint="get Cart Contents">
		<cfargument name="storeid" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">
		<cfargument name="appInstanceID" type="numeric" required="true">
		<cfargument name="shippingid" type="numeric" required="false">

		<cfset var local = structNew()>
		<cfset local.shippingid = 0>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfset local.thisStoreCart = local.store[arguments.appInstanceID]>
		
		<!--- Get "default" shipping Option --->
		<cfif not len(local.store[arguments.appInstanceID].shippingoption)>
			<cfquery name="local.qryShippingMethods" datasource="#application.dsn.memberCentral.dsn#">
				select shippingID
				from dbo.store_ShippingMethods as sm
				where sm.storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer">  
				and sm.Visible = 1
			</cfquery>	
			<cfif local.qryShippingMethods.recordCount>
				<cfset local.shippingid = local.qryShippingMethods.shippingID>
			</cfif>
		<cfelse>
			<cfif isDefined("arguments.shippingid")>
				<cfset local.shippingid = arguments.shippingid>
			<cfelse>
				<cfset local.shippingid = replaceNoCase(local.store[arguments.appInstanceID].shippingoption,"PID", "")>
			</cfif>
		</cfif>	

		<cfstoredproc procedure="store_getCartData" datasource="#application.dsn.memberCentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.storeID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.orderNumber#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.shippingid#" null="No">
			<cfprocresult name="local.qryCart" resultset="1">
		</cfstoredproc>

		<cfset local.hasDiscounts = false>
		<cfif structKeyExists(local.thisStoreCart,"crtCoupon") and not structIsEmpty(local.thisStoreCart.crtCoupon) and local.thisStoreCart.crtCoupon.qryDiscountItems.recordCount>
			<cfset local.hasDiscounts = true>
		</cfif>
		<cfloop query="local.qryCart">
			<cfset local.itemDiscountExcTax = 0>
			<cfif local.hasDiscounts>
				<cfquery name="local.qryDiscountTmp" dbtype="query">
					select itemDiscountExcTax
					from [local].thisStoreCart.CrtCoupon.qryDiscountItems
					where cartItemID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryCart.cartItemID#">
				</cfquery>
				<cfif local.qryDiscountTmp.recordCount>
					<cfset local.itemDiscountExcTax = local.qryDiscountTmp.itemDiscountExcTax>
				</cfif>
			</cfif>
			<cfset QuerySetCell(local.qryCart,"itemDiscountExcTax",local.itemDiscountExcTax,local.qryCart.currentrow)>
		</cfloop>

		<cfreturn local.qryCart>
	</cffunction>
		
	<cffunction name="getCartItem" access="public" output="FALSE" returntype="query">
		<cfargument name="appInstanceID" type="numeric" required="yes">
		<cfargument name="ProductItemID" type="numeric" required="yes">
		<cfargument name="formatID" type="numeric" required="yes">
		
		<cfset var qryCart = "">
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryCart">
			SELECT * 
			FROM dbo.store_cartItems 
			WHERE ProductItemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ProductItemID#">
			AND formatID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">
			AND OrderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#local.store[arguments.appInstanceID].orderNumber#">
		</cfquery>

		<cfreturn qryCart>
	</cffunction>
	
	<cffunction name="insertCartItem" access="public" output="FALSE" returntype="void" hint="insert Cart Item in DB">
		<cfargument name="storeID" type="numeric" required="yes">
		<cfargument name="orderNumber" type="string" required="yes">
		<cfargument name="ItemID" type="numeric" required="yes">
		<cfargument name="formatID" type="numeric" required="yes">
		<cfargument name="rateID" type="numeric" required="yes">
		<cfargument name="Quantity" type="numeric" required="yes">

		<cfset var qryInsert = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryInsert">
			INSERT INTO dbo.store_cartItems(storeID, orderNumber, memberID, statSessionID, ProductItemID, FormatID, quantity, dateEntered, rateid)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">,
				<cfif session.cfcUser.memberData.memberID gt 0>
					<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">,
				<cfelse>
					null,
				</cfif>
				<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.statsSessionID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.ItemID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.formatID#">,
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.Quantity#">,
				getdate(),
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.rateID#">
			)
		</cfquery>		
	</cffunction>
			
	<cffunction name="updateQuantity" access="public" output="FALSE" returntype="void" hint="insert Cart Item in DB">
		<cfargument name="cartItemID" type="numeric">
		<cfargument name="Quantity" type="numeric">

		<cfset var qryUpdate = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUpdate">
			UPDATE dbo.store_cartItems
			SET Quantity = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.Quantity#">
			WHERE cartItemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.cartItemID#">
		</cfquery>		
	</cffunction>
	
	<cffunction name="checkQuantity" access="public" output="FALSE" returntype="void" hint="insert Cart Item in DB">
		<cfargument name="orderNumber" type="string" required="True">

		<cfset var qryDelete = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDelete">
			DELETE dbo.store_cartItems
			WHERE orderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
			AND quantity = 0
		</cfquery>		
	</cffunction>
	
	<cffunction name="removeCartItem" access="public" output="FALSE" returntype="void">
		<cfargument name="cartItemID" type="numeric">

		<cfset var qryDelete = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryDelete">
			DELETE dbo.store_cartItems
			WHERE cartItemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.cartItemID#">
		</cfquery>		
	</cffunction>
	
	<cffunction name="updateMemberID" access="public" output="FALSE" returntype="void">
		<cfargument name="appInstanceID" type="numeric" required="True">
		<cfargument name="memberID" type="numeric" required="True">
		<cfargument name="orderNumber" type="string" required="True">

		<cfset var qryUpdate = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUpdate">
			UPDATE dbo.store_cartItems
			SET memberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.memberID#">
			WHERE orderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
		</cfquery>
	</cffunction>
	
	<cffunction name="totalCartItems" access="public" output="FALSE" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true" />
		<cfargument name="cart" type="query" required="true" />

		<cfset var local = structNew() />
		<cfset local.totalRate = 0 />
		<cfset local.totalShipping = 0 />
		<cfset local.shippingOptions = structNew() />

		<cfset local.rateIds = valuelist(arguments.cart.rateid) />
		<cfif len(local.rateIds) eq 0>
			<cfset local.rateIds = 0 />
		</cfif>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryShippingMethods">
			select *
			from dbo.store_ShippingMethods sm
			where sm.storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer" />  
			and sm.Visible = 1
		</cfquery>

		<cfloop query="local.qryShippingMethods">		
			<cfif not isDefined("local.shippingOptions.PID#local.qryShippingMethods.ShippingID#")>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"] = structNew()>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].name = "#local.qryShippingMethods.ShippingName#">
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].PerShipment = 0>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].totalItems = 0>
				<cfset local.shippingOptions["PID#local.qryShippingMethods.ShippingID#"].shippingGLAccountID = 0>
			</cfif>
		</cfloop>

		<cfloop query="arguments.cart">
			
			<cfif arguments.cart.rateOverride eq "">
				<cfset local.totalRate = local.totalRate + (val(arguments.cart.rate) * arguments.cart.quantity)>
			<cfelse>
				<cfset local.totalRate = local.totalRate + (val(arguments.cart.rateOverride) * arguments.cart.quantity)>
			</cfif>			
			
			<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryCurrShip">
				SET NOCOUNT ON;

				DECLARE @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">;

				select rateid, sm.ShippingID, sm.ShippingName, 
					isNull(sps.PerShipment,convert(decimal(18,2),0.00)) as PerShipment,
					isNull(sps.PerItem,convert(decimal(18,2),0.00)) as PerItem,
					gl.rateGLAccountID, gl.shippingGLAccountID					
				from dbo.store_ShippingMethods sm
				left outer join dbo.store_ProductShipping sps on sps.shippingID = sm.shippingID 
					and sps.rateid = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.cart.rateid#">
				CROSS APPLY dbo.fn_store_getRateGLAccountID(@storeID, rateid) as gl
				where sm.storeID = @storeID
				and sm.Visible = 1
				order by sm.ShippingID;
			</cfquery>		
			
			<cfloop query="local.qryCurrShip">
				<cfif local.qryCurrShip.PerShipment GT local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].PerShipment>
					<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].PerShipment = local.qryCurrShip.PerShipment>
					<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].shippingGLAccountID = local.qryCurrShip.shippingGLAccountID>
				</cfif>				
				<cfset local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].totalItems = local.shippingOptions["PID#local.qryCurrShip.ShippingID#"].totalItems + (local.qryCurrShip.PerItem * arguments.cart.quantity)>
			</cfloop>
			
		</cfloop>
		
		<cfreturn local>
	</cffunction>
	
	<cffunction name="createOrder" access="public" output="FALSE" returntype="numeric">
		<cfargument name="storeID" type="numeric" required="yes">
		<cfargument name="orderNumber" type="string" required="yes">
		<cfargument name="Event" type="any" required="yes">

		<cfset var qryInsert = "">
		
		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="qryInsert">
			SET NOCOUNT ON;

			INSERT INTO dbo.store_orders (storeID, orderNumber, DateOfOrder, totalProduct, totalShipping, shippingKey, totalTax)
			VALUES(
				<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeID#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">,
				getdate(),
				<cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.event.getValue('totalProduct',0),'0.00')#">,
				<cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.event.getValue('totalShipping',0),'0.00')#">,
				<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('shippingKey','PID0')#">,
				<cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.event.getValue('totalTax',0),'0.00')#">
			)

			SELECT SCOPE_IDENTITY() as orderID;
		</cfquery>

		<cfreturn qryInsert.orderID>
	</cffunction>	

	<cffunction name="updateOrder" access="public" output="FALSE" returntype="void">
		<cfargument name="storeID" type="numeric" required="yes">
		<cfargument name="orderNumber" type="string" required="yes">
		<cfargument name="shippingKey" type="string" required="yes">
		<cfargument name="appInstanceID" type="numeric" required="yes">

		<cfscript>
			var local = structNew();
			local.totals = structNew();
			local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});
			// check to see if order already exists.
			if (this.doesOrderExist(arguments.orderNumber))
			{
				local.cart = this.getCartData(storeID=arguments.storeID,orderNumber=arguments.orderNumber,appInstanceID=arguments.appInstanceID);
				if (local.cart.recordCount) {
					local.qryTotalItems = this.totalCartItems(arguments.storeID,local.cart);
					local.totals.totalRate = local.qryTotalItems.totalRate;
					local.totals.shippingTotal = (local.qryTotalItems.shippingOptions[arguments.shippingKey].PerShipment + local.qryTotalItems.shippingOptions[arguments.shippingKey].totalItems);
					local.totals.shippingKey = arguments.shippingKey;
					updateDiscountItems(qryCart=local.cart, appInstanceID=arguments.appInstanceID);
				} else {
					local.totals.totalRate = 0;
					local.totals.shippingTotal = 0;
					local.totals.shippingKey = 'PID0';
					paramOrderNumber(appInstanceID=arguments.appInstanceID, orderNumber=arguments.orderNumber); // reset store cart session
				}
		
				local.thisOrderID = this.getOrderID(arguments.orderNumber);

				local.totalTax = 0;
				if (hasShippingAddress(appInstanceID=arguments.appInstanceID)) {
					local.strTaxes = calculateCartTaxes(storeID=arguments.storeID, orderNumber=arguments.ordernumber, appInstanceID=arguments.appInstanceID, 
									stateidForTax=local.store[arguments.appInstanceID].CrtShipping.state, zipForTax=local.store[arguments.appInstanceID].CrtShipping.zip);
					local.strTaxes.stateIDForTax = local.store[arguments.appInstanceID].CrtShipping.state;
					local.strTaxes.zipForTax = local.store[arguments.appInstanceID].CrtShipping.zip;
					local.totalTax = local.strTaxes.totaltax;
					if (NOT structkeyExists(session,"strStoreTax")) session.strStoreTax = structNew();
					session.strStoreTax["#arguments.storeID#|#arguments.orderNumber#"] = local.strTaxes;
				}

				updateOrderTotals(orderID=local.thisOrderID, totalProduct=local.totals.totalRate, totalShipping=local.totals.shippingTotal,	
					shippingKey=local.totals.shippingKey, totaltax=local.totalTax);

				CreateObject("component","storeReg").prepStoreCartForCheckout();
			}
		</cfscript>
	</cffunction>	
	
	<cffunction name="updateOrderTotals" access="public" output="FALSE" returntype="void">
		<cfargument name="orderID" type="numeric" required="TRUE">
		<cfargument name="totalProduct" type="String" required="TRUE">
		<cfargument name="totalShipping" type="String" required="TRUE">
		<cfargument name="shippingKey" type="String" required="TRUE">
		<cfargument name="totalTax" type="String" required="TRUE">
		
		<cfset var qryUpdate = "">
		
		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="qryUpdate">
			UPDATE dbo.store_orders
			SET totalProduct = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.totalProduct,'0.00')#">,
				totalShipping = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.totalShipping,'0.00')#">,
				shippingKey = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.shippingKey#">,
				totalTax = <cfqueryparam cfsqltype="cf_sql_double" value="#NumberFormat(arguments.totalTax,'0.00')#">
			WHERE orderID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
			and orderCompleted = 0
		</cfquery>
	</cffunction>

	<cffunction name="updateOrderDiscounts" access="public" output="false" returntype="void">
		<cfargument name="orderID" type="numeric" required="true">
		<cfargument name="totalDiscount" type="numeric" required="true">
		<cfargument name="totalDiscountExcTax" type="numeric" required="true">
		
		<cfset var qryUpdate = "">
		
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryUpdate">
			UPDATE dbo.store_orders
			SET totalDiscount = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.totalDiscount,'0.00')#">,
				totalDiscountExcTax = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#NumberFormat(arguments.totalDiscountExcTax,'0.00')#">
			WHERE orderID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orderID#">
			AND orderCompleted = 0;
		</cfquery>
	</cffunction>	
		
	<cffunction name="getOrderID" access="public" output="false" returntype="numeric">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var qryOrder = "">

		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="qryOrder">
			SELECT orderID
			FROM dbo.store_orders
			WHERE orderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
		</cfquery>

		<cfreturn qryOrder.orderID>
	</cffunction>
	
	<cffunction name="getOrderTotals" access="public" output="false" returntype="query">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var qryOrder = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryOrder">
			SELECT s.applicationInstanceID, so.totalProduct, so.totalShipping, so.totalTax, so.totalDiscount, so.totalDiscountExcTax, 
				grandTotal = so.totalProduct + so.totalShipping + so.totalTax,
				actualTotal = (so.totalProduct + so.totalShipping + so.totalTax) - so.totalDiscount,
				so.shippingKey, ssm.ShippingName as shippingMethod, '' as redeemDetail
			FROM dbo.store_orders as so
			INNER JOIN dbo.store as s on s.storeID = so.storeID
			LEFT OUTER JOIN dbo.store_ShippingMethods as ssm on ('PID' + convert(varchar,ssm.shippingID)) = so.shippingKey
			WHERE so.orderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
		</cfquery>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif val(qryOrder.totalDiscount) gt 0 and structKeyExists(local.store,qryOrder.applicationInstanceID) and structKeyExists(local.store[qryOrder.applicationInstanceID],"crtCoupon") and NOT structIsEmpty(local.store[qryOrder.applicationInstanceID].crtCoupon)>
			<cfset qryOrder.setcell("redeemDetail",local.store[qryOrder.applicationInstanceID].crtCoupon.redeemDetail)>
		</cfif>

		<cfreturn qryOrder>
	</cffunction>
	
	<cffunction name="doesOrderExist" access="public" output="false" returntype="boolean">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.check = FALSE>
		
		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="local.qryCheck">
			SELECT orderID
			FROM dbo.store_orders
			WHERE orderNumber = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
			and orderCompleted = 0
		</cfquery>

		<cfif local.qryCheck.recordCount>
			<cfset local.check = TRUE>
		</cfif>

		<cfreturn local.check>
	</cffunction>
		
	<cffunction name="isOrderOKForCheckout" access="private" output="FALSE" returntype="boolean">
		<cfargument name="storeid" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">

		<cfset var local = structNew()>
		
		<cfquery Datasource="#application.dsn.memberCentral.dsn#" name="local.qryCheck">
			SET NOCOUNT ON;

			DECLARE @storeID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.storeid#">;

			SELECT o.OrderID
			FROM dbo.store_orders AS o
			INNER JOIN dbo.store_CartItems AS ci on ci.OrderNumber = o.OrderNumber AND ci.storeID = o.storeID
			INNER JOIN dbo.store_Products AS p ON p.storeID = @storeID and ci.ProductItemID = p.ItemID
			WHERE o.OrderNumber =  <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.orderNumber#">
			AND o.storeID = @storeID
			AND o.OrderCompleted = 0 
			AND p.showAvailable = 1;
		</cfquery>

		<cfreturn local.qryCheck.recordCount>
	</cffunction>
	
	<cffunction name="getAppInstanceIDFromOrderNumber" access="public" output="false" returntype="query">
		<cfargument name="orderNumber" type="string" required="yes">

		<cfset var qryAppInstanceID = "">
		
		<cfquery name="qryAppInstanceID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 o.orderID, ai.applicationInstanceID, ai.siteResourceID
			FROM dbo.store_Orders AS o 
			INNER JOIN dbo.store AS s ON o.storeID = s.storeID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = s.applicationInstanceID
			WHERE o.orderNumber = <cfqueryparam value="#arguments.orderNumber#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>
		
		<cfreturn qryAppInstanceID>
	</cffunction>

	<cffunction name="hasShippingAddress" access="private" output="false" returntype="boolean">
		<cfargument name="appInstanceID" type="numeric" required="yes">
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfreturn structKeyExists(local.store[arguments.appInstanceID],"CrtShipping") AND isStruct(local.store[arguments.appInstanceID].CrtShipping) AND NOT structIsEmpty(local.store[arguments.appInstanceID].CrtShipping)>
	</cffunction>
	
	<!--- ----------------- -------->
	<!--- Buy now functions -------->
	<!--- ----------------- -------->
	<cffunction name="buyNow_parseItem" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
		<cfargument name="strBuyNowReturn" type="struct" required="yes">

		<cfscript>
		var local = structNew();

		// custom template overrides
		arguments.strBuyNowReturn.notIdentifiedTemplate = "NotLoggedIn_store";
		arguments.strBuyNowReturn.notFoundTemplate = "ItemNotFound_store";
		
		// setup item parsing
		arguments.strBuyNowReturn.ItemType = arguments.strBuyNow.itemType;				// store
		arguments.strBuyNowReturn.ItemKey = arguments.strBuyNow.itemKey;				// store-#storeID#|#ordernumber#
		arguments.strBuyNowReturn.ItemID = listRest(arguments.strBuyNow.itemKey,"-");	// #storeID#|#ordernumber#
		arguments.strBuyNowReturn.ItemFolder = "store";
		arguments.strBuyNowReturn.thisCFC = this;

		// make sure we have both storeid and ordernumber
		if (listLen(arguments.strBuyNowReturn.ItemID,"|") is not 2) return arguments.strBuyNowReturn;
		
		// parse to get storeid and ordernumber
		arguments.strBuyNowReturn.storeID = GetToken(listRest(arguments.strBuyNow.itemKey,"-"),1,"|");
		arguments.strBuyNowReturn.ordernumber = GetToken(listRest(arguments.strBuyNow.itemKey,"-"),2,"|");

		// make sure ordernumber is valid and not already checked out 
		if (NOT isOrderOKForCheckout(storeid=arguments.strBuyNowReturn.storeID,ordernumber=arguments.strBuyNowReturn.ordernumber)) return arguments.strBuyNowReturn;
		arguments.strBuyNowReturn.appInstanceID = getAppInstanceIDFromOrderNumber(arguments.strBuyNowReturn.ordernumber);
		paramOrderNumber(appInstanceID=arguments.strBuyNowReturn.appInstanceID.applicationInstanceID, orderNumber=arguments.strBuyNowReturn.ordernumber);

		// make sure there are items in the cart
		arguments.strBuyNowReturn.qryItems = getCartData(storeid=arguments.strBuyNowReturn.storeID,ordernumber=arguments.strBuyNowReturn.ordernumber,appInstanceID=arguments.strBuyNowReturn.appInstanceID.applicationInstanceID);
		if (NOT arguments.strBuyNowReturn.qryItems.recordcount) return arguments.strBuyNowReturn;
		arguments.strBuyNowReturn.qryItemsTotal = getOrderTotals(orderNumber=arguments.strBuyNowReturn.ordernumber);
		arguments.strBuyNowReturn.evProductPurchase = {
			"currency": "USD",
			"value":arguments.strBuyNowReturn.qryItemsTotal.actualTotal,
			"items": []
		};
		if(arguments.strBuyNowReturn.qryItemsTotal.totalShipping gt 0)
			arguments.strBuyNowReturn.evProductPurchase.shipping_tier = arguments.strBuyNowReturn.qryItemsTotal.shippingMethod
		for (var i = 1; i <= arguments.strBuyNowReturn.qryItems.recordCount; i++) {
			local.tmpStr = {
				"item_id": arguments.strBuyNowReturn.qryItems["productID"][i],
				"item_name": arguments.strBuyNowReturn.qryItems["contentTitle"][i],
				"quantity": arguments.strBuyNowReturn.qryItems["quantity"][i],
				"price": arguments.strBuyNowReturn.qryItems["rate"][i],
				"item_category":arguments.strBuyNowReturn.qryItems["categoryName"][i]
			};
			arrayAppend(arguments.strBuyNowReturn.evProductPurchase.items, local.tmpStr);
		}
		arguments.strBuyNowReturn.eventProductPurchaseJSON = SerializeJSON(arguments.strBuyNowReturn.evProductPurchase);

		// basics
		arguments.strBuyNowReturn.itemOK = true;
		arguments.strBuyNowReturn.buyNowPageTitle = "Store Checkout";
		arguments.strBuyNowReturn.receiptTitle = "Purchase Complete";

		// pricing defaults
		arguments.strBuyNowReturn.showPaymentArea = true;		// payment is required unless overridden below
		arguments.strBuyNowReturn.offerCoupon = offerCouponForStoreOrder(siteID=arguments.event.getValue('mc_siteinfo.siteID'), totalDiscount=arguments.strBuyNowReturn.qryItemsTotal.totalDiscount);
		arguments.strBuyNowReturn.noPaymentStatement = "No payment is due for this purchase.";
		arguments.strBuyNowReturn.onReceiptStatement = "You have purchased the following items:";
		arguments.strBuyNowReturn.purchaserTitle = "Purchaser";
	
		// merchant profiles allowed
		arguments.strBuyNowReturn.paymentGateways = getCartMerchantProfiles(orgID=arguments.event.getValue('mc_siteinfo.orgID'), storeid=arguments.strBuyNowReturn.storeID, qryItems=arguments.strBuyNowReturn.qryItems);
		
		// pricing overrides
		if (arguments.strBuyNowReturn.qryItemsTotal.actualTotal lte 0)
			arguments.strBuyNowReturn.showPaymentArea = false;		// dont show payment area if cart total is 0

		// tax info
		arguments.strBuyNowReturn.stateIDForTax = 0;
		arguments.strBuyNowReturn.zipForTax = "";

		// shipping panel
		if (NOT hasShippingAddress(appInstanceID=arguments.strBuyNowReturn.appInstanceID.applicationInstanceID)) {
			arguments.strBuyNowReturn.showShippingArea = true;
		} else {
			local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});
			arguments.strBuyNowReturn.stateIDForTax = local.store[arguments.strBuyNowReturn.appInstanceID.applicationInstanceID].CrtShipping.state;
			arguments.strBuyNowReturn.zipForTax = local.store[arguments.strBuyNowReturn.appInstanceID.applicationInstanceID].CrtShipping.zip;
			arguments.strBuyNowReturn.showShippingArea = false;
		}

		return arguments.strBuyNowReturn;
		</cfscript>
	</cffunction>

	<cffunction name="buynow_buy" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.objStore = CreateObject('component', 'model.admin.store.store')>
		<cfset local.objAdminCart = CreateObject('component', 'model.admin.store.shoppingCart')>
		<cfset local.strResponse = { success=false, response='' }>

		<!--- get app instance id, orderid --->
		<cfset local.qryAppInstanceID = getAppInstanceIDFromOrderNumber(arguments.strBuyNow.orderNumber)>
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif not len(local.store[local.qryAppInstanceID.applicationInstanceID].CrtMemberID)>
			<cfreturn local.strResponse>
		</cfif>

		<cfset arguments.event.setValue('orderID',local.qryAppInstanceID.orderID)>
		<cfset local.storeInfo = getStoreInfo(appInstanceID=local.qryAppInstanceID.applicationInstanceID)>

		<!--- update member id for ordernumber --->
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcUser.memberData.IdentifiedAsMemberID gt 0>
			<cfset local.useMID = session.cfcUser.memberData.IdentifiedAsMemberID>
		<cfelse>
			<cfset local.useMID = session.cfcUser.memberData.memberID>
		</cfif>
		<cfif local.useMID gt 0>
			<cfset updateMemberID(local.qryAppInstanceID.applicationInstanceID,local.useMID,local.store[local.qryAppInstanceID.applicationInstanceID].orderNumber)>
		</cfif>
		<cfset arguments.event.setValue('orderID',getOrderID(arguments.strBuyNow.orderNumber))>
		<cfset arguments.event.setValue('storeid',arguments.strBuyNow.storeid)>

		<!--- determine payment profileID and profileCode --->
		<cfset arguments.event.paramValue('profileid',0)>
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			select profileID, profileCode, gatewayid, gatewayType, emailRecipient, tabTitle, enableProcessingFeeDonation, processFeeDonationFeePercent,
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, enableSurcharge, surchargePercent, surchargeRevenueGLAccountID,
				processingFeeLabel
			from arguments.strBuyNow.paymentGateways
			where profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- shipping info for storage --->
		<cfscript>
		local.xmlShippingInfo = '<shipping>';

		// param values from session
		local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={});
		if (isStruct(local.store[local.qryAppInstanceID.applicationInstanceID].CrtShipping) AND NOT structIsEmpty(local.store[local.qryAppInstanceID.applicationInstanceID].CrtShipping)) {
			local.strShipping = duplicate(local.store[local.qryAppInstanceID.applicationInstanceID].CrtShipping);
			arguments.event.paramValue('fldship_attn',local.strShipping.attn);
			arguments.event.paramValue('fldship_firm',local.strShipping.firm);
			arguments.event.paramValue('fldship_address1',local.strShipping.address1);
			arguments.event.paramValue('fldship_city',local.strShipping.city);
			arguments.event.paramValue('fldship_state',local.strShipping.state);
			arguments.event.paramValue('fldship_zip',local.strShipping.zip);
		}
		
		for (local.fld in arguments.event.getCollection()) {
			if (findNoCase("fldship_",local.fld))
				local.xmlShippingInfo = local.xmlShippingInfo & "<#lcase(local.fld)#>#xmlformat(arguments.event.getValue(local.fld))#</#lcase(local.fld)#>";
		}
		local.xmlShippingInfo = local.xmlShippingInfo & '</shipping>';
		</cfscript>
		
		<!--- get cart data--->
		<cfquery name="local.qryGetShippingData" datasource="#application.dsn.membercentral.dsn#">
			select ssm.shippingID, ssm.shippingName
			from dbo.store_orders as o
			inner join dbo.store_ShippingMethods as ssm on ssm.shippingID = replace(o.shippingKey,'PID','')
			where o.ordernumber = <cfqueryparam value="#arguments.strBuyNow.ordernumber#" cfsqltype="cf_sql_varchar">
			and o.storeid = <cfqueryparam value="#arguments.strBuyNow.storeid#" cfsqltype="cf_sql_integer">				
		</cfquery>	
		<cfset local.qryCart = getCartData(storeid=arguments.strBuyNow.storeid,ordernumber=arguments.strBuyNow.ordernumber, appInstanceID=local.qryAppInstanceID.applicationInstanceID, shippingid=local.qryGetShippingData.shippingid)>
		<cfset local.stateIDForTax = val(arguments.event.getValue('fldship_state',0))>
		<cfset local.zipForTax = trim(arguments.event.getValue('fldship_zip',''))>

		<cfset local.strDiscount = getCartDiscountStructure(appInstanceID=local.qryAppInstanceID.applicationInstanceID)>
		
		<!--- amount to charge and accounting totals --->
		<cfset local.qryItemsTotal = getOrderTotals(orderNumber=arguments.strBuyNow.ordernumber)>
		<cfset local.amountToCharge = local.qryItemsTotal.actualTotal>
		
		<!--- prepare fields for gateway and send --->
		<cfif local.amountToCharge gt 0>

			<!--- get fields --->
			<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
			<cfset local.tmpFields = structNew()>
			<cfloop query="local.qryGatewayProfileFields">
				<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
			</cfloop>

			<!--- get info on file if applicable --->
			<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
			<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
				<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=local.usemid, profileID=local.qryMerchantProfile.profileID)>
				<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
			</cfif>

			<!--- Surcharge / Processing Fee Donation --->
			<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.amountToCharge, 
				stateIDForTax=val(local.stateIDForTax), zipForTax=local.zipForTax, processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
				surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

			<!--- failed --->
			<cfif NOT local.additionalFeesInfo.success>
				<cfset local.strResponse.response = "Unable to get additional payment fees info.">
				<cfreturn local.strResponse>
			</cfif>
			
			<cfset local.finalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

			<!--- prepare fields for gateway and send --->
			<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
									 profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
									 statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.finalAmountToCharge, 
									 x_description='#arguments.event.getValue('mc_siteinfo.sitename')# Store Purchase', offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>
			<cfset structAppend(local.strTemp,local.tmpFields)>
			
			<!--- apple or google pay token --->
			<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
				<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
			</cfif>
			
			<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
				<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
				<cfloop query="local.qryCart">
					<cfset local.thisItemPrice = val(local.qryCart.rateOverride) ? val(local.qryCart.rateOverride) : val(local.qryCart.rate)>
					<cfset local.totalPrice = precisionEvaluate((local.thisItemPrice * val(local.qryCart.quantity)) - val(local.qryCart.itemDiscountExcTax))>
					<cfset local.itemPriceExcDiscount = precisionEvaluate(local.totalPrice / val(local.qryCart.quantity))>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "#local.qryCart.contentTitle# (#local.qryCart.formatName#)",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Store Product Purchase",
						"itemPriceExcDiscount": local.itemPriceExcDiscount,
						"itemPriceIncDiscount": local.thisItemPrice,
						"discount": val(local.qryCart.itemDiscountExcTax),
						"qty": #val(local.qryCart.quantity)#,
						"total": local.totalPrice
					})>
				</cfloop>
				<cfif val(local.qryItemsTotal.totalShipping)>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "Shipping Total",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Store Purchase Shipping Total",
						"itemPriceExcDiscount": local.qryItemsTotal.totalShipping,
						"itemPriceIncDiscount": local.qryItemsTotal.totalShipping,
						"discount": 0,
						"qty": 1,
						"total": local.qryItemsTotal.totalShipping
					})>
				</cfif>
				<cfif val(local.qryItemsTotal.totalTax)>
					<cfif val(local.qryItemsTotal.totalDiscount) gt 0>
						<cfset local.discountTax = precisionEvaluate(local.qryItemsTotal.totalDiscount - local.qryItemsTotal.totalDiscountExcTax)>
						<cfset local.totalTax = precisionEvaluate(local.qryItemsTotal.totalTax - local.discountTax)>
					<cfelse>
						<cfset local.totalTax = local.qryItemsTotal.totalTax>
					</cfif>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "Store Product Purchase Tax",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# Store Product Purchase Tax",
						"itemPriceExcDiscount": local.totalTax,
						"itemPriceIncDiscount": local.totalTax,
						"discount": 0,
						"qty": 1,
						"total": local.totalTax
					})>
				</cfif>
				<cfif local.additionalFeesInfo.additionalFees GT 0>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "#local.additionalFeesInfo.additionalFeesLabel#",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
						"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
						"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
						"discount": 0,
						"qty": 1,
						"total": local.additionalFeesInfo.additionalFees
					})>
					<!--- Surcharge --->
					<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
						<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
					</cfif>
				</cfif>
				<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
			</cfif>
			<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

			<!--- if payment not successful --->
			<cfif local.paymentResponse.responseCode is not 1>
				<cfset local.strResponse.response = local.paymentResponse.publicResponseReasonText>
				<cfreturn local.strResponse>
			</cfif>

			<!--- Record Surcharge / Processing Fee Donation --->
			<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
				<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
					siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.useMID, recordedByMemberID=local.useMID, 
					statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
					GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
					paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
				
				<!--- if not successful --->
				<cfif NOT local.strRecordAdditionalPmtFees.success>
					<cfset local.strResponse.response = "Unable to record additional payment fees.">
					<cfreturn local.strResponse>
				</cfif>
			</cfif>

		</cfif>

		<cfsavecontent variable="local.addOrderSQL">
			<cfoutput>
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @invoiceID int, @orgID int, @siteID int, @loggedInMemberID int, @statsSessionID int, @orderID int, 
						@GLAccountID int, @itemTransactionID int, @paymentTransactionID int, @invoiceProfileID int, @invoiceMemberID int, @assignedToMemberID int,
						@minVID int, @od_statsSessionID int, @od_itemID int, @od_rateID int, @od_formatID int, @newOrderDetailID int, 
						@shippingTransactionID int, @invoiceNumber varchar(19), @od_formatName varchar(250), @storeID int, 
						@od_contentTitle varchar(200), @detail varchar(500), @invoiceIDList varchar(max), @shippingdetail varchar(500),
						@nowDate datetime, @vAmount decimal(18,2), @od_ratepaid decimal(18,2), @od_perItem decimal(18,2), 
						@od_perShipment decimal(18,2), @xmlShipping xml, @stateIDForTax int, @zipForTax varchar(25), @taxAmount decimal(18,2),
						@taxAmountShip decimal(18,2), @couponID int, @discountAmount decimal(18,2), @adjTransactionID int, @maxOverallUsageCount int, 
						@maxMemberUsageCount int, @redemptionCount int, @redemptionCountPerMember int, @couponCode varchar(15), @SPRedemptionCount int;
				DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int, amount decimal(18,2));
				
				select @nowDate = getdate();
				set @orgID = #int(val(arguments.event.getValue('mc_siteinfo.orgid')))#;
				set @siteID = #int(val(arguments.event.getValue('mc_siteinfo.siteid')))#;
				set @storeID = #arguments.strBuyNow.storeid#;
				set @loggedInMemberID = #local.useMID#;
				set @assignedToMemberID = #int(val(local.store[local.qryAppInstanceID.applicationInstanceID].CrtMemberID))#;
				set @statsSessionID = #val(session.cfcUser.statsSessionID)#;
				set @orderID = #val(local.qryAppInstanceID.orderID)#;
				set @stateIDForTax = <cfif local.stateIDForTax gt 0>#local.stateIDForTax#<cfelse>null</cfif>;
				set @zipForTax = <cfif len(local.zipForTax)>'#replace(local.zipForTax,"'","''","ALL")#'<cfelse>null</cfif>;

				select @invoiceMemberID = case when orgID = @orgID then memberID else @assignedToMemberID end
				from dbo.ams_members 
				where memberID = @loggedInMemberID;

				BEGIN TRAN;
			</cfoutput>
		</cfsavecontent>

		<!--- loop over items in cart --->
		<cfloop query="local.qryCart">

			<!--- get item sale amount and GL --->
			<cfset local.thisRateAmount = local.qryCart.rate>
			<cfif len(local.qryCart.rateOverride)>
				<cfset local.thisRateAmount = local.qryCart.rateOverride>
			</cfif>	
			<cfset local.thisItemGLAccount = val(local.qryCart.rateGLAccountID)>
			<cfset local.thisItemShippingGLAccount = val(local.qryCart.shippingGLAccountID)>
			<cfset local.thisItemDetail = "Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle#">
			<cfset local.thisItemShippingDetail = "Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryCart.contentTitle# - #local.qryCart.shippingName# shipping">
			<cfset local.qryCartDataByID = local.objAdminCart.getCartDataByID(cartItemID=local.qryCart.cartItemID)>

			<cfset local.couponAppliedItem = false>
			<cfif local.strDiscount.qryDiscountItems.recordCount>
				<cfquery name="local.qryItemDiscount" dbtype="query">
					select itemDiscount, itemDiscountExcTax
					from [local].strDiscount.qryDiscountItems
					where cartItemID = #val(local.qryCart.cartItemID)#
				</cfquery>

				<cfif local.qryItemDiscount.recordCount and val(local.qryItemDiscount.itemDiscountExcTax) gt 0>
					<cfset local.couponAppliedItem = true>
					<cfset local.thisItemDiscount = local.qryItemDiscount.itemDiscountExcTax>
					<cfset local.thisQtyDiscountAmt = 0>
				</cfif>
			</cfif>

			<cfsavecontent variable="local.addOrderSQL">
				<cfoutput>
				#local.addOrderSQL#

				select @od_statsSessionID = null, @od_itemID = null, @od_rateID = null, @od_formatID = null, @od_ratepaid = null,
					   @od_formatName = null, @od_perItem = null, @od_perShipment = null, @od_contentTitle = null, @detail = null,
					   @taxAmount = null, @taxAmountShip = null;
				set @od_statsSessionID = #val(local.qryCartDataByID.StatSessionID)#;
				set @od_itemID = #val(local.qryCartDataByID.ProductItemID)#;
				set @od_rateID = #val(local.qryCartDataByID.rateid)#;
				set @od_formatID = #val(local.qryCartDataByID.formatID)#;
				set @od_ratepaid = nullif(#val(local.qryCartDataByID.ratePaid)#,0);
				set @od_formatName = '#replace(local.qryCartDataByID.formatName,"'","''","ALL")#';
				set @od_perItem = #val(local.qryCartDataByID.perItem)#;
				set @od_perShipment = #val(local.qryCartDataByID.perShipment)#;
				set @od_contentTitle = '#replace(local.qryCartDataByID.contentTitle,"'","''","ALL")#';
				SET @detail = '#left(replace(local.thisItemDetail,"'","''","ALL"),500)#';
				SET @shippingdetail = '#left(replace(local.thisItemShippingDetail,"'","''","ALL"),500)#';
				<cfif structkeyExists(session.strStoreTax[arguments.strBuyNow.ItemID],local.qryCart.cartItemID)>
					set @taxAmount = #session.strStoreTax[arguments.strBuyNow.ItemID][local.qryCart.cartItemID]#;
				</cfif>
				<cfif local.qryCart.perItem and structkeyExists(session.strStoreTax[arguments.strBuyNow.ItemID],"#local.qryCart.cartItemID#_ship")>
					set @taxAmountShip = #session.strStoreTax[arguments.strBuyNow.ItemID]["#local.qryCart.cartItemID#_ship"]#;
				</cfif>
				</cfoutput>
			</cfsavecontent>

			<!--- loop over each qty of the item. each gets its own sale. --->
			<cfloop from="1" to="#local.qryCart.quantity#" index="local.i">

				<cfsavecontent variable="local.addOrderSQL">
					<cfoutput>
					#local.addOrderSQL#
					
					<!--- ------------------- --->
					<!--- for the item itself --->
					<!--- ------------------- --->
					select @GLAccountID = null, @invoiceProfileID = null, @invoiceID = null,  @newOrderDetailID = null, @discountAmount = null;
					set @GLAccountID = #local.thisItemGLAccount#;
					select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
					select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
	
					-- if necessary, create invoice assigned to purchaser based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
							@assignedToMemberID=@invoiceMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
	
						insert into @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);
					END
	
					-- add item to completed order
					INSERT INTO dbo.store_orderDetails (orderID, StatSessionID, ProductItemID, rateid, formatID, Quantity, 
						ratePaid, formatName, perItem, perShipment, contentTitle)
					VALUES (@orderID, @od_statsSessionID, @od_itemID, @od_rateID, @od_formatID, 1, @od_ratepaid, @od_formatName, 
						@od_perItem, @od_perShipment, @od_contentTitle);
					select @newOrderDetailID = scope_identity();

					-- record item transaction
					EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
						@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
						@statsSessionID=@statsSessionID, @status='Active', @detail=@detail, @parentTransactionID=null, 
						@amount=#val(local.thisRateAmount)#, @transactionDate=@nowDate,  
						@creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
						@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, 
						@bypassAccrual=0, @xmlSchedule=null, @transactionID=@itemTransactionID OUTPUT;

					-- record item-app transaction
					EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@itemTransactionID, 
						@itemType='Product', @itemID=@newOrderDetailID, @subItemID=null;

					<cfif local.couponAppliedItem and local.thisItemDiscount gt 0>
						<cfset local.thisQtyDiscountAmt = min(val(local.thisRateAmount),local.thisItemDiscount)>

						-- promo code applied
						select @couponID = null, @couponCode = null, @maxOverallUsageCount = null, @maxMemberUsageCount = null, 
							@redemptionCount = null, @redemptionCountPerMember = null, @SPRedemptionCount = null;

						set @couponID = #int(val(local.strDiscount.couponID))#;

						-- check if coupon max usage count met
						select @couponCode = couponCode, @maxOverallUsageCount = maxOverallUsageCount, @maxMemberUsageCount = maxMemberUsageCount,
							@SPRedemptionCount = storeXML.value('(/store/p/i[text()=sql:variable("@od_itemID")]/@rc)[1]', 'int')
						from dbo.tr_coupons
						where couponID = @couponID;

						set @SPRedemptionCount = isnull(@SPRedemptionCount,1);

						;with redemptionsCheck#local.qryCart.cartItemID# as (
							select distinct td.itemType, td.itemID, td.redemptionCount, 
								case when m.memberID is not null then td.redemptionCount else 0 end as memberRedemptionCount
							from dbo.tr_transactionDiscounts as td
							inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = td.transactionID and t.statusID = 1
							left outer join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t.assignedToMemberID and m.activeMemberID = @assignedToMemberID
							where td.couponID = @couponID
							and not (itemID = @orderID and itemType = 'StoreOrder')
						)
						select @redemptionCount = SUM(redemptionCount), @redemptionCountPerMember = SUM(memberRedemptionCount) 
						from redemptionsCheck#local.qryCart.cartItemID#;

						IF @maxOverallUsageCount > 0 AND ISNULL(@redemptionCount,0) + @SPRedemptionCount > @maxOverallUsageCount
							UPDATE dbo.store_orders
							SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count for Coupon Code(' + @couponCode + ') exceeded; '
							WHERE orderID = @orderID;

						IF @maxMemberUsageCount > 0 AND ISNULL(@redemptionCountPerMember,0) + @SPRedemptionCount > @maxMemberUsageCount
							UPDATE dbo.store_orders
							SET orderNotes = isnull(orderNotes,'') + ' - Overall Redemptions Count Per Member for Coupon Code(' + @couponCode + ') exceeded; '
							WHERE orderID = @orderID;

						set @discountAmount = #local.thisQtyDiscountAmt# * - 1;

						EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@nowDate, 
							@saleTransactionID=@itemTransactionID, @invoiceID=@invoiceID, @couponID=@couponID, 
							@itemType='StoreOrder', @itemID=@orderID, @redemptionCount=@SPRedemptionCount, 
							@transactionID=@adjTransactionID OUTPUT;

						<cfset local.thisItemDiscount = local.thisItemDiscount - local.thisQtyDiscountAmt>
					</cfif>

					<!--- ------------------------- --->
					<!--- for the per item shipping --->
					<!--- ------------------------- --->
					<cfif local.qryCart.perItem>
						select @GLAccountID = null, @invoiceProfileID = null, @invoiceID = null;
						set @GLAccountID = #local.thisItemShippingGLAccount#;
						select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
						select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;

						-- if necessary, create invoice assigned to purchaser based on invoice profile
						IF @invoiceID is null BEGIN
							EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
								@assignedToMemberID=@invoiceMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
								@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;
		
							insert into @tblInvoices (invoiceID, invoiceProfileID)
							values (@invoiceID, @invoiceProfileID);
						END
						
						-- record shipping transaction
						EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
							@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @status='Active', @detail=@shippingdetail, @parentTransactionID=null, 
							@amount=#val(local.qryCart.perItem)#, @transactionDate=@nowDate, 
							@creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
							@zipForTax=@zipForTax, @taxAmount=@taxAmountShip, @bypassTax=0, @bypassInvoiceMessage=0, 
							@bypassAccrual=0, @xmlSchedule=null, @transactionID=@shippingTransactionID OUTPUT;
						
						-- record shipping-app transaction
						EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@shippingTransactionID, 
							@itemType='ProductPerItem', @itemID=@newOrderDetailID, @subItemID=null;
					</cfif>
					
					</cfoutput>
				</cfsavecontent>

			</cfloop> <!--- //loop from="1" to="#local.qryCart.quantity#" --->

		</cfloop>	<!--- //loop query="local.qryCart" --->

		<!--- lookup per order Shipping --->
		<cfquery name="local.qryPerShipment" dbtype="query" maxrows="1">
			select shippingGLAccountID, perShipment
			from [local].qryCart
			order by perShipment DESC
		</cfquery>		
		<cfif local.qryPerShipment.perShipment gt 0>
			<cfset local.shippingDetail = "Store #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(arguments.event.getValue('orderID'),'0000')# - #local.qryGetShippingData.shippingName# shipping">

			<cfsavecontent variable="local.addOrderSQL">
				<cfoutput>
				#local.addOrderSQL#

				select @shippingDetail = null, @shippingTransactionID = null, @taxAmount = null;
				select @shippingDetail = '#replaceNoCase(local.shippingDetail,"'","''","ALL")#';

				select @GLAccountID = null, @invoiceProfileID = null, @invoiceID = null;
				set @GLAccountID = #local.qryPerShipment.shippingGLAccountID#;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where glAccountID = @GLAccountID;
				select @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				<cfif structkeyExists(session.strStoreTax[arguments.strBuyNow.ItemID],"pershipment")>
					set @taxAmount = #session.strStoreTax[arguments.strBuyNow.ItemID].pershipment#;
				</cfif>

				-- if necessary, create invoice assigned to purchaser based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@loggedInMemberID, 
						@assignedToMemberID=@invoiceMemberID, @dateBilled=@nowDate, @dateDue=@nowDate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);
				END
				
				-- record shipping transaction
				EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, 
					@assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID, 
					@statsSessionID=@statsSessionID, @status='Active', @detail=@shippingDetail, @parentTransactionID=null, 
					@amount=#val(local.qryPerShipment.perShipment)#, @transactionDate=@nowDate, 
					@creditGLAccountID=@GLAccountID, @invoiceID=@invoiceID, @stateIDForTax=@stateIDForTax, 
					@zipForTax=@zipForTax, @taxAmount=@taxAmount, @bypassTax=0, @bypassInvoiceMessage=0, 
					@bypassAccrual=0, @xmlSchedule=null, @transactionID=@shippingTransactionID OUTPUT;
				
				-- record shipping-app transaction
				EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Store', @transactionID=@shippingTransactionID, 
					@itemType='ProductPerShipment', @itemID=@newOrderDetailID, @subItemID=null;
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfsavecontent variable="local.addOrderSQL">
			<cfoutput>
			#local.addOrderSQL#

				-- update order info and move cart to order details
				set @xmlShipping = '#replace(ToString(local.xmlShippingInfo),"'","''","ALL")#';
				EXEC dbo.store_finalizeOrder @storeID=@storeID, @orderID=@orderID, @orderNumber='#arguments.strBuyNow.orderNumber#', 
					@memberID=@assignedToMemberID, @profileID=#val(local.qryMerchantProfile.profileID)#, @xmlShippingInfo=@xmlShipping;

				-- return invoiceIDList
				select @invoiceIDList = COALESCE(@invoiceIDList+',','') + cast(invoiceID as varchar(20))
				from @tblInvoices;

				-- close invoices
				EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceIDList;

				<cfif val(local.amountToCharge) gt 0 and local.paymentResponse.mc_transactionID gt 0>
					set @paymentTransactionID = #local.paymentResponse.mc_transactionID#;
					
					-- get final amount of invoices (with tax, etc included)
					update tmp
					set tmp.amount = tmp2.InvDue
					from @tblInvoices as tmp
					inner join (
						select tmp.invoiceID, sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue
						from @tblInvoices as tmp
						left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
						group by tmp.invoiceID
					) as tmp2 on tmp2.invoiceID = tmp.invoiceID;

					-- allocate payment to invoices
					select @minVID = min(invoiceID) from @tblInvoices;
					while @minVID is not null BEGIN
						select @vAmount = amount from @tblInvoices where invoiceID = @minVID;
						EXEC dbo.tr_allocateToInvoice @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
							@statsSessionID=@statsSessionID, @amount=@vAmount, @transactionDate=@nowDate, 
							@paymentTransactionID=@paymentTransactionID, @invoiceID=@minVID;
						select @minVID = min(invoiceID) from @tblInvoices where invoiceID > @minVID;
					END
				<cfelse>
					set @paymentTransactionID = 0;
				</cfif>

				COMMIT TRAN;
			
				SELECT @invoiceIDList as invoiceIDList, @paymentTransactionID as paymentTransactionID, 1 as success;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
			</cfoutput>
		</cfsavecontent>

		<!--- save order info --->
		<cftry>
			<cfquery name="local.qryAddOrder" datasource="#application.dsn.membercentral.dsn#">
				#preserveSingleQuotes(local.addOrderSQL)#
			</cfquery>	
			<cfset local.qryAddOrderSuccess = val(local.qryAddOrder.success)>
			<cfset local.qryAddOrderPayTID = val(local.qryAddOrder.paymentTransactionID)>
			<cfset local.qryAddOrderInvoiceIDList = local.qryAddOrder.invoiceIDList>
			<cfif local.qryAddOrderSuccess is not 1>
				<cfthrow message="qryAddOrder did not return a status of 1.">
			</cfif>
		<cfcatch type="any">
			<cfset local.qryAddOrderSuccess = 0>
			<cfset local.qryAddOrderPayTID = 0>
			<cfset local.qryAddOrderInvoiceIDList = 0>
			<cfset local.tmpErrStr = { sql=replace(local.addOrderSQL,chr(10),"<br/>","ALL") }>
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.tmpErrStr)>
			<cfrethrow>
		</cfcatch>
		</cftry>
		
		<!--- if success, run hooks and email --->
		<cfif local.qryAddOrderSuccess is 1>

			<!--- run storeOrderFinalized hook with orderID --->
			<cfset local.strHookData = { orderID=local.qryAppInstanceID.orderID }>
			<cfset application.objCMS.runHook(event='storeOrderFinalized', siteResourceID=local.qryAppInstanceID.siteResourceID, data=local.strHookData)>

			<!--- send email --->
			<cfset local.objStoreReg = CreateObject("component","model.store.storeReg")>

			<cftry>
				<cfset local.sendFrom = trim(local.storeInfo.emailReplyTo)>
				<cfif NOT len(local.sendFrom) or NOT isValid("regex",local.sendFrom,application.regEx.email)>
					<cfthrow>
				</cfif>
			<cfcatch type="any">
				<cfset local.sendFrom = arguments.event.getValue('mc_siteInfo.supportProviderEmail')>
			</cfcatch>
			</cftry>

			<cfset local.qryMainEmail = application.objMember.getMainEmail(local.useMID)>
			<cfif len(local.storeInfo.emailRecipient) OR isValid("regex",local.qryMainEmail.email,application.regEx.email)>
				<cfset local.emailsubject = "#arguments.event.getValue('mc_siteinfo.siteName')# Order #arguments.event.getValue('mc_siteinfo.sitecode')##Numberformat(local.qryAppInstanceID.orderID,"0000")# Confirmation and Receipt">
				<cfset local.emailtitle = "#arguments.event.getValue('mc_siteinfo.sitename')# Order Confirmation and Receipt">
				<cfset local.emailContent = local.objStoreReg.orderReceiptForEmail(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
												storeID=arguments.strBuyNow.storeid, orderID=local.qryAppInstanceID.orderID,
												onlineReceipt=0, notes='')>
				
				<cfif isValid("regex",local.qryMainEmail.email,application.regEx.email)>
					<cfset local.qryMember = application.objMember.getMemberInfo(memberID=local.useMID)>
					
					<cfset local.strReturn = application.objEmailWrapper.sendMailESQ(
												emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
												emailto=[{ name="#local.qryMember.firstName# #local.qryMember.lastName#", email=local.qryMainEmail.email }],
												emailreplyto=local.sendFrom,
												emailsubject=local.emailsubject,
												emailtitle=local.emailtitle,
												emailhtmlcontent=local.emailContent,
												emailAttachments=[],
												siteID=arguments.event.getValue('mc_siteinfo.siteid'),
												memberID=local.useMID,
												messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="STOREORDER"),
												sendingSiteResourceID=local.qryAppInstanceID.siteResourceID
											)>
				</cfif>

				<cfif len(local.storeInfo.emailRecipient)>
					<cfset local.arrEmailTo = []>
					<cfset local.toEmailArr = listToArray(local.storeInfo.emailRecipient,';')>
					<cfloop array="#local.toEmailArr#" item="local.thisEmail">
						<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
					</cfloop>

					<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
						emailto=local.arrEmailTo,
						emailreplyto=local.sendFrom,
						emailsubject=local.emailsubject,
						emailtitle=local.emailTitle,
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=arguments.event.getValue('mc_siteInfo.sysmemberid'),
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="STOREORDER"),
						sendingSiteResourceID=local.qryAppInstanceID.siteResourceID)/>
				</cfif>
			</cfif>

			<!--- remove store from cache so we generate a new ordernumber if they go back to the store --->
			<cfset application.mcCacheManager.sessionDeleteValue(keyname='store')>

			<cfset local.strResponse.success = true>
		</cfif>

		<cfset local.strResponse.profileCode = local.qryMerchantProfile.profileCode>
		
		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="buyNow_shipping" access="public" output="false" returntype="boolean">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">

		<cfset var local = structNew()>

		<!--- get app instance id, orderid --->		
		<cfset local.qryAppInstanceID = getAppInstanceIDFromOrderNumber(arguments.strBuyNow.orderNumber)>

		<!--- empty cart --->
		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif NOT isStruct(local.store) OR NOT structKeyExists(local.store,local.qryAppInstanceID.applicationInstanceID)>
			<cfreturn false>
		</cfif>

		<!--- param values --->
		<cfset arguments.event.paramValue('fldship_attn','')>
		<cfset arguments.event.paramValue('fldship_firm','')>
		<cfset arguments.event.paramValue('fldship_address1','')>
		<cfset arguments.event.paramValue('fldship_city','')>
		<cfset arguments.event.paramValue('fldship_state',0)>
		<cfset arguments.event.paramValue('fldship_zip','')>

		<cfscript>
			// billing fields
			local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=arguments.event.getTrimValue('fldship_zip'), billingStateID=val(arguments.event.getValue('fldship_state')));
			if (local.strBillingZip.isvalidzip) {
				arguments.event.setValue('fldship_zip',local.strBillingZip.billingzip);
			} else {
				throw(message="Invalid State/Zip.");
			}
		</cfscript>

		<!--- update address --->
		<cfset local.store[local.qryAppInstanceID.applicationInstanceID].CrtShipping = {
					attn = arguments.event.getValue('fldship_attn'),
					firm = arguments.event.getValue('fldship_firm'),
					address1 = arguments.event.getValue('fldship_address1'),
					city = arguments.event.getValue('fldship_city'),
					state = arguments.event.getValue('fldship_state'),
					zip = arguments.event.getValue('fldship_zip')
				}>
		<cfset application.mcCacheManager.sessionSetValue(keyname='store', value=local.store)>
		<!--- get totals --->
		<cfset local.qryItemsTotal = getOrderTotals(orderNumber=arguments.strBuyNow.ordernumber)>

		<!--- get tax --->
		<cfset local.strTaxes = calculateCartTaxes(storeID=arguments.strBuyNow.storeID, orderNumber=arguments.strBuyNow.ordernumber, 
									appInstanceID=local.qryAppInstanceID.applicationInstanceID, stateidForTax=arguments.event.getValue('fldship_state'),
									zipForTax=arguments.event.getValue('fldship_zip'))>	
		<cfset local.strTaxes.stateIDForTax = arguments.event.getValue('fldship_state')>												
		<cfset local.strTaxes.zipForTax=arguments.event.getValue('fldship_zip')>

		<cfset session.strStoreTax[arguments.strBuyNow.ItemID] = local.strTaxes>

		<!--- update totals and re-calculate discounts if any --->
		<cfset updateOrderTotals(orderID=local.qryAppInstanceID.orderID, totalProduct=local.qryItemsTotal.totalProduct, totalShipping=local.qryItemsTotal.totalShipping,
					shippingKey=local.qryItemsTotal.shippingKey, totaltax=local.strTaxes.totaltax)>

		<cfset CreateObject("component","storeReg").prepStoreCartForCheckout()>

		<cfreturn true>
	</cffunction>

	<cffunction name="buyNow_receipt" access="public" output="no" returntype="struct">
		<cfargument name="event" type="any" required="yes">
		<cfargument name="strBuyNow" type="struct" required="yes">
	
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		
		<cfset local.objStoreAdmin = CreateObject("component","model.admin.store.store")>
		<!--- make sure storeid, orderid are set in event. They are the only variables getOrder uses. --->
		<cfset arguments.event.setValue('orderID',getOrderID(arguments.strBuyNow.orderNumber))>
		<cfset arguments.event.setValue('storeid',arguments.strBuyNow.storeid)>
		<cfset arguments.strBuyNow.evProductPurchaseFinal = {
			"currency": "USD",
			"transaction_id":arguments.event.getValue('mc_siteinfo.sitecode') & Numberformat(arguments.event.getValue('orderID'),"0000"),
			"coupon":getUsedCouponCode(orderID=arguments.event.getValue('orderID')),
			"value":arguments.strBuyNow.qryItemsTotal.actualTotal,
			"tax":arguments.strBuyNow.qryItemsTotal.totalTax,
			"items": []
		}>
		<cfif (arguments.strBuyNow.qryItemsTotal.totalShipping gt 0)>
			<cfset arguments.strBuyNow.evProductPurchaseFinal.shipping_tier = arguments.strBuyNow.qryItemsTotal.shippingMethod>
			<cfset arguments.strBuyNow.evProductPurchaseFinal.shipping = arguments.strBuyNow.qryItemsTotal.totalShipping>
		</cfif>	
		<cfloop query="arguments.strBuyNow.qryItems">
			<cfset arrayAppend(arguments.strBuyNow.evProductPurchaseFinal.items, {
				"item_id": arguments.strBuyNow.qryItems.productID,
				"item_name": arguments.strBuyNow.qryItems.contentTitle,
				"quantity": arguments.strBuyNow.qryItems.quantity,
				"price": arguments.strBuyNow.qryItems.rate,
				"discount": arguments.strBuyNow.qryItems.itemDiscountExcTax,
				"item_category":arguments.strBuyNow.qryItems.categoryName
			})>
		</cfloop>
		<cfset arguments.strBuyNow.evProductPurchaseFinalJSON = SerializeJSON(arguments.strBuyNow.evProductPurchaseFinal)>
		<cfset local.returnStruct.qryOrder = local.objStoreAdmin.getOrder(storeID=arguments.event.getValue('storeid'), orderID=arguments.event.getValue('orderID'))>
		<cfset application.mcCacheManager.sessionSetValue(keyname='eventProductPurchaseJSON', value=arguments.strBuyNow.evProductPurchaseFinalJSON)>
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getStateID" access="private" output="false" returntype="query">
		<cfargument name="stateCode" type="string" required="true" />

		<cfset var qryStateID = "">
		
		<cfquery name="qryStateID" datasource="#application.dsn.membercentral.dsn#">
			SELECT top 1 stateID
			FROM dbo.ams_states
			WHERE code = <cfqueryparam value="#arguments.stateCode#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>
		
		<cfreturn qryStateID>
	</cffunction>
	
	<cffunction name="calculateCartTaxes" access="public" output="false" returntype="struct">
		<cfargument name="storeID" type="numeric" required="true">
		<cfargument name="orderNumber" type="string" required="true">
		<cfargument name="appInstanceID" type="numeric" required="true">
		<cfargument name="stateidForTax" type="numeric" required="true">
		<cfargument name="zipForTax" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.taxableTotal = 0>
		<cfset local.shippingid = 0>
		<cfset local.strTaxes = { totaltax=0 }>

		<!--- Get "default" shipping Option --->

		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif not len(local.store[arguments.appInstanceID].shippingoption)>
			<cfquery name="local.qryShippingMethods" datasource="#application.dsn.memberCentral.dsn#">
				select shippingID
				from dbo.store_ShippingMethods sm
				where sm.storeID = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer">  
				and sm.Visible = 1
			</cfquery>	
			<cfif local.qryShippingMethods.recordCount>
				<cfset local.shippingid = local.qryShippingMethods.shippingID>
			</cfif>
		<cfelse>
			<cfset local.shippingid = replace(local.store[arguments.appInstanceID].shippingoption,"PID", "")>
		</cfif>			
		
		<cfquery name="local.qryCart" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @storeID int = <cfqueryparam value="#arguments.storeID#" cfsqltype="cf_sql_integer">;

			SELECT ci.cartItemID, ci.quantity, gl.rateGLAccountID, sr.rate, gl.shippingGLAccountID, 
				(isNULL(sr.rate,0) * ci.quantity) as ItemTotal,
				(isNULL(ps.perItem,0) * ci.quantity) as itemShipping, 
				(isNULL(ps.perItem,0)) as perItem,
				(isNULL(ps.perShipment,0)) as perShipment,
				(select top 1 spo.rateOverride * ci.quantity
				 from dbo.store_ProductRatesOverride as spo 
				 where spo.rateid = sr.rateid 
				 and (
				 	(spo.startDate <= getDate() and spo.endDate >= getDate()) 
				 	 or spo.startDate is null 
				 	 or spo.endDate is null
				 	 )
				 order by spo.rateOverride) as ItemTotalOverride,
				(select top 1 spo.rateOverride
				 from dbo.store_ProductRatesOverride as spo 
				 where spo.rateid = sr.rateid 
				 and (
				 	(spo.startDate <= getDate() and spo.endDate >= getDate())
				 	or spo.startDate is null 
				 	or spo.endDate is null 
				 	)
				 order by spo.rateOverride) as rateOverride
			FROM dbo.store_CartItems AS ci
			INNER JOIN dbo.store_rates AS sr ON ci.rateid = sr.rateid 
			CROSS APPLY dbo.fn_store_getRateGLAccountID(@storeID, sr.rateid) as gl
			LEFT OUTER JOIN dbo.store_productShipping ps ON ps.rateid = sr.rateid
				and ps.shippingid = <cfqueryparam value="#local.shippingid#" cfsqltype="cf_sql_integer">
			WHERE ci.storeID = @storeID
			AND ci.orderNumber = <cfqueryparam value="#arguments.orderNumber#" cfsqltype="cf_sql_varchar">;
		</cfquery>		
		
		<cfloop query="local.qryCart">
			<cfif local.qryCart.quantity gt 1>	
				
				<!--- get the tax for each item --->
				<cfif NOT len(trim(local.qryCart.ItemTotalOverride))>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
													saleAmount=local.qryCart.rate, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>	
				<cfelse>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
													saleAmount=local.qryCart.rateOverride, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>
				</cfif>
				<cfif val(local.strTaxIndiv.totalTaxAmt)>
					<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt*local.qryCart.quantity,"0.00")>
				</cfif>	
				<cfset local.strTaxes[local.qryCart.cartItemID] = val(local.strTaxIndiv.totalTaxAmt)>

				<!--- get the tax on item shipping --->
				<cfif local.qryCart.perItem>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.shippingGLAccountID,
													saleAmount=local.qryCart.perItem, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
													zipForTax=arguments.zipForTax)>					
					<cfif val(local.strTaxIndiv.totalTaxAmt)>
						<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt*local.qryCart.quantity,"0.00")>
					</cfif>				
					<cfset local.strTaxes["#local.qryCart.cartItemID#_ship"] = val(local.strTaxIndiv.totalTaxAmt)>
				</cfif>						

			<cfelse>

				<!--- get the tax for each item --->
				<cfif NOT len(trim(local.qryCart.ItemTotalOverride))>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
														saleAmount=local.qryCart.ItemTotal, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
				<cfelse>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.rateGLAccountID,
														saleAmount=local.qryCart.ItemTotalOverride, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
				</cfif>
				<cfif val(local.strTaxIndiv.totalTaxAmt)>
					<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt,"0.00")>
				</cfif>	
				<cfset local.strTaxes[local.qryCart.cartItemID] = val(local.strTaxIndiv.totalTaxAmt)>
				
				<!--- get the tax on item shipping --->
				<cfif local.qryCart.ItemShipping>
					<cfset local.strTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryCart.shippingGLAccountID,
														saleAmount=local.qryCart.ItemShipping, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
					<cfif val(local.strTaxIndiv.totalTaxAmt)>
						<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strTaxIndiv.totalTaxAmt,"0.00")>
					</cfif>				
					<cfset local.strTaxes["#local.qryCart.cartItemID#_ship"] = val(local.strTaxIndiv.totalTaxAmt)>
				</cfif>				

			</cfif>										
		</cfloop>
		
		<!--- get the tax on pershipment shipping --->
		<cfquery name="local.qryShipment" dbtype="query" maxrows="1">
			select shippingGLAccountID, PerShipment
			from [local].qryCart
			order by PerShipment DESC					
		</cfquery>																		
		<cfif local.qryShipment.recordCount and val(local.qryShipment.PerShipment)>
			<cfset local.strShipmentTaxIndiv = local.objAccounting.getTaxForUncommittedSale(saleGLAccountID=local.qryShipment.shippingGLAccountID,
														saleAmount=local.qryShipment.PerShipment, transactionDate=now(), stateIDForTax=arguments.stateIDForTax,
														zipForTax=arguments.zipForTax)>
			<cfif val(local.strShipmentTaxIndiv.totalTaxAmt)>
				<cfset local.taxableTotal = local.taxableTotal + numberFormat(local.strShipmentTaxIndiv.totalTaxAmt,"0.00")>
			</cfif>	
			<cfset local.strTaxes["pershipment"] = val(local.strShipmentTaxIndiv.totalTaxAmt)>
		</cfif>

		<cfset local.strTaxes["totaltax"] = local.taxableTotal>
		
		<cfreturn local.strTaxes>
	</cffunction>

	<cffunction name="updateDiscountItems" access="private" output="false" returntype="void">
		<cfargument name="qryCart" type="query" required="true">
		<cfargument name="appInstanceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif structKeyExists(local.store,arguments.appInstanceID) AND NOT structIsEmpty(local.store[arguments.appInstanceID].CrtCoupon)>
			<cfset local.storeCart = local.store[arguments.appInstanceID]>

			<cfif arguments.qryCart.recordCount and local.storeCart.CrtCoupon.qryDiscountItems.recordCount>
				<cfloop query="local.storeCart.CrtCoupon.qryDiscountItems">
					<cfif NOT listFind(valueList(arguments.qryCart.cartItemID),local.storeCart.CrtCoupon.qryDiscountItems.cartItemID)>
						<cfset local.storeCart.CrtCoupon.qryDiscountItems.deleteRow(local.storeCart.CrtCoupon.qryDiscountItems.currentRow)>
					</cfif>
				</cfloop>

				<!--- no discounted items --->
				<cfif NOT local.storeCart.CrtCoupon.qryDiscountItems.recordCount>
					<cfset local.storeCart.CrtCoupon = structNew()>
				</cfif>
			<cfelse>
				<cfset local.storeCart.CrtCoupon = structNew()>
				<cfset local.qryAppInstanceID = getAppInstanceIDFromOrderNumber(orderNumber=local.storeCart.orderNumber)>
				<cfif val(local.qryAppInstanceID.orderID) gt 0>
					<cfset updateOrderDiscounts(orderID=local.qryAppInstanceID.orderID, totalDiscount=0, totalDiscountExcTax=0)>
				</cfif>
			</cfif>
		</cfif>
	</cffunction>
	
	<cffunction name="getCartDiscountStructure" access="private" output="false" returntype="struct">
		<cfargument name="appInstanceID" type="numeric" required="true">

		<cfset var strDiscount = structNew()>

		<cfset local.store = application.mcCacheManager.sessionGetValue(keyname='store', defaultValue={})>
		<cfif structKeyExists(local.store,arguments.appInstanceID) AND NOT structIsEmpty(local.store[arguments.appInstanceID].CrtCoupon)>
			<cfset strDiscount = local.store[arguments.appInstanceID].CrtCoupon>
		<cfelse>
			<cfset strDiscount = { qryDiscountItems = queryNew('') }>
		</cfif>

		<cfreturn strDiscount>
	</cffunction>

	<cffunction name="offerCouponForStoreOrder" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="totalDiscount" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.offerCoupon = false>

		<cfif arguments.totalDiscount gt 0>
			<cfset local.offerCoupon = false>
		<cfelse>
			<cfset local.strItem = CreateObject("component","storeReg").cartQualifiedForCoupon()>

			<cfif local.strItem.isQualified>
				<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="Store">
					<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.strItem.cartItemsXML#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
					<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.offerCoupon">
				</cfstoredproc>
			</cfif>
		</cfif>

		<cfreturn local.offerCoupon>
	</cffunction>

	<cffunction name="getUsedCouponCode" access="public" output="FALSE" returntype="string">
		<cfargument name="orderID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.couponCode = "">
	
		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryGetCouponCode">
			select c.couponCode 
			from dbo.tr_transactionDiscounts as td
			inner join dbo.tr_coupons as c on c.couponID = td.couponID
			where td.itemType ='StoreOrder' 
			AND td.itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.orderID#">
		</cfquery>

		<cfif local.qryGetCouponCode.recordCount>
			<cfset local.couponCode = local.qryGetCouponCode.couponCode>
		</cfif>

		<cfreturn local.couponCode>
	</cffunction>

</cfcomponent>