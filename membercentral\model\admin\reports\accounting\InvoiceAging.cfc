<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// call common report controller
			reportController(event=arguments.event);

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>		
	
	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfsavecontent variable="local.js">
				<cfoutput>
					<script language="javascript">
						function forceRequired() {
							var validResult = true;
							var arrReq = [];

							if ($('##frmDateTo').val() == '') {
								arrReq.push('Select the As of Date.');
							}

							if(arrReq.length){
								validResult = false;
								rptShowAlert(arrReq.join('<br/>'));
							}

							return validResult;
						}
						$(function() {
								mca_setupDatePickerField('frmDateTo');
								mca_setupCalendarIcons('frmReport');
							});
					</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
					
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmDateTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmDateTo/text())")>
					<cfset local.frmDisplay = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmDisplay/text())")>
					<cfset local.frmGroupBy = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmGroupBy/text())")>
					<cfif not len(local.frmDateTo)>
						<cfset local.frmDateTo = "#dateformat(dateadd("d",-1,"#month(now())#/1/#year(now())#"),"m/d/yyyy")#">
					</cfif>

					<cfform name="frmReport" id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">
					<div class="mb-5 stepDIV">
						<h5>Report Filters</h5>
						<div class="form-group row mt-2">
							<label for="frmDateTo" class="col-2 col-form-label">As of:</label>
							<div class="col-2">
								<div class="input-group input-group-sm">
									<input type="text" name="frmDateTo" id="frmDateTo" value="#local.frmDateTo#" mcrdtxt="As of Date" class="form-control form-control-sm dateControl rolldate" placeholder="As of">
									<div class="input-group-append">
										<span class="input-group-text cursor-pointer calendar-button" data-target="frmDateTo"><i class="fa-solid fa-calendar"></i></span>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group row">
							<label for="frmGroupBy" class="col-2 col-form-label">Group By:</label>
							<div class="col-2">
								<cfselect name="frmGroupBy" id="frmGroupBy" class="form-control form-control-sm">
									<option value="member" <cfif local.frmGroupBy eq "member">selected</cfif>>Member</option>
									<option value="invoice" <cfif local.frmGroupBy eq "invoice">selected</cfif>>Invoice Number</option>
								</cfselect>
							</div>
						</div>
						<div class="form-group row">
							<label for="frmDisplay" class="col-2 col-form-label">Display Option:</label>
							<div class="col-3">
								<cfselect name="frmDisplay" id="frmDisplay" class="form-control form-control-sm">
									<option value="summary" <cfif local.frmDisplay eq "summary">selected</cfif>>Invoice Total</option>
									<option value="detail" <cfif local.frmDisplay eq "detail">selected</cfif>>Invoice Total by Revenue GL</option>
								</cfselect>
							</div>
						</div>
					</div>
					#showStepRollingDates(event=arguments.event)#
					#showStepFieldsets(event=arguments.event)#
					#showButtonBar(event=arguments.event,validateFunction='forceRequired')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.frmDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdateto/text())")>
			<cfset local.frmDisplay = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplay/text())")>
			<cfset local.frmGroupBy = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmgroupby/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>
			<cfset local.strReport = generateData(qryReportInfo=arguments.qryReportInfo, reportAction=arguments.reportAction, siteCode=arguments.siteCode)>

			<cfif local.strReport.qryInvoices.recordcount>
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.strReport.qryInvoices.mc_outputFieldsXML)>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where fieldcodeSect NOT IN ('mc','m','ma','mat')
					or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated')
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteID, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>

				<cfif local.strReport.qryInvoices.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
						<cfif local.strReport.qryInvoices.openInvoices gt 0>
							<cfoutput>
								<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning alert-dismissible fade show" role="alert">
									<span class="font-size-lg d-block d-40 mr-2 text-center">
										<i class="fa-solid fa-triangle-exclamation"></i>
									</span>
									<span>
										There <cfif local.strReport.qryInvoices.openInvoices eq 1>is 1 open invoice<cfelse>are #local.strReport.qryInvoices.openInvoices# open invoices</cfif> with BILLED DATE before #dateformat(local.frmDateTo,"m/d/yyyy")# that do not appear on this report.
									</span>
									<button type="button" class="close d-print-none" data-dismiss="alert" aria-label="Close">
										<span aria-hidden="true">&times;</span>
									</button>
								</div>
							</cfoutput>
						</cfif>

						<cfif local.strReport.qryInvoices.recordcount is 0>
							<cfoutput><div class="text-dark">No invoices to report.</div></cfoutput>
						<cfelseif local.frmGroupBy eq "invoice">
							<cfoutput>
								<div class="card card-box mb-3">
									<div class="card-body p-2">
										<table class="table table-borderless table-sm">
											<thead>
												<tr>
													<th class="text-left">Invoice ##</th>
													<th class="text-right">Due</th>
													<th class="text-right">Future</th>
													<th class="text-right">0-30</th>
													<th class="text-right">31-60</th>
													<th class="text-right">61-90</th>
													<th class="text-right">91-120</th>
													<th class="text-right">120+</th>
												</tr>
											</thead>
											<tbody>
											<cfif local.frmDisplay eq "summary">
												<cfoutput query="local.strReport.qryInvoices">
													<cfif local.strReport.qryInvoices.rowNum is *********>
														<tr>
															<td class="font-weight-bold"><br/>#local.strReport.qryInvoices.lastName#</td>
															<td></td>
															<td class="text-right font-weight-bold">Future<br/>#dollarFormat(local.strReport.qryInvoices.invFut)#</td>
															<td class="text-right font-weight-bold">0-30<br/>#dollarFormat(local.strReport.qryInvoices.inv030)#</td>
															<td class="text-right font-weight-bold">31-60<br/>#dollarFormat(local.strReport.qryInvoices.inv3160)#</td>
															<td class="text-right font-weight-bold">61-90<br/>#dollarFormat(local.strReport.qryInvoices.inv6190)#</td>
															<td class="text-right font-weight-bold">91-120<br/>#dollarFormat(local.strReport.qryInvoices.inv91120)#</td>
															<td class="text-right font-weight-bold">120+<br/>#dollarFormat(local.strReport.qryInvoices.inv120over)#</td>
														</tr>
													<cfelse>
														<tr valign="top">
															<td class="align-top">
																<div class="font-weight-bold">#local.strReport.qryInvoices.invoiceNumber#</div>
																<table class="pl-3">
																	<tr>
																		<cfif local.frmShowPhotos is 1>
																			<td class="w-5 align-top">
																				<cfif local.strReport.qryInvoices.hasMemberPhotoThumb is 1>
																					<cfif arguments.reportAction eq "screen">
																						<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
																					<cfelse>
																						<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
																					</cfif>
																				<cfelse>
																					<cfif arguments.reportAction eq "screen">
																						<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
																					<cfelse>
																						<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
																					</cfif>
																				</cfif>
																			</td>
																		</cfif>
																		<td class="align-top">
																		<cfif arguments.reportAction eq "screen" and len(local.memberLink)>
																			<a href="#local.memberLink#&memberid=#local.strReport.qryInvoices.memberId#&tab=transactions" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></a><br/>
																		<cfelse>
																			<div class="font-weight-bold"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></div>
																		</cfif>
																		<cfif local.frmShowCompany is 1 AND len(local.strReport.qryInvoices.company)>#local.strReport.qryInvoices.company#<br/></cfif>
																		<cfloop query="local.qryOutputFields">
																			<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
																				<cfset local.AddrToShow = local.strReport.qryInvoices[local.qryOutputFields.dbfield][local.strReport.qryInvoices.currentrow]>
																				<cfloop condition="Find(', , ',local.AddrToShow)">
																					<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
																				</cfloop>
																				<cfif len(local.AddrToShow)>
																					#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
																				</cfif>
																			</cfif>
																		</cfloop>
																		<cfloop query="local.qryOutputFieldsForLoop">
																			<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
																				#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow])#<br/>
																			<cfelseif len(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldLabel][local.strReport.qryInvoices.currentrow])>
																				<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
																					#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow], "m/d/yyyy")#<br/>
																				<cfelse>
																					#local.qryOutputFieldsForLoop.fieldlabel#: #local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow]#<br/>
																				</cfif>
																			</cfif>
																		</cfloop>
																		</td>
																	</tr>
																</table>
															</td>
															<td class="text-right align-top">#dateformat(local.strReport.qryInvoices.dateDue,'m/d/yy')#</td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.ageFut is 1>#dollarFormat(local.strReport.qryInvoices.invFut)#<cfelse>&nbsp;</cfif></td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.age030 is 1>#dollarFormat(local.strReport.qryInvoices.inv030)#<cfelse>&nbsp;</cfif></td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.age3160 is 1>#dollarFormat(local.strReport.qryInvoices.inv3160)#<cfelse>&nbsp;</cfif></td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.age6190 is 1>#dollarFormat(local.strReport.qryInvoices.inv6190)#<cfelse>&nbsp;</cfif></td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.age91120 is 1>#dollarFormat(local.strReport.qryInvoices.inv91120)#<cfelse>&nbsp;</cfif></td>
															<td class="text-right align-top"><cfif local.strReport.qryInvoices.age120over is 1>#dollarFormat(local.strReport.qryInvoices.inv120over)#<cfelse>&nbsp;</cfif></td>
														</tr>
													</cfif>
												</cfoutput>
											<cfelse>
												<cfoutput query="local.strReport.qryInvoices" group="invoiceID">
													<cfif local.strReport.qryInvoices.rowNum is *********>
														<tr>
															<td class="font-weight-bold">#local.strReport.qryInvoices.lastName#</td>
															<td></td>
															<td class="text-right align-top font-weight-bold">Future</td>
															<td class="text-right align-top font-weight-bold">0-30</td>
															<td class="text-right align-top font-weight-bold">31-60</td>
															<td class="text-right align-top font-weight-bold">61-90</td>
															<td class="text-right align-top font-weight-bold">91-120</td>
															<td class="text-right align-top font-weight-bold">120+</td>
														</tr>
														<cfoutput>
															<tr valign="top">
																<td class="font-weight-bold"><div class="pl-3">#local.strReport.qryInvoices.revenueGL# <cfif len(local.strReport.qryInvoices.revenueGLCode)>(#local.strReport.qryInvoices.revenueGLCode#)</cfif></div></td>
																<td></td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.invFut)#</td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv030)#</td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv3160)#</td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv6190)#</td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv91120)#</td>
																<td class="text-right align-top font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv120over)#</td>
															</tr>
														</cfoutput>
													<cfelse>
														<tr valign="top">
															<td class="align-top">
																<div class="font-weight-bold">#local.strReport.qryInvoices.invoiceNumber#</div>
																<table>
																	<tr>
																	<cfif local.frmShowPhotos is 1>
																		<td class="w-5 align-top">
																			<cfif local.strReport.qryInvoices.hasMemberPhotoThumb is 1>
																				<cfif arguments.reportAction eq "screen">
																					<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
																				<cfelse>
																					<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
																				</cfif>
																			<cfelse>
																				<cfif arguments.reportAction eq "screen">
																					<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
																				<cfelse>
																					<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
																				</cfif>
																			</cfif>
																		</td>
																	</cfif>
																	<td class="align-top">
																		<cfif arguments.reportAction eq "screen" and len(local.memberLink)>
																			<a href="#local.memberLink#&memberid=#local.strReport.qryInvoices.memberId#&tab=transactions" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></a><br/>
																		<cfelse>
																			<div class="font-weight-bold"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></div>
																		</cfif>
																		<cfif local.frmShowCompany is 1 AND len(local.strReport.qryInvoices.company)>#local.strReport.qryInvoices.company#<br/></cfif>
																		<cfloop query="local.qryOutputFields">
																			<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
																				<cfset local.AddrToShow = local.strReport.qryInvoices[local.qryOutputFields.dbfield][local.strReport.qryInvoices.currentrow]>
																				<cfloop condition="Find(', , ',local.AddrToShow)">
																					<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
																				</cfloop>
																				<cfif len(local.AddrToShow)>
																					#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
																				</cfif>
																			</cfif>
																		</cfloop>
																		<cfloop query="local.qryOutputFieldsForLoop">
																			<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
																				#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow])#<br/>
																			<cfelseif len(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldLabel][local.strReport.qryInvoices.currentrow])>
																				<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
																					#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow], "m/d/yyyy")#<br/>
																				<cfelse>
																					#local.qryOutputFieldsForLoop.fieldlabel#: #local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow]#<br/>
																				</cfif>
																			</cfif>
																		</cfloop>
																	</td>
																	</tr>
																</table>
															</td>
															<td class="align-top text-right">#dateformat(local.strReport.qryInvoices.dateDue,'m/d/yy')#</td>
															<td colspan="6">&nbsp;</td>
														</tr>
														<cfoutput>
															<tr>
																<td class="align-top" colspan="2"><div class="pl-5">#local.strReport.qryInvoices.revenueGL# <cfif len(local.strReport.qryInvoices.revenueGLCode)>(#local.strReport.qryInvoices.revenueGLCode#)</cfif></div></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.ageFut is 1>#dollarFormat(local.strReport.qryInvoices.invFut)#<cfelse>&nbsp;</cfif></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.age030 is 1>#dollarFormat(local.strReport.qryInvoices.inv030)#<cfelse>&nbsp;</cfif></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.age3160 is 1>#dollarFormat(local.strReport.qryInvoices.inv3160)#<cfelse>&nbsp;</cfif></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.age6190 is 1>#dollarFormat(local.strReport.qryInvoices.inv6190)#<cfelse>&nbsp;</cfif></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.age91120 is 1>#dollarFormat(local.strReport.qryInvoices.inv91120)#<cfelse>&nbsp;</cfif></td>
																<td class="align-top text-right"><cfif local.strReport.qryInvoices.age120over is 1>#dollarFormat(local.strReport.qryInvoices.inv120over)#<cfelse>&nbsp;</cfif></td>
															</tr>
														</cfoutput>
													</cfif>
												</cfoutput>
											</cfif>
											</tbody>
										</table>
									</div>
								</div>
							</cfoutput>

						<cfelseif local.frmGroupBy eq "member">
							<cfoutput>
							<div class="card card-box mb-3">
								<div class="card-body p-2">
									<table class="table table-sm table-borderless">
									<thead>
										<tr>
											<cfif local.frmShowPhotos eq 1><th class="text-left"></th></cfif>
											<th class="text-left">Member</th>
											<th class="text-right">Due</th>
											<th class="text-right">Future</th>
											<th class="text-right">0-30</th>
											<th class="text-right">31-60</th>
											<th class="text-right">61-90</th>
											<th class="text-right">91-120</th>
											<th class="text-right">120+</th>
										</tr>
									</thead>
									<tbody>
									</cfoutput>

									<cfoutput query="local.strReport.qryInvoices" group="memberId">
										<cfif local.strReport.qryInvoices.rowNum is *********>
											<cfif local.frmDisplay eq "summary">
												<tr>
													<cfif local.frmShowPhotos eq 1><td></td></cfif>
													<td class="font-weight-bold"><br/>#local.strReport.qryInvoices.lastname#</td>
													<td></td>
													<td class="text-right font-weight-bold">Future<br/>#dollarFormat(local.strReport.qryInvoices.invFut)#</td>
													<td class="text-right font-weight-bold">0-30<br/>#dollarFormat(local.strReport.qryInvoices.inv030)#</td>
													<td class="text-right font-weight-bold">31-60<br/>#dollarFormat(local.strReport.qryInvoices.inv3160)#</td>
													<td class="text-right font-weight-bold">61-90<br/>#dollarFormat(local.strReport.qryInvoices.inv6190)#</td>
													<td class="text-right font-weight-bold">91-120<br/>#dollarFormat(local.strReport.qryInvoices.inv91120)#</td>
													<td class="text-right font-weight-bold">120+<br/>#dollarFormat(local.strReport.qryInvoices.inv120over)#</td>
												</tr>
											<cfelse>
												<tr>
													<cfif local.frmShowPhotos eq 1><td></td></cfif>
													<td class="font-weight-bold">#local.strReport.qryInvoices.lastname#</td>
													<td></td>
													<td class="text-right font-weight-bold">Future</td>
													<td class="text-right font-weight-bold">0-30</td>
													<td class="text-right font-weight-bold">31-60</td>
													<td class="text-right font-weight-bold">61-90</td>
													<td class="text-right font-weight-bold">91-120</td>
													<td class="text-right font-weight-bold">120+</td>
												</tr>
												<cfoutput>
													<tr valign="top">
														<cfif local.frmShowPhotos eq 1><td></td></cfif>
														<td class="font-weight-bold"><div class="pl-5">#local.strReport.qryInvoices.revenueGL# <cfif len(local.strReport.qryInvoices.revenueGLCode)>(#local.strReport.qryInvoices.revenueGLCode#)</cfif></div></td>
														<td></td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.invFut)#</td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv030)#</td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv3160)#</td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv6190)#</td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv91120)#</td>
														<td class="text-right font-weight-bold">#dollarFormat(local.strReport.qryInvoices.inv120over)#</td>
													</tr>
												</cfoutput>
											</cfif>
										<cfelse>
											<tr>
												<cfif local.frmShowPhotos is 1>
													<td class="w-5 align-top">
														<cfif local.strReport.qryInvoices.hasMemberPhotoThumb is 1>
															<cfif arguments.reportAction eq "screen">
																<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
															<cfelse>
																<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.strReport.qryInvoices.MemberNumber)#.jpg">
															</cfif>
														<cfelse>
															<cfif arguments.reportAction eq "screen">
																<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
															<cfelse>
																<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
															</cfif>
														</cfif>
													</td>
												</cfif>
												<td class="align-top">
													<div class=" <cfif local.frmShowPhotos is 1>pl-3</cfif>">
														<cfif arguments.reportAction eq "screen" and len(local.memberLink)>
															<a href="#local.memberLink#&memberid=#local.strReport.qryInvoices.memberId#&tab=transactions" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></a><br/>
														<cfelse>
															<div class="font-weight-bold"><cfif local.frmShowMemberNumber is 1>#local.strReport.qryInvoices["Extended MemberNumber"]#<cfelse>#local.strReport.qryInvoices["Extended Name"]#</cfif></div>
														</cfif>
														<cfif local.frmShowCompany is 1 AND len(local.strReport.qryInvoices.company)>#local.strReport.qryInvoices.company#<br/></cfif>
														<cfloop query="local.qryOutputFields">
															<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
																<cfset local.AddrToShow = local.strReport.qryInvoices[local.qryOutputFields.dbfield][local.strReport.qryInvoices.currentrow]>
																<cfloop condition="Find(', , ',local.AddrToShow)">
																	<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
																</cfloop>
																<cfif len(local.AddrToShow)>
																	#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
																</cfif>
															</cfif>
														</cfloop>
														<cfloop query="local.qryOutputFieldsForLoop">
															<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
																#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow])#<br/>
															<cfelseif len(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldLabel][local.strReport.qryInvoices.currentrow])>
																<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
																	#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow], "m/d/yyyy")#<br/>
																<cfelse>
																	#local.qryOutputFieldsForLoop.fieldlabel#: #local.strReport.qryInvoices[local.qryOutputFieldsForLoop.fieldlabel][local.strReport.qryInvoices.currentrow]#<br/>
																</cfif>
															</cfif>
														</cfloop>
													</div>
												</td>
											</tr>
											<cfif local.frmDisplay eq "summary">
												<cfoutput>
												<tr valign="top">
													<cfif local.frmShowPhotos eq 1><td></td></cfif>
													<td><div class="pl-3">#local.strReport.qryInvoices.invoiceNumber#</div></td>
													<td class="text-right">#dateformat(local.strReport.qryInvoices.dateDue,'m/d/yy')#</td>
													<td class="text-right"><cfif local.strReport.qryInvoices.ageFut is 1>#dollarFormat(local.strReport.qryInvoices.invFut)#<cfelse>&nbsp;</cfif></td>
													<td class="text-right"><cfif local.strReport.qryInvoices.age030 is 1>#dollarFormat(local.strReport.qryInvoices.inv030)#<cfelse>&nbsp;</cfif></td>
													<td class="text-right"><cfif local.strReport.qryInvoices.age3160 is 1>#dollarFormat(local.strReport.qryInvoices.inv3160)#<cfelse>&nbsp;</cfif></td>
													<td class="text-right"><cfif local.strReport.qryInvoices.age6190 is 1>#dollarFormat(local.strReport.qryInvoices.inv6190)#<cfelse>&nbsp;</cfif></td>
													<td class="text-right"><cfif local.strReport.qryInvoices.age91120 is 1>#dollarFormat(local.strReport.qryInvoices.inv91120)#<cfelse>&nbsp;</cfif></td>
													<td class="text-right"><cfif local.strReport.qryInvoices.age120over is 1>#dollarFormat(local.strReport.qryInvoices.inv120over)#<cfelse>&nbsp;</cfif></td>
												</tr>
												</cfoutput>
											<cfelse>
												<cfoutput group="invoiceID">
													<tr>
														<cfif local.frmShowPhotos eq 1><td></td></cfif>
														<td class="font-weight-bold"><div class="pl-3">#local.strReport.qryInvoices.invoiceNumber#</div></td>
													</tr>
													<cfoutput>
														<tr>
															<cfif local.frmShowPhotos eq 1><td></td></cfif>
															<td class="align-top"><div class="pl-5">#local.strReport.qryInvoices.revenueGL# <cfif len(local.strReport.qryInvoices.revenueGLCode)>(#local.strReport.qryInvoices.revenueGLCode#)</cfif></div></td>
															<td class="align-top text-right">#dateformat(local.strReport.qryInvoices.dateDue,'m/d/yy')#</td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.ageFut is 1>#dollarFormat(local.strReport.qryInvoices.invFut)#<cfelse>&nbsp;</cfif></td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.age030 is 1>#dollarFormat(local.strReport.qryInvoices.inv030)#<cfelse>&nbsp;</cfif></td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.age3160 is 1>#dollarFormat(local.strReport.qryInvoices.inv3160)#<cfelse>&nbsp;</cfif></td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.age6190 is 1>#dollarFormat(local.strReport.qryInvoices.inv6190)#<cfelse>&nbsp;</cfif></td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.age91120 is 1>#dollarFormat(local.strReport.qryInvoices.inv91120)#<cfelse>&nbsp;</cfif></td>
															<td class="align-top text-right"><cfif local.strReport.qryInvoices.age120over is 1>#dollarFormat(local.strReport.qryInvoices.inv120over)#<cfelse>&nbsp;</cfif></td>
														</tr>
													</cfoutput>
												</cfoutput>
											</cfif>
										</cfif>
									</cfoutput>
									
									<cfoutput>
									</tbody>
									</table>
								</div>
							</div>
							</cfoutput>
						</cfif>
					</cfoutput>
				</cfif>
				<cfoutput>
					#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
					#showRawSQL(reportAction=arguments.reportAction, qryName="local.strReport.qryInvoices", strQryResult=local.strReport.qryInvoicesResult)#
					</div>
				</cfoutput>
			</cfsavecontent>

		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>
	
		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.frmGroupBy = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmGroupBy/text())")>

		<cftry>
			<cfset local.strReport = generateData(qryReportInfo=arguments.qryReportInfo, reportAction=arguments.reportAction, siteCode=arguments.siteCode)>

			<cfset local.arrInitialReportSort = arrayNew(1)>
			<cfif local.frmGroupBy EQ 'invoice'>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Invoice', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='LastName', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='FirstName', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='MemberNumber', dir='asc' })>
			<cfelse>
				<cfset arrayAppend(local.arrInitialReportSort, { field='LastName', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='FirstName', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='MemberNumber', dir='asc' })>
				<cfset arrayAppend(local.arrInitialReportSort, { field='Invoice', dir='asc' })>
			</cfif>

			<cfset local.strReportQry = { qryReportFields=local.strReport.qryInvoices, strQryResult=local.strReport.qryInvoicesResult }>
			<cfset local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML)>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="generateData" access="package" output="false" returntype="struct" hint="also invoked from pending payments report">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="siteCode" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.reportAction = arguments.reportAction>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>

		<cfset local.frmDateTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdateto/text())")>
		<cfset local.frmDisplay = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdisplay/text())")>
		<cfset local.frmGroupBy = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmgroupby/text())")>

		<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
			reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
			existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
		<cfif local.strSQLPrep.ruleErr>
			<cfthrow message="There was an error in the report criteria.">
		</cfif>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.strReturn.qryInvoices" result="local.strReturn.qryInvoicesResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int, @endDate datetime, @selectsql varchar(max), @outputFieldsXML xml;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.mc_siteInfo.orgid)#">;
				SET @enddate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmDateTo#">;
				
				<cfif len(local.strSQLPrep.RuleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL 
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..##tmpINVAGEReport') IS NOT NULL 
					DROP TABLE ##tmpINVAGEReport;
				IF OBJECT_ID('tempdb..##tmpARReport') IS NOT NULL 
					DROP TABLE ##tmpARReport;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				CREATE TABLE ##tmpARReport (invoiceID int, revenueGLAccountID int, AREnd decimal(18,2), openInvoices int);
				CREATE TABLE ##tmpINVAGEReport (invoiceid int, invoiceNumber varchar(30), dateCreated datetime, dateBilled datetime, 
					dateDue datetime, revenueGLAID int, revenueGL varchar(max),	revenueGLCode varchar(max), memberId int, 
					firstName varchar(75), lastName varchar(75), membernumber varchar(50), company varchar(200), hasMemberPhotoThumb bit, AREnd decimal(18,2), 
					ageFut bit, age030 bit, age3160 bit, age6190 bit, age91120 bit, age120over bit, openInvoices int);
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

				<cfif local.frmDisplay eq "detail">
					INSERT INTO ##tmpARReport (invoiceID, revenueGLAccountID, AREnd, openInvoices)
					EXEC dbo.tr_report_baseAR @orgID, @enddate, null, 'invgl';

					CREATE CLUSTERED INDEX IX_tmpARReport_invGLAID ON ##tmpARReport (invoiceID asc, revenueGLAccountID asc);

					IF OBJECT_ID('tempdb..##tblGL') IS NOT NULL 
						DROP TABLE ##tblGL;
					CREATE TABLE ##tblGL (GLAccountID int PRIMARY KEY, AccountCode varchar(200), thePathExpanded varchar(max));
					
					insert into ##tblGL
					select GLAccountID, AccountCode, thePathExpanded
					from dbo.fn_getRecursiveGLAccounts(@orgID);
				
					insert into ##tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, revenueGLAID, revenueGL, 
						revenueGLCode, memberId, firstName, lastName, memberNumber, company, hasMemberPhotoThumb, AREnd, ageFut, age030, age3160, 
						age6190, age91120, age120over, openInvoices)
					select i.invoiceID, i.fullInvoiceNumber as invoiceNumber, 
						i.dateCreated, i.dateBilled, i.dateDue, gl.glAccountID, gl.thePathExpanded, gl.accountCode,
						m2.memberID, m2.firstName, m2.lastName, m2.membernumber, m2.company, m2.hasMemberPhotoThumb, tmp.AREnd,
						ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
						age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
						age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
						age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
						age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
						age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
						tmp.openInvoices
					from ##tmpARReport as tmp
					inner join dbo.tr_invoices AS i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = i.assignedToMemberID
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
					inner join ##tblGL as gl on gl.glAccountID = tmp.revenueGLAccountID;

					IF OBJECT_ID('tempdb..##tblGL') IS NOT NULL 
						DROP TABLE ##tblGL;
				<cfelseif local.frmDisplay eq "summary">
					INSERT INTO ##tmpARReport (invoiceID, AREnd, openInvoices)
					EXEC dbo.tr_report_baseAR @orgID, @enddate, null, 'inv';

					CREATE CLUSTERED INDEX IX_tmpARReport_invID ON ##tmpARReport (invoiceID asc);

					insert into ##tmpINVAGEReport (invoiceid, invoiceNumber, dateCreated, dateBilled, dateDue, memberId, 
						firstname, lastname, memberNumber, company, hasMemberPhotoThumb, AREnd, ageFut, age030, age3160, age6190, age91120, age120over, openInvoices)
					select i.invoiceID, i.fullInvoiceNumber as invoiceNumber, 
						i.dateCreated, i.dateBilled, i.dateDue, m2.memberID, m2.firstname, m2.lastname, m2.membernumber,
						m2.company, m2.hasMemberPhotoThumb, tmp.AREnd,
						ageFut = case when datediff(dd,i.dateDue,@enddate) < 0 then 1 else 0 end,
						age030 = case when datediff(dd,i.dateDue,@enddate) between 0 and 30 then 1 else 0 end,
						age3160 = case when datediff(dd,i.dateDue,@enddate) between 31 and 60 then 1 else 0 end,
						age6190 = case when datediff(dd,i.dateDue,@enddate) between 61 and 90 then 1 else 0 end,
						age91120 = case when datediff(dd,i.dateDue,@enddate) between 91 and 120 then 1 else 0 end,
						age120over = case when datediff(dd,i.dateDue,@enddate) > 120 then 1 else 0 end,
						tmp.openInvoices
					from ##tmpARReport as tmp
					inner join dbo.tr_invoices AS i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = i.assignedToMemberID
					inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
					<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>;
				</cfif>
				
				-- get fieldset data and set back to snapshot because proc ends in read committed
				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
					@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
					@existingFields='memberId, m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
					@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
					@membersTableName='##tmpINVAGEReport', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
					@mode='<cfif local.reportAction eq 'customcsv'>export<cfelse>report</cfif>', @outputFieldsXML=@outputFieldsXML OUTPUT;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif local.reportAction eq 'customcsv'>
					SELECT 
						<cfif local.frmGroupBy EQ 'member'>
							FirstName, LastName, MemberNumber, Company, invoiceNumber as [Invoice], 
						<cfelse>
							invoiceNumber as [Invoice], FirstName, LastName, MemberNumber, Company, 
						</cfif>						
						convert(varchar(10), dateCreated, 101) as [Date Created], 
						convert(varchar(10), dateBilled, 101) as [Date Billed], 
						convert(varchar(10), dateDue, 101) as [Date Due],
						<cfif local.frmDisplay EQ 'detail'>
							revenueGL as [Revenue Account], revenueGLCode as [Revenue Account Code],
						</cfif>
						case when ageFut = 1 then AREnd else null end as [Due Future],
						case when age030 = 1 then AREnd else null end as [0-30 Days],
						case when age3160 = 1 then AREnd else null end as [31-60 Days],
						case when age6190 = 1 then AREnd else null end as [61-90 Days],
						case when age91120 = 1 then AREnd else null end as [91-120 Days],
						case when age120over = 1 then AREnd else null end as [120+ Days],
						tmp.*
					INTO ###local.tempTableName#
					FROM ##tmpINVAGEReport rep
					INNER JOIN ##tmpMembersFS as tmp on tmp.memberID = rep.memberID;

					#generateFinalBCPTable(tblName="###local.tempTableName#", dropFields="memberID")#
				<cfelse>					
					SELECT *, CASE WHEN rowNum = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
					FROM (
						SELECT 
							rep.invoiceID, rep.invoiceNumber, rep.dateBilled, rep.dateDue, rep.firstname,
							rep.lastname, rep.membernumber, rep.company, rep.hasMemberPhotoThumb, rep.revenueGLAID, rep.revenueGL,
							rep.revenueGLCode, rep.ageFut, rep.age030, rep.age3160, rep.age6190, rep.age91120,
							rep.age120over, 
							CASE WHEN rep.ageFut = 1 THEN rep.AREnd ELSE NULL END AS invFut,
							CASE WHEN rep.age030 = 1 THEN rep.AREnd ELSE NULL END AS inv030,
							CASE WHEN rep.age3160 = 1 THEN rep.AREnd ELSE NULL END AS inv3160,
							CASE WHEN rep.age6190 = 1 THEN rep.AREnd ELSE NULL END AS inv6190,
							CASE WHEN rep.age91120 = 1 THEN rep.AREnd ELSE NULL END AS inv91120,
							CASE WHEN rep.age120over = 1 THEN rep.AREnd ELSE NULL END AS inv120over, 
							rep.openInvoices, ROW_NUMBER() OVER (ORDER BY 
							<cfif local.frmGroupBy eq "invoice">
								CASE WHEN rep.invoiceID is null THEN 1 else 0 END, dateDue, invoiceNumber
							<cfelseif local.frmGroupBy eq "member">
								CASE WHEN rep.invoiceID is null THEN 1 else 0 END, rep.lastname, rep.firstname, rep.membernumber, rep.memberId, rep.invoiceNumber, rep.revenueGL
							</cfif>
							) AS rowNum,
							tmp.*
						FROM ##tmpINVAGEReport rep
						INNER JOIN ##tmpMembersFS AS tmp 
						ON tmp.memberID = rep.memberId
						
						UNION ALL
						
						SELECT tmpouter.*, tmp.* FROM (
							SELECT 
								NULL AS invoiceID, NULL AS invoiceNumber, NULL AS dateBilled, NULL AS dateDue, 
								NULL AS firstname, 'Report Total' AS lastname, NULL AS membernumber, 
								NULL AS company, NULL AS hasMemberPhotoThumb, rep.revenueGLAID, rep.revenueGL, rep.revenueGLCode, 
								1 AS ageFut, 1 AS age030, 1 AS age3160, 1 AS age6190, 1 AS age91120, 1 AS age120over, 
								SUM(CASE WHEN rep.ageFut = 1 THEN rep.AREnd ELSE 0 END) AS invFut,
								SUM(CASE WHEN rep.age030 = 1 THEN rep.AREnd ELSE 0 END) AS inv030,
								SUM(CASE WHEN rep.age3160 = 1 THEN rep.AREnd ELSE 0 END) AS inv3160,
								SUM(CASE WHEN rep.age6190 = 1 THEN rep.AREnd ELSE 0 END) AS inv6190,
								SUM(CASE WHEN rep.age91120 = 1 THEN rep.AREnd ELSE 0 END) AS inv91120,
								SUM(CASE WHEN rep.age120over = 1 THEN rep.AREnd ELSE 0 END) AS inv120over, 
								NULL AS openInvoices, ********* AS rowNum
							FROM ##tmpINVAGEReport rep	
							GROUP BY rep.revenueGLAID, rep.revenueGL, rep.revenueGLCode	) as tmpouter
							OUTER APPLY (SELECT TOP 1 * FROM ##tmpMembersFS WHERE memberID = 0) tmp
						) as finalData
					ORDER BY rowNum;
				</cfif>
				
				<cfif len(local.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>

				IF OBJECT_ID('tempdb..##tmpINVAGEReport') IS NOT NULL 
					DROP TABLE ##tmpINVAGEReport;
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL 
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..##tmpARReport') IS NOT NULL 
					DROP TABLE ##tmpARReport;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		local.strFields = structNew();
		local.strFields.frmdateTo = { label="As of Date", value=arguments.event.getValue('frmdateTo','') };
		local.strFields.frmDisplay = { label="Display Option", value=arguments.event.getValue('frmDisplay','') };
		local.strFields.frmGroupBy = { label="Group By", value=arguments.event.getValue('frmGroupBy','') };

		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>