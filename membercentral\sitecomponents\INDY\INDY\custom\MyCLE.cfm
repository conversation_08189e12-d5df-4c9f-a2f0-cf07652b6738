<cfscript>
variables.applicationReservedURLParams 	= "";
local.customPage.baseURL = "/?#getBaseQueryString(false)#";

arguments.event.paramValue('ca','showList');
arguments.event.paramValue('periodStartDate','1/1/#year(now())-1#');
arguments.event.paramValue('periodEndDate',dateFormat(now(),'m/d/yyyy'));
arguments.event.paramValue('membernumber','');

local.periodStartDate = arguments.event.getValue('periodStartDate');
local.periodEndDate = arguments.event.getValue('periodEndDate');
local.membernumber = arguments.event.getValue('membernumber');
</cfscript>

<cfif arguments.event.getValue('ca') eq "viewCert">

	<cfscript>
	// get encrypted registrantid
	local.encryptedRID = arguments.event.getValue('rid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedRID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedRID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedRID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.events.certificate").generateCertificate(registrantID=local.decryptedRID);
	</cfscript>
	
	<!--- redirect to pdf --->
	<cfif len(local.strCertificate.certificateURL)>
		<cflocation url="#local.strCertificate.certificateURL#" addtoken="no">
	<cfelse>
		<cflocation url="#local.customPage.baseURL#&ca=certErr&mode=direct" addtoken="no">
	</cfif>

<cfelseif arguments.event.getValue('ca') eq "viewCertStore">

	<cfscript>
	// get encrypted affirmationID
	local.encryptedAID = arguments.event.getValue('aid','');
	
	// change xPcmKx to % (case sensitive), decode, fromBase64, and decrypt
	try { local.decryptedAID = val(decrypt(toString(toBinary(URLDecode(replace(local.encryptedAID,"xPcmKx","%","ALL")))),"TRiaL_SMiTH")); } 
	catch (any e) { local.decryptedAID = 0; }
	
	// generate certificate for registrantID
	local.strCertificate = CreateObject("component","model.admin.store.certificate").generateCertificate(affirmationID=local.decryptedAID);
	local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath=local.strCertificate.certificatePath, displayName=ListLast(local.strCertificate.certificatePath,"/"), deleteSourceFile=1);
	</cfscript>

	<cfif not local.docResult>
		<cflocation url="#local.customPage.baseURL#&ca=certErr&mode=direct" addtoken="no">
	</cfif>	

<cfelseif arguments.event.getValue('ca') eq "certErr">

	<cfoutput>
	<h4>Your IndyBar CLE History</h4></br>
	<br/>
	<div>
		<b>Sorry...</b>, we were unable to generate a certificate at this time.<br/><br/>
		If you continue to see this message, please contact INDYBAR for assistance.
	</div>
	</cfoutput>

<cfelse>

	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventsAttended">
		SET NOCOUNT ON;

		declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

		select e.eventid, r.registrantID, et.startTime as eventStart, cl.contentTitle as eventName, e.reportCode, 
			regFee.totalRegFee-regFee.regFeePaid as amountDue, 
			case when dateDiff(d,et.endTime,getdate()) >= 28 then 1 else 0 end as Past28Days,
			cast(isnull((
				select req.creditValueAwarded, isnull(ast.ovTypeName,at.typeName) as creditType
				from dbo.crd_requests as req
				inner join dbo.crd_offeringTypes as offt on offt.offeringTypeID = req.offeringTypeID
				inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = offt.ASTID
				inner join dbo.crd_authorityTypes as at on at.typeID = ast.typeID
				where req.registrantID = r.registrantID
				and req.creditAwarded = 1
				for XML AUTO, ROOT('credits')
			),'<credits/>') as xml) as awardedCreditsXML,
			cast(isnull((
				select distinct i.invoiceID, i.invoiceCode, i.fullInvoiceNumber as invoiceNumber
				from dbo.fn_ev_registrantTransactions(r.registrantID) as rt
				inner join dbo.tr_invoiceTransactions as it on it.orgID = rt.ownedByOrgID and it.transactionID = rt.transactionID
				inner join dbo.tr_invoices as i on i.invoiceID = it.invoiceID
				inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
				where ins.status in ('Closed','Delinquent')
				and it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount-it.cache_pendingPaymentAllocatedAmount > 0
				and dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) = 1
				for XML AUTO, ROOT('invoices')
			),'<invoices/>') as xml) as invoicesXML
		from dbo.ev_registrants as r
		inner join dbo.ams_members as m1 on m1.memberID = r.memberID
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
				and m.orgID = @orgID
				AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR"> 	
		<cfelse>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
				and m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		inner join dbo.ev_registration as evr on evr.registrationID = r.registrationID and evr.siteID = r.recordedOnSiteID
		inner join dbo.ev_events as e on e.eventid = evr.eventid and e.siteID = evr.siteID
		inner join dbo.ev_times as et on et.eventID = e.eventID and et.timeZoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
		inner join dbo.cms_contentLanguages as cl on cl.contentID = e.eventContentID and cl.languageID = 1
		inner join dbo.crd_offerings as co on co.eventID = e.eventID
			and et.endTime between co.offeredStartDate and co.offeredEndDate
			and co.offeredStartDate >= <cfqueryparam value="#local.periodStartDate#" cfsqltype="cf_sql_date">
			and co.offeredEndDate <= <cfqueryparam value="#local.periodEndDate# 23:59:59.997" cfsqltype="cf_sql_timestamp">
		OUTER APPLY dbo.fn_ev_totalRegFeeAndPaid(@orgID, r.registrantID) as regFee
		where r.status = 'A'
		and r.attended = 1
		and co.statusID = 4
		order by et.startTime desc, e.eventid;
	</cfquery>

	<cfset local.qryEventsAttendedTotalsTemp = QueryNew("creditYear,creditAmount,creditType","integer,double,varchar")>
	<cfloop query="local.qryEventsAttended">
		<cfset local.arrCredits = xmlSearch(local.qryEventsAttended.awardedCreditsXML,"/credits/req")>
		<cfloop array="#local.arrCredits#" index="local.thisAward">
			<cfif QueryAddRow(local.qryEventsAttendedTotalsTemp)>
				<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditYear",year(local.qryEventsAttended.eventStart))>
				<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditAmount",local.thisAward.xmlAttributes.creditValueAwarded)>
				<cfset querySetCell(local.qryEventsAttendedTotalsTemp,"creditType",local.thisAward.xmlAttributes.creditType)>
			</cfif>
		</cfloop>
	</cfloop>

	<cfquery name="local.qryEventsAttendedTotals" dbtype="query">
		select creditYear, creditType, sum(creditAmount) as creditAmount
		from [local].qryEventsAttendedTotalsTemp
		group by creditYear, creditType
		order by creditYear DESC, creditType
	</cfquery>


	<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryStorePurchases">
		SET NOCOUNT ON;

		declare @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
		declare @storeID int;

		SELECT @storeID = storeID
		FROM dbo.store
		WHERE siteID = @siteID;

		select req.affirmationID, cl.contentTitle as productName, p.productID, aff.dateClaimed, 
			req.creditValueAwarded, isnull(ast.ovTypeName,at.typeName) as creditType,
			case when dateDiff(d,aff.dateClaimed,getdate()) >= 28 then 1 else 0 end as Past28Days
		from dbo.crd_requests as req
		inner join dbo.crd_offeringTypes as offt on offt.offeringTypeID = req.offeringTypeID
		inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = offt.ASTID
		inner join dbo.crd_authorityTypes as at on at.typeID = ast.typeID
		inner join dbo.crd_affirmations as aff on aff.affirmationID = req.affirmationID 
			and aff.status = 'A'
			and aff.dateClaimed >= <cfqueryparam value="#local.periodStartDate#" cfsqltype="cf_sql_date">
			and aff.dateClaimed <= <cfqueryparam value="#local.periodEndDate# 23:59:59.997" cfsqltype="cf_sql_timestamp">
		inner join dbo.crd_affirmationTypes as afft on afft.affirmationTypeID = aff.affirmationTypeID and afft.affirmationType = 'staff-approved'
		inner join dbo.ams_members as m1 on m1.memberID = aff.assignToMemberID
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
				and m.orgID = #arguments.event.getValue('mc_siteinfo.orgID')# 
				AND m.membernumber=<cfqueryparam value="#local.membernumber#" cfsqltype="CF_SQL_VARCHAR"> 	
		<cfelse>
			inner join dbo.ams_members as m on m.memberID = m1.activeMemberID
				and m.memberID = <cfqueryparam value="#session.cfcUser.memberData.memberID#" cfsqltype="CF_SQL_INTEGER">
		</cfif>
		inner join dbo.store_ProductFormats as pf on pf.formatID = aff.productFormatID
		inner join dbo.store_products as p on p.storeID = @storeID and p.itemID = pf.itemID
		inner join dbo.cms_contentLanguages as cl on cl.contentID = p.productContentID and cl.languageID = 1
		where req.creditAwarded = 1
		order by aff.dateClaimed desc, cl.contentTitle;
	</cfquery>

	<cfset local.qryStorePurchasesTotalsTemp = QueryNew("creditYear,creditAmount,creditType","integer,double,varchar")>
	<cfloop query="local.qryStorePurchases">
		<cfif QueryAddRow(local.qryStorePurchasesTotalsTemp)>
			<cfset querySetCell(local.qryStorePurchasesTotalsTemp,"creditYear",year(local.qryStorePurchases.dateClaimed))>
			<cfset querySetCell(local.qryStorePurchasesTotalsTemp,"creditAmount",local.qryStorePurchases.creditValueAwarded)>
			<cfset querySetCell(local.qryStorePurchasesTotalsTemp,"creditType",local.qryStorePurchases.creditType)>
		</cfif>
	</cfloop>

	<cfquery name="local.qryStorePurchasesTotals" dbtype="query">
		select creditYear, creditType, sum(creditAmount) as creditAmount
		from [local].qryStorePurchasesTotalsTemp
		group by creditYear, creditType
		order by creditYear DESC, creditType
	</cfquery>



	<cfsavecontent variable="local.dataHead">
		<cfoutput>
		<script language="javascript">
			function viewEVCertificate(rid) {
				var certURL = '#local.customPage.baseURL#&ca=viewCert&mode=stream&rid=' + rid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			function viewStoreCert(aid) {
				var certURL = '#local.customPage.baseURL#&ca=viewCertStore&mode=stream&aid=' + aid;
				window.open(certURL,'ViewCertificate','width=990,height=500');
			}
			$(function(){
				mca_setupDatePickerRangeFields('periodStartDate','periodEndDate');
			});

			function _FB_validateForm(){
				<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
					if ($('##membernumber').val() == '') {
						alert('Must enter MemberNumber before you can filter report.');
						return false;
					}
				</cfif>
				return true;
			}
		</script>
		<style type="text/css">
			##periodStartDate, ##periodEndDate { background-image:url("/assets/common/images/calendar/monthView.gif"); background-position:right center; background-repeat:no-repeat; }
			.creditTable td, .creditTable th { border:1px solid ##707070; border-collapse:collapse; padding:4px; }
			.balTable td { border:0px; padding:2px; }
			.padTop10{
				padding-top:10px !important;
			}
			.titleLabel{
				display:none ;
			}
			
			@media only screen and (max-width: 768px) {
				.toWrap,.filterWrap {
					text-align:left!important;
				}
				
				/* Force table to not be like tables anymore */
				.contentWrap table, .contentWrap thead, .contentWrap tbody, .contentWrap th, .contentWrap td, .contentWrap tr {
					display: block;
				}

				/* Hide table headers (but not display: none;, for accessibility) */
				.contentWrap thead tr {
					position: absolute;
					top: -9999px;
					left: -9999px;
				}

				.contentWrap tr { border: 1px solid ##ccc; }

				.contentWrap td {
					/* Behave  like a "row" */
					border: none;
					border-bottom: 1px solid ##eee;
					position: relative;
					//padding-left: 50%;
				}

				.contentWrap td:before {
					/* Now like a table header */
					position: absolute;
					/* Top/left values mimic padding */
					top: 6px;
					left: 6px;
					width: 45%;
					padding-right: 10px;
					white-space: nowrap;
				}
				.tableHead,.blankTd{
					display:none !important;
				}
				.titleLabel{
					display:block !important;
				}
				.creditTypeDisp{
					padding-left: 20px!important;
				
			}
		</style>
		</cfoutput>
	</cfsavecontent>
	<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
	<cfform class="form-horizontal" method="POST" action="#local.customPage.baseURL#" name="frmCLE" onsubmit="return _FB_validateForm();">
		<cfoutput>
		<div class="row-fluid hideInPrint" >
			<button class="btn btn-default pull-right" type="button" onClick="window.print();" title="Print certificate"><i class="icon-print"></i> Print</button>
		</div>
		<div class="row-fluid hideInPrint" >
			<h4>#session.cfcuser.memberdata.firstname# #session.cfcuser.memberdata.middlename# #session.cfcuser.memberdata.lastname# #session.cfcuser.memberdata.suffix# IndyBar CLE History</h4>
		</div>
		<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) or application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
			<div class="row-fluid padTop10 hideInPrint">
				<div class="span2">
					<label  for="membernumber"><strong>MemberNumber:</strong></label>
				</div>
				<div class="span4">
					<cfinput style="width:100%" type="text" name="membernumber" id="membernumber"  value="#local.membernumber#" placeholder="Must enter MemberNumber.">
				</div>
			</div>
		</cfif>
		<div class="row-fluid padTop10 hideInPrint">
			<div class="span2">
				<label for="membernumber"><strong>Select your dates:</strong></label>
			</div>
			<div class="span2">
				<cfinput class="form-control" style="width:100%" type="text" name="periodStartDate" id="periodStartDate" value="#local.periodStartDate#" > 
			</div>
			<div class="span1 text-center toWrap" style="padding-top: 5px;">
				&nbsp;&nbsp;to&nbsp;
			</div>
			<div class="span2">
				<cfinput class="form-control" style="width:100%" type="text" name="periodEndDate" id="periodEndDate" value="#local.periodEndDate#" >
			</div>
			<div class="span4" >
				<button class="btn btn-default" type="submit">Filter Report</button>
			</div>
		</div>
		<div class="row-fluid padTop10" >
			<p>Your IndyBar course registrations for the dates selected appear below.</p>
			<ul>
				<li>For courses needing your action, a comment will appear in the Notes section along with a link to take you to the area requiring attention. </li>
				<li>If a Note of <i>In Processing</i> appears, course is pending staff action. Please check back at a later date.</li>
				<li>When credit has been awarded and reported to the Indiana CLE Commission, <i class="icon-certificate" style="font-size:16px;"></i> icon appears. Click the icon to open the certificate details.</li>
				<li>Need to report credit to another licensing body? Use the certificate to do so.</li>
			</ul>
		
		</div>
		</cfoutput>
	</cfform>

	<cfoutput>
	<div class="row-fluid padTop10 contentWrap" >
	<h5>Conferences & Events</h5></cfoutput>
	<cfif local.qryEventsAttended.recordcount>
		<cfoutput>
		<table class="creditTable">
		<tr class="tableHead">
			<th colspan="2"><cfoutput>Credit Totals for (#DateFormat(local.periodStartDate,"m/d/yyyy")# � #DateFormat(local.periodEndDate,"m/d/yyyy")#)</cfoutput></th>
		</tr>
		</cfoutput>
		<cfset local.yearsShown = "">
		<cfset local.indexVal = 0>
		<cfoutput query="local.qryEventsAttendedTotals">
			<tr>
				<cfif  local.indexVal eq 0>
					<td class="titleLabel"><strong>Credit Totals for (#DateFormat(local.periodStartDate,"m/d/yyyy")# � #DateFormat(local.periodEndDate,"m/d/yyyy")#)</strong></td>	
					<cfset local.indexVal = 1>
				</cfif >
				
				<cfif NOT listFind(local.yearsShown,local.qryEventsAttendedTotals.creditYear)>
					<cfset local.yearsShown = listAppend(local.yearsShown,local.qryEventsAttendedTotals.creditYear)>
					<td>#local.qryEventsAttendedTotals.creditYear#</td>
				<cfelse>
					<td class="blankTd">&nbsp;</td>
				</cfif>
				<td class="creditTypeDisp">#NumberFormat(local.qryEventsAttendedTotals.creditAmount,"0.99")# #local.qryEventsAttendedTotals.creditType#</td>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>
		<br/>
		<table class="creditTable" width="100%">
		<tr class="tableHead">
			<th width="5%">Date</th>
			<th width="43%">Title</th>
			<th width="10%">Report Code</th>
			<th width="20%" colspan="2">Credit Reported</th>
			<th width="22%">Notes</th>
		</tr>
		</cfoutput>
		<cfoutput query="local.qryEventsAttended">
			<cfset local.arrCredits = xmlSearch(local.qryEventsAttended.awardedCreditsXML,"/credits/req")>
			<tr>
				<td class="titleLabel"><strong>Date</strong></td>	
				<td>#dateformat(local.qryEventsAttended.eventStart,"m/d/yyyy")#</td>
				<td class="titleLabel"><strong>Title</strong></td>					
				<td>#HTMLEditFormat(local.qryEventsAttended.eventName)#</td>
				<td class="titleLabel"><strong>Report Code</strong></td>		
				<td>#HTMLEditFormat(local.qryEventsAttended.reportCode)#</td>
				<cfif arrayLen(local.arrCredits)>
					<cfset local.rID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryEventsAttended.registrantID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
					<td class="titleLabel"><strong>Credit Reported</strong></td>	
					<td width="30" align="center"><a href="javascript:viewEVCertificate('#local.rID#');"><i class="icon-certificate" style="font-size:16px;"></i></a></td>
					<td>
						<cfloop array="#local.arrCredits#" index="local.thisAward">
							#local.thisAward.xmlAttributes.creditValueAwarded# #local.thisAward.xmlAttributes.creditType#<br/>
						</cfloop>
					</td>	
					<td class="titleLabel"><strong>Notes</strong></td>		
					<cfif local.qryEventsAttended.Past28Days is 1>
						<td class="blankTd">&nbsp;</td>
					<cfelse>
						<td><i>In Processing</i></td>
					</cfif>
				<cfelseif local.qryEventsAttended.amountDue is 0>
					<td width="30" class="blankTd">&nbsp;</td>	
					<td class="titleLabel"><strong>Credit Reported</strong></td>	
					<td><b style="color:red;">No Credit Reported</b></td>
					<td class="titleLabel"><strong>Notes</strong></td>		
					<td><a href="mailto:<EMAIL>?subject=CLE Affidavit for #URLEncodedFormat(local.qryEventsAttended.eventName)#">Click Here</a> to E-mail Affidavit</td>
				<cfelse>
					<cfset local.arrInvoices = xmlSearch(local.qryEventsAttended.invoicesXML,"/invoices/i")>
					<td width="30" class="blankTd">&nbsp;</td>	
					<td class="titleLabel"><strong>Credit Reported</strong></td>	
					<td><b style="color:red;">No Credit Reported</b></td>
					<td class="titleLabel"><strong>Notes</strong></td>	
					<td>
						<table class="balTable">
						<tr valign="top"><td>Balance Due*:&nbsp;</td><td>
							<cfloop array="#local.arrInvoices#" index="local.thisInv">
								<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.thisInv.xmlAttributes.invoicenumber#|#right(GetTickCount(),5)#|#local.thisInv.xmlAttributes.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
								<a href="/invoices/#local.stInvEnc#" target="_blank">Pay Invoice #local.thisInv.xmlAttributes.invoiceNumber#</a><br/>
							</cfloop>
						</td></tr>
						</table>
					</td>
				</cfif>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>
		</div>
		</cfoutput>
	<cfelse>
		<cfoutput>Nothing to report during the selected period.</div></cfoutput>
	</cfif>

	<cfoutput><br/></cfoutput>

	<cfoutput>
	
	<div class="row-fluid padTop10 contentWrap" ><h5>Online Education</h5></cfoutput>
	<cfif local.qryStorePurchases.recordcount>
	<cfoutput>
		<table class="creditTable">				
		<tr class="tableHead">
			<th colspan="2"><cfoutput>Credit Totals for (#DateFormat(local.periodStartDate,"m/d/yyyy")# � #DateFormat(local.periodEndDate,"m/d/yyyy")#)</cfoutput></th>
		</tr>
		</cfoutput>
		<cfset local.yearsShown = "">
		<cfset local.indexVal = 0>
		<cfoutput query="local.qryStorePurchasesTotals">
			<tr>
				<cfif  local.indexVal eq 0>
					<td class="titleLabel"><strong>Credit Totals for (#DateFormat(local.periodStartDate,"m/d/yyyy")# � #DateFormat(local.periodEndDate,"m/d/yyyy")#)</strong></td>	
					<cfset local.indexVal = 1>
				</cfif >
				<cfif NOT listFind(local.yearsShown,local.qryStorePurchasesTotals.creditYear)>
					<cfset local.yearsShown = listAppend(local.yearsShown,local.qryStorePurchasesTotals.creditYear)>
					<td>#local.qryStorePurchasesTotals.creditYear#</td>
				<cfelse>
					<td>&nbsp;</td>
				</cfif>
				<td class="creditTypeDisp">#NumberFormat(local.qryStorePurchasesTotals.creditAmount,"0.99")# #local.qryStorePurchasesTotals.creditType#</td>
			</tr>
		</cfoutput>
		<cfoutput>
		</table>
		<br/>
		<table class="creditTable" width="100%">
		<tr  class="tableHead">
			<th width="5%">Date</th>
			<th width="43%">Title</th>
			<th width="10%">Product ID</th>
			<th width="20%" colspan="2">Credit Reported</th>
			<th width="22%">Notes</th>
		</tr>
		</cfoutput>
		<cfoutput query="local.qryStorePurchases" group="affirmationID">
			<cfset local.aID = Replace(URLEncodedFormat(ToBase64(Encrypt(local.qryStorePurchases.affirmationID,"TRiaL_SMiTH"))),"%","xPcmKx","ALL")>
			<tr>
				<td class="titleLabel"><strong>Date</strong></td>	
				<td>#dateformat(local.qryStorePurchases.dateClaimed,"m/d/yyyy")#</td>	
				<td class="titleLabel"><strong>Title</strong></td>	
				<td>#HTMLEditFormat(local.qryStorePurchases.productName)#</td>
				<td class="titleLabel"><strong>Report Code</strong></td>	
				<td>#HTMLEditFormat(local.qryStorePurchases.productID)#</td>
				<td class="titleLabel"><strong>Credit Reported</strong></td>	
				<td width="30" align="center">
					<!--- commented until store cert is ready
					<a href="javascript:viewStoreCert('#local.aID#')"><i class="icon-certificate" style="font-size:16px;"></i></a>
					--->
				</td>	
				<td>
					<cfoutput>#local.qryStorePurchases.creditValueAwarded# #local.qryStorePurchases.creditType#<br/></cfoutput>
				</td>	
				<td class="titleLabel"><strong>Notes</strong></td>		
				<cfif local.qryEventsAttended.Past28Days is 1>
					<td class="blankTd">&nbsp;</td>
				<cfelse>
					<td><i>In Processing</i></td>
				</cfif>
			</tr>
		</cfoutput>
		<cfoutput>		
		</table></div></cfoutput>
	<cfelse>
		<cfoutput>Nothing to report during the selected period.</div></cfoutput>
	</cfif>

	<cfoutput>
	<div class="row-fluid padTop10 hideInPrint" >
	<br/>

	<p>* Please allow up to 2 business days for processing.</p>
	<p><b>Filing for CLE Credit.</b> The IndyBar reports credit for paid attendees with completed affidavits to the Indiana CLE Commission ONLY no later than 28 days after the CLE. For filing in other states and jurisdictions, individuals may use this certificate of attendance to file.</p>
	<p><b>Late Filings:</b> Reporting of credit for unpaid/incomplete affidavits beyond 30 days post-event is the individual's responsibility. Fees for late filings may be assessed by the Indiana CLE Commission.</p>
	<p><b>Speaker Credit.</b> For information on how to calculate your speaker/author credit go to the Indiana CLE Commission's website, <a href="http://www.in.gov/judiciary/cle/">http://www.in.gov/judiciary/cle/</a></p>
	<p style="text-align:center"><b>Questions?</b> Call IndyBar at 317-269-2000 or Email <a href="mailto:<EMAIL>"><EMAIL></a></p>
	</div>
	</cfoutput>
	
</cfif>