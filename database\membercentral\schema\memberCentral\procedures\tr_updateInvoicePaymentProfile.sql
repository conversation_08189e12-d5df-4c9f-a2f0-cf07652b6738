ALTER PROC dbo.tr_updateInvoicePaymentProfile
@siteID int,
@invoiceIDList varchar(2000),
@MPProfileID int,
@payProfileID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;
	CREATE TABLE #tmpAuditLog (auditCode varchar(10), msg varchar(max));
	CREATE TABLE #tmpUpdateInvoices (invoiceID int);

	DECLARE @orgID int;

	SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	SET @payProfileID = NULLIF(@payProfileID,0);
	SET @MPProfileID = NULLIF(@MPProfileID,0);
	
	INSERT INTO #tmpUpdateInvoices (invoiceID)
	SELECT DISTINCT i.invoiceID
	FROM dbo.fn_intListToTableInline(@invoiceIDList,',') AS tmp
	INNER JOIN dbo.tr_invoices AS i ON i.orgID = @orgID AND i.invoiceID = tmp.listitem
	WHERE (ISNULL(i.payProfileID,0) <> ISNULL(@payProfileID,0) OR ISNULL(i.MPProfileID,0) <> ISNULL(@MPProfileID,0));
	
	IF EXISTS (SELECT 1 FROM #tmpUpdateInvoices) BEGIN
		INSERT INTO #tmpAuditLog (auditCode, msg)
		SELECT 'INV', 'Pay Profile ' + 
				CASE WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NOT NULL THEN 'changed from ' + mpp.detail + ' to ' + mpp2.detail + ' for'
					WHEN mpp.payProfileID IS NOT NULL AND mpp2.payProfileID IS NULL THEN mpp.detail + ' removed from'
					WHEN mpp.payProfileID IS NULL AND mpp2.payProfileID IS NOT NULL THEN mpp2.detail + ' associated to'
				END + ' Invoice ' + i.fullInvoiceNumber
		FROM dbo.tr_invoices as i
		INNER JOIN #tmpUpdateInvoices as tmp on tmp.invoiceID = i.invoiceID
		INNER JOIN dbo.organizations as o on o.orgID = i.orgID
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		LEFT OUTER JOIN dbo.ams_memberPaymentProfiles as mpp2 on mpp2.payProfileID = @payProfileID
		WHERE i.orgID = @orgID
		AND ISNULL(i.payProfileID,0) <> ISNULL(@payProfileID,0);

		BEGIN TRAN;
			UPDATE i
			SET i.payProfileID = @payProfileID,
				i.MPProfileID = @MPProfileID
			FROM dbo.tr_invoices AS i
			INNER JOIN #tmpUpdateInvoices AS tmp ON tmp.invoiceID = i.invoiceID;

			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"' + auditCode + '",
				"ORGID":' + cast(@orgID as varchar(10)) + ',
				"SITEID":' + cast(@siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
			FROM #tmpAuditLog;
		COMMIT TRAN;
	END

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	IF OBJECT_ID('tempdb..#tmpUpdateInvoices') IS NOT NULL 
		DROP TABLE #tmpUpdateInvoices;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
