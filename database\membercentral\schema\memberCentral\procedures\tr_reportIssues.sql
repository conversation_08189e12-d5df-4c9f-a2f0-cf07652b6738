ALTER PROC dbo.tr_reportIssues
@orgID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @SevenDaysAgo datetime = dateadd(d,-7,getdate()), @accountingEmail varchar(max), @triggerJob bit = 0;

	SELECT @accountingEmail = isnull(accountingEmail,'') 
	FROM dbo.organizations 
	WHERE orgID = @orgID;

	/* clear tables */
	DELETE FROM datatransfer.dbo.tr_reportIssues_openInv WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_nonPostedBatches WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_outOfOrderInv WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_invProfAllocViolations WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_flaggedTransactions WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.tr_reportIssues_noNonSurchargePaymentProfile WHERE orgID = @orgID;

	IF @accountingEmail = ''
		GOTO on_done;


	/* ****************************** */
	/* open invoices due > 7 days ago */
	/* ****************************** */
	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;
	CREATE TABLE #tblOpenInv (invoiceID int PRIMARY KEY, dateDue datetime, invoiceNumber int, payProfileID int, assignedToMemberID int, fullInvoiceNumber VARCHAR(19));

	INSERT INTO #tblOpenInv (invoiceID, dateDue, invoiceNumber, payProfileID, assignedToMemberID, fullInvoiceNumber)
	select invoiceID, dateDue, invoiceNumber, payProfileID, assignedToMemberID, fullInvoiceNumber
	from dbo.tr_invoices
	where orgID = @orgID
	and statusID = 1
	and dateDue < @SevenDaysAgo;

	INSERT INTO datatransfer.dbo.tr_reportIssues_openInv (orgID, dateDue, invoiceNumber, memberID, firstName, lastName, memberNumber, hasCard, invDue)
	select @orgID, tmp.dateDue, tmp.fullInvoiceNumber, m2.memberid, m2.lastname, m2.firstname, m2.membernumber, 
		case when tmp.payProfileID is null then 0 else 1 end as hasCard,
		isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0) as InvDue
	from #tblOpenInv as tmp
	inner join dbo.ams_members as m on m.orgid = @orgID and m.memberid = tmp.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgid = @orgID and m2.memberid = m.activeMemberID
	left outer join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
	group by tmp.dateDue, tmp.fullInvoiceNumber, m2.memberid, m2.lastname, m2.firstname, m2.membernumber, tmp.payProfileID;

	IF @@ROWCOUNT > 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblOpenInv') IS NOT NULL 
		DROP TABLE #tblOpenInv;


	/* ************************************************ */
	/* non-posted batches with depositdate > 7 days ago */
	/* ************************************************ */
	IF OBJECT_ID('tempdb..#tblOpenBatches') IS NOT NULL 
		DROP TABLE #tblOpenBatches;
	CREATE TABLE #tblOpenBatches (batchID int PRIMARY KEY, payProfileID int, batchName varchar(400), depositDate datetime, batchstatus varchar(25));

	INSERT INTO #tblOpenBatches (batchID, payProfileID, batchName, depositDate, batchstatus)
	select b.batchID, b.payProfileID, b.batchName, b.depositDate, bs.[status]
	from dbo.tr_batches as b
	inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
	where b.orgID = @orgID 
	and b.statusID <> 4
	and isnull(b.batchCode,'') <> 'PENDINGPAYMENTS'
	and b.depositDate < @SevenDaysAgo;

	INSERT INTO datatransfer.dbo.tr_reportIssues_nonPostedBatches (orgID, batchID, [status], batchName, depositDate, profileName)
	select @orgid, tmp.batchID, tmp.batchstatus, tmp.batchName, tmp.depositDate, mp.profileName
	from #tblOpenBatches as tmp
	left outer join dbo.mp_profiles as mp on mp.profileID = tmp.payProfileID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblOpenBatches') IS NOT NULL 
		DROP TABLE #tblOpenBatches;


	/* ********************* */
	/* out of order invoices */
	/* ********************* */
	IF OBJECT_ID('tempdb..#tblIClosed') IS NOT NULL 
		DROP TABLE #tblIClosed;
	IF OBJECT_ID('tempdb..#tblIPaid') IS NOT NULL 
		DROP TABLE #tblIPaid;
	CREATE TABLE #tblIClosed (activeMemberID int, invoiceProfileID int, dateDue datetime);
	CREATE TABLE #tblIPaid (invoiceID int, activeMemberID int, invoiceProfileID int);

	INSERT INTO #tblIClosed (activeMemberID, invoiceProfileID, dateDue)
	select distinct mClosed.activeMemberID, ipClosed.profileID, iClosed.dateDue
	from dbo.tr_invoices as iClosed 
	inner join dbo.tr_invoiceProfiles as ipClosed on ipClosed.orgID = @orgID and ipClosed.profileID = iClosed.invoiceProfileID
	inner join dbo.ams_members as mClosed on mClosed.orgID = @orgID and mClosed.memberid = iClosed.assignedToMemberID
	where iClosed.orgID = @orgID
	and iClosed.statusID in (3,5)
	and ipClosed.enforcePayOldest = 1;

	INSERT INTO #tblIPaid (invoiceID, activeMemberID, invoiceProfileID)
	select distinct iPaid.invoiceID, mPaid.activeMemberID, iPaid.invoiceProfileID
	from dbo.tr_invoices as iPaid 
	inner join dbo.ams_members as mPaid on mPaid.orgID = @orgID and mPaid.memberid = iPaid.assignedToMemberID
	inner join #tblIClosed as iClosed on iClosed.activeMemberID = mPaid.activeMemberID
		and iClosed.invoiceProfileID = iPaid.invoiceProfileID
		and iClosed.dateDue < iPaid.dateDue
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = iPaid.invoiceID
	where iPaid.orgID = @orgID
	and iPaid.statusID = 4
	group by iPaid.invoiceID, mPaid.activeMemberID, iPaid.invoiceProfileID
	having sum(it.cache_invoiceAmountAfterAdjustment) > 0;

	INSERT INTO datatransfer.dbo.tr_reportIssues_outOfOrderInv (orgID, memberID, memberName, memberNumber)
	select distinct @orgID, mClosed.activeMemberID, mClosed.lastName + ', ' + mClosed.firstName as memberName, mClosed.memberNumber
	from #tblIClosed as iClosed
	inner join dbo.ams_members as mClosed on mClosed.orgID = @orgID and mClosed.memberid = iClosed.activeMemberID
		intersect
	select distinct @orgID, mPaid.activeMemberID, mPaid.lastName + ', ' + mPaid.firstName as memberName, mPaid.memberNumber
	from #tblIPaid as iPaid
	inner join dbo.ams_members as mPaid on mPaid.orgID = @orgID and mPaid.memberid = iPaid.activeMemberID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblIClosed') IS NOT NULL 
		DROP TABLE #tblIClosed;
	IF OBJECT_ID('tempdb..#tblIPaid') IS NOT NULL 
		DROP TABLE #tblIPaid;


	/* ************************************************************ */
	/* allocations that violate Invoice/Payment Profile constraints */
	/* ************************************************************ */
	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;
	CREATE TABLE #tblProfiles (invoiceProfileID int, merchantProfileID int);
	CREATE TABLE #tblAlloc (recordedByMemberID int, amount decimal(18,2), dateRecorded datetime, payTID int, saleTID int);

	insert into #tblProfiles (invoiceProfileID, merchantProfileID)
	select ip.profileID, mp.profileID
	from dbo.tr_invoiceProfiles as ip
	inner join dbo.tr_invoiceProfilesMerchantProfiles as ipmp on ipmp.invoiceProfileID = ip.profileID
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
		and mp.[status] = 'A'
	where ip.orgID = @orgID
	and ip.[status] = 'A';

	insert into #tblAlloc (recordedByMemberID, amount, dateRecorded, payTID, saleTID)
	select tAlloc.recordedByMemberID, alloc.amount_alloc, tAlloc.dateRecorded, alloc.transactionID_cash as payTID, 
		alloc.transactionID_rev as saleTID
	from dbo.cache_tr_allocations as alloc
	inner join dbo.tr_transactions as tAlloc on tAlloc.ownedByOrgID = @orgID and tAlloc.transactionID = alloc.transactionID_alloc
		and tAlloc.statusID = 1
		and tAlloc.dateRecorded > @SevenDaysAgo
	 where alloc.orgID = @orgID;

	INSERT INTO datatransfer.dbo.tr_reportIssues_invProfAllocViolations (orgID, dateRecorded, memberName, memberNumber, allocAmount, 
		detail, invoiceNumber, invoiceProfileName, payProfileName)
	select distinct @orgID, tmpAlloc.dateRecorded, 
		mActive.lastname + ', ' + mActive.firstname as memberName, mActive.membernumber, 
		tmpAlloc.amount as allocAmount, tPay.detail, 
		i.fullInvoiceNumber as invoiceNumber, ip.profileName as invoiceProfileName, 
		mp.profileName as payProfileName 
	from #tblAlloc as tmpAlloc
	inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @orgID and tPay.transactionID = tmpAlloc.payTID
	inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = tPay.transactionID
	inner join dbo.mp_profiles as mp on mp.profileID = tp.profileID
	inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.ownedByOrgID = @orgID and tSaleTaxAdj.transactionID = tmpAlloc.saleTID
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSaleTaxAdj.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join #tblProfiles as tmp on tmp.invoiceProfileID = i.invoiceProfileID
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmpAlloc.recordedByMemberID
	inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
	where not exists (
		select invoiceProfileID from #tblProfiles where invoiceProfileID = i.invoiceProfileID and merchantProfileID = tp.profileID
	);

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	IF OBJECT_ID('tempdb..#tblProfiles') IS NOT NULL 
		DROP TABLE #tblProfiles;
	IF OBJECT_ID('tempdb..#tblAlloc') IS NOT NULL 
		DROP TABLE #tblAlloc;


	/* ****************** */
	/* transaction alerts */
	/* ****************** */
	INSERT INTO datatransfer.dbo.tr_reportIssues_flaggedTransactions (orgID, dateRecorded, [message], memberID, memberName)
	select @orgID, t.dateRecorded, ta.[message], m2.memberid, m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' as memberName
	from dbo.tr_transactionAlerts as ta
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = ta.transactionID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = t.assignedToMemberID
	inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
	where ta.orgID = @orgID;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;

	/* ****************** */
	/* Non Surcharge Payment Profile */
	/* ****************** */
	INSERT INTO datatransfer.dbo.tr_reportIssues_noNonSurchargePaymentProfile (orgID, siteCode, applicationTypeName, title, linkToAppAdmin)
	-- events with no alternative to surcharge
	SELECT @orgID,s.siteCode,'Events',cl.contentTitle as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=4&mca_a=31&mca_tt=12&mca_ta=editEvent&eid=',e.eventID,'&bc=e&peid=0&tab=rates') as linkToEventAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID=@orgID
	INNER JOIN dbo.ev_registrationMerchantProfiles as rmp on rmp.profileID = mp.profileID
	INNER JOIN dbo.ev_registration as evr on evr.siteID = mp.siteID 
		and evr.registrationID = rmp.registrationID
		and evr.status = 'A'
	INNER JOIN dbo.ev_events as e on e.eventID = evr.eventID and e.status <> 'D'
	INNER JOIN dbo.cms_contentLanguages as cl on cl.siteID = s.siteID
		and cl.contentID = e.eventContentID
		and cl.languageID = 1
	LEFT OUTER JOIN dbo.ev_registrationMerchantProfiles as rmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = rmpNoSur.profileID
			AND mpNoSur.enableSurcharge = 0
		on rmpNoSur.registrationID = evr.registrationID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND rmpNoSur.regProfileID IS NULL
	UNION 
	-- job bank with no alternative to surcharge
	SELECT @orgID, s.siteCode,'JobBank',ai.applicationInstanceName as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=2&mca_a=71&mca_tt=43&mca_ta=editJB&jbID=',jb.jobBankID,'&tab=paymentmethods') as linkToJobBankAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgiD=@orgID
	INNER JOIN dbo.jobBankMerchantProfiles as jmp on jmp.merchantProfileID = mp.profileID
	INNER JOIN dbo.jobBank jb ON jmp.jobBankID = jb.jobBankID
		and jb.disabled = 0
	INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = jb.applicationInstanceID
		AND ai.siteID = s.siteID
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	LEFT OUTER JOIN dbo.jobBankMerchantProfiles as jmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = jmpNoSur.merchantProfileID
			AND mpNoSur.enableSurcharge = 0
		on jmpNoSur.jobBankID = jmp.jobBankID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND jmpNoSur.jobBankProfileID IS NULL
	UNION 
	-- contributions with no alternative to surcharge
	SELECT @orgID, s.siteCode,'Contributions', mActive.lastName + ', ' + mActive.firstName + ' (' + mActive.memberNumber + ')' as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=3&mca_a=283&mca_tt=179&mca_ta=viewContribution&mid=',c.memberID,'&cid=',c.contributionID) as linkToContributionAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID = @orgID
	INNER JOIN dbo.cp_contributionPayProfiles as cpp on cpp.MPProfileID = mp.profileID
	INNER JOIN dbo.cp_contributions as c ON c.contributionID = cpp.contributionID
		AND c.statusID in (1,2,6)
	INNER JOIN dbo.cp_programs as cp on cp.programID = c.programID
	inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = c.memberID
				inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
	INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = cp.applicationInstanceID
		AND ai.siteID = s.siteID
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	LEFT OUTER JOIN dbo.cp_contributionPayProfiles as jmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = jmpNoSur.MPProfileID
			AND mpNoSur.enableSurcharge = 0
		on jmpNoSur.contributionID = cpp.contributionID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND jmpNoSur.contributionPayProfileID IS NULL
	UNION 
	-- referral settings with no alternative to surcharge
	SELECT @orgID, s.siteCode,'Referral','Referral Settings' as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=212&mca_a=356&mca_tt=141&mca_ta=manageMainSettings&mca_lt=221') as linkToReferralAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID = @orgID
	INNER JOIN dbo.ref_merchantProfiles as rmp on rmp.merchantProfileID = mp.profileID
	INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = rmp.applicationInstanceID
		AND ai.siteID = s.siteID
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	LEFT OUTER JOIN dbo.ref_merchantProfiles as rmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = rmpNoSur.merchantProfileID
			AND mpNoSur.enableSurcharge = 0
		on rmpNoSur.applicationInstanceID = rmp.applicationInstanceID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND rmpNoSur.referralProfileID IS NULL
	UNION 
	-- seminarweb settings with no alternative to surcharge
	SELECT @orgID, s.siteCode,'Seminarweb','SeminarWeb Settings' as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=4&mca_a=335&mca_tt=191&mca_ta=manageSettings&mca_hk=') as linkToSemWebAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID =@orgID
	INNER JOIN seminarWeb.dbo.tblParticipantMerchantProfiles as rmp on rmp.profileID = mp.profileID
	INNER JOIN seminarweb.dbo.tblParticipants as p on p.participantID = rmp.participantID
		and p.orgcode = s.siteCode
	LEFT OUTER JOIN seminarWeb.dbo.tblParticipantMerchantProfiles as rmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = rmpNoSur.profileID
			AND mpNoSur.enableSurcharge = 0
		on rmpNoSur.participantID = rmp.participantID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND rmpNoSur.participantProfileID IS NULL
	UNION 
	-- store settings with no alternative to surcharge
	SELECT @orgID, s.siteCode,'Store', 'Store Settings' as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=2&mca_a=18&mca_tt=9&mca_ta=home&tab=settings') as linkToStoreAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID = @orgID
	INNER JOIN dbo.store_merchantProfiles as rmp on rmp.merchantProfileID = mp.profileID
	INNER JOIN dbo.store as p on p.storeID = rmp.storeID
		and p.siteID = s.siteID
	LEFT OUTER JOIN dbo.store_merchantProfiles as rmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = rmpNoSur.merchantProfileID
			AND mpNoSur.enableSurcharge = 0
		on rmpNoSur.storeID = rmp.storeID
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND rmpNoSur.storeProfileID IS NULL
	UNION 
	-- sub rate frequencies with no alternative to surcharge
	SELECT @orgID, s.siteCode,'Subscription',rs.scheduleName as titlt, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=3&mca_a=67&mca_tt=39&mca_ta=editSchedule&schedID=',rs.scheduleID) as linkToSubscriptionAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID = @orgID
	INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rmp on rmp.profileID = mp.profileID
	INNER JOIN dbo.sub_rateFrequencies as rf on rf.rfid = rmp.rfid
	INNER JOIN dbo.sub_rates as r on r.rateID = rf.rateID
		and r.status <> 'D'
	INNER JOIN dbo.sub_rateSchedules as rs on rs.scheduleID = r.scheduleID
		and rs.siteID = s.siteID
	LEFT OUTER JOIN dbo.sub_rateFrequenciesMerchantProfiles as rmpNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = rmpNoSur.profileID
			AND mpNoSur.enableSurcharge = 0
		on rmpNoSur.rfid = rmp.rfid
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND rmpNoSur.rfmpid IS NULL
	UNION 
	-- tasks with no alternative to surcharge
	SELECT @orgID, s.siteCode,'TASK', projectContent.contentTitle+'('+ai.applicationInstanceName +')' as title, concat('https://',dbo.fn_getSiteHostNameBySiteCode(s.sitecode,'production'),'/?pg=admin&mca_s=58&mca_a=315&mca_tt=190&mca_ta=editProject&pid=',p.projectID,'&tab=setup') as linkToTasksAdmin
	FROM dbo.mp_profiles as mp
	INNER JOIN dbo.sites as s on s.siteID = mp.siteID and s.orgID = @orgID
	INNER JOIN dbo.tasks_projects as p on p.profileID = mp.profileID
	INNER JOIN dbo.tasks_workspaces as w on w.workspaceID = p.workspaceID
	INNER JOIN dbo.cms_applicationInstances as ai on ai.applicationInstanceID = w.applicationInstanceID
		AND ai.siteID = s.siteID
	INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
		AND sr.siteResourceID = ai.siteResourceID 
		AND sr.siteResourceStatusID = 1
	INNER JOIN dbo.cms_contentLanguages as cl on cl.siteID = s.siteID
		and cl.contentID = p.projectContentID
		and cl.languageID = 1
	LEFT OUTER JOIN dbo.tasks_projects as pNoSur 
		INNER JOIN dbo.mp_profiles as mpNoSur on mpNoSur.profileID = pNoSur.profileID
			AND mpNoSur.enableSurcharge = 0
		on pNoSur.projectID = p.projectID
	cross apply dbo.fn_getContent(p.projectContentID,1) as projectContent
	WHERE mp.enableMCPay = 1
	and mp.enableSurcharge = 1
	AND pNoSur.projectID IS NULL
	ORDER BY s.siteCode;

	IF @@ROWCOUNT > 0 AND @triggerJob = 0 
		SET @triggerJob = 1;	


	IF @triggerJob = 1 BEGIN
		DECLARE @statusReady int; 
		EXEC platformQueue.dbo.queue_getStatusIDbyType @queueType='acctIssuesReport', @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;

		INSERT INTO platformQueue.dbo.queue_acctIssuesReport (orgID, statusID, dateAdded, dateUpdated)
		VALUES (@orgID, @statusReady, GETDATE(), GETDATE());

		EXEC dbo.sched_resumeTask @name='Accounting Issues Report', @engine='MCLuceeLinux';
	END


	on_done:
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
