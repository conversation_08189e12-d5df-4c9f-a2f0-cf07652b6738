<cfcomponent extends="model.admin.admin" output="no">
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.edit = buildCurrentLink(arguments.event,"edit");
			this.link.saveSettings = buildCurrentLink(arguments.event,"saveSettings");	

			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>
		
	<cffunction name="edit" access="public" output="false" returntype="struct" hint="Edit">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery name="local.qrySettings" datasource="#application.dsn.membercentral.dsn#">
			select useBatches, accountingEmail, defaultPending, notifyBadCOF, notifyBadCOFMessage,
				fiscalYearStartMonth, useAccrualAcct, invoiceNumPrefix
			from dbo.organizations
			where orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfquery name="local.qryGetResourceID" datasource="#application.dsn.membercentral.dsn#">
			select top 1 sr.siteResourceID 
			from dbo.cms_siteResources as sr
			inner join dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID 
			where srt.resourceType = 'AccountingAdmin'
			and sr.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			and sr.siteResourceStatusID = 1
		</cfquery>

		<cfset local.objInvoices = createObject("component","model.invoices.invoices")>
		<cfset local.qryInvAppContent = local.objInvoices.getInvoiceAppContent(siteID=arguments.event.getValue('mc_siteInfo.siteID'), contentTitle='InvoiceAppContent')>
		<cfset local.qryManagePayMethodsContent = local.objInvoices.getInvoiceAppContent(siteID=arguments.event.getValue('mc_siteInfo.siteID'), contentTitle='ManagePayMethodsContent')>
		
		<cfset local.qryIPReportFieldSet = createObject("component","model.admin.memberFieldSets.memberFieldSets").getSettingsFieldsetID(siteResourceID=local.qryGetResourceID.siteResourceID, area='ipreport', module="Accounting")>		
		<cfset local.ipreportFieldsetID = len(local.qryIPReportFieldSet.fieldsetID) eq 0 ? 0 : local.qryIPReportFieldSet.fieldsetID>
		<cfset local.ipreportUseID = len(local.qryIPReportFieldSet.useID) eq 0 ? 0 : local.qryIPReportFieldSet.useID>
		<cfset local.strIPReportFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fs_IPReport", selectedValue=local.ipreportFieldsetID)>
		<cfset local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>

		<cfset local.isSuperUser = application.objUser.isSuperUser(cfcuser=session.cfcuser)> 

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_accountingSettings.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>		

	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('notifyBadCOF',0) is not 1>
			<cfset arguments.event.setValue('notifyBadCOFMessage','')>
		</cfif>

		<cfset local.accountingEmail = application.objcommon.getValidatedEmailAddresses(emailAddressList=arguments.event.getValue('accountingEmail',''), delimiter=';')>

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int, @orgID int, @prevFYStartMonth tinyint, @newFYStartMonth tinyint, @prevInvoiceNumPrefix VARCHAR(10), @newInvoiceNumPrefix VARCHAR(10), @readyQueueStatusID int, 
					@recordedByMemberID int, @nowDate datetime = getdate(), @loopCount int;
				SET @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
				SET @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
				SET @newFYStartMonth = <cfqueryparam cfsqltype="CF_SQL_TINYINT" value="#arguments.event.getValue('fiscalYearStartMonth',1)#">;
				SET @newInvoiceNumPrefix = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#UCase(REReplace(arguments.event.getValue('invoiceNumPrefix',''), '[^A-Z0-9]', '', 'all'))#">;
				SELECT @prevFYStartMonth = fiscalYearStartMonth, @prevInvoiceNumPrefix = invoiceNumPrefix FROM dbo.organizations WHERE orgID = @orgID;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

				BEGIN TRAN;
					UPDATE dbo.organizations
					SET useBatches = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('useBatches',1)#">,
						defaultPending = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('defaultPending',1)#">,
						accountingEmail = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.accountingEmail#">,
						notifyBadCOF = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('notifyBadCOF',0)#">,
						notifyBadCOFMessage = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#Replace(Replace(arguments.event.getTrimValue('notifyBadCOFMessage',''),chr(13),'','ALL'),chr(10),'','ALL')#">,
						invoiceNumPrefix = @newInvoiceNumPrefix,
						fiscalYearStartMonth = @newFYStartMonth
						<!--- cannot disable this, so protect updating the value --->
						<cfif val(arguments.event.getValue('useAccrualAcct',0)) eq 1 AND arguments.event.getValue('mc_siteinfo.useAccrualAcct') eq 0>
							, useAccrualAcct = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('useAccrualAcct',0)#">
						</cfif>
					WHERE orgID = @orgID;
					
					IF ISNULL(@prevInvoiceNumPrefix, '') <> @newInvoiceNumPrefix
						SET @loopCount = 1;
						WHILE @loopCount > 0 BEGIN
							UPDATE TOP (10000) dbo.tr_invoices
							SET fullInvoiceNumber = 
								CASE 
								WHEN @newInvoiceNumPrefix <> '' THEN @newInvoiceNumPrefix + '-' + RIGHT('00000000' + CAST(invoiceNumber AS VARCHAR(8)), 8)
								ELSE RIGHT('00000000' + CAST(invoiceNumber AS VARCHAR(8)), 8)
								END
							WHERE orgID = @orgID;

							SET @loopCount = @@ROWCOUNT;
						END

					<!--- if changing FY Start Month, requeue the dashboard objects so they will be redrawn --->
					IF @newFYStartMonth <> @prevFYStartMonth
						UPDATE doc
						SET doc.nextUpdate = @nowDate
						FROM platformStatsMC.dbo.rpt_dashboardObjectsCache as doc
						INNER JOIN dbo.rpt_dashboardObjects as do on do.objectID = doc.objectID
						INNER JOIN dbo.rpt_dashboards as d on d.dashboardID = do.dashboardID AND d.siteID = @siteID;

					<cfif val(arguments.event.getValue('origfs_IPReport',0)) neq val(arguments.event.getValue('fs_IPReport',0))>
						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('useid_IPReport',0))#" cfsqltype="CF_SQL_INTEGER">;

						<cfif arguments.event.getValue('fs_IPReport',0) gt 0>
							DECLARE @srid int;

							SELECT top 1 @srid = sr.siteResourceID 
							FROM dbo.cms_siteResources as sr
							INNER JOIN dbo.cms_siteResourceTypes as srt on srt.resourceTypeID = sr.resourceTypeID and srt.resourceType = 'AccountingAdmin'
							WHERE sr.siteID = @siteID
							AND sr.siteResourceStatusID = 1;

							INSERT INTO dbo.ams_memberFieldUsage (siteResourceID, fieldsetID, area, fieldSetOrder)
							VALUES (@srid, <cfqueryparam value="#arguments.event.getValue('fs_IPReport')#" cfsqltype="CF_SQL_INTEGER">, 'ipreport', 1);
						</cfif>
					</cfif>

					<cfif val(arguments.event.getValue('useAccrualAcct',0)) eq 1 AND arguments.event.getValue('mc_siteinfo.useAccrualAcct') eq 0>
						declare @sysMemberID int, @GLAccountID int;
						select @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

						EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Revenue Accounts', 
							@accountCode='', @GLCode='DEFERREDREVENUE', @parentGLAccountID=null, @invoiceProfileID=null, 
							@isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, 
							@salesTaxTaxJarCategoryID=null, @recordedByMemberID=@sysMemberID, @GLAccountID=@GLAccountID output;
					
						EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=5, @accountName='Deferred Sales Tax Accounts', 
							@accountCode='', @GLCode='DEFERREDTAX', @parentGLAccountID=null, @invoiceProfileID=null, 
							@isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, @salesTaxProfileID=null, 
							@salesTaxTaxJarCategoryID=null, @recordedByMemberID=@sysMemberID, @GLAccountID=@GLAccountID output;

						insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
						select tooltypeID, @siteID
						from dbo.admin_toolTypes
						where toolType in ('AccrualScheduleReport','DeferredIncomeAnalysisReport','RecognizedReceivableReport')
							except
						select tooltypeID, siteID 
						from dbo.admin_siteToolRestrictions
						where siteID = @siteID;
					</cfif>

					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue("invAppContentID")#">, 
						@languageID=1, @isHTML=1, @contentTitle='InvoiceAppContent', @contentDesc='', 
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue("invAppContent")#">,
						@memberID=@recordedByMemberID;

					EXEC dbo.cms_updateContent @contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue("managePayMethodsContentID")#">, 
						@languageID=1, @isHTML=1, @contentTitle='ManagePayMethodsContent', @contentDesc='', 
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue("managePayMethodsContent")#">,
						@memberID=@recordedByMemberID;

				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	
		<!--- Triggering ResetAppVars --->
		<cfif val(arguments.event.getValue('useAccrualAcct',0)) eq 1 AND arguments.event.getValue('mc_siteinfo.useAccrualAcct') eq 0>
			<cfset application.objSiteInfo.triggerClusterWideReload()>
		</cfif>
		<cflocation url="#this.link.edit#&c=1" addtoken="no">
	</cffunction>

</cfcomponent>
