<cfcomponent output="no">

	<cffunction name="generateInvoice" access="public" returntype="struct" output="no">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="invoiceID" type="numeric" required="yes">
		<cfargument name="tmpFolder" type="string" required="yes">
		<cfargument name="encryptFile" type="boolean" required="yes">
		<cfargument name="namedForBundle" type="boolean" required="yes">

		<cfset var local = structnew()>
		<cfset local.generateInvoiceOnBER = false>

		<cfif NOT local.generateInvoiceOnBER>
			<cfset local.strInvoice = { invoicePath='', invoicePayOnlineStr=structNew() }>
			
			<!--- if no invoice to generate, kick out --->
			<cfquery name="local.qryInvoice" datasource="#application.dsn.membercentral.dsn#">
				select i.invoiceID, i.invoiceProfileID, ip.imageExt, o.orgid, 
					i.fullInvoiceNumber as invoiceNumber, i.dateCreated, i.dateBilled, i.dateDue,
					i.invoiceCode, istat.status as invoiceStatus, m2.memberid, m2.firstname, m2.middlename, m2.lastname, s.sitecode
				from dbo.tr_invoices as i 
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
				inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = m.orgID
				inner join dbo.sites as s on s.orgID = o.orgID and s.siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
				where i.invoiceID = <cfqueryparam value="#arguments.invoiceID#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>
			<cfif local.qryInvoice.recordcount is 0>
				<cfreturn local.strInvoice>
			</cfif>
			
			<!--- Name and address --->
			<cfset local.qryMember = application.objMember.getMemberInfo(memberid=local.qryInvoice.memberid, orgid=local.qryInvoice.orgID)>
			<cfset local.qryAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryInvoice.orgID, memberid=local.qryInvoice.memberid)>
			<cfset local.addressinfo = "#local.qryMember.prefix# #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix#<br/>">
			<cfif len(trim(local.qryMember.company))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryMember.company)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.attn))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.attn)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address1))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address1)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address2))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address2)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address3))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address3)#<br/>">
			</cfif>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.city)#, #trim(local.qryAddress.statename)# #trim(local.qryAddress.postalCode)#<br/>">
			<cfif local.qryAddress.countryID is not 1>
				<cfset local.addressinfo = "#local.addressinfo##local.qryAddress.country#<br/>">
			</cfif>
			
			<!--- transactions on invoice --->
			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="tr_invoiceData">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoice.invoiceID#">
				<cfprocresult resultset="1" name="local.qryInvoiceMessages">
				<cfprocresult resultset="2" name="local.qrySection1Sales">
				<cfprocresult resultset="3" name="local.qrySection1Tax">
				<cfprocresult resultset="4" name="local.qrySection2Sales">
				<cfprocresult resultset="5" name="local.qrySection2Tax">
				<cfprocresult resultset="6" name="local.qrySection3Sales">
				<cfprocresult resultset="7" name="local.qrySection3Tax">
			</cfstoredproc>
			<cfquery name="local.qryS1STotal" dbtype="query">
				select sum(amount) as amt from [local].qrySection1Sales
			</cfquery>
			<cfquery name="local.qryS1TTotal" dbtype="query">
				select sum(amount) as amt from [local].qrySection1Tax
			</cfquery>
			<cfset local.section1Total = val(local.qryS1STotal.amt) + val(local.qryS1TTotal.amt)>
			<cfquery name="local.qryS3STotal" dbtype="query">
				select sum(amount) as amt from [local].qrySection3Sales
			</cfquery>
			<cfquery name="local.qryS3TTotal" dbtype="query">
				select sum(amount) as amt from [local].qrySection3Tax
			</cfquery>
			<cfset local.section3Total = val(local.qryS3STotal.amt) + val(local.qryS3TTotal.amt)>

			<!--- display pay online link if:
				1 - invoice is closed/delinq
				2 - section1total gt 0
				3 - invoice is not tied to any payment profiles (so it uses the default setting) OR is tied to at least 1 payment profile that is an online gateway 
			--->
			<cfset local.invPayOnline = { link='', directlink='', invnumber='', code='' } >
			<cfquery name="local.qryShowLink" datasource="#application.dsn.membercentral.dsn#">
				select dbo.fn_tr_showInvoicePayOnlineLink(#local.qryInvoice.invoiceID#) as showLink
			</cfquery>
			<cfif listFindNoCase("Closed,Delinquent",local.qryInvoice.invoiceStatus) and local.section1Total gt 0 and local.qryShowLink.showLink is 1>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryInvoice.invoicenumber#|#right(GetTickCount(),5)#|#local.qryInvoice.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.invPayOnline = { 
					link = '#application.objSiteInfo.getSiteInfo(local.qryInvoice.sitecode).mainhostname#/invoices',
					directlink = '#application.objSiteInfo.getSiteInfo(local.qryInvoice.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(local.qryInvoice.sitecode).mainhostname#/invoices/#local.stInvEnc#',
					invnumber = local.qryInvoice.invoicenumber,
					code = local.qryInvoice.invoiceCode
					} >
			</cfif>

			<cfset local.haystack = local.addressinfo>
			<cfset local.needle = "<br/>">
			<cfset local.numLines = (len(local.haystack) - len(ReplaceNoCase(local.haystack,local.needle,'','ALL')))/len(local.needle)>
			<cfset local.margintop = "3.5">
			<cfif len(local.invPayOnline.code)>
				<cfset local.margintop = local.margintop + .275>

				<cftry>
					<cfset local.platformMergeTagLibrary = createObject("component","model.system.common.platformMergeTagLibrary")>
					<cfset local.strQRCode =  local.platformMergeTagLibrary.qrcode(text=local.invPayOnline.directlink, width=85, height=85, format='struct')>
					<cfset local.strQRFolder = application.objDocDownload.createHoldingFolder()>
					<cfset structInsert(local.strQRCode, 'imagedir', local.strQRFolder.folderPath)>
					<cfset structInsert(local.strQRCode, 'imagefn', '#createUUID()#.png')>
					<cfhttp method="GET" url="#local.strQRCode.dataStruct.qrcode[1].url#" path="#local.strQRCode.imagedir#" file="#local.strQRCode.imagefn#" timeout="60" throwonerror="true" getasbinary="true" />
					<cfset local.qrcodeGenerated = true>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch)>
					<cfset local.qrcodeGenerated = false>
				</cfcatch>
				</cftry>
			</cfif>
			
			<!--- render invoice --->
			<cfsavecontent variable="local.invHeader">
				<cfset local.c = "text-align:center;">
				<cfset local.l = "text-align:left;">
				<cfset local.r = "text-align:right;">
				<cfset local.bt = "border-top:1px solid ##000;">
				<cfset local.bb = "border-bottom:1px solid ##000;">
				<cfset local.bl = "border-left:1px solid ##000;">
				<cfset local.br = "border-right:1px solid ##000;">
				<cfset local.address = "font-size:12px;font-family:verdana;font-weight:bold;line-height:16px;margin-left:100px;">
				<cfset local.inv1 = "font-family:verdana;font-size:16px;line-height:22px;font-weight:bold;letter-spacing:4px;">
				<cfset local.inv2 = "font-family:verdana;font-size:11.5px;line-height:16px;padding:2px 0;">
				<cfset local.inv2a = "font-family:verdana;font-size:11px;line-height:16px;padding:2px 0;">
				<cfset local.inv3 = "font-family:verdana;font-size:11.5px;padding-top:8px;">
				<cfset local.logo = "width:400px;height:150px;">
				<cfset local.payonline = "font-size:11px;font-family:verdana;text-align:center;padding:4px;margin-top:6px;">
				<cfset local.notfinal = "border:2px solid ##f00;font-family:verdana;color:##f00;font-size:14px;font-weight:bold;text-align:center;line-height:22px;padding:4px;margin-top:6px;">
				<cfset local.paidbox = "margin-top:4px; text-align:center; ">
				<cfset local.infotbl = "margin-top:6px;">
				<cfset local.height = "180">
				<cfset local.width = "400">
				<cfset local.tdwidth = "310">

				<cfoutput>
					<div>
						<table cellspacing="0" cellpadding="0" width="100%">
							<tr>
								<td height="#local.height#" valign="top">
									<div style="#local.logo#;">
										<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryInvoice.invoiceProfileID#.#local.qryInvoice.imageExt#")>
											<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryInvoice.invoiceProfileID#.#local.qryInvoice.imageExt#" width="#local.width#"  />
										</cfif>
									</div>
								</td>
								<td height="#local.height#" width="#local.tdwidth#" valign="top">
									<div style="#local.infotbl#">
										<table cellspacing="0" cellpadding="2" width="#local.tdwidth#">
										<tr>
											<td colspan="2" style="#local.c##local.bt##local.bl##local.br##local.inv1#">INVOICE</td>
										</tr>
										<tr valign="bottom">
											<td style="#local.bt##local.bl##local.r##local.inv2a#"><b>Invoice Number</b> &nbsp;</td>
											<td style="#local.bt##local.br##local.inv2a#">#local.qryInvoice.invoiceNumber#</td>
										</tr>
										<tr valign="bottom">
											<td style="#local.bl##local.r##local.inv2a#"><b>Account Number</b> &nbsp;</td>
											<td style="#local.br##local.inv2a#">#local.qryMember.membernumber#</td>
										</tr>
										<tr>
											<td style="#local.bl##local.bb##local.r##local.inv2#"><b>Amount Due</b> &nbsp;</td>
											<td style="#local.br##local.bb##local.inv2#">
												<cfif local.qrySection1Sales.recordcount or local.qrySection1Tax.recordcount and local.section1Total gt 0>
													#dollarformat(local.section1Total+local.section3Total)# 
												<cfelse>
													$0.00
												</cfif>
												due by #dateformat(local.qryInvoice.dateDue,"m/d/yyyy")#
											</td>
										</tr>
										</table>
									</div>
									<cfif NOT ListFindNoCase("Closed,Delinquent,Paid",local.qryInvoice.invoiceStatus)>
										<div style="#local.notfinal#">
											INVOICE IS NOT FINALIZED<br/>
											DO NOT PAY THIS INVOICE
										</div>
									<cfelseif local.qryInvoice.invoiceStatus eq "Paid">
										<div style="#local.paidbox#">
											<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/invoices/paidstamp.gif" width="187" height="53" />
										</div>
									</cfif>
								</td>
							</tr>					
							<tr>
								<td valign="top">
									<div style="#local.address#">	
										#local.addressinfo#<cfif local.numLines lt 8>#repeatString("<br/>",8-local.numLines)#</cfif>	
									</div>
								</td>
								<td width="#local.tdwidth#" align="right">
									<cfif len(local.invPayOnline.code) and local.qrcodeGenerated>
										<div style="text-align:center;width:#local.strQRCode.dataStruct.qrcode[1].width#px;">
											<div style="#local.inv3#">Scan to Pay</div>
											<img src="file:///#local.strQRCode.imagedir#/#local.strQRCode.imagefn#" width="#local.strQRCode.dataStruct.qrcode[1].width#px" height="#local.strQRCode.dataStruct.qrcode[1].height#px" />
										</div>
									</cfif>
								</td>
							</tr>
						</table>

						<cfif len(local.invPayOnline.code)>
							<div style="#local.payonline##local.bb##local.bt##local.bl##local.br#">
								<a href="#local.invPayOnline.directlink#">Pay online @ #local.invPayOnline.link#</a>
								&nbsp; &nbsp; &nbsp; 
								Invoice Number: #local.invPayOnline.invnumber#
								&nbsp; &nbsp; 
								Invoice Code: #local.invPayOnline.code#
							</div>
						</cfif>
					</div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.invFooter">
				<cfoutput>
				<div style="font-family:verdana;font-size:9px;text-align:center;">
					Invoice generated #dateFormat(now(),'m/dd/yyyy')#<!-- cpp -->
				</div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.invBody">
				<cfset local.sec1_th = "font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;background-color:##cecece;">
				<cfset local.sec1_td_sttl = "text-align:right;background-color:transparent; padding-top:8px;">
				<cfset local.sec1_td = "font-size:11px;font-family:verdana;line-height:12px;">
				<cfset local.sec2_th = "font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;background-color:##cecece;">
				<cfset local.sec2_th_s1ttl = "text-align:right;background-color:transparent;">
				<cfset local.sec2_td = "font-size:11px;font-family:verdana;line-height:13px;">
				<cfset local.sec3_th = "font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;background-color:##cecece;">
				<cfset local.sec3_td_sttl = "text-align:right;background-color:transparent; padding-top:8px;">
				<cfset local.sec3_td = "font-size:11px;font-family:verdana;line-height:13px;">
				<cfset local.sec4_th = "font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;">
				<cfset local.invmsg = "font-size:11px;font-family:verdana;line-height:13px;margin-top:14px;">
				<cfset local.sup = "font-size:8px;">
				<cfset local.sec3 = "background-color:##eeeeee;">
				<cfset local.sec4 = "background-color:##cecece;">
				<cfset local.r = "text-align:right;">
				<cfset local.l = "text-align:left;">
				<cfset local.p = "padding-bottom:6px;">
				<cfset local.ph = "height:6px;">
				<cfset local.ind = "margin-left:20px; padding-top:2px;">
				<cfset local.bt = "border-top:1px solid ##666;">
				<cfset local.bb = "border-bottom:1px solid ##666;">
				<cfset local.bl = "border-left:1px solid ##666;">
				<cfset local.br = "border-right:1px solid ##666;">
				<cfoutput>

				<html>
					<head>
						<style type="text/css">
							html, body { width:8in; padding:0; margin: 0; }
						</style>
					</head>
					<body>
				<cfif local.qrySection1Sales.recordcount or local.qrySection1Tax.recordcount>
					<div id="sec1">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th width="*" colspan="2" style="#local.sec1_th##local.bb##local.l#">&nbsp; Details of Invoice #local.qryInvoice.invoiceNumber#</th>
							<th width="10" style="#local.sec1_th##local.bb#">&nbsp;</th>
							<th width="70" style="#local.sec1_th##local.bb##local.r#">Amount&nbsp;</th>
						</tr>
						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						<cfloop query="local.qrySection1Sales">
							<tr valign="top">
								<td width="2" style="#local.sec1_td##local.p#">&nbsp;</td>
								<td width="*" style="#local.sec1_td##local.p#">
									#local.qrySection1Sales.detail#&nbsp;<cfif len(local.qrySection1Sales.footnote)> <sup style="#local.sup#">#local.qrySection1Sales.footnote#</sup></cfif><br/>
									<div style="#local.ind#">#encodeForHTML(local.qrySection1Sales.assignedToMember)#<cfif len(local.qrySection1Sales.assignedToMemberCompany)> - #encodeForHTML(local.qrySection1Sales.assignedToMemberCompany)#</cfif></div>
								</td>
								<td width="10" style="#local.sec1_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec1_td##local.r##local.p#">#dollarformat(local.qrySection1Sales.amount)#<cfif local.qrySection1Sales.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						<cfloop query="local.qrySection1Tax">
							<tr valign="top">
								<td width="2" style="#local.sec1_td##local.p#">&nbsp;</td>
								<td width="*" style="#local.sec1_td##local.p#">Tax: #local.qrySection1Tax.detail#&nbsp;</td>
								<td width="10" style="#local.sec1_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec1_td##local.r##local.p#">#dollarformat(local.qrySection1Tax.amount)#<cfif local.qrySection1Tax.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						<tr><td colspan="4" style="#local.sec1_td##local.ph#"></td></tr>
						<tr>
							<td width="*" colspan="2" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">Invoice Sub Total:</td>
							<td width="10" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">&nbsp;</td>
							<td width="70" style="#local.sec1_td##local.sec1_td_sttl##local.bt#">#dollarformat(local.section1Total)#<cfif local.section1Total gte 0>&nbsp;</cfif></td>
						</tr>
						</table>
					</div>
					<br/>
				</cfif>

				<cfif local.qrySection2Sales.recordcount or local.qrySection2Tax.recordcount>
					<cfif local.qrySection1Sales.recordcount or local.qrySection1Tax.recordcount>
						<br/>
					</cfif>
					<div id="sec2">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th width="*" colspan="2" style="#local.sec2_th##local.bb##local.l#">&nbsp; Transactions Affecting Other Invoices</th>
							<th width="10" style="#local.sec2_th##local.bb#">&nbsp;</th>
							<th width="70" style="#local.sec2_th##local.bb##local.r#">Amount&nbsp;</th>
						</tr>
						<tr><td colspan="4" style="#local.sec2_td##local.ph#"></td></tr>
						<cfloop query="local.qrySection2Sales">
							<tr valign="top">
								<td width="2" style="#local.sec2_td##local.p#">&nbsp;</td>
								<td width="*" class="#local.sec2_td##local.p#">
									#local.qrySection2Sales.detail#&nbsp;<cfif len(local.qrySection2Sales.footnote)> <sup style="#local.sup#">#local.qrySection2Sales.footnote#</sup></cfif><br/>
									<div style="#local.ind#">#encodeForHTML(local.qrySection2Sales.assignedToMember)#<cfif len(local.qrySection2Sales.assignedToMemberCompany)> - #encodeForHTML(local.qrySection2Sales.assignedToMemberCompany)#</cfif></div>
									<div style="#local.ind#">Applies to invoice #local.qrySection2Sales.invoicenumber#</div>
								</td>
								<td width="10" style="#local.sec2_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec2_td##local.r##local.p#">#dollarformat(local.qrySection2Sales.amount)#<cfif local.qrySection2Sales.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						<cfloop query="local.qrySection2Tax">
							<tr valign="top">
								<td width="2" style="#local.sec2_td##local.p#">&nbsp;</td>
								<td width="*" style="#local.sec2_td##local.p#">
									Tax: #local.qrySection2Tax.detail#&nbsp;<br/>
									<div style="#local.ind#">
										Applies to invoice #local.qrySection2Tax.invoicenumber#
									</div>
								</td>
								<td width="10" style="#local.sec2_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec2_td##local.r##local.p#">#dollarformat(local.qrySection2Tax.amount)#<cfif local.qrySection2Tax.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						</table>
					</div>
					<br/>
				</cfif>
				
				<cfif local.qrySection3Sales.recordcount or local.qrySection3Tax.recordcount>
					<cfif local.qrySection1Sales.recordcount or local.qrySection1Tax.recordcount or local.qrySection2Sales.recordcount or local.qrySection2Tax.recordcount>
						<br/>
					</cfif>
					<div id="sec3" style="#local.sec3#">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th width="*" colspan="3" style="#local.sec3_th##local.bb##local.l#">&nbsp; Activity Against This Invoice</th>
							<th width="10" style="#local.sec3_th##local.bb#">&nbsp;</th>
							<th width="70" style="#local.sec3_th##local.bb##local.r#">Amount&nbsp;</th>
						</tr>
						<tr><td colspan="5" style="#local.sec3_td##local.ph#"></td></tr>
						<cfloop query="local.qrySection3Sales">
							<tr valign="top">
								<td width="80" style="#local.sec3_td##local.p##local.r#">#dateformat(local.qrySection3Sales.transactionDate,"m/d/yyyy")# &nbsp;</td>
								<td width="2" style="#local.sec3_td##local.p#">&nbsp;</td>
								<td width="*" style="#local.sec3_td##local.p#">
									#local.qrySection3Sales.detail#&nbsp;<br/>
									<div style="#local.ind#">#encodeForHTML(local.qrySection3Sales.assignedToMember)#<cfif len(local.qrySection3Sales.assignedToMemberCompany)> - #encodeForHTML(local.qrySection3Sales.assignedToMemberCompany)#</cfif></div>
									<div style="#local.ind#">
										<cfif len(local.qrySection3Sales.invoicenumber)>
											Applied from invoice #local.qrySection3Sales.invoicenumber#
										<cfelse>
											#dollarformat(abs(local.qrySection3Sales.amount))# applied to this invoice
										</cfif>
									</div>
								</td>
								<td width="10" style="#local.sec3_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec3_td##local.r##local.p#">#dollarformat(local.qrySection3Sales.amount)#<cfif local.qrySection3Sales.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						<cfloop query="local.qrySection3Tax">
							<tr valign="top">
								<td colspan="2" style="#local.sec3_td##local.p#">&nbsp;</td>
								<td width="*" style="#local.sec3_td##local.p#">
									Tax: #local.qrySection3Tax.detail#&nbsp;<br/>
									<div style="#local.ind#">
										Applied from invoice #local.qrySection3Tax.invoicenumber#
									</div>
								</td>
								<td width="10" style="#local.sec3_td##local.p#">&nbsp;</td>
								<td width="70" style="#local.sec3_td##local.r##local.p#">#dollarformat(local.qrySection3Tax.amount)#<cfif local.qrySection3Tax.amount gte 0>&nbsp;</cfif></td>
							</tr>
						</cfloop>
						<tr><td colspan="5" style="#local.sec3_td##local.ph#"></td></tr>
						<tr>
							<td width="*" colspan="3" style="#local.sec3_td##local.sec3_td_sttl##local.bt#">Activity Sub Total:</td>
							<td width="10" style="#local.sec3_td##local.sec3_td_sttl##local.bt#">&nbsp;</td>
							<td width="70" style="#local.sec3_td##local.sec3_td_sttl##local.bt#">#dollarformat(local.section3Total)#<cfif local.section3Total gte 0>&nbsp;</cfif></td>
						</tr>
						</table>
					</div>
					<br/>
				</cfif>
				
				<cfif ListFindNoCase("Closed,Delinquent,Paid",local.qryInvoice.invoiceStatus)>
					<div id="sec4" style="#local.sec4##local.bb##local.bl##local.bt##local.br#">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<th width="*" style="#local.sec4_th##local.r#">Total Due:</th>
							<th width="10" style="#local.sec4_th#">&nbsp;</th>
							<cfif local.qryInvoice.invoiceStatus eq "Paid">
								<th width="70" style="#local.sec4_th##local.r#">$0.00&nbsp;</th>
							<cfelseif local.qrySection1Sales.recordcount or local.qrySection1Tax.recordcount and local.section1Total gt 0>
								<th width="70" style="#local.sec4_th##local.r#">#dollarformat(local.section1Total+local.section3Total)#<cfif local.section1Total+local.section3Total gte 0>&nbsp;</cfif></th>
							<cfelse>
								<th width="70" style="#local.sec4_th##local.r#">$0.00&nbsp;</th>
							</cfif>
						</tr>
						</table>
					</div>
					<br/>
				</cfif>
				
				<!--- messages ... htmleditformat so NO HTML --->
				<cfloop query="local.qryInvoiceMessages">
					<cfif len(local.qryInvoiceMessages.messageContent)>
						<div style="#local.invmsg#"><sup style="#local.sup#">#local.qryInvoiceMessages.footnote#</sup> #rereplace(htmleditformat(local.qryInvoiceMessages.messageContent),"(#chr(13)##chr(10)#|#chr(10)#)","<br />","all")#</div>
					</cfif>
				</cfloop>
				</body></html>

				</cfoutput>
			</cfsavecontent>

			<!--- create a PDF --->
			<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.invHeader } >
			<cfset local.footercol = { type="footer", evalAtPrint=true, txt=local.invFooter } >
			<cftry>
				<cfdocument filename="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoice.pdf" pagetype="letter" margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
						<cfdocumentsection margintop=".25" marginbottom=".5" marginright=".25" marginleft=".25">
							<cfdocumentitem attributeCollection="#local.headercol#">
								#local.invHeader#
							</cfdocumentitem>
							#local.invBody#
							<cfdocumentitem attributeCollection="#local.footercol#">
								#replace(local.invFooter,'<!-- cpp -->',' &bull; Page #cfdocument.currentsectionpagenumber# of #cfdocument.totalsectionpagecount#')#
							</cfdocumentitem>
						</cfdocumentsection>
					</cfoutput>
				</cfdocument>		
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfcatch>
			</cftry>

			<!--- File Name of Invoice --->
			<cfif NOT arguments.namedForBundle>
				<cfset local.invFileNameNoExt = "Inv #local.qryMember.membernumber# #local.qryInvoice.invoicenumber# #local.qryMember.firstname# #local.qryMember.lastname#">
			<cfelse>
				<cfset local.invFileNameNoExt = "Inv #local.qryMember.membernumber# #numberformat(local.qryInvoice.invoiceProfileID,'00000')# #local.qryInvoice.invoicenumber#">
			</cfif>
			<cfset local.invFileNameNoExt = replace(replace(rereplaceNoCase(local.invFileNameNoExt,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL')>

			<!--- watermark/encrypt pdf --->
			<cfif NOT ListFindNoCase("Closed,Delinquent,Paid",local.qryInvoice.invoiceStatus)>
				<cfset local.opacity = "1">
				<cfpdf action="addWatermark" copyFrom="#ExpandPath('/assets/common/watermarks/WatermarkDoNotPay.pdf')#" source="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoice.pdf" destination="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoiceW.pdf" foreground="true" opacity="#local.opacity#" showonprint="yes"/>
				<cffile action="move" source="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoiceW.pdf" destination="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoice.pdf">
			</cfif>
			<cfif arguments.encryptFile>
				<cfset application.objCommon.encryptPDF("#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoice.pdf","#arguments.tmpFolder#/#local.invFileNameNoExt#.pdf","","MC.#timeformat(now(),'hhmmss')#/#getTickCount()#tr!@l")>
			<cfelse>
				<cffile action="move" source="#arguments.tmpFolder#/un_#local.qryInvoice.invoicenumber#_invoice.pdf" destination="#arguments.tmpFolder#/#local.invFileNameNoExt#.pdf">
			</cfif>

			<cfset local.strInvoice.folderPath = arguments.tmpFolder>
			<cfset local.strInvoice.fileName = "#local.invFileNameNoExt#.pdf">
			<cfset local.strInvoice.invoicePath = "#local.strInvoice.folderPath#/#local.strInvoice.fileName#">
			<cfset local.strInvoice.displayName = local.strInvoice.fileName>
			<cfset local.strInvoice.invoicePayOnlineStr = local.invPayOnline>
		<cfelse>
			<cfhttp url="#application.paths.backendPlatform.url#?event=tasks.invoice.generateInvoice&siteID=#arguments.siteID#&invoiceID=#arguments.invoiceID#&encryptFile=#arguments.encryptFile#&Bundle=#arguments.namedForBundle#&bundleSourceFilePath=#arguments.tmpFolder#"></cfhttp>
			<cfset local.strInvoice = deserializejson(cfhttp.filecontent.tostring())>
		</cfif>

		<cfreturn local.strInvoice>
	</cffunction>

	<cffunction name="generateInvoiceStatement" access="public" returntype="void" output="no">
		<cfargument name="qryInvoices" type="query" required="yes">
		<cfargument name="forceIP" type="numeric" required="yes">
		<cfargument name="message" type="string" required="yes">
		<cfargument name="tmpFolder" type="string" required="yes">

		<cfset var local = structnew()>
		<cfset local.generateInvoiceOnBER = false>

		<!--- if no invoice statement to generate, kick out --->
		<cfif arguments.qryInvoices.recordcount is 0>
			<cfreturn>
		</cfif>

		<cfif NOT local.generateInvoiceOnBER>
			<cfquery name="local.qryInvoices" datasource="#application.dsn.membercentral.dsn#">
				select i.invoiceID, ip.profileID as invoiceProfileID, ip.imageExt, o.orgid, i.fullInvoiceNumber as invoiceNumber, i.dateDue, m2.memberid, 
					sum(it.cache_invoiceAmountAfterAdjustment) as totalBilled,
					sum(it.cache_activePaymentAllocatedAmount) as totalPayments,
					sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as balanceDue, i.invoiceCode,
					CASE WHEN 
						istat.status in ('Closed','Delinquent')
						AND i.invoiceCode is not null AND sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) > 0
						AND dbo.fn_tr_showInvoicePayOnlineLink(i.invoiceID) = 1
						THEN 1 ELSE 0 END as invPayOnline
				from dbo.tr_invoices as i 			
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.tr_invoiceProfiles as ip on ip.profileID = <cfif arguments.forceIP gt 0>#arguments.forceIP#<cfelse>i.invoiceProfileID</cfif>
				inner join dbo.ams_members as m on m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.memberid = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = m.orgID
				left outer join dbo.tr_invoiceTransactions as it on it.orgID = o.orgID and it.invoiceID = i.invoiceID
				where i.invoiceID IN (<cfqueryparam value="#valueList(arguments.qryInvoices.invoiceid)#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				group by i.invoiceID, ip.profileID, ip.imageExt, o.orgid, i.fullInvoiceNumber, i.dateDue, m2.memberid, i.invoiceCode, istat.status   
				order by invoiceProfileID, invoiceNumber
			</cfquery>

			<!--- Name and address --->
			<cfset local.qryMember = application.objMember.getMemberInfo(memberid=local.qryInvoices.memberid, orgID=local.qryInvoices.orgID)>
			<cfset local.qryAddress = application.objMember.getMemberAddressByBillingAddressType(orgID=local.qryInvoices.orgID, memberid=local.qryInvoices.memberid)>
			<cfset local.addressinfo = "#local.qryMember.prefix# #local.qryMember.firstname# #local.qryMember.middlename# #local.qryMember.lastname# #local.qryMember.suffix#<br/>">
			<cfif len(trim(local.qryMember.company))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryMember.company)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.attn))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.attn)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address1))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address1)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address2))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address2)#<br/>">
			</cfif>
			<cfif len(trim(local.qryAddress.address3))>
				<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.address3)#<br/>">
			</cfif>
			<cfset local.addressinfo = "#local.addressinfo##trim(local.qryAddress.city)#, #trim(local.qryAddress.statename)# #trim(local.qryAddress.postalCode)#<br/>">
			<cfif local.qryAddress.countryID is not 1>
				<cfset local.addressinfo = "#local.addressinfo##local.qryAddress.country#<br/>">
			</cfif>
			
			<cfset local.haystack = local.addressinfo>
			<cfset local.needle = "<br/>">
			<cfset local.numLines = (len(local.haystack) - len(ReplaceNoCase(local.haystack,local.needle,'','ALL')))/len(local.needle)>

			<cfoutput query="local.qryInvoices" group="invoiceProfileID">
				<cfquery name="local.qryInvoicesIP" dbtype="query">
					select *
					from [local].qryInvoices 
					where invoiceProfileID = #local.qryInvoices.invoiceProfileID#
					order by invoiceProfileID, invoiceNumber
				</cfquery>		
				<cfquery name="local.qryTotalDue" dbtype="query">
					select sum(balanceDue) as amt 
					from [local].qryInvoices 
					where invoiceProfileID = #local.qryInvoices.invoiceProfileID#
				</cfquery>	
				<cfquery name="local.qryTotalInvPayOnline" dbtype="query">
					select sum(invPayOnline) as totalInvPayOnline 
					from [local].qryInvoices 
					where invoiceProfileID = #local.qryInvoices.invoiceProfileID#
				</cfquery>	
				<cfset local.isShowInvoiceCode = 0>
				<cfif local.qryTotalInvPayOnline.totalInvPayOnline GT 0>
					<cfset local.isShowInvoiceCode = 1>
				</cfif>
						

				<!--- PDF header Styles--->
				<cfset local.c = "text-align:center;">
				<cfset local.l = "text-align:left;">
				<cfset local.r = "text-align:right;">
				<cfset local.bt = "border-top:1px solid ##000;">
				<cfset local.bb = "border-bottom:1px solid ##000;">
				<cfset local.bl = "border-left:1px solid ##000;">
				<cfset local.br = "border-right:1px solid ##000;">
				<cfset local.address = "font-size:12px;font-family:verdana;font-weight:bold;line-height:16px;margin-left:100px;">
				<cfset local.inv1 = "font-family:verdana;font-size:16px;line-height:22px;font-weight:bold;letter-spacing:4px;">
				<cfset local.inv2 = "font-family:verdana;font-size:11.5px;line-height:16px;padding:2px 0;">
				<cfset local.inv2a = "font-family:verdana;font-size:11px;line-height:16px;padding:2px 0;">
				<cfset local.inv3 = "font-family:verdana;font-size:11.5px;padding-top:8px;">
				<cfset local.logo = "width:400px;height:150px;">
				<cfset local.payonline = "font-size:11px;font-family:verdana;text-align:center;padding:4px;margin-top:6px;">
				<cfset local.notfinal = "border:2px solid ##f00;font-family:verdana;color:##f00;font-size:14px;font-weight:bold;text-align:center;line-height:22px;padding:4px;margin-top:6px;">
				<cfset local.paidbox = "margin-top:4px; text-align:center; ">
				<cfset local.infotbl = "margin-top:6px;">
				<cfset local.height = "180">
				<cfset local.width = "400">
				<cfset local.tdwidth = "310">


				<cfsavecontent variable="local.invHeader">
					<div id="header">
						<table cellspacing="0" cellpadding="0" width="100%">
						<tr>
							<td height="180" valign="top">
								<div style="#local.logo#">
									<cfif fileExists("#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryInvoices.invoiceProfileID#.#local.qryInvoices.imageExt#")>
										<img src="file:///#application.paths.RAIDUserAssetRoot.path#common/invoices/#local.qryInvoices.invoiceProfileID#.#local.qryInvoices.imageExt#" style="#local.logo#" />
									</cfif>
								</div>
							</td>
							<td height="180" width="310" valign="top">
								<div style="#local.infotbl#">
									<table cellspacing="0" cellpadding="2" width="310">
									<tr>
										<td colspan="2" style="#local.c# #local.bt# #local.bl# #local.br# #local.inv1#">STATEMENT OF ACCOUNT</td>
									</tr>
									<tr>
										<td style="#local.bt# #local.bl# #local.r# #local.inv2a#" width="50%"><b>Statement Date</b> &nbsp;</td>
										<td style="#local.bt# #local.br# #local.inv2a#">#dateformat(now(),"m/d/yyyy")#</td>
									</tr>
									<tr>
										<td style="#local.bl# #local.r# #local.inv2a#"><b>Account Number</b> &nbsp;</td>
										<td style="#local.br# #local.inv2a#">#local.qryMember.membernumber#</td>
									</tr>
									<tr>
										<td style="#local.bl# #local.bb# #local.r# #local.inv2a#"><b>Balance Due</b> &nbsp;</td>
										<td style="#local.br# #local.bb# #local.inv2a#">#dollarformat(local.qryTotalDue.amt)#</td>
									</tr>
									</table>
								</div>
							</td>
						</tr>					
						</table>
						<div style="#local.address#">
							#local.addressinfo#<cfif local.numLines lt 8>#repeatString("<br/>",8-local.numLines)#</cfif>
						</div>
					</div>				
				</cfsavecontent>

				<cfsavecontent variable="local.invBody">
					<html>
						<head>
							<style type="text/css">
								html, body { width:8in; padding:0; margin: 0; }
								##sec1 th { font-size:12px;font-family:verdana;line-height:15px;padding:4px 0;background-color:##cecece; }
								##sec1 td.sttl { text-align:right;background-color:transparent; padding-top:8px; }
								##sec1 td { font-size:11px;font-family:verdana;line-height:12px; }
								##sec4 { background-color:##cecece; }
								##sec4 th { font-size:12px;font-family:verdana;line-height:15px;padding:4px 0; }
								.invmsg { font-size:11px;font-family:verdana;line-height:13px;margin-top:14px; }
								.r { text-align:right; }
								.l { text-align:left; }
								.p { padding-bottom:6px; }
								.ph { height:6px; }
								.ind { margin-left:20px; padding-top:2px; }
								.bt { border-top:1px solid ##666; }
								.bb { border-bottom:1px solid ##666; }
								.bl { border-left:1px solid ##666; }
								.br { border-right:1px solid ##666; }
							</style>
						</head>
						<body>
							<div id="sec1">
								<table cellspacing="0" cellpadding="0" width="100%">
								<tr>
									<th class="bb l">&nbsp; Invoice</th>
									<cfif local.isShowInvoiceCode>
										<th class="bb r">Invoice Code &nbsp;</th>
									</cfif>	
									<th class="bb r">Due Date &nbsp;</th>
									<th class="bb r">Total Billed &nbsp;</th>
									<th class="bb r">Payments &nbsp;</th>
									<th class="bb r">Balance Due &nbsp;</th>
								</tr>
								<tr><td colspan="5" class="ph"></td></tr>
								<cfloop query="local.qryInvoicesIP">
									<tr valign="top">
										<td class="p">&nbsp; #local.qryInvoicesIP.invoiceNumber#</td>
										<cfif local.isShowInvoiceCode>
											<td class="p r">#local.qryInvoicesIP.InvoiceCode#  &nbsp;</td>
										</cfif>
										<td class="p r">#DateFormat(local.qryInvoicesIP.dateDue,"m/d/yyyy")# &nbsp;</td>
										<td class="p r">#dollarformat(local.qryInvoicesIP.totalBilled)#  &nbsp;</td>
										<td class="p r">#dollarformat(local.qryInvoicesIP.totalPayments)#  &nbsp;</td>
										<td class="p r">#dollarformat(local.qryInvoicesIP.balanceDue)# &nbsp;</td>
									</tr>
								</cfloop>
								</table>
							</div>
							<br/>

							<div id="sec4" class="bb bl bt br">
								<table cellspacing="0" cellpadding="0" width="100%">
								<tr>
									<th class="r">Total Due: &nbsp; #dollarformat(local.qryTotalDue.amt)# &nbsp;</th>
								</tr>
								</table>
							</div>

							<!--- messages ... htmleditformat so NO HTML --->
							<cfif len(trim(arguments.message))>
								<div class="invmsg">#rereplace(htmleditformat(trim(arguments.message)),"(#chr(13)##chr(10)#|#chr(10)#)","<br />","all")#</div>
							</cfif>
						</body>
					</html>
				</cfsavecontent>

				<cfset local.margintop = ".25">

				<!--- create a PDF --->
				<cfset local.headercol = { type="header", evalAtPrint=true, txt=local.invHeader } >
				<cftry>
					<cfdocument filename="#arguments.tmpFolder#/un_#local.qryMember.membernumber#_#local.qryInvoicesIP.invoiceprofileid#_statement.pdf" pagetype="letter" margintop="#local.margintop#" marginbottom=".25" marginright=".25" marginleft=".25" format="PDF" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
						<cfdocumentsection margintop="#local.margintop#" marginbottom=".5" marginright=".25" marginleft=".25">
							<cfdocumentitem attributeCollection="#local.headercol#">
								#local.invHeader#
							</cfdocumentitem>
							#local.invBody#
						</cfdocumentsection>
					</cfdocument>		
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
				</cfcatch>
				</cftry>

				<cfif arguments.forceIP gt 0>
					<cfset local.invFileNameNoExt = "Inv #local.qryMember.membernumber# #numberformat(0,'00000')#">
				<cfelse>
					<cfset local.invFileNameNoExt = "Inv #local.qryMember.membernumber# #numberformat(local.qryInvoicesIP.invoiceProfileID,'00000')#">
				</cfif>
				<cfset local.invFileNameNoExt = replace(replace(rereplaceNoCase(local.invFileNameNoExt,'[^A-Z0-9 ]','','ALL'),' ','_','ALL'),'__','_','ALL')>
				<cffile action="move" source="#arguments.tmpFolder#/un_#local.qryMember.membernumber#_#local.qryInvoicesIP.invoiceprofileid#_statement.pdf" destination="#arguments.tmpFolder#/#local.invFileNameNoExt#.pdf">
			</cfoutput>
		<cfelse>
			<cfhttp url="#application.paths.backendPlatform.url#?event=tasks.invoice.generateInvoiceStatement&invoiceids=#valueList(arguments.qryInvoices.invoiceid)#&forceIP=#arguments.forceIP#&message=#arguments.message#&tmpFolder=#arguments.tmpFolder#"></cfhttp>
		</cfif>
	</cffunction>

	<cffunction name="getInvoiceStatuses" access="public" output="false" returntype="query">
		<cfset var qryStatuses = "">

		<cfquery name="qryStatuses" datasource="#application.dsn.membercentral.dsn#">
			select statusID, status
			from dbo.tr_invoiceStatuses
			order by displayOrder
		</cfquery>

		<cfreturn qryStatuses>
	</cffunction>

	<cffunction name="getInvoiceProfiles" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">

		<cfset var qryInvoiceProfiles = "">

		<cfquery name="qryInvoiceProfiles" datasource="#application.dsn.membercentral.dsn#">
			select profileID, profileName
			from dbo.tr_invoiceProfiles
			where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">
			and status <> 'D'
			order by profileName
		</cfquery>

		<cfreturn qryInvoiceProfiles>
	</cffunction>

	<cffunction name="getPaymentProfiles" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryPayProfiles = "">

		<cfquery name="qryPayProfiles" datasource="#application.dsn.membercentral.dsn#">
			select mp.profileID, mp.profileName
			from dbo.mp_profiles as mp
			inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
			where mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and mp.status = 'A'
			and mg.gatewayType in ('AuthorizeCCCIM','SageCCCIM','BankDraft','AffiniPayCC','MCPayEcheck')
			order by mp.profileName
		</cfquery>

		<cfreturn qryPayProfiles>
	</cffunction>

	<cffunction name="getPaymentProfilesForAccountImport" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">

		<cfset var qryPayProfiles = "">

		<cfquery name="qryPayProfiles" datasource="#application.dsn.membercentral.dsn#">
			select mp.profileID, mp.profileName
			from dbo.mp_profiles as mp
			inner join dbo.mp_gateways as mg on mg.gatewayID = mp.gatewayID
			where mp.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			and mp.status = 'A'
			and mg.gatewayType = 'AcctImport'
			order by mp.profileName
		</cfquery>

		<cfreturn qryPayProfiles>
	</cffunction>

	<cffunction name="getFilteredMailInvoiceMemberList" access="public" output="false"  returntype="query">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.objInvoiceAdmin = CreateObject('component','model.admin.transactions.invoiceAdmin');

			if (not (len(arguments.event.getValue('statuses',''))))
				local.statuses = '3,4';
			else
				local.statuses = arguments.event.getValue('statuses','');

			local.invoiceNumber = reReplace(arguments.event.getValue('invoiceNumber',''),'[^0-9]','','ALL');
			local.trDetail = arguments.event.getTrimValue('trDetail','');
			local.invoiceProfiles = arguments.event.getValue('invoiceProfiles','');

			local.dueAmtStart = '';
			local.dueAmtEnd = '';
			if (len(arguments.event.getValue('dueAmtStart','')))
				local.dueAmtStart = val(ReReplace(arguments.event.getValue('dueAmtStart'),'[^0-9\.]','','ALL'));
			if (len(arguments.event.getValue('dueAmtEnd','')))
				local.dueAmtEnd = val(ReReplace(arguments.event.getValue('dueAmtEnd'),'[^0-9\.]','','ALL'));

			local.invAmtStart = '';
			local.invAmtEnd = '';
			if (len(arguments.event.getValue('invAmtStart','')))
				local.invAmtStart = val(ReReplace(arguments.event.getValue('invAmtStart'),'[^0-9\.]','','ALL'));
			if (len(arguments.event.getValue('invAmtEnd','')))
				local.invAmtEnd = val(ReReplace(arguments.event.getValue('invAmtEnd'),'[^0-9\.]','','ALL'));
		</cfscript>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.event.getValue('cardOnFile','')>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.event.getValue('assocType',''))) and len(trim(arguments.event.getValue('linkedRecordOptions','')))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
				declare @totalCount int;

				IF OBJECT_ID('tempdb..##tmpInvMembers') is not null
					DROP TABLE ##tmpInvMembers;

				<cfif local.runGetAssociated>
					#local.objInvoiceAdmin.getAssociatedWithQuery(associatedMemberID=arguments.event.getValue('associatedMemberID',''), 
						associatedGroupID=arguments.event.getValue('associatedGroupID',''), linkedRecords=arguments.event.getValue('linkedRecords',''),
						linkedRecordOptions=arguments.event.getValue('linkedRecordOptions'), assocType=arguments.event.getValue('assocType',''))#
				</cfif>

				select m.memberid, RTRIM(m.lastname + ' ' + isnull(m.suffix, '')) + ', ' + m.firstname + ' (' + m.membernumber + ')' AS memberName,
					m.company as membercompany, me.email, dbo.sortedIntList(tmp.invoiceID) as invoiceIDList, 
					ROW_NUMBER() OVER (ORDER BY m.lastname + isnull(m.suffix,'') + m.firstname  + m.membernumber #arguments.event.getValue('orderDir')#) as row
				into ##tmpInvMembers
				from (
					select i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber as invoiceNumber,
						ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode,
						sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
						sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
						mActive.memberID, istat.status as invoiceStatus
					from dbo.tr_invoices as i
					inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
					inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
					inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
					inner join dbo.ams_members as mActive on mActive.orgID = @orgID and mActive.memberID = m.activeMemberID 
					inner join dbo.organizations as o on o.orgID = m.orgID
					<cfif local.runGetAssociated>
						inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
					</cfif>
					left outer join dbo.tr_invoiceTransactions as it 
					<cfif len(local.trDetail)>
						inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
					</cfif>
					on it.orgID = @orgID and it.invoiceID = i.invoiceID
					where i.orgID = @orgID
					and mActive.status = 'A'
					and istat.status in ('Closed','Delinquent','Paid')
					<cfif arguments.event.getValue('chkAll',0) is 0 and len(arguments.event.getTrimValue('invoiceIDList','')) and arrayLen(reMatch("[^0-9,]",arguments.event.getTrimValue('invoiceIDList'))) is 0>
						and i.invoiceID in (#arguments.event.getTrimValue('invoiceIDList')#)
					<cfelse>
						<cfif arguments.event.getValue('chkAll',0) is 1 and len(arguments.event.getTrimValue('notInvoiceIDList','')) and arrayLen(reMatch("[^0-9,]",arguments.event.getTrimValue('notInvoiceIDList'))) is 0>
							and i.invoiceID not in (#arguments.event.getTrimValue('notInvoiceIDList')#)
						</cfif>
						<cfif len(local.statuses)>
							and i.statusID in (<cfqueryparam value="#local.statuses#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceProfiles)>
							and i.invoiceProfileID in (<cfqueryparam value="#local.invoiceProfiles#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						<cfif len(local.invoiceNumber)>
							and i.invoiceNumber = <cfqueryparam value="#val(local.invoiceNumber)#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
						<cfif len(local.trDetail)>
							and t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.trDetail#%">
						</cfif>
						<cfif len(arguments.event.getTrimValue('duestartDate',''))>
							and i.dateDue >= <cfqueryparam value="#arguments.event.getValue('duestartDate','')#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.event.getTrimValue('dueendDate',''))>
							and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('dueendDate',''))#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.event.getTrimValue('billedstartDate',''))>
							and i.dateBilled >= <cfqueryparam value="#arguments.event.getValue('billedstartDate','')#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.event.getTrimValue('billedendDate',''))>
							and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('billedendDate',''))#" cfsqltype="CF_SQL_DATE">
						</cfif>
						<cfif len(arguments.event.getValue('cardOnFile',''))>
							and (
								<cfif local.cof0>
									i.payProfileID is null
								</cfif>
								<cfif local.cof0 and listLen(local.cofList)>
									or
								</cfif>
								<cfif listLen(local.cofList)>
									i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
								</cfif>
								)
						</cfif>
					</cfif>
					group by i.invoiceID, i.invoiceProfileID, i.statusID, i.fullInvoiceNumber, ip.profileName, i.dateBilled, i.dateDue, i.invoiceCode, mActive.memberID, istat.status
					having i.invoiceCode = i.invoiceCode
					<cfif len(local.dueAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#local.dueAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.dueAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#local.dueAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtStart)>
						and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#local.invAmtStart#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
					<cfif len(local.invAmtEnd)>
						and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#local.invAmtEnd#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					</cfif>
				) as tmp
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = tmp.memberID
				inner join dbo.ams_memberEmails as me on me.orgID = @orgID and me.memberID = m.memberID
				inner join dbo.ams_memberEmailTags as metag on metag.orgID = @orgID 
					and metag.memberID = me.memberID 
					and metag.emailTypeID = me.emailTypeID
				inner join dbo.ams_memberEmailTagTypes as metagt on metagt.orgID = @orgID 
					and metagt.emailTagTypeID = metag.emailTagTypeID 
					and metagt.emailTagTypeID = <cfqueryparam value="#arguments.event.getValue('emailTagType',0)#" cfsqltype="CF_SQL_INTEGER">
				group by m.memberid, m.lastname, m.suffix, m.firstname, m.membernumber, m.company, me.email;

				set @totalCount = @@ROWCOUNT;

				select *, @totalCount as totalCount
				from ##tmpInvMembers
				where row > <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">
				and row <= <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart') + arguments.event.getValue('count')#">
				order by row;

				IF OBJECT_ID('tempdb..##tmpInvMembers') is not null
					DROP TABLE ##tmpInvMembers;
				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfreturn local.qryInvoices>	
	</cffunction>

	<cffunction name="getInvoiceItemInfo" access="public" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="invoiceID" type="numeric" required="true">

		<cfset var qryInvoiceItemInfo = "">

		<cfquery name="qryInvoiceItemInfo" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @invoiceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.invoiceID#">, 
				@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">, 
				@siteID int, @appType varchar(30), @generatedBy varchar(400), @allowUpdateCOF bit = 1;

			SELECT TOP 1 @siteID = siteID, 
				@appType = CASE 
					WHEN itemType IN ('contribFieldInstallment','contribInstallment') THEN 'contribution'
					WHEN itemType = 'Dues' THEN 'subscription'
					ELSE ''
					END
			FROM dbo.tr_invoiceItems
			WHERE orgID = @orgID
			AND invoiceID = @invoiceID;

			IF @appType = 'Contribution' BEGIN
				SET @allowUpdateCOF = 0;

				SELECT TOP 1 @generatedBy = p.programName
				FROM dbo.tr_invoiceItems AS ti
				INNER JOIN dbo.cp_contributionDistributions AS cd ON cd.contribDistributionID = ti.itemID
				INNER JOIN dbo.cp_distributions AS d ON d.distribID = cd.distribID
				INNER JOIN dbo.cp_programs AS p ON p.programID = d.programID
				WHERE ti.orgID = @orgID
				AND ti.itemType = 'contribInstallment'
				AND ti.invoiceID = @invoiceID;
				
				IF @generatedBy IS NULL
					SELECT TOP 1 @generatedBy = p.programName
					FROM dbo.tr_invoiceItems AS ti
					INNER JOIN dbo.cf_fieldData AS fd ON fd.dataID = ti.itemID
					INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
					INNER JOIN dbo.cp_programs AS p ON p.programID = f.detailID
						AND p.siteResourceID = f.controllingSiteResourceID
					WHERE ti.orgID = @orgID
					AND ti.itemType = 'contribInstallment'
					AND ti.invoiceID = @invoiceID;
			END

			IF @appType = 'Subscription' BEGIN
				SET @allowUpdateCOF = 0;

				SELECT TOP 1 @generatedBy = s.subscriptionName
				FROM dbo.tr_invoiceItems AS ti
				INNER JOIN dbo.sub_subscribers AS ss ON ss.orgID = @orgID
					AND ss.subscriberID = ti.itemID
				INNER JOIN dbo.sub_subscriptions AS s ON s.orgID = @orgID 
					AND s.subscriptionID = ss.subscriptionID
				WHERE ti.orgID = @orgID
				AND ti.itemType = 'Dues'
				AND ti.invoiceID = @invoiceID;
			END

			SELECT @invoiceID AS invoiceID, @appType AS generatedByApp, @generatedBy AS generatedBy, @allowUpdateCOF AS allowUpdateCOF;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryInvoiceItemInfo>
	</cffunction>

</cfcomponent>
