﻿<cfsetting requesttimeout="300" />
<cfparam name="local.msgText" default="" />
<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>
<cfsavecontent variable="local.clientFormJS">
	<cfoutput>
	<style type="text/css">
	##feesDueTbl tr.ev_modern { background-color:##dddee3; }
	##feesDueTbl tr.odd_modern { background-color:##fff; }
	##feesDueTbl th.longDesc, ##feesDueTbl td.longDesc { height:78px; }
	##feesDueTbl td.totals { font-weight:bold;}	
	##clientFeesDueTbl tr.ev_modern { background-color:##dddee3; }
	##clientFeesDueTbl tr.odd_modern { background-color:##fff; }
	##clientFeesDueTbl th.longDesc, ##feesDueTbl td.longDesc { height:78px; }
	##clientFeesDueTbl td.totals {font-weight:bold;}	
	.saveButton { padding: 0 35px 0 0; }	
	.dspTabTrue { visibility:visible; }
	.dspTabFalse { visibility:hidden; }
	.confirmationMsg { padding: 10px 5px 10px 5px; border: 2px solid ##cccccc; }
	div.card:not(:first-child){margin-top:8px;}
	##buttonSetTop button.btn,##buttonSetBottom button.btn{margin: 5px 5px;}
	a.mc_anchor{display: block; position: relative; top: -120px; visibility: hidden;}
	##frmClient .iti__selected-country-primary{
		margin-top: 10px !important;
	}
	</style>
	<link rel="stylesheet" href="/assets/common/javascript/intl-tel-input/25.2.0/css/intlTelInput.css" />
	<script type="text/javascript" src="/assets/common/javascript/resourceFields.js#local.assetCachingKey#"></script>
	<script language="JavaScript">
		
	var subCheckHasRun1 = false;
	var subCheckHasRun2 = false;
	var subCheckHasRun3 = false;		
		
	$(document).ready(function(){	
		var currTab = $('##currTab').val();
		var typeOption = $('##typeID option:selected').text();
		var sourceOption = $.trim($('##sourceID option:selected').text());
		formOriginalData = $("##frmClient").serialize();
		
		<cfif currTab eq 'clientpayment' and local.clientPaymentSaleRecorded and not val(local.qryGetPaymentDataTotals.amtToBePaidTotal)>		
			$("##clientReferralAmount").val('0');
			$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=lawyers');
			$('##frmClient').submit();
		</cfif>

		<cfif currTab eq 'filters' and  (local.callPaymentProcessed or local.clientPaymentSaleRecorded)>
			$("##filterFS").find("input:not(:disabled), select:not(:disabled), textarea:not(:disabled)").prop("disabled",true);
		</cfif>

		<cfif (local.callPaymentProcessed or local.clientPaymentSaleRecorded) and arguments.event.getValue("prevPanelID","0") and arguments.event.getValue("prevPanelID","0") neq arguments.event.getValue("panelid1","0")>
			$("##changePanelWarning").html();
			$('##changePanelWarning').removeClass('alert alert-danger');
			$('##changePanelWarning').hide();
			$("##changePanelWarning").addClass('alert alert-info');
			$("##changePanelWarning").html('<div>Please review the <a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editMemberLink#&memberID=#local.clientFeeMemberID#&tab=transactions" target="_blank" style="text-decoration: underline;">Client Fees record</a> to manage the transactions related to this panel change.</div>');
			$("##changePanelWarning").show();			
		</cfif>

		$("##changePanelWarning").html();
		$('##btnChangePanel').click(function(){
			var _this = $(this);
			var thisDivContent = "<div><b>Warning!</b></div>
				<br/>			
				<div>
					Changing the primary panel may result in accounting adjustments for the Client Fee.&nbsp;
					<button type='button' class='btn btn-light' name='btnProceedChangePanel' id='btnProceedChangePanel' onClick='editPanel();'>Proceed</button>&nbsp;&nbsp;
					<button type='button' class='btn btn-light' name='btnCancelChangePanel' id='btnCancelChangePanel'  onClick='cancelEditPanel();'>Cancel</button>
			</div>";
			$('##changePanelWarning').addClass('alert alert-danger');
			$("##changePanelWarning").html(thisDivContent);
			$("##changePanelWarning").show();
		});
		$('##btnCancelChangePanel').click(function(){
			var _this = $(this);
			$("##filterFS").find("input:not(:disabled), select:not(:disabled), textarea:not(:disabled)").prop("disabled",true);
			$("##changePanelWarning").hide();
		});		
		
		<cfif currTab eq 'clientpayment'>
			$(".proceedBtn").attr("disabled","disabled");
			$('##skipPaymentBtn').click(function(){
				$('##payLater').val(1);
				$(".proceedBtn").trigger('click');
			});
		</cfif>	
		
		if (currTab == "client") {
		<cfif val(local.isDeleted)>
			$('##filtersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
			$('##clientPaymentTab').removeClass('dspTabTrue').addClass('dspTabFalse');
			$('##lawyersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
			$("##agencyFields").hide();
			$("a[href=##agencySection]").addClass('d-none');
			$("##otherSource").hide();
		<cfelse>		
			switch (typeOption) {
				case "Referral to Agency":
					$('##filtersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##clientPaymentTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##lawyersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$("##agencyFields").show();
					$("a[href=##agencySection]").removeClass('d-none');
					$('.proceedBtn').html('Submit Referral');	
					$(".reqPhoneText").hide();
					break;
				default:
					$('##filtersTab').removeClass('dspTabFalse').addClass('dspTabTrue');
					$('##clientPaymentTab').removeClass('dspTabFalse').addClass('dspTabTrue');
					$('##lawyersTab').removeClass('dspTabFalse').addClass('dspTabTrue');					
					$("##agencyFields").hide();
					$("a[href=##agencySection]").addClass('d-none');
					$('.proceedBtn').html('Proceed');
					$(".reqPhoneText").show();				
			}

			switch (sourceOption) {
				case "Other":
					$("##otherSource").show();
					break;
				default:
					$("##otherSource").hide();
			}
			<cfif val(local.isAgencyReferral)>
				$(".reqPhoneText").hide();
				<cfif val(local.isClosed)>
					$('##filtersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##clientPaymentTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##lawyersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
				<cfelse>
					dspAgencyData(#local.agencyID#);
				</cfif>
			</cfif>			
		</cfif>
		}

		$('.expandPanelBtn').on('click',function(){
			var rowNum =  $(this).attr("data-rowNum");
			$('##clientPanelContent_'+rowNum).toggle('100',function(){
				if($('##clientPanelContent_'+rowNum).css('display') == 'none') {
					$('img##expandPanelIcon_'+rowNum).attr('src','/assets/common/javascript/dhtmlxgrid/imgs/plus.gif');
				} else {
					$('img##expandPanelIcon_'+rowNum).attr('src','/assets/common/javascript/dhtmlxgrid/imgs/minus.gif');
				}
			});
		}); 

		if(currTab == 'lawyers'){
			<cfif local.isCoordination>
				$('.proceedBtn').html('Submit as Pending');
			<cfelse>
				$('.proceedBtn').html('Submit Referral');
			</cfif>
		} else{
			if (typeOption != "Referral to Agency") {
				$('.proceedBtn').html('Proceed');
			}
		}

		$("select").change(function(){			
			var sourceOption = "";
			
			$("##sourceID option:selected").each(function () {
				sourceOption += $(this).text() + " ";
			});

			if($.trim(sourceOption) == "Other"){
				$("##otherSource").show();
			}
			else {
				$("##otherSource").val('');
				$("##otherSource").hide();
			}
		});

		$('.copyClientBtn').click(function(){
			var errorMsg = "";
			if (confirm("Are you sure you want to copy this Client's referral information?")) {
				$('##clientID').val('0');
				$('##clientParentID').val('0');
				$('##typeID').val('0');
				$('##agencyID').val('0');
				$('##repID').val('0');
				$('##repParentID').val('0');
				$('##clientReferralID').val('0');
				$('##caseID').val('0');	
				$("##frmClient").attr('action', '#this.link.addClient#&copy=1');
				$('##frmClient').submit();
			}
		});
		
		$('##btnPrevResults,##btnNextResults').click(function(){
			var _this = $(this);
			$("##frmClient").attr('action', _this.data('action'));
			$('##frmClient').submit();
		});

		$('.memberListButton').click(function(){
			var _this = $(this).find('input:checkbox:first');
			var _selected = $('##selectedMemberIDs');
			
			if($(this).find('label##checkBoxBtn').hasClass('btn-success')){
				_this.removeAttr("checked");
				$(this).find('label##checkBoxBtn').toggleClass('btn-success btn-outline-first');
				$(_this).siblings('##checkBoxText').html('   SELECT   ');
				if ($.inArray(_this.val(), _selected.val().replace(/,\s+/g, ',').split(',')) >= 0) {
					var _newValue = removeValue(_selected.val(),_this.val(),',');
					_selected.val(_newValue);
				}
			} else {
				_this.attr("checked", "checked");
				$(this).find('label##checkBoxBtn').toggleClass('btn-outline-first btn-success');
				$(_this).siblings('##checkBoxText').html('SELECTED');
				if(_selected.val() == ""){
					_selected.val(_this.val());
				} else {
					if (! $.inArray(_this.val(), _selected.val().replace(/,\s+/g, ',').split(',')) >= 0) {
						_selected.val(_selected.val()+ ',' + _this.val());
					}
				}
			}
		});
		
		$('.proceedBtn').click(function(){
			var errorMsg = "";
			var typeLabel = $('##typeID option:selected').text();
 			var changeInd = checkFormChanged();
			var skipClientPayment = $("##skipClientPayment").val();
			
			disableButtons();

			switch(currTab) {
				case "client":
					$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=filters');
					break;
				case "filters":
					<cfif not local.callPaymentProcessed>
						if(skipClientPayment == 0){
							$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=clientpayment');
						}
						else {
							$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=lawyers');
						}
					<cfelse>
						$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=lawyers');
					</cfif>
					break;
				case "clientpayment":
					$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=lawyers');
					break;						
				case "lawyers":
					$("##frmClient").attr('action', '#this.link.completeReferral#');
					break;
			}

			errorMsg = validateClientReferral();

			if (errorMsg.length > 0) {
				errorMsg = "There were errors with your submission.<br>" + errorMsg ;
				showMsg(errorMsg);
				enableButtons();
			} else {
				if(typeLabel == 'Referral to Agency') 
					$("##frmClient").attr('action', '#this.link.completeReferral#');
				if(currTab == "lawyers"){
					fnCompleteReferral();
				} else {
					$('##frmClient').submit();
				}
			}
		});

		function fnCompleteReferral(){
			$('.resultContainer').hide();
			$('.loaderContainer').show();
			$('##lawyerLink, ##clientLink, ##filterLink').addClass('disabled');
			$.ajax({
				url: '#this.link.completeReferral#&mode=stream&',
				type: 'POST',
				data: $("##frmClient").serialize(),
				dataType:'json',
				success: function(response){
					$('.loaderContainer').hide();
					$('.resultContainer').show();
					$('##confMessage').html(response.confMessage);
					$('##clientMsgWrapper').hide();
					$('##confMessage').show();
					$('.memberCard').hide();
					$('.hideAfterSubmit').hide();
					$('.showAfterSubmit').show();
					if (response.selectedMemberIDs.length > 0) {
						for (var i=0; i < response.selectedMemberIDs.length; i++){
							$('##memberCard_' + response.selectedMemberIDs[i]).show();
						}
					}
				},
				error: function(ErrorMsg){
					alert(ErrorMsg);
				}
			});
		}

		$('.saveClientBtn').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=' + currTab);		
 			var changeInd = checkFormChanged();
			
			disableButtons();
			
			if(currTab == "client") {
				if($('##typeID option:selected').text() == 'Referral to Agency' && $.trim($('##otherAgency').val()).length > 0) {
					var chkResult = function(r) {
						if (r.success && r.success.toLowerCase() == 'true') {
							if (r.html && r.html.toString().toLowerCase() == 'true'){
								errorMsg = validateClientReferral();
								if (errorMsg.length > 0) {
									errorMsg = "There were errors with your submission.<br>" + errorMsg ;
									showMsg(errorMsg);
									enableButtons();
								} else {
									$('##frmClient').submit();
								}
							}
							else{
								errorMsg = "The Agency name entered is already in use. Please enter a new Agency name.";
								if (errorMsg.length > 0) {
									showMsg(errorMsg);
									enableButtons();
								}
							}
						}
					};
					var objParams = { referralID:$('##referralID').val(), agencyName:$('##otherAgency').val(), agencyID:0 };
					TS_AJX('ADMINREFERRALS','checkAgencyName',objParams,chkResult,chkResult,10000,chkResult);
				} else {
					errorMsg = validateClientReferral();
					if (errorMsg.length > 0) {
						errorMsg = "There were errors with your submission.<br>" + errorMsg ;
						showMsg(errorMsg);
						enableButtons();
					} else {
						$('##frmClient').submit();
					}
				}	
			} else {
				errorMsg = validateClientReferral();
				if (errorMsg.length > 0) {
					errorMsg = "There were errors with your submission.<br>" + errorMsg ;
					showMsg(errorMsg);
					enableButtons();
				} else {
					$('##frmClient').submit();
				}		
			}			
		});
		
		$('.deleteClientBtn').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&delete=1&tab=' + currTab);			
 			var changeInd = checkFormChanged();
					
			if (confirm("Are you sure you want to delete this Referral?")) {
				<cfif currTab is "client" and val(local.clientReferralID)>
				errorMsg = validateClientReferral();
				</cfif>	
				if (errorMsg.length > 0) {
					errorMsg = "There were errors with your submission.<br>" + errorMsg ;
					showMsg(errorMsg);
				}
				else {
					$('##frmClient').submit();
				}
			}
		});
		
		$('.activateClientBtn').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&activate=1&tab=' + currTab);			
 			var changeInd = checkFormChanged();
					
			if (confirm("Are you sure you want to activate this Referral?")) {
				<cfif currTab is "client" and val(local.clientReferralID)>
				errorMsg = validateClientReferral();
				</cfif>	
				if (errorMsg.length > 0) {
					errorMsg = "There were errors with your submission.<br>" + errorMsg ;
					showMsg(errorMsg);
				}
				else {
					$('##frmClient').submit();
				}
			}
		});			

		$('.reopenBtn').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&reopen=1&tab=' + currTab);			
			var changeInd = checkFormChanged();
					
			if (confirm("Are you sure you want to re-open this Referral?")) {
				<cfif currTab is "client" and val(local.clientReferralID)>
				errorMsg = validateClientReferral();
				</cfif>	
				if (errorMsg.length > 0) {
					errorMsg = "There were errors with your submission.<br>" + errorMsg ;
					showMsg(errorMsg);
				}
				else {
					$('##frmClient').submit();
				}
			}
		});			
		
		$('##clientPaymentBtn').click(function(){
			$(".clientPaymentBtn").attr("disabled","disabled");
			<cfif (currTab eq 'clientpayment' OR currTab eq 'client') and local.clientPaymentSaleRecorded and val(local.qryGetPaymentDataTotals.amtToBePaidTotal)>			
				addPayment('#local.addClientPaymentEncString#');
			<cfelse>
				alert("There is no client referral fee due at this time.");
			</cfif>			
		});
		
		$('##clientSaleBtn').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&sr=1&tab=' + currTab);		
 			var changeInd = checkFormChanged();
			
			$(".clientSaleBtn").attr("disabled","disabled");
			disableButtons();
						
			errorMsg = validateClientReferral();
			if (errorMsg.length > 0) {
				errorMsg = "There were errors with your submission.<br>" + errorMsg ;
				showMsg(errorMsg);
				enableButtons();
				$('##clientSaleBtn').removeAttr("disabled");
			} else {
				$('##frmClient').submit();
			}			
		});		
		
		
		$('##clientLink').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.tabLink#&tabSwitch=1&tab=client');
			var changeInd = checkFormChanged();			
			var currTab = $('##currTab').val();

			if ((currTab != "lawyers")) {
				errorMsg = validateClientReferral();
			}

			if (errorMsg.length > 0) {
				errorMsg = "There were errors with your submission.<br>" + errorMsg ;
				showMsg(errorMsg);
			} else {
				$('##frmClient').submit();
			}
		});	

		$('##filterLink').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=filters');
			var changeInd = checkFormChanged();			
			var currTab = $('##currTab').val();

			if ((currTab != "lawyers")) {
				errorMsg = validateClientReferral();
			}

			if (errorMsg.length > 0) {
				errorMsg = "There were errors with your submission.<br>" + errorMsg ;
				showMsg(errorMsg);
			} else {
				$('##frmClient').submit();
			}
		});	
				
		$('##clientPaymentLink').click(function(event){
			event.preventDefault();
		});
	
		$('##lawyerLink').click(function(){
			var errorMsg = "";
			$("##frmClient").attr('action', '#local.saveLink#&saveClientBtn=1&tab=lawyers');
 			var changeInd = checkFormChanged();				

			errorMsg = validateClientReferral();

			if (errorMsg.length > 0) {
				errorMsg = "There were errors with your submission.<br>" + errorMsg ;
				showMsg(errorMsg);
			} else {
				$('##frmClient').submit();
			}
		});

		$('##dapAllLink').click(function(){
			$("##frmClient").attr('action', '#local.tabLink#&saveClientBtn=1&tab=lawyers&dspAll=1');
			$('##frmClient').submit();
		});

		$("##typeID").change(function(){
			var typeLabel = $('##typeID option:selected').text();
			switch(typeLabel) {
				case "Referral to Agency":
					$('##filtersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##clientPaymentTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$('##lawyersTab').removeClass('dspTabTrue').addClass('dspTabFalse');
					$("##agencyFields").show();
					$("a[href=##agencySection]").removeClass('d-none');
					$('.proceedBtn').html('Submit Referral');
					$(".reqPhoneText").hide();
					break;	
				default:	
					$('##filtersTab').removeClass('dspTabFalse').addClass('dspTabTrue');
					$('##clientPaymentTab').removeClass('dspTabFalse').addClass('dspTabTrue');
					$('##lawyersTab').removeClass('dspTabFalse').addClass('dspTabTrue');
					$("##agencyFields").hide();
					$("a[href=##agencySection]").addClass('d-none');
					$('.proceedBtn').html('Proceed');
					$(".reqPhoneText").show();					
			}
		});
		
		$("##statusID").change(function(){
			let thisVal = 0;

			let selectedArr = $('##statusID').find('option:selected')
				.map(function(index,element){ 
					return $(element).attr("data-custom");
			}).toArray();

			if (selectedArr.length > 0) {
				thisVal = selectedArr[0];
			}

			if (thisVal == 1){
				$('##emailNotificationModal').modal('show');
			} else {
				$('##sendEmailInd').val('0');
			}
		});

		$('##DoNotSendEmailBtn').click(function(){
			$('##sendEmailInd').val('0');
			$('##emailNotificationModal').modal('hide');
		});

		$('##sendEmailBtn').click(function(){
			$('##sendEmailInd').val('1');
			$('##emailNotificationModal').modal('hide');
		});	

		$('.addNotesBtn').click(function(){
			fnAddNote($(this).attr('data-type'),$(this).attr('data-cid'));
		});

		$('.printBtn').click(function(){
			var rrlink = '#this.link.editClient#&print=1&mode=direct&clientReferralID=#local.clientReferralID#';
			$('##divReportShowScreen').html('<iframe width="1" height="1" src="' + rrlink + '" onload="hideDiveReport();"></iframe>');
			window.setTimeout(function(){hideDiveReport()},6000);
		});

		$(document).on('change','##newCounselorID',function(){
			var _name = $('##newCounselorID option:selected').attr('data-name');
			var _mid = $('##newCounselorID option:selected').val();
			$('##counselorMemberID').val(_mid);
			$('##counselorNameHolder').html(_name);
		});

		<cfif this.allowFeeDiscrepancy>
		$(document).on('click','##btnSendFeeSbmsnEmail',function(){
			emailFeeSubmissionLinkToClient();
		});
		function emailFeeSubmissionLinkToClient() {
			var localOptions = '';
			MCModalUtils.showModal({
					isslideout: true,
					size: 'lg',
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					title: 'E-mail Status Update',
					iframe: true,
					contenturl: "#this.link.emailFeeSubmission#&clientreferralid=#local.clientReferralID#&clientid=#local.clientID#",
					strmodalfooter : {
					showclose: false,
					classlist: 'd-flex',
					buttons: [
							{
								class:"btn btn-sm btn-primary d-none",
								clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnEmailFeeSubmission").click',
								label: 'Preview E-mail', 
								name: 'btnEmailFeeSubmission',
								id: 'btnEmailFeeSubmission'
							},
							{
								class: "btn btn-primary btn-sm d-none",
								clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnContinuePreview").click',
								label: 'Continue', 
								name: 'btnContinuePreview',
								id: 'btnContinuePreview'
							},
							{
								class: "btn btn-primary btn-sm d-none",
								clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSubmit").click',
								label: 'Send E-mail', 
								name: 'btnSubmit',
								id: 'btnSubmit'
							}
						]
				}
				});
		}
		</cfif>
		function chainedSelect(elemIdName,elemIdDefault,elemValueDefault,selected,format){
			var strURL = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mode=stream&pg=admin&mca_jsonlib=referral&mca_jsonfunc=getSubPanels&panelid="+ selected + "&isActive=1";
			$.ajax({
				url: strURL,
				dataType: 'json',
				success: function(response){
					$('##' + elemIdName).empty();
						for (var i = 0; i < response.DATA.length; i++) {
						var o = new Option(response.DATA[i][1], response.DATA[i][0]);
						/* jquerify the DOM object 'o' so we can use the html method */
						$(o).html(response.DATA[i][1]);
						$('##' + elemIdName).append(o);
						}
				},
				error: function(ErrorMsg){
					/* alert(ErrorMsg); */
				}
			})
		}
		function showMsg(msg){
			$('##clientMsg').html(msg);
			$('##clientMsgWrapper').show();
			scrollTo($('##clientMsgWrapper'));
		}	
		function scrollTo(_obj){
			if(_obj.offset() != undefined){
				$('html, body').animate({					
					scrollTop: _obj.offset().top -200					
				}, 1000);
			}
		}
		function callChainedSelect(panel,subpanel){
			var strSelected = $("##" + panel).val();
			chainedSelect(
				subpanel,        /* select box id  */
				0,                  /* select box default value */
				'Select Options',   /* select box default text */
				strSelected,        /* value of the select */
				'json'              /* return format */
			);					
		}			
		
		$('body').on('change', '##panelid1', function(e) {			
			$('##changePanelWarning').removeClass('alert alert-danger');
			$('##changePanelWarning').hide();
			var dspResult = function(r) {
				$("##skipClientPayment").val(0);
				var clientreferralamount = r.data.clientreferralamount[0];
				var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
				if( $.trim(clientreferralamount).length == 0 || ($.trim(clientreferralamount).length > 0 && !amountRegex.test($.trim(clientreferralamount)) ) || ($.trim(clientreferralamount).length > 0 && amountRegex.test($.trim(clientreferralamount)) && clientreferralamount == 0 )) {
					$("##skipClientPayment").val(1);
				} 
			};

			var dspResult = function(r) {
				$("##skipClientPayment").val(0);
				var clientreferralamount = r.data.clientreferralamount[0];
				<cfif structKeyExists(local, "qryGetPaymentData")>
					var #ToScript(local.qryGetPaymentData.cache_amountAfterAdjustment,"clientpaidamount")#;
				<cfelse>
					var #ToScript("0","clientpaidamount")#;
				</cfif>
				var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;	

				<cfif currTab eq 'filters' and  not local.callPaymentProcessed and not local.clientPaymentSaleRecorded>	
				if( $.trim(clientreferralamount).length == 0 || ($.trim(clientreferralamount).length > 0 && !amountRegex.test($.trim(clientreferralamount)) ) || ($.trim(clientreferralamount).length > 0 && amountRegex.test($.trim(clientreferralamount)) && clientreferralamount == 0 )) {
					$("##skipClientPayment").val(1);
				}
				<cfelseif currTab eq 'filters' and  (local.callPaymentProcessed or local.clientPaymentSaleRecorded)>							
					$("##refClientsTab li").each(function(){
						var liText = $(this).text();
						var thisText = "Lawyers";
						if(liText.indexOf(thisText) == 1){
							$(this).hide();
						}
					});

					if($.trim(clientpaidamount).length > 0 && amountRegex.test($.trim(clientpaidamount)) && $.trim(clientreferralamount).length > 0 && amountRegex.test($.trim(clientreferralamount))){
						if(clientreferralamount == clientpaidamount){
							var thisDivContent = '<div>No difference in Client Fees for this change. Click “Proceed” to continue.</div>';
							$("##changePanelWarning").addClass('alert alert-info');
						} else {
							var thisDivContent = '<div>This change may result in a credit balance or a balance due for the Client Fee. Click “Proceed” to continue. Credit balance or balances due may be managed on the <a href="#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editMemberLink#&memberID=#local.clientFeeMemberID#&tab=transactions" target="_blank" style="text-decoration: underline;">Client Fees record</a>.</div>';
							$("##changePanelWarning").addClass('alert alert-danger alert-dismissible fade show');
						}						
						$("##changePanelWarning").html(thisDivContent);
						$("##changePanelWarning").show();
						$("##skipClientPayment").val(1);
					}				
					$("##panelChangedInd").val(1);
					$("##prevPanelID").val(#local.panelid1#);
				</cfif>
			};			

			var panelID = $(this).val();
			var objParams = { panelID:panelID };
			TS_AJX('ADMINREFERRALS','getPanelByID',objParams,dspResult,dspResult,10000,dspResult);			

			callChainedSelect("panelid1","subpanelid1");
		});
		
		$('body').on('change', '##panelid2', function(e) {
			callChainedSelect("panelid2","subpanelid2");
		});
		
		$('body').on('change', '##panelid3', function(e) {
			callChainedSelect("panelid3","subpanelid3");
		});		

		$("[data-hide]").on("click", function(){
			$(this).closest("." + $(this).attr("data-hide")).hide();
		});

		$('##assignToMeBtn').on('click',function(){
			doAssignMeAsCounselor();
		})
	});	
	function toggleRefDatesInfo() {
		$('##refDatesInfoContainer,##refDatesInfoController').toggle(400);
	}
	function editPanel(){
		$("##filterFS").find("input, textarea, select").removeAttr("disabled");
		$('##changePanelWarning').removeClass('alert alert-danger');
		$("##changePanelWarning").html();
		$("##changePanelWarning").hide();	
		$('.filterHidden').attr('disabled', true);
	}
	function cancelEditPanel(){
		$('##changePanelWarning').removeClass('alert alert-danger');
		$("##changePanelWarning").html();
		$("##changePanelWarning").hide();
		$("##filterFS").find("input:not(:disabled), select:not(:disabled), textarea:not(:disabled)").prop("disabled",true);
	}	
	function removeValue(list, value, separator) {
		separator = separator || ",";
		var values = list.split(separator);
		for(var i = 0 ; i < values.length ; i++) {
			if(values[i] == value) {
				values.splice(i, 1);
				return values.join(separator);
			}
		}
		return list;
	}
	function chkSubPanel1Select(subPanel,subID){		
		if (!subCheckHasRun1){
			var dd = document.getElementById('subpanelid1');
			<cfloop list="#local.subpanelid1#" index="local.thisItem">			
			for (var i=0; i < dd.length; i++){
				if (dd.options[i].value == #local.thisItem#) {
					dd.options[i].selected = true;
					break;
				}
			}
			</cfloop>
			subCheckHasRun1 = true;
		}					
	}	
	function chkSubPanel2Select(subPanel,subID){
		if(subCheckHasRun1){
			if (!subCheckHasRun2){		
					<cfloop list="#local.subpanelid2#" index="local.thisItem">			
						$("##subpanelid2 option").each(function(){							
							if ($(this).val() == "#local.thisItem#") {
								$(this).attr("selected",true);
							} else {
								$(this).removeAttr("selected");
							}
						});	
					</cfloop>				
				subCheckHasRun2 = true;
			}
		}
	}
	function chkSubPanel3Select(subPanel,subID){
		if(subCheckHasRun2){
			if (!subCheckHasRun3){
				var dd = document.getElementById('subpanelid3');
				<cfloop list="#local.subpanelid3#" index="local.thisItem">			
				for (var i=0; i < dd.length; i++){
					if (dd.options[i].value == #local.thisItem#) {
						dd.options[i].selected = true;
						break;
					}
				}
				</cfloop>
				subCheckHasRun3 = true;
			}
		}
	}	
	function interval(func, wait, times){
		var interv = function(w, t){
			return function(){
				if(typeof t === "undefined" || t-- > 0){
					setTimeout(interv, w);
					try{
						func.call(null);
					}
					catch(e){
						t = 0;
						throw e.toString();
					}
				}
			};
		}(wait, times);

		setTimeout(interv, wait);
	}
	function refreshDropDown(panelField){
		setTimeout(function() {
			$("##" + panelField).multiselect('refresh');
		}, 1500);
	}	
	function checkFormChanged(){
		var changeInd = false;
		var currTab = $('##currTab').val();
		if ((currTab == "client") && ($('##referralCanEditClient').val() == 1) && ($("##frmClient").serialize() != formOriginalData)) {
			$("##formChanged").val('1');
			changeInd = true;
		}	
		return changeInd;
	}
	function validateClientFrm(){		
		var emailRegex = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z]{2,4})+$/;
		var phoneRegex = /^(1\s*[-\/\.]?)?(\((\d{3})\)|(\d{3}))\s*([\s-./\\])?([0-9]*)([\s-./\\])?([0-9]*)$/;
		var errorMsg = "";
		var typeLabel = $('##typeID option:selected').text();
		var sourceOption = $.trim($('##sourceID option:selected').text());
		var clientEmail = $.trim($('##email').val());
		$('##email').val(clientEmail);
		var repEmail = $.trim($('##repEmail').val());
		$('##repEmail').val(repEmail);

		if ($.trim($('##firstName').val()).length == 0) {
			errorMsg += '- Enter a valid client first name.<br/>';
		}	
		if ($.trim($('##lastName').val()).length == 0) {
			errorMsg += '- Enter a valid client last name.<br/>';
		}
		if (typeLabel != 'Referral to Agency') {
			if(clientEmail.length == 0) {
				if (($.trim($('##homePhone').val()).length == 0) && ($.trim($('##cellPhone').val()).length == 0) && ($.trim($('##alternatePhone').val()).length == 0)) {
					errorMsg += '- Enter the client telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************) or enter a valid client e-mail.<br>';
				}
			}
		}
		if (typeLabel != 'Referral to Agency') {
			if(($.trim($('##repFirstName').val()).length > 0 || $.trim($('##repLastName').val()).length > 0 || 
			$.trim($('##relationToClient').val()).length > 0 || $.trim($('##repAddress1').val()).length > 0 || 
			$.trim($('##repAddress2').val()).length > 0 || $.trim($('##repCity').val()).length > 0 || 
			$.trim($('##repState').val()).length > 0 || $.trim($('##repPostalCode').val()).length > 0 ) && 
			repEmail.length == 0 ) {
				if (($.trim($('##repHomePhone').val()).length == 0) && ($.trim($('##repCellPhone').val()).length == 0) && ($.trim($('##repAlternatePhone').val()).length == 0)) {
					errorMsg += '- Enter the representative telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************) or enter a valid representative e-mail.<br>';
				}
			}
		}
		
		index = arrPhoneNumbersIds.indexOf("homePhone");
		if ($.trim($('##homePhone').val()).length > 0 && index >= 0) {
			if(!MFAPhNoInput[index].isValidNumber()) {
				errorMsg += '- Enter the client home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}
		}
		index = arrPhoneNumbersIds.indexOf("cellPhone");			
		if($.trim($('##cellPhone').val()).length > 0 && index >= 0) {
			if(!MFAPhNoInput[index].isValidNumber()){
				errorMsg += '- Enter the client cell number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}				
		}
		index = arrPhoneNumbersIds.indexOf("alternatePhone");	
		if($.trim($('##alternatePhone').val()).length > 0 && index >= 0) {
			if(!MFAPhNoInput[index].isValidNumber()){
				errorMsg += '- Enter the client alternate telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}				
		}
		index = arrPhoneNumbersIds.indexOf("repHomePhone");	
		if($.trim($('##repHomePhone').val()).length > 0 && index >= 0) {
			if(!MFAPhNoInput[index].isValidNumber()){
				errorMsg += '- Enter the representative home telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}			
		}
		
		index = arrPhoneNumbersIds.indexOf("repCellPhone");	
		if($.trim($('##repCellPhone').val()).length > 0 && index >= 0) {
			if(!MFAPhNoInput[index].isValidNumber()){
				errorMsg += '- Enter the representative cell telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}				
		}
		index = arrPhoneNumbersIds.indexOf("repAlternatePhone");	
		if( ($.trim($('##repAlternatePhone').val()).length > 0 && index >= 0)) {
			if(!MFAPhNoInput[index].isValidNumber()){
				errorMsg += '- Enter the representative cell telephone number, formatted xxx-xxx-xxxx (e.g. (************* or ************).<br>';
			}else{
				setNumberFormats(index);
			}				
		}					
		if($('##sourceID').val().length == 0) {
			errorMsg += '- Select source.<br>';
		}	
		if($('##sourceID').val().length > 0 && sourceOption == "Other" && $.trim($('##otherSource').val()).length == 0) {
			errorMsg += '- Enter Other Source.<br>';
		}
		if($('##typeID').val().length == 0) {
			errorMsg += '- Select call type.<br>';
		}
		if(typeLabel == 'Referral to Agency' && ($('##agencyID').val().length == 0 && $.trim($('##otherAgency').val()).length == 0 )) {
			errorMsg += '- Select or enter the agency name.<br>';
		}
		if( (clientEmail.length > 0) && (!emailRegex.test(clientEmail)) ) {
			errorMsg += '- Enter a valid client e-mail. <br>';
		}		
		<cfif val(local.emailAgencyInfoToClient) and not val(local.isReferred) and not val(local.isClosed) and not val(local.isDeleted)>
			var valiEmailInd = (clientEmail.length > 0 && emailRegex.test(clientEmail));
			if(typeLabel == 'Referral to Agency' && $("##sendAgencyInfo").prop("checked") && !valiEmailInd) {
				errorMsg += '- Enter a valid client e-mail for sending Agency information. <br>';
			}
		</cfif>				
		if($('##communicateLanguageID').val().length == 0) {
			errorMsg += '- Select your preferred language.<br>';
		}		
		<cfif val(this.dspLegalDescription)>
		if( ($.trim($('##issueDesc').val()).length == 0)) {
			errorMsg += '- Enter the legal issue description. <br>';
		}	
		</cfif>							
		if( repEmail.length > 0 && !emailRegex.test(repEmail) ) {
			errorMsg += '- Enter a valid representative e-mail. <br>';
		}	
		<cfif val(local.caseID)>
			<cfif len(local.allowFeeTypeMgmt) AND local.allowFeeTypeMgmt>
			if($('##caseFeeTypeID').val().length == 0) {
				errorMsg += '- Select Fee Type.<br>';
			}
			</cfif>
			$('##caseFees').val( $('##caseFees').val().replace(/\$|,/g,''));
			<cfif len(local.dspLessFilingFeeCosts) AND local.dspLessFilingFeeCosts>
				$('##filingFee').val( $('##filingFee').val().replace(/\$|,/g,'') );
			</cfif>
			$('##collectedFee').val( $('##collectedFee').val().replace(/\$|,/g,''));
		</cfif>	

		var errorMsgArray = [];
		<cfif local.extraInformation.hasFields>
			#local.extraInformation.JSVALIDATION#
		</cfif>	
		<cfif local.canViewAttorney and local.strAttorneyFields.hasFields>
			#local.strAttorneyFields.jsValidation#
		</cfif>	

		/*drop empty elements*/
		var finalErrors = $.map(errorMsgArray, function(thisError){
			if (thisError.length) return "- "+thisError;
			else return null;
		});
		errorMsg += finalErrors.join('<br>');
													
		return errorMsg;		
	}
	function setNumberFormats(key){
		if (MFAPhNo[key].value.trim()) {
			if (MFAPhNoInput[key].isValidNumber()) {
				strContactNational = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.NATIONAL);
				strContactE164 = MFAPhNoInput[key].getNumber(intlTelInput.utils.numberFormat.E164);
				$('##'+arrPhoneNumbersIds[key]+'National').val(strContactNational);							
				$('##'+arrPhoneNumbersIds[key]+'E164').val(strContactE164);							
			}else {
				$('##'+arrPhoneNumbersIds[key]+'National').val('');							
				$('##'+arrPhoneNumbersIds[key]+'E164').val('');		
			}
		}
	}
	function validateFilterFrm(){		
		var errorMsg = "";
		var panelLabel = $('##panelid1 option:selected').text();
			
		if($('##panelid1').val() == 0) {
			errorMsg += '- Select a primary panel.'+"<br/>"+validateCustomField();
		}else{
			errorMsg = validateCustomField();
		}
		
		return errorMsg;		
	}
	function validateClientPaymentFrm(){		
		var errorMsg = "";
		<cfif currTab is "clientpayment" and val(local.clientReferralID) and not local.clientPaymentSaleRecorded and local.qryGetSearchResults.recordCount>
			var amountRegex =  /(?:^\d{1,3}(?:\.?\d{3})*(?:,\d{2})?$)|(?:^\d{1,3}(?:,?\d{3})*(?:\.\d{2})?$)/;
			$('##clientReferralAmount').val( $('##clientReferralAmount').val().replace(/\$|,/g,''));
			if( $.trim($('##clientReferralAmount').val()).length == 0 || ($.trim($('##clientReferralAmount').val()).length > 0 && !amountRegex.test($.trim($('##clientReferralAmount').val())) ) ) {
				errorMsg += '- Enter a valid amount. Only positive amounts are allowed. \n';
			}			
		</cfif>
		return errorMsg;
	}	
	function validateLawyerFrm(){
		var errorMsg = "";
		var checkedMember = false;

		if($('##selectedMemberIDs').val().length > 0 ){
			checkedMember = true;
		}
		
		if (!checkedMember) {
			errorMsg += '- Select a Lawyer.\n';
		}	
			
		return errorMsg;
	}
	function validateClientReferral(){
		var errorMsg = "";
		var currTab = $('##currTab').val();

		switch(currTab) {
			case "client":
				errorMsg = validateClientFrm();
				break;				
			case "filters":
				errorMsg = validateFilterFrm();
				break;
			case "clientpayment":
				errorMsg = validateClientPaymentFrm();
				break;				
			case "lawyers":
				<cfif local.canReferInd>
					errorMsg = validateLawyerFrm();
				</cfif>
				break;				
		}
		return errorMsg;
	}	
	function hideDiveReport() {
		$('##divReportShowScreen').hide();
	}	
	<!--- pay for fees --->
	function addPayment(po) {
		mca_addPayment(po,'#this.link.addPayment#');
	}
	function closeAddPayment(po) { 
		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is not 1>
			MCModalUtils.hideModal();
		<cfelse>
			allocIndivPayment(po);
		</cfif>
	}
	function allocIndivPayment(po) {
		mca_allocIndivPayment(po,'#this.link.allocatePayment#');
	}
	function closeAllocPayment() { 
		MCModalUtils.hideModal(); 

		var removeResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				reloadPage();
			} else {
				alert('We were unable to complete the allocation process.');
			}
		};

		var objParams = { clientID:#local.clientID#, referralID:#local.referralID# };
		TS_AJX('ADMINREFERRALS','removeClientPaymentInfo',objParams,removeResult,removeResult,10000,removeResult);
	}
	<!--- adjust fees --->
	function adjustTransaction(cid,cfid,mid,pid,tidlist,clid,clpid) {
		MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Adjust Referral Fee',
				iframe: true,
				contenturl: '#this.link.adjustTransaction#&cid=' + cid + '&cfid=' + cfid + '&mid=' + mid + '&tidlist=' + tidlist + '&pid=' + pid+ '&clid=' + clid + '&clpid=' + clpid,
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnAdjust").click',
					extrabuttonlabel: 'Save Adjustment',
				}
			});
	}	
	<!--- remove fees row --->
	function removeFeesRow(cfid,tidlist,atz) {
		var confMsg = "Are you sure you want to remove this row?";
		if(atz==1)
			confMsg = "Are you sure you want to remove this row and adjust the sale amount to $0.00??";
			
		if (confirm(confMsg)) {
			$("##frmClient").attr('action', '#local.saveLink#&saveClientBtn=1&tab=client&cfid='+cfid+'&tidlist='+tidlist+'&atz='+atz);	
			$('##frmClient').submit();
		}
	}
	function deleteImportedFee(cid,cfid){
		var dspResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				$('##delImportedFeeMsg').removeClass('d-none');
				reloadPage();
			} else {
				alert('Unable to remove fee.');
			}
		};

		var objParams = { caseID:cid, collectedFeeID:cfid };
		TS_AJX('ADMINREFERRALS','deleteImportedFees',objParams,dspResult,dspResult,10000,dspResult);
	}	
	function reloadPage() {
		$("##frmClient").attr('action', '#local.saveLink#&tabSwitch=1&tab=#currTab#');
		$('##frmClient').submit();		 
	}
	function reloadCasePage() {
		self.location.href = "#this.link.editClient#&clientReferralID=#local.clientReferralID#&tc=#getTickCount()###feesInfo";
	}	
	function closeBox() { MCModalUtils.hideModal(); }
	function dspAgencyData(aid) {
		$('##agencyData').html('');
		$("##agencyData").hide();
		var dspResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				$('##agencyData').html(r.html);
				$("##agencyData").show();
			}
			else {
				alert('Unable to locate agency.');
			}
		};

		var objParams = { agencyID:aid };
		TS_AJX('ADMINREFERRALS','getAgencyByIDAjax',objParams,dspResult,dspResult,10000,dspResult);
	}	
	function disableButtons(){
		$('##buttonSetTop').find('input, textarea, button, select').attr('disabled','disabled');
		$('##buttonSetBottom').find('input, textarea, button, select').attr('disabled','disabled');		
	}
	function enableButtons(){
		$('##buttonSetTop').find('input, textarea, button, select').removeAttr("disabled");	
		$('##buttonSetBottom').find('input, textarea, button, select').removeAttr("disabled");
	}	
	function toggleATRow(rowID) {
		var st = $('##atChanges_' + rowID).css("display");
		if (st == 'none') {
			$('##atChanges_' + rowID).show();
			$('##atTreeImg_' + rowID).attr("src","/assets/common/images/tree-open.jpg");
		} else {
			$('##atChanges_' + rowID).hide();
			$('##atTreeImg_' + rowID).attr("src","/assets/common/images/tree-closed.jpg");
		}
	}
	function showResendReferralEmailWindow(){
		MCModalUtils.showModal({
				isslideout: true,
				size: 'md',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: 'Re-Send E-mails',
				iframe: true,
				contenturl: '#local.showResendEmailLink#&clientID=#local.clientID#&callUID=#local.callUID#&clientReferralID=#local.clientReferralID#&clientEmail=#len(trim(local.email))#&agencyID=#val(local.agencyID)#',
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto d-none',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnResendEmail").click',
					extrabuttonlabel: 'Send',
				}
			});
	}
	var isTransferMemberClicked = false;
	function showMemSelector() {
		var _url = "";
		_url = _url + '&panelid1='+$.trim(($("[panelid1]").attr("panelid1"))) + '&panelid2='+$.trim(($("[panelid2]").attr("panelid2")))  + '&panelid3='+$.trim(($("[panelid3]").attr("panelid3")))  + '&subpanelid1='+$.trim(($("[subpanelid1]").attr("subpanelid1"))) + '&subpanelid2='+$.trim(($("[subpanelid2]").attr("subpanelid2"))) + '&subpanelid3='+$.trim(($("[subpanelid3]").attr("subpanelid3"))) ;
		_url = _url + '&currentMemberId='+ '#local.memberID#' ;				
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'xl',
			title: 'Choose Panel Member for Case Transfer',
			iframe: true,
			contenturl: '#this.link.memSelectReferralGotoLink#'+_url+'&autoClose=0&retFunction=top.selectAssignedTo&fldName=transferMemberId',
			strmodalfooter : {
				classlist: 'd-none'
			}
		});
		$('##MCModal').on('hidden.bs.modal', function() {
			if(isTransferMemberClicked){$('##transferAlert').removeClass('d-none');}isTransferMemberClicked=false;
		});
	}
	function selectAssignedTo(mID,mName) {
		isTransferMemberClicked = true;
		$('##transferMemberId').val(mID);	
		$('.transferMemberIdElement').text(mName);
		$('.transferMemberIdElement').attr("href","#(application.objPlatform.isRequestSecure() ? 'https' : 'http')#://#application.objPlatform.getCurrentHostname()#/#local.editMemberLink#&memberID="+mID);		
		MCModalUtils.hideModal();
	}	
	function fnAddNote(type){
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Add Note',
			iframe: true,
			contenturl: '#this.link.addNote#&type=' + type + '&cID=#local.clientReferralID#',
			strmodalfooter: {
				classlist: 'd-flex',
				showclose: true,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmNote :submit").click',
				extrabuttonlabel: 'Save Note'
			}
		});
	}
	function fnAddNoteFromGrid(type){
		MCModalUtils.hideModal();
		$('##MCModal').on('hidden.bs.modal', function() { 
			fnAddNote(type);
		
			$('##MCModal').on('hidden.bs.modal', function() {
				fnShowReferralNotes(#local.clientReferralID#,type);
			});
			
		});
	}
	function fnEditNote(noteid,type){
		MCModalUtils.hideModal();
		$('##MCModal').on('hidden.bs.modal', function() { 
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (type== 'C')?'Edit Counselor Note':'View Lawyer Note',
				iframe: true,
				contenturl: '#this.link.addNote#&noteid='+noteid+'&type=' + type + '&cID=#local.clientReferralID#',
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: (type== 'C')?true:false,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmNote :submit").click',
					extrabuttonlabel: 'Save Note'
				}
			});
			$('##MCModal').on('hidden.bs.modal', function() {
				fnShowReferralNotes(#local.clientReferralID#,type);
			});
		});
	}
	function fnShowReferralNotes(cid,type){
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'xl',
			title: 'View Notes',
			iframe: true,
			contenturl: '#this.link.showNotes#&mode=direct&type=' + type + '&cID=' + cid,
			strmodalfooter: {
				classlist: 'text-right',
				showclose: true
			}
		});
		if(type == 'C')
		top.$('##MCModalFooter').prepend('<button type="button" class="btn btn-sm btn-primary float-left" name="btnAddNote" id="btnAddNote" onclick="top.fnAddNoteFromGrid(\''+type+'\')" >Add Note</button>');
	}
	function fnCalcCounselorNotesPending(){
		var _pendingCount = 0;
		_pendingCount = $('input[name^=counselorNoteFollowUpStatus_][value=P]').length;
		$('##pendingNoteCount').html(_pendingCount+ ' Pending');
	}
	<cfif val(local.clientReferralID)>
	function fnReAssignCounselor(){
		let counselorsTemplateSource = $('##mc_counselorsTemplate').html().replace(/\n/g,'');
		let counselorsTemplate = Handlebars.compile(counselorsTemplateSource);

		var chkResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				$('div##divCounselorListWrapper').html(counselorsTemplate(r));
			} else 
				$('div##divCounselorListWrapper').html('');
		};

		$('div##divCounselorListWrapper').html('<div class="text-left"><i class="fa-light fa-circle-notch fa-spin fa-lg"></i></div>');
		var objParams = { memberID:#session.cfcuser.memberdata.memberID#, counselorSelectGroupID:#val(local.counselorSelectGroupID)#};
		TS_AJX('ADMINREFERRALS','getCounselorsToAssign',objParams,chkResult,chkResult,10000,chkResult);
	}
	function fnAssignToMyself(){
		$('##assignToMyselfModal').modal('show');
	}
	function doAssignMeAsCounselor(){
		var chkResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				if(r.counselorname)
					$('##counselorNameHolder').html(r.counselorname);
				$('##assignToMyselfModal').modal('hide');
			}
		};
		var objParams = {clientReferralID:#local.clientreferralID#, memberID:#session.cfcuser.memberdata.memberID#, actorMemberID:#session.cfcuser.memberdata.memberID#};
		TS_AJX('ADMINREFERRALS','reAssignCounselor',objParams,chkResult,chkResult,10000,chkResult);
	}
	</cfif>
	<cfif this.allowFeeDiscrepancy>
		function showStatusFields(){
			$('.statusFields').removeClass('d-none');
		}
		function reloadPage() { location.reload();}
	</cfif>
	function closeColorBox() { MCModalUtils.hideModal(); }<!---called from ReferralsAdmin.cfc also--->
	</script>
	
	<cfif local.extraInformation.hasFields>
		#local.extraInformation.head#
	</cfif>

	<cfif local.canViewAttorney and local.strAttorneyFields.hasFields>
		#local.strAttorneyFields.head#
	</cfif>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.clientFormJS)#" />

<cfhtmlhead text="<link rel=""stylesheet"" type=""text/css"" href=""/assets/common/css/mcTabs.css"" />">

<cfif arguments.event.valueExists('msg')>
	<cfswitch expression="#arguments.event.getValue('msg')#">
		<cfcase value="1"><cfset local.msgText = "Client Information Saved" /></cfcase>
	</cfswitch>		
</cfif>

<cfform name="frmClient" id="frmClient" method="POST">
	<cfinput type="hidden" name="referralID" id="referralID" value="#local.referralID#" />
	<cfinput type="hidden" name="clientID" id="clientID" value="#local.clientID#" />
	<cfinput type="hidden" name="clientParentID" id="clientParentID" value="#local.clientParentID#" />
	<cfinput type="hidden" name="repID" id="repID" value="#local.repID#" />
	<cfinput type="hidden" name="repParentID" id="repParentID" value="#local.repParentID#" />
	<cfinput type="hidden" name="currTab" id="currTab" value="#currTab#" />
	<cfif NOT local.allowStatusUpdate>
		<cfinput type="hidden" name="statusID" id="statusID" value="#local.statusID#" />
	</cfif>
	<cfinput type="hidden" name="memberID" id="memberID" value="#local.memberID#" />
	<cfinput type="hidden" name="callUID" id="callUID" value="#local.callUID#" />
	<cfinput type="hidden" name="statusName" id="statusName" value="#local.statusName#" />
	<cfinput type="hidden" name="canReferInd" id="canReferInd" value="#local.canReferInd#" />
	<cfinput type="hidden" name="referralCanEditClient" id="referralCanEditClient" value="#local.referralCanEditClient#" />
	<cfinput type="hidden" name="referralCanEditFilter" id="referralCanEditFilter" value="#local.referralCanEditFilter#" />
	<cfinput type="hidden" name="referralCanEditLawyer" id="referralCanEditLawyer" value="#local.referralCanEditLawyer#" />
	<cfinput type="hidden" name="memberLoggedInID" id="memberLoggedInID" value="#local.memberLoggedInID#" />
	<cfinput type="hidden" name="clientReferralID" id="clientReferralID" value="#local.clientReferralID#" />
	<cfinput type="hidden" name="copyClientReferralID" id="copyClientReferralID" value="#val(local.clientReferralID)#" />
	<cfinput type="hidden" name="caseID" id="caseID" value="#local.caseID#" />
	<cfinput type="hidden" name="dateCaseClosed" id="dateCaseClosed" value="#local.dateCaseClosed#" />
	<cfinput type="hidden" name="clientFieldNames" id="clientFieldNames" value="#local.clientFieldNames#" />
	<cfinput type="hidden" name="filterFieldNames" id="filterFieldNames" value="#local.filterFieldNames#" />
	<cfinput type="hidden" name="lawyerFieldNames" id="lawyerFieldNames" value="#local.lawyerFieldNames#" />
	<cfinput type="hidden" name="formChanged" id="formChanged" value="0" />
	<cfinput type="hidden" name="saveData" id="saveData" value="1" />
	<cfinput type="hidden" name="prevStatusID"  id="prevStatusID" value="#local.statusID#" />
	<cfinput type="hidden" name="cancelLink" id="cancelLink" value="#local.cancelLink#" />
	<cfinput type="hidden" name="isReferred" id="isReferred" value="#local.isReferred#" />	
	<cfinput type="hidden" name="selectedMemberIDs" id="selectedMemberIDs" value="#listRemoveDuplicates(arguments.event.getValue('selectedMemberIDs',''))#" />
	<cfinput type="hidden" name="questionAnswerPath" id="questionAnswerPath" value="#local.questionAnswerPath#" />
	<cfinput type="hidden" name="sendEmailInd" id="sendEmailInd" value="0" />
	<cfinput type="hidden" name="counselorMemberID" id="counselorMemberID" value="0" />
	<cfinput type="hidden" name="counselorNoteCount" id="counselorNoteCount" value="0" />
	<cfif listFindNoCase("filters",local.currTab)>
		<cfinput type="hidden" name="skipClientPayment" id="skipClientPayment" value="#local.skipClientPayment#" />
	</cfif>	
	<cfinput type="hidden" name="payLater" id="payLater" value="#local.payLater#" />
	<cfif local.keyExists("dateLastUpdated") AND len(local.dateLastUpdated)>
		<cfinput type="hidden" name="prevLastUpdatedDate" id="prevLastUpdatedDate" value="#DateTimeFormat(local.dateLastUpdated,'ISO8601')#">
	</cfif>
	
	<cfoutput>
	<span id="saveArea" class="float-right d-none">
		<span id="saveMsg">#local.msgText#</span>
	</span>	
	
	<div class="card card-box p-3 mb-4 d-flex flex-row align-items-start">
		<div class="pr-5">
			<h4>#local.clientRefTitle#</h4>
		</div>
		<div class="pr-7 flex-grow-1 text-right" id="buttonSetTop">
			<cfif val(local.clientReferralID) and listFindNoCase("client",currTab)>
				<button type="button" id="copyClientBtn" class="copyClientBtn btn btn-sm btn-secondary" title="Copy Client Referral Data">Copy</button>
			</cfif>
			<cfif not val(local.isDeleted)>
				<button type="button" id="saveClientBtn" class="saveClientBtn btn btn-sm btn-primary">Save</button>
				<cfif (val(local.canReferInd))>						
					<button type="button" id="proceedBtn" class="proceedBtn btn btn-sm btn-primary">Proceed</button>
				</cfif>
			</cfif>
			<cfif val(local.isPending) and val(local.clientReferralID)>
				<button type="button" id="deleteClientBtn" class="deleteClientBtn btn btn-sm btn-secondary BodyButton">Delete</button>
			</cfif>
			<cfif val(local.isDeleted)>
				<button type="button" id="activateClientBtn" class="activateClientBtn btn btn-sm btn-secondary">Activate</button>
			</cfif>		
			<cfif val(local.isClosed) and val(local.clientReferralID) and len(trim(local.clientReferralDate)) and not val(local.isAgencyReferral)>
				<button type="button" id="reopenBtn" class="reopenBtn btn btn-sm btn-secondary">Re-Open</button>
			</cfif>			
			<button type="button" id="cancelBtn" class="btn btn-sm btn-secondary" onClick="disableButtons();parent.location.href='#local.cancelLink#';">Cancel</button>
			<cfif val(local.clientReferralID)>
				<button type="button" id="printBtn" class="printBtn btn btn-sm btn-secondary">Printer-Friendly</button>
			</cfif>
			<cfif val(local.clientReferralID) and listFindNoCase("client",currTab)>
				<button class="btn btn-sm btn-secondary" id="btnShowResendEmail" name="btnShowResendEmail" type="button" onClick="showResendReferralEmailWindow();">Resend Email</button>	
			</cfif>
		</div>
	</div>

	<cfif arguments.event.valueExists('copy')>
		<div class="alert alert-success  fade show" role="alert" id="copyMsg">
			Client data has successfully been copied. Please click "Save" in order to create a referral record.
			<button type="button" class="close"  aria-label="Close" data-hide="alert">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
	</cfif>
	<cfif arguments.event.valueExists('activate')>		
		<div class="alert alert-success  fade show" role="alert" id="activateArea">
			The referral has successfully been undeleted. You can now refer this client to an attorney.
			<button type="button" class="close" aria-label="Close" data-hide="alert">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
	</cfif>	
	<cfif arguments.event.valueExists('reopen')>
		<div class="alert alert-success  fade show" role="alert" id="reopenArea">
			The referral has successfully been reopened. A confirmation e-mail has been sent to the attorney handling this case.
			<button type="button" class="close" aria-label="Close" data-hide="alert">
				<span aria-hidden="true">&times;</span>
			</button>
		</div>
	</cfif>

	<ul class="nav nav-pills nav-pills-dotted" id="refClientsTab" role="tablist">
		<cfif val(local.clientReferralID)>
			<cfset local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','')>
			<li class="nav-item">
				<a href="##" id="clientLink" class="nav-link<cfif currTab EQ "client"> active</cfif>">#iif(not local.canViewAttorney,DE('Client'),DE('Referral'))#</a>
			</li>
			<cfif not local.canViewAttorney and not val(local.isAgencyReferral)>
				<li class="nav-item">
					<a href="##" id="filterLink" class="nav-link<cfif currTab EQ "filters"> active</cfif>">Filters</a>
				</li>
				<cfif currTab eq 'clientpayment'>
					<li class="nav-item">
						<a href="##" id="clientPaymentLink" class="nav-link<cfif currTab EQ "clientpayment"> active</cfif>">Client Payment</a>
					</li>
				</cfif>
				<cfif currTab eq 'lawyers' or local.callPaymentProcessed>
					<li class="nav-item">
						<a href="##" id="lawyerLink" class="nav-link<cfif currTab EQ "lawyers"> active</cfif>">Lawyers</a>
					</li>
				</cfif>
			</cfif>
		</cfif>
	</ul>

	<div class="tab-content <cfif val(local.clientReferralID)> mc_tabcontent</cfif> p-3 pb-0">
		<cfswitch expression="#currTab#">
			<cfcase value="client">
				<div class="tab-pane active">
					<cfinclude template="frm_client.cfm">
					<cfinclude template="dsp_clientReferralDocuments.cfm">
					<cfif val(local.clientReferralID)>
						<cfinclude template="dsp_clientReferralChangeHistory.cfm">
					</cfif>
				</div>
			</cfcase>
			<cfcase value="filters">
				<div class="tab-pane active">
					<cfinclude template="frm_clientReferralFilter.cfm">
				</div>
			</cfcase>
			<cfcase value="clientpayment">
				<div class="tab-pane active">
					<cfinclude template="frm_clientPayment.cfm">
				</div>
			</cfcase>
			<cfcase value="lawyers">
				<div class="tab-pane active">
					<cfinclude template="frm_clientReferralResults.cfm">
				</div>
			</cfcase>
		</cfswitch>
	</div>

	<div id="buttonSetBottom" align="right">
		<br>
		<cfif val(local.clientReferralID) and listFindNoCase("client",currTab)>
			<button type="button" id="copyClientBtn2" class="copyClientBtn btn btn-sm btn-secondary" title="Copy Client Referral Data">Copy</button>
		</cfif>
		<cfif not val(local.isDeleted)>
			<button type="button" id="saveClientBtn2" class="saveClientBtn btn btn-sm btn-primary">Save</button>
			<cfif (val(local.canReferInd))>	
				<button type="button" id="proceedBtn2" class="proceedBtn btn btn-sm btn-primary">Proceed</button>
			</cfif>
		</cfif>
		<cfif val(local.isPending) and val(local.clientReferralID)>
			<button type="button" id="deleteClientBtn2" class="deleteClientBtn btn btn-sm btn-secondary">Delete</button>
		</cfif>
		<cfif val(local.isDeleted)>
			<button type="button" id="activateClientBtn2" class="activateClientBtn btn btn-sm btn-secondary">Activate</button>
		</cfif>			
		<cfif val(local.isClosed) and val(local.clientReferralID) and len(trim(local.clientReferralDate)) and not val(local.isAgencyReferral)>
			<button type="button" id="reopenBtn2" class="reopenBtn btn btn-sm btn-secondary">Re-Open</button>
		</cfif>		
		<button type="button" id="cancelBtn" class="btn btn-sm btn-secondary" onClick="disableButtons();parent.location.href='#local.cancelLink#';">Cancel</button>
		<cfif val(local.clientReferralID)>
			<button type="button" id="printBtn2" class="printBtn btn btn-sm btn-secondary">Printer-Friendly</button>
		</cfif>		
	</div>
	<br />
	</cfoutput>		
</cfform>
<cfoutput>
<div id="divReportShowScreen"></div>
<script type="text/javascript">
	$('##saveArea').removeClass('d-none');
	$(function(){
		mca_setupSelect2();
	});
</script>
</cfoutput>