ALTER PROC dbo.tr_viewTransaction_adjustment
@transactionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @siteID int, @orgID int, @tr_DITSaleTrans int, @tr_AdjustTrans int, @tr_WriteOffSaleTrans int;
	declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), G<PERSON><PERSON> varchar(30), thePath varchar(max));
	declare @tblHold TABLE (transactionID int, debitglAccountID int, creditglAccountID int, amount decimal(18,2));
	declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount decimal(18,2), creditAmount decimal(18,2));

	select @orgID = ownedByOrgID, @siteID = recordedOnSiteID from dbo.tr_transactions where transactionID = @transactionID;
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_WriteOffSaleTrans = dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans');
	
	insert into @allGLS
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl;

	-- transaction info
	select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, t.amount, 
		case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment' else 'Adjustment' end as [type],
		case when glCred.glCode = 'ACCOUNTSRECEIVABLE' then 'Negative Adjustment of ' else 'Adjustment of ' end + isnull(t.detail,'') as detail,
		t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
		mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
		mAss2.company as assignedToMemberCompany,
		m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
		m2.company as recordedByMemberCompany
	from dbo.tr_transactions as t
	inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
	inner join dbo.ams_members as mAss on mAss.memberid = t.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.memberID = mAss.activeMemberID
	inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join @allGLs as glCred on glCred.GLAccountID = t.creditGLAccountID
	where t.transactionID = @transactionID;

	-- adjustment info
	select top 1 i.invoiceID, ins.status, i.fullInvoiceNumber as invoiceNumber, 
		i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode,
		btn_canVoid = 
			case 
			when t.statusID = 1 and tAdjee.statusID = 1 and tAdjee.typeID <> 7 then 1 
			else 0 
			end,
		hasSchedule = case when exists (
			select dit.transactionID
			from dbo.tr_transactions as dit
			inner join dbo.tr_relationships as rInner on rInner.orgID = @orgID and rInner.typeID = @tr_DITSaleTrans and rInner.transactionID = dit.transactionID and rInner.appliedToTransactionID = t.transactionID
			inner join dbo.tr_transactionDIT as tdit on tdit.orgID = @orgID and tdit.transactionID = dit.transactionID
			where dit.ownedByOrgID = @orgID 
			and dit.statusID = 1
			and tdit.isActive = 1
			) then 1 else 0 end
	from dbo.tr_invoiceTransactions as it
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
	inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
	inner join dbo.organizations as o on o.orgID = @orgID
	inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.typeID = @tr_AdjustTrans and rAdj.transactionID = t.transactionID
	inner join dbo.tr_transactions as tAdjee on tAdjee.ownedByOrgID = @orgID and tAdjee.transactionID = rAdj.appliedToTransactionID
	where it.orgID = @orgID
	and t.transactionID = @transactionID;

	-- coupon info
	select c.couponCode
	from dbo.tr_coupons as c
	inner join dbo.tr_transactionDiscounts as td on td.orgID = @orgID and td.couponID = c.couponID
		and td.transactionID = @transactionID
	where c.siteID = @siteID;
	
	-- adjusting transaction
	select top 1 tSaleTax.transactionID, tt.type, tSaleTax.amount, tSaleTax.detail,
		mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
		mAss2.company as assignedToMemberCompany,
		glCred.thePathExpanded + isnull(' (' + nullIf(glCred.accountCode,'') + ')','') as creditGL
	from dbo.tr_transactions as tSaleTax
	inner join dbo.tr_types as tt on tt.typeID = tSaleTax.typeID
	inner join dbo.tr_relationships as rAdj on rAdj.orgID = @orgID and rAdj.typeID = @tr_AdjustTrans and rAdj.appliedToTransactionID = tSaleTax.transactionID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = tSaleTax.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join @allGLS as glCred on glCred.GLAccountID = tSaleTax.creditGLAccountID
	where tSaleTax.ownedByOrgID = @orgID 
	and rAdj.transactionID = @transactionID;

	-- current gl spread

	-- adj	
	INSERT INTO @tblHold
	select transactionID, debitglAccountID, creditglAccountID, amount
	from dbo.tr_transactions
	WHERE transactionID = @transactionID
	and statusID = 1;

	-- wo
	INSERT INTO @tblHold
	select distinct wo.transactionID, wo.debitglAccountID, wo.creditglAccountID, wo.amount
	from dbo.tr_transactions as wo
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_WriteOffSaleTrans and r.transactionID = wo.transactionID
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where wo.ownedByOrgId = @orgID
	and wo.statusID = 1;

	-- dit
	INSERT INTO @tblHold
	select distinct dit.transactionID, dit.debitglAccountID, dit.creditglAccountID, dit.amount
	from dbo.tr_transactions as dit
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = dit.transactionID
	inner join @tblHold as tbl on tbl.transactionID = r.appliedToTransactionID
	where dit.ownedByOrgID = @orgID
	and dit.statusID = 1;

	insert into @tblTrans
	select transactionID, debitglAccountID, amount, 0
	from @tblHold
		union all
	select transactionID, creditglAccountID, 0, amount
	from @tblHold;

	select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		else null
		end as debits,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		else null
		end as credits
	from (
		select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
		from @tblTrans
		group by glAccountID
		having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
	) as sd
	inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
	order by gl.thePath;
	
	-- deferred schedule
	select dit.recognitionDate, sum(t.amount) as recogAmt
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = t.transactionID and r.appliedToTransactionID = @transactionID
	inner join dbo.tr_transactionDIT as dit on dit.orgID = @orgID and dit.transactionID = t.transactionID
	where t.ownedByOrgId = @orgID 
	and t.statusID = 1
	and dit.isActive = 1
	group by dit.recognitionDate
	order by dit.recognitionDate;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
