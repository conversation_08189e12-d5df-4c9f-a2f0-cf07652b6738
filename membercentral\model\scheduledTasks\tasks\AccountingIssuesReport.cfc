<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">

	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>

		<cfsetting requesttimeout="600">

		<cfset local.itemCount = getQueueItemCount()>

		<cfif local.itemCount GT 0>
			<cfset local.success = processQueue()>
			<cfif NOT local.success>
				<cfthrow message="Error running processQueue()">
			</cfif>
		</cfif>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=local.itemCount)>
	</cffunction>

	<cffunction name="processQueue" access="public" output="false" returntype="boolean">
		<cfset var local = structnew()>
		<cfset local.success = true>
		<cfset local.tdStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;padding:2px;">
		<cfset local.tdStyle2 = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##999;padding:0 0 4px 20px;">
		<cfset local.thStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;border-bottom:1px solid ##333;padding:0;">
		<cfset local.pageStyle = "width:600px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:9pt;color:##333;">
		<cfset local.headingStyle = "font-family:Verdana, Arial, Helvetica, sans-serif;font-size:11pt;color:##333;font-weight:bold;padding:22px 0 10px 0;">

		<cfstoredproc procedure="queue_acctIssuesReport_grabForProcessing" datasource="#application.dsn.platformQueue.dsn#">
			<cfprocresult name="local.qryOrgInfo" resultset="1">
			<cfprocresult name="local.qryOpenInvoices" resultset="2">
			<cfprocresult name="local.qryNonPostedBatches" resultset="3">
			<cfprocresult name="local.qryOutOfOrderInvoices" resultset="4">
			<cfprocresult name="local.qryInvProfAllocViolations" resultset="5">
			<cfprocresult name="local.qryFlaggedTransactions" resultset="6">
			<cfprocresult name="local.qryNoNonSurchargePaymentProfile" resultset="7">
		</cfstoredproc>

		<cfif local.qryOrgInfo.recordCount>
			<cftry>
				<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(local.qryOrgInfo.sitecode)>

				<cfquery name="local.qryUpdateToProcessing" datasource="#application.dsn.platformQueue.dsn#">
					SET NOCOUNT ON;
	
					DECLARE @statusProcessing int;
					EXEC dbo.queue_getStatusIDbyType @queueType='acctIssuesReport', @queueStatus='processing', @queueStatusID=@statusProcessing OUTPUT;

					UPDATE dbo.queue_acctIssuesReport
					SET statusID = @statusProcessing,
						dateUpdated = GETDATE()
					WHERE itemID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryOrgInfo.itemID#">;
				</cfquery>

				<cfif len(local.qryOrgInfo.accountingEmail)>
					<cfquery name="local.qryOpenInvoicesOrg" dbtype="query">
						select *
						from [local].qryOpenInvoices
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">
						order by lastname, firstname, membernumber, dateDue
					</cfquery>

					<cfquery name="local.qryNonPostedBatchesOrg" dbtype="query">
						select *
						from [local].qryNonPostedBatches
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">
						order by batchName
					</cfquery>

					<cfquery name="local.qryOutOfOrderInvoicesOrg" dbtype="query">
						select *
						from [local].qryOutOfOrderInvoices
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">
						order by memberName
					</cfquery>

					<cfquery name="local.qryInvProfAllocViolationsOrg" dbtype="query">
						select *
						from [local].qryInvProfAllocViolations
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">
						order by payProfileName, invoiceNumber
					</cfquery>

					<cfquery name="local.qryFlaggedTransactionsOrg" dbtype="query">
						select *
						from [local].qryFlaggedTransactions
						where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">
						order by dateRecorded, memberName
					</cfquery>

					<cfsavecontent variable="local.orgEmailContent_invoices">
						<cfif local.qryOpenInvoicesOrg.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Open Invoices</div>
							<div>
								The following invoices are <b>open</b> with due dates more than 7 days in the past.
								Until these invoices are closed, they will not appear on invoice aging reports, cannot have payments applied 
								to them, and cannot be printed or emailed to members.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>Invoice</b></td>
							</tr>
							<cfloop query="local.qryOpenInvoicesOrg">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryOpenInvoicesOrg.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryOpenInvoicesOrg.memberID#&tab=transactions">#local.qryOpenInvoicesOrg.lastname#, #local.qryOpenInvoicesOrg.firstname# (#local.qryOpenInvoicesOrg.membernumber#)</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#local.qryOpenInvoicesOrg.invoiceNumber#<br/>
										#dollarformat(local.qryOpenInvoicesOrg.invDue)# due on #dateFormat(local.qryOpenInvoicesOrg.dateDue,"m/d/yyyy")#
										<cfif local.qryOpenInvoicesOrg.hasCard>
											<br/>There is a pay method on file for this invoice.
										</cfif>
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_captured">
						<cfif local.qryFlaggedTransactionsOrg.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Transaction Messages</div>
							<div>
								The following transactions have been flagged as needing your attention.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>Message</b></td>
							</tr>
							<cfloop query="local.qryFlaggedTransactionsOrg">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryFlaggedTransactionsOrg.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryFlaggedTransactionsOrg.memberID#&tab=transactions">#local.qryFlaggedTransactionsOrg.memberName#</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#dateFormat(local.qryFlaggedTransactionsOrg.dateRecorded,"m/d/yyyy")#<br/>
										#local.qryFlaggedTransactionsOrg.message#
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_batches">
						<cfif local.qryNonPostedBatchesOrg.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Non-Posted Batches</div>
							<div>
								The following batches are <b>not posted</b> and have deposit dates more than 7 days in the past. 
								Until these batches are posted, they will not appear on any accounting reports and your data analysis will be incomplete. 
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Batch</b></td>
								<td style="#local.thStyle#padding-left:4px;">&nbsp;</td>
							</tr>
							<cfloop query="local.qryNonPostedBatchesOrg">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryNonPostedBatchesOrg.currentrow#.</td>
									<td style="#local.tdStyle#">
										<a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=BatchAdmin%7Clist%7CviewBatch&bid=#local.qryNonPostedBatchesOrg.batchID#">#local.qryNonPostedBatchesOrg.batchName#</a>
										<br/>Payment Profile: #local.qryNonPostedBatchesOrg.profileName#
									</td>
									<td style="#local.tdStyle#padding-left:4px;">
										Deposit Date: #dateFormat(local.qryNonPostedBatchesOrg.depositDate,"m/d/yyyy")#
										<br/>Batch Status: #local.qryNonPostedBatchesOrg.status#
									</td>
								</tr>
							</cfloop>
							</table>							
							</cfoutput>
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_outOfOrderInvoices">
						<cfif local.qryOutOfOrderInvoicesOrg.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Out Of Order Invoices</div>
							<div>
								The following members have invoices due that are <b>out of order</b>.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Member</b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>MemberNumber</b></td>
							</tr>
							<cfloop query="local.qryOutOfOrderInvoicesOrg">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryOutOfOrderInvoicesOrg.currentrow#.</td>
									<td style="#local.tdStyle#"><a href="#local.mc_siteInfo.scheme#://#local.mc_siteinfo.mainhostname#/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=#local.qryOutOfOrderInvoicesOrg.memberID#&tab=transactions">#local.qryOutOfOrderInvoicesOrg.memberName#</a></td>
									<td style="#local.tdStyle#padding-left:4px;">
										#local.qryOutOfOrderInvoicesOrg.memberNumber#
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_InvProfAllocViolations">
						<cfif local.qryInvProfAllocViolationsOrg.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">Invoice Profile / Payment Profile Violations</div>
							<div>
								The following allocations are tied to payment profiles that violate the constraint set on invoice profiles.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" colspan="2"><b>Allocation</b></td>
							</tr>
							<cfloop query="local.qryInvProfAllocViolationsOrg">
								<tr valign="top">
									<td style="#local.tdStyle#width:18px;" nowrap>#local.qryInvProfAllocViolationsOrg.currentrow#.</td>
									<td style="#local.tdStyle#">
										On #dateformat(local.qryInvProfAllocViolationsOrg.dateRecorded,"m/d/yyyy")# 
										#local.qryInvProfAllocViolationsOrg.memberName# (#local.qryInvProfAllocViolationsOrg.memberNumber#) 
										allocated #dollarformat(local.qryInvProfAllocViolationsOrg.allocAmount)# of #local.qryInvProfAllocViolationsOrg.detail# 
										to invoice #local.qryInvProfAllocViolationsOrg.invoiceNumber#. 
										Invoice profile #local.qryInvProfAllocViolationsOrg.invoiceProfileName# cannot be paid 
										by pay profile #local.qryInvProfAllocViolationsOrg.payProfileName#.
									</td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent_noNonSurchargePaymentProfile">
						<cfif local.qryNoNonSurchargePaymentProfile.recordcount>
							<cfoutput>
							<div style="#local.headingStyle#">No Non-Surcharge alternative Payment Profiles </div>
							<div>
								The following applications doesn't have a alternative non-surcharge payment profile.
							</div>
							<br/>
							<table style="width:600px;">
							<tr>
								<td style="#local.thStyle#" width="40%"><b>Application </b></td>
								<td style="#local.thStyle#padding-left:4px;"><b>Link To Application</b></td>
							</tr>
							<cfloop query="local.qryNoNonSurchargePaymentProfile">
								<tr valign="top">
									<td style="#local.tdStyle#" nowrap>#local.qryNoNonSurchargePaymentProfile.applicationTypeName#</td>
									<td style="#local.tdStyle#padding-left:4px;"><a href="#local.qryNoNonSurchargePaymentProfile.linkToAppAdmin#">#local.qryNoNonSurchargePaymentProfile.title#</a></td>
								</tr>
							</cfloop>
							</table>
							</cfoutput>							
						</cfif>
					</cfsavecontent>

					<cfsavecontent variable="local.orgEmailContent">
						<cfoutput>
						<html>
						<head>
							<title>Accounting Issues Report</title>
						</head>
						<body>
						<div style="#local.pageStyle#">
							<div>
								As of #dateformat(now(),"dddd, mmmm d, yyyy")# at #timeformat(now(),"h:mm tt")# the following accounting issues exist on your website. 
							</div>
							<br/>

							<cfif len(local.orgEmailContent_batches)>
								#local.orgEmailContent_batches#
								<br/><br/>
							</cfif>
							<cfif len(local.orgEmailContent_captured)>
								#local.orgEmailContent_captured#
								<br/><br/>
							</cfif>
							<cfif len(local.orgEmailContent_invoices)>
								#local.orgEmailContent_invoices#
								<br/><br/>
							</cfif>
							<cfif len(local.orgEmailContent_outOfOrderInvoices)>
								#local.orgEmailContent_outOfOrderInvoices#
								<br/><br/>
							</cfif>
							<cfif len(local.orgEmailContent_InvProfAllocViolations)>
								#local.orgEmailContent_InvProfAllocViolations#
								<br/><br/>
							</cfif>

							<cfif len(local.orgEmailContent_noNonSurchargePaymentProfile)>
								#local.orgEmailContent_noNonSurchargePaymentProfile#
								<br/><br/>
							</cfif>

							<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div>
							<br/>
							<div><b>E-mail Removal</b><br/>If you no longer wish to receive these types of emails, you can adjust the 
							recipient list by logging into your website's Control Panel, clicking on the Accounting tab, and modifying this 
							report's recipient address in the "Accounting Settings" tool.</div>
						</div>
						</body>
						</html>
						</cfoutput>
					</cfsavecontent>
					
					<cfscript>
						local.arrEmailTo = [];
						local.toEmailArr = listToArray(local.qryOrgInfo.accountingEmail,';');
						for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
							local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
						}
					</cfscript>
					
					<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=local.qryOrgInfo.orgName, email=local.mc_siteInfo.networkEmailFrom },
						emailto=local.arrEmailTo,
						emailreplyto="<EMAIL>",
						emailsubject="#local.qryOrgInfo.orgName# Accounting Issues Report",
						emailtitle="Accounting Issues Report",
						emailhtmlcontent=local.orgEmailContent,
						emailAttachments=[],
						siteID=local.mc_siteinfo.siteID,
						memberID=local.mc_siteInfo.sysmemberid,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SCHEDTASK"),
						sendingSiteResourceID=local.mc_siteInfo.siteSiteResourceID
					)>
				</cfif>

				<cfquery name="local.qryClearQueueItem" datasource="#application.dsn.platformQueue.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.orgID#">, 
							@itemID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgInfo.itemID#">;

						BEGIN TRAN;
							DELETE FROM datatransfer.dbo.tr_reportIssues_openInv WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_nonPostedBatches WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_outOfOrderInv WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_invProfAllocViolations WHERE orgID = @orgID;
							DELETE FROM datatransfer.dbo.tr_reportIssues_flaggedTransactions WHERE orgID = @orgID;
							DELETE FROM dbo.queue_acctIssuesReport WHERE itemID = @itemID;
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.success = true>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.success = false>
			</cfcatch>
			</cftry>
		</cfif>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="getQueueItemCount" access="private" output="false" returntype="numeric">
		<cfset var qryQueueItems = "">

		<cfquery name="qryQueueItems" datasource="#application.dsn.platformQueue.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select count(itemID) as itemCount
			from dbo.queue_acctIssuesReport;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryQueueItems.itemCount>
	</cffunction>
	
</cfcomponent>