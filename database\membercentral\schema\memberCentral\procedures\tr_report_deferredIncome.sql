ALTER PROC dbo.tr_report_deferredIncome
@siteID int,
@groupBy varchar(12),
@endDate datetime,
@filename varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @orgID int, @dtlist varchar(max), @fullSQL varchar(max), @selectsql varchar(max),
		@tr_DITOffsetTrans int, @tr_OffsetTrans int, @tr_DITSaleTrans int;

	select @orgID = o.orgID
	from dbo.sites as s
	inner join dbo.organizations as o on o.orgID = s.orgID
	where s.siteID = @siteID;

	set @tr_DITOffsetTrans = dbo.fn_tr_getRelationshipTypeID('DITOffsetTrans');
	set @tr_OffsetTrans = dbo.fn_tr_getRelationshipTypeID('OffsetTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');

	declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max));
	insert into @allGLS
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl;

	-- set date to 11:59:59 of asOfDate
	set @endDate = dateadd(ms,-3,dateadd(day,1,DATEADD(dd,DATEDIFF(dd,0,@endDate),0)));


	-- get DITs to consider
	IF OBJECT_ID('tempdb..#tmpRecog_a') IS NOT NULL 
		DROP TABLE #tmpRecog_a;
	CREATE TABLE #tmpRecog_a (transactionID int, recognitionDate datetime, recogMonth varchar(7), amount decimal(18,2), GLAccountID int);

	insert into #tmpRecog_a
	select t.transactionID, tDIT.recognitionDate, convert(varchar(7),tDit.recognitionDate,20) as recogMonth, t.amount, t.debitGlAccountID
	from dbo.tr_transactionDIT as tDIT
	inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.recordedOnSiteID = @siteID and t.transactionID = tDIT.transactionID
	where tDit.orgID = @orgID
	and tDIT.recognitionDate > @endDate
	and tDIT.isActive = 1;

	insert into #tmpRecog_a
	select tmp.transactionID, tmp.recognitionDate, tmp.recogMonth, t.amount * -1, tmp.GLAccountID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITOffsetTrans and r.transactionID = t.transactionID
	inner join #tmpRecog_a as tmp on tmp.transactionID = r.appliedToTransactionID
	where t.ownedByOrgID = @orgID
	and t.transactionDate <= @endDate;

	insert into #tmpRecog_a
	select tmp.transactionID, tmp.recognitionDate, tmp.recogMonth, tmp.amount * -1, tmp.GLAccountID
	from dbo.tr_transactions as t
	inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
	inner join #tmpRecog_a as tmp on tmp.transactionID = r.appliedToTransactionID
	where t.ownedByOrgID = @orgID
	and t.transactionDate <= @endDate;


	-- sum by transactionID
	IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL
		DROP TABLE #tmpRecog;
	CREATE TABLE #tmpRecog (transactionID int, recognitionDate datetime, recogMonth varchar(7), amount decimal(18,2), GlAccountID int);

	insert into #tmpRecog
	select transactionID, recognitionDate, recogMonth, sum(amount) as amount, GLAccountID
	from #tmpRecog_a
	group by transactionID, recognitionDate, recogMonth, GLAccountID
	having sum(amount) > 0;


	IF @groupBy = 'raw' BEGIN
		IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
			DROP TABLE #tmpRecog2;

		select 
			ROW_NUMBER() OVER(order by tmp.recognitionDate, m2.LastName, m2.FirstName) as row,
			convert(varchar(10),tmp.recognitionDate,101) as [Recognition Date], tmp.recogMonth as [Recognition Period], 
			tmp.amount as [Recognition Amount],
			glDeb.thePathExpanded as [Debit Account], glDeb.accountCode as [Debit Account Code], 
			glCred.thePathExpanded as [Credit Account], glCred.accountCode as [Credit Account Code],
			tSaleTaxAdj.detail as [Revenue Detail], i.fullInvoiceNumber as [Invoice Number],
			convert(varchar(10),i.dateBilled,101) as [Invoice Billed Date], 
			convert(varchar(10),i.dateDue,101) as [Invoice Due Date],
			m2.LastName, m2.FirstName, m2.MemberNumber, m2.Company
		into #tmpRecog2
		from #tmpRecog as tmp
		inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = tmp.transactionID
		inner join @allGLs as glDeb on glDeb.glAccountID = t.debitGlAccountID
		inner join @allGLs as glCred on glCred.glAccountID = t.creditGlAccountID
		inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_DITSaleTrans and r.transactionID = t.transactionID
		inner join dbo.tr_transactions as tSaleTaxAdj on tSaleTaxAdj.ownedByOrgID = @orgID and tSaleTaxAdj.transactionID = r.appliedToTransactionID
		inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tSaleTaxAdj.transactionID
		inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tSaleTaxAdj.assignedToMemberID
		inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID;

		SET @selectsql = '
			select [Recognition Date], [Recognition Period], [Recognition Amount], [Debit Account], [Debit Account Code], 
				[Credit Account], [Credit Account Code], [Revenue Detail], [Invoice Number], [Invoice Billed Date], 
				[Invoice Due Date], LastName, FirstName, MemberNumber, Company, row as mcCSVorder 
			*FROM* #tmpRecog2';
		EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;

		IF OBJECT_ID('tempdb..#tmpRecog2') IS NOT NULL
			DROP TABLE #tmpRecog2;
	END 
	ELSE BEGIN

		SELECT @dtlist = COALESCE(@dtlist + ',','') + quoteName(recogMonth)
		FROM #tmpRecog
		group by recogMonth
		ORDER BY recogMonth;

		IF @dtlist is not null begin	
			IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
				DROP TABLE ##tmpRecog;
			set @fullSQL = 'select *
				into ##tmpRecog
				from (select recogMonth, amount, GLAccountID from #tmpRecog) as tmp
				PIVOT (sum(amount) FOR recogMonth in (' + @dtlist + ')) as pvt';
			EXEC(@fullSQL);

			IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
				DROP TABLE #tmpRecogTotal;
			select GlAccountID, sum(amount) as total
			into #tmpRecogTotal
			from #tmpRecog
			group by GlAccountID;

			IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
				DROP TABLE #tmpRecogFinal;
			select ROW_NUMBER() OVER(ORDER BY gl.thePath) as row, gl.thePathExpanded as [GL Account], gl.accountCode as [GL Account Code], 
				tmp.*, tmpt.total as Total
			into #tmpRecogFinal
			from ##tmpRecog as tmp
			inner join @allGLs as gl on gl.glAccountID = tmp.GlAccountID
			inner join #tmpRecogTotal as tmpt on tmpt.GlAccountID = tmp.GlAccountID;

			IF OBJECT_ID('tempdb..##tmpRecog') IS NOT NULL
				DROP TABLE ##tmpRecog;
			IF OBJECT_ID('tempdb..#tmpRecogTotal') IS NOT NULL
				DROP TABLE #tmpRecogTotal;

			EXEC('ALTER TABLE #tmpRecogFinal DROP COLUMN GlAccountID;');

			SET @selectsql = '
				SELECT *, row as mcCSVorder 
				*FROM* #tmpRecogFinal';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;

			IF OBJECT_ID('tempdb..#tmpRecogFinal') IS NOT NULL
				DROP TABLE #tmpRecogFinal;
		END ELSE BEGIN
			SET @selectsql = '
				SELECT null as row, null as [GL Account], null as [GL Account Code], null as [Recognition Date], null as [Recognition Period], 0 as mcCSVorder
				*FROM* #tmpRecog
				where 1=0';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename=@filename, @returnColumns=0;
		END
	END


	IF OBJECT_ID('tempdb..#tmpRecog_a') IS NOT NULL 
		DROP TABLE #tmpRecog_a;
	IF OBJECT_ID('tempdb..#tmpRecog') IS NOT NULL
		DROP TABLE #tmpRecog;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
