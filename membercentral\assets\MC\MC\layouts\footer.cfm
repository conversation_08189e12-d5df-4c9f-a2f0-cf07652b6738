<cfoutput>
	<cfset local.zoneP1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='P',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['P'],1)>
			<cfset local.zoneP1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['P'][1].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<cfset local.zoneQ1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='Q',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Q'],1)>
			<cfset local.zoneQ1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['Q'][1].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<cfset local.zoneR1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='R',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['R'],1)>
			<cfset local.zoneR1Content = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['R'][1].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
	<cfset local.zoneS1Content = "">
	<cfif application.objCMS.getZoneItemCount(zone='S',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['S'],1)>
			<cfset local.zoneS1Content =  trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['S'][1].data,"<p>",""),"</p>",""))>
		</cfif>
	</cfif>
	<!--Footer Start-->
	<div class="footer">
		<div class="container containerCustom w-1200">
			<div class="row d-flex-wrap">
				<div class="foot-logo-wrap">
				#local.zoneP1Content#
				</div>

				<div class="rightfoot d-flex-wrap">
					<span class="footerQuickWrapper hide">#local.zoneQ1Content#</span>

					<cfif len(trim(local.zoneR1Content))>
					<div class="col4 footer-links footerContact">
						#local.zoneR1Content#
					</div>
					</cfif>
				</div>
			</div>
		</div>
		<div class="copyright-block">
			<div class="container containerCustom w-1200">
				<div class="row-fluid">
					<div class="copyright">
						<p>#local.zoneS1Content#</p>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!--Footer End-->
	<!-- Print Footer -->
	<div class="printFooter">
		<p>#local.zoneS1Content#</p>
	</div>
	<!-- Print Footer -->
</cfoutput>