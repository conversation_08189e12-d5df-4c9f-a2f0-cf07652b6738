ALTER PROC dbo.enableSiteFeature
@siteID int,
@toolTypeList varchar(1000)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @GLAccountID int, @sysMemberID int, @subscriptionAdminSiteResourceID int, @languageID int,
		@rootSectionID int, @applicationTypeID int, @zoneID int, @pgResourceTypeID int, @subscriptionsPageID int,
		@subscriptionsSiteResourceID int, @subscriptionsTitle varchar(50), @applicationInstanceID int;
	select @orgID = orgID from dbo.sites where siteID = @siteID;
	select @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

	declare @tblTools TABLE (toolType varchar(100));
	insert into @tblTools (toolType)
	select listItem from dbo.fn_varCharListToTable(@toolTypeList,',');

	declare @toolType varchar(100);
	SELECT @toolType = min(toolType) from @tblTools;
	WHILE @toolType is not null BEGIN

		-- APIAccess
		if @toolType = 'APIAccess'
			update dbo.siteFeatures
			set mcAPI = 1
			where siteID = @siteID
			and mcAPI = 0;

		-- contributions
		if @toolType = 'ContributionAdmin' begin
			update dbo.siteFeatures
			set contributions = 1
			where siteID = @siteID
			and contributions = 0;

			EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='PLEDGESRECEIVABLE', @GLAccountID=@GLAccountID OUTPUT;

			IF @GLAccountID IS NULL
				EXEC dbo.tr_createGLAccount @orgID=@orgID, @accountTypeID=2, @accountName='Pledges Receivable', @accountCode='', @GLCode='PLEDGESRECEIVABLE', 
					@parentGLAccountID=null, @invoiceProfileID=null, @isSystemAccount=1, @invoiceContentID=null, @deferredGLAccountID=null, 
					@salesTaxProfileID=null, @salesTaxTaxJarCategoryID=null, @recordedByMemberID=@sysMemberID, @GLAccountID=@GLAccountID output;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- email blast
		if @toolType = 'EmailBlast' begin
			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- member documents
		if @toolType = 'MemberDocs'
			update dbo.siteFeatures
			set memberDocuments = 1
			where siteID = @siteID
			and memberDocuments = 0;

		-- member history
		if @toolType = 'MemberHistoryAdmin'
			update dbo.siteFeatures
			set memberHistory = 1
			where siteID = @siteID
			and memberHistory = 0;

		-- relationships
		if @toolType = 'RelationshipAdmin' begin
			update dbo.siteFeatures
			set relationships = 1
			where siteID = @siteID
			and relationships = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- reports
		if @toolType = 'Reports' begin
			declare @siteCode varchar(10);
			select @sitecode = sitecode from dbo.sites where siteID = @siteID;

			insert into dbo.admin_siteToolRestrictions (toolTypeID, siteID)
			select tooltypeid, @siteID 
			from dbo.admin_toolTypes 
			where (includeInAllReportsGrid = 1 or tooltype in ('rpt_ReportSettings','rpt_SavedReports')) 
			and tooltype not in (
				'AcctCreditBalancesReport',
				'AcctFailedPaymentReport',
				'AcctGrossSalesReport',
				'AcctInvoiceAgingReport',
				'AcctNegativeWriteOffsReport',
				'AcctPaymentReport',
				'AcctPendingPaymentReport',
				'AcctReconciliationsReport',
				'AcctTransactionReport',
				'AcctVoidTransactionReport',
				'AcctWriteOffsReport',
				'LiveWebinarRegistrantsAwardedCredit',
				'OnDemandExpiringCredit',
				'OnDemandExpiringFromCatalog',
				'OnDemandRegistrantsAwardedCredit',
				'OnDemandRegistrantsEnrollment',
				'UTMRevenueReport',
				'UTMRevenueTSReport'
			) and (
				left(toolCFC,15) <> 'Reports.custom.' 
				OR toolCFC like 'Reports.custom.' + @sitecode + '.%'
			)
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- Badges
		if @toolType = 'BadgeDeviceAdmin'
			update dbo.siteFeatures
			set badgePrinters = 1
			where siteID = @siteID
			and badgePrinters = 0;

		-- subscriptions
		if @toolType = 'SubscriptionAdmin' begin
			update dbo.siteFeatures
			set subscriptions = 1
			where siteID = @siteID
			and subscriptions = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = 'SubRenewalAdmin'
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;

			-- add/update default FULL Frequency
			if not exists(select frequencyID from dbo.sub_frequencies where frequencyShortName = 'F' and siteID = @siteID and status = 'A')
				insert into dbo.sub_frequencies(frequencyName, frequency, frequencyShortName, uid, 
					rateRequired, hasInstallments, monthlyInterval, isSystemRate, siteID, status)
				values('Full', 1, 'F', newid(), 1, 1, 1, 1, @siteID, 'A');
			else
				update dbo.sub_frequencies 
				set isSystemRate = 1 
				where frequencyShortName = 'F' 
					and siteID = @siteID 
					and status = 'A';
		end

		-- Subscriptions Renew V2
		if @toolType = 'EnableSubscriptionAddV2'
			update dbo.siteFeatures
			set subsAddV2 = 1
			where siteID = @siteID
			and subsAddV2 = 0;

		-- tasks
		if @toolType = 'TaskAdmin' begin
			update dbo.siteFeatures
			set tasks = 1
			where siteID = @siteID
			and tasks = 0;

			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		if @toolType = 'EnableSitePasswords'
			EXEC dbo.enableSiteFeature_sitePasswords @siteID=@siteID;
			
		-- ads
		if @toolType = 'AdsAdmin' begin
			insert into dbo.admin_siteToolRestrictions (tooltypeID, siteID) 
			select tooltypeID, @siteID
			from dbo.admin_toolTypes
			where toolType = @toolType
				except
			select tooltypeID, siteID 
			from dbo.admin_siteToolRestrictions
			where siteID = @siteID;
		end

		-- Referrals SMS
		if @toolType = 'referralsSMS'
			update dbo.siteFeatures
			set referralsSMS = 1
			where siteID = @siteID
			and referralsSMS = 0;

		-- Recurring Events
		if @toolType = 'RecurringEvents'
			update dbo.siteFeatures
			set recurringEvents = 1
			where siteID = @siteID
			and recurringEvents = 0;
		
		SELECT @toolType = min(toolType) from @tblTools where toolType > @toolType;
	END

	-- refresh and assign resources
	exec dbo.createAdminSuite @siteID=@siteid;

	-- set cache siteResourceIDs once admin tool resource for SubscriptionAdmin is created
	IF EXISTS (SELECT 1 FROM @tblTools WHERE toolType = 'SubscriptionAdmin') BEGIN
		SELECT @subscriptionAdminSiteResourceID = sr.siteResourceID
		FROM dbo.cms_siteResources AS sr
		INNER JOIN dbo.cms_siteResourceTypes AS srt ON srt.resourceTypeID = sr.resourceTypeID
			AND srt.resourceType = 'subscriptionAdmin'
		WHERE sr.siteID = @siteID
		AND sr.siteResourceStatusID = 1;

		-- create front-end subscriptions app instance
		SELECT @languageID = defaultLanguageID from dbo.sites where siteID = @siteID;
		SELECT @rootSectionID = dbo.fn_getRootSectionID(@siteID);
		SET @zoneID = dbo.fn_getZoneID('Main');
		SET @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
		SET @subscriptionsTitle = 'Manage Membership';

		SELECT @applicationTypeID = applicationTypeID
		FROM dbo.cms_applicationTypes
		WHERE applicationTypeName = 'subscriptions';

		EXEC dbo.cms_createApplicationInstance @siteID=@siteID, @languageID=@languageID, @sectionID=@rootSectionID, @applicationTypeID=@applicationTypeID,
			@isVisible=1, @pageName='manageSubscriptions', @pageTitle=@subscriptionsTitle, @pagedesc=@subscriptionsTitle, @zoneID=@zoneID, @pageTemplateID=NULL,
			@pageModeID=NULL, @pgResourceTypeID=@pgResourceTypeID, @pgParentResourceID=NULL, @allowReturnAfterLogin=1, @applicationInstanceName=@subscriptionsTitle, 
			@applicationInstanceDesc=@subscriptionsTitle, @applicationInstanceID=@applicationInstanceID OUTPUT, @siteResourceID=@subscriptionsSiteResourceID OUTPUT,
			@pageID=@subscriptionsPageID OUTPUT;

		UPDATE dbo.sites
		SET subscriptionAdminSiteResourceID = @subscriptionAdminSiteResourceID,
			subscriptionsSiteResourceID = @subscriptionsSiteResourceID
		WHERE siteID = @siteID;
	END

	IF EXISTS (select toolType from @tblTools where toolType = 'BadgeDeviceAdmin')
		EXEC dbo.cms_createDefaultBadgeTemplateCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'EmailBlast')
		EXEC dbo.cms_createDefaultEmailBlastCategories @siteID=@siteID;

	IF EXISTS (select toolType from @tblTools where toolType = 'MemberHistoryAdmin')
		EXEC dbo.cms_createDefaultHistoryAdminCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'RelationshipAdmin')
		EXEC dbo.cms_createDefaultRelationshipCategories @siteID=@siteID, @contributingMemberID=@sysMemberID;

	IF EXISTS (select toolType from @tblTools where toolType = 'Reports')
		EXEC dbo.ams_createDefaultExtendedNameFieldset @siteID=@siteID;

	IF EXISTS (select toolType from @tblTools where toolType = 'SubscriptionAdmin')
		EXEC dbo.cms_createDefaultSubscriptionAdminFieldSets @siteID=@siteID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
