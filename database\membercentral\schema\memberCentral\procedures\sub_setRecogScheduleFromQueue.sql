ALTER PROC dbo.sub_setRecogScheduleFromQueue
@itemUID uniqueidentifier

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @statusReady int, @itemStatus int, @siteID int, @subscriberID int, @recordedByMemberID int,
		@transactionDate datetime, @profileID int, @GLAccountID int, @invoiceID int, @invoiceNumber varchar(19),
		@recogStartDate datetime, @recogEndDate datetime, @totalAmount float, @splitAmount float, @numPayments int, 
		@totalSplitAmount float, @xmlSchedule xml, @transactionID int, @queueTypeID int, @statusProcessing int,
		@statusNotify int;

	declare @tblDeferredSchedule table (rowID int IDENTITY(1,1), scheduleDate datetime, amt decimal(18,2));

	EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='setSubRecogRange', @queueTypeID=@queueTypeID OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@statusReady OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='processingItem', @queueStatusID=@statusProcessing OUTPUT;
	EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToNotify', @queueStatusID=@statusNotify OUTPUT;

	select @siteID=qid.siteID, @subscriberID=qid.subscriberID, @recordedByMemberID=qid.recordedByMemberID,
		@GLAccountID = ss.GLAccountID, @totalAmount = ss.lastPrice, @recogStartDate = qid.recogStartDate, 
		@recogEndDate = qid.recogEndDate, @itemStatus = qid.statusID
	from platformQueue.dbo.queue_setSubRecogRange AS qid
	inner join dbo.sub_subscribers as ss on ss.subscriberID = qid.subscriberID
	where qid.itemUID = @itemUID;

	-- if itemUID is not readyToProcess, kick out now
	IF @itemStatus <> @statusReady OR @subscriberID is null
		RAISERROR('Item not in readyToProcess state',16,1);

	select @orgID = orgID from dbo.sites where siteID = @siteID;

	UPDATE platformQueue.dbo.queue_setSubRecogRange
	set statusID = @statusProcessing
	where itemUID = @itemUID;

	BEGIN TRY
		update dbo.sub_subscribers
		set recogStartDate = @recogStartDate,
			recogEndDate = @recogEndDate
		where subscriberID = @subscriberID;
	END TRY
	BEGIN CATCH
		IF @@trancount > 0 ROLLBACK TRANSACTION;
		UPDATE platformQueue.dbo.queue_setSubRecogRange set errMessage = ERROR_MESSAGE() where itemUID = @itemUID;
	END CATCH

	UPDATE platformQueue.dbo.queue_setSubRecogRange
	set statusID = @statusNotify
	where itemUID = @itemUID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
