@charset "utf-8";

body {
    margin: 0;
    padding: 0;
    background-color: #fff;
    font-weight: 400;
    font-family: 'Adobe Garamond Pro';
    font-style: normal;
    font-size: 18px;
    line-height: 1.5;
    color: #121412;
}

*,
input[type="search"] {
    -moz-box-sizing: border-box;
    -ms-box-sizing: border-box;
    -o-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

input {
    outline: none;
}

img {
    max-width: 100%;
}

a {
    color: #ba892c;
    text-decoration: none;
}

a:hover,
a:focus {
    color: #92140C;
    text-decoration: underline;
}

.px-5 {
    padding-left: 5px;
    padding-right: 5px;
}

.SectionHeader {
    font-size: 45px;
    font-weight: 700;
    line-height: 1.3;
    font-family: 'Adobe Garamond Pro';
    margin-bottom: 30px;
}

.SectionHeaderUnderline {
    font-size: 34px;
    font-weight: 700;
    color: #0B0E2C;
    margin-bottom: 40px;
    line-height: 1.3;
}

.SectionHeaderUnderline:after {
    display: block;
    content: "";
    height: 2px;
    width: 60px;
    background: #ba892c;
    margin: 8px 0 0;
    border-radius: 5px;
}

.HeaderText {
    font-weight: 700;
    font-size: 55px;
    color: #043b56;
    font-family: 'Adobe Garamond Pro';
}

.HeaderText b {
    font-weight: 900;
}

.HeaderTextSmall {
    font-weight: 700;
    font-size: 30px;
    line-height: 1.5;
    color: #023b56;
    font-family: 'Adobe Garamond Pro';
}

.HighlightTitle {
    font-weight: 700;
    font-size: 35px;
    line-height: 1.3;
    color: #043b56;
    margin: 0 0 10px;
    display: block;
}

.SubHeading {
    font-size: 26px;
    color: #434345;
    font-weight: 800;
    font-family: 'Adobe Garamond Pro';
    line-height: 1.2;
    display: block;
    text-align: center;
}
.SubHeading:after {
    content: "";
    display: block;
    background: #ba892c;
    width: 50px;
    height: 1px;
    margin: 10px auto 20px;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    color: #043b56;
    font-family: 'Adobe Garamond Pro';
    line-height: 1.3;
}

p,
.BodyText, li {
    font-family: 'Carbona Test';
    font-style: normal;
    font-weight: 400;
    font-size: 18px;
    line-height: 1.34;
    color: #0c2c48;
}

p.BodyTextLarge,
.BodyTextLarge {
    font-size: 20px;
}

p.InfoText,
.InfoText {
    font-size: 14px;
}

.TitleText {
    font-size: 55px;
    font-weight: 700;
    color: #46505d;
    font-family: 'Adobe Garamond Pro';
    line-height: 1.2;
}

.Brown {
    color: #472103 !important;
}

.Gray {
    color: #9A8D83 !important;
}

.Blue {
    color: #121412 !important;
}

.LightBlue {
    color: #619EED !important;
}

.RalewayRoman {font-family: 'RalewayRoman', sans-serif !important;}
.PTSerif {font-family: 'PT Serif', serif !important;}
.Lato {font-family: 'Lato', sans-serif !important;}
.BaskervilleSemiBold {font-family: 'Baskervville', serif; font-weight: 600 !important;}
.AbhayaLibreExtraBold {font-family: 'Adobe Garamond Pro'; font-weight: 800 !important;}


.AdobeGaramondProand {font-family: 'Adobe Garamond Proand', serif;}
.CarbonaVariableRegular{font-family: 'Carbona Variable Regular', sans-serif;}

.text-yellow {
    color: #cca876 !important;
}
a.btn,
a.btn:hover {
	transition: all 0.3s ease !important; 
	background-size: 100% 150% !important;
}

.MAJButton {
    background: rgba(236, 169, 79, 0);
    border: 2px solid #ba892c;
    color: #ba892c;
    font-size: 16px;
    font-weight: 700;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 20px;
    text-align: center;
    font-family: 'Carbona Test';
    text-transform: uppercase;
}

.MAJButton:hover,
.MAJButton:focus {
    background: #ba892c;
    color: #ffffff;
    text-decoration: none;
}

.MAJButton-2 {
    background: #ba892c;
    border: 2px solid #ba892c;
    color: #fff;
    font-size: 16px;
    font-weight: 700;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 20px;
    text-align: center;
    font-family: 'Carbona Test';
    text-transform: uppercase;
}

.MAJButton-2:hover,
.MAJButton-2:focus {
    background: #ffffff;
    color: #ba892c;
    text-decoration: none;
}

.WhiteBorder {
    background: transparent;
    border: 1px solid #ffffff;
    color: #ffffff;
    font-size: 14px;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 15px;
    text-align: center;
}

.WhiteBorder:hover,
.WhiteBorder:focus {
    color: #ffffff;
    text-decoration: none;
    background: #023b56;
}

.TextButton {
    color: #ba792c;
    font-family: 'Carbona Test';
    font-weight: 700;
    font-size: 18px;
    text-transform: uppercase;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.TextButton:hover {
    color: #ba892c;
}
    


.QuoteAuthor {
    font-size: 18px;
    color: #023b56;
    font-family: 'Carbona Test';
}

.pd_40 {
    padding: 40px 0px;
}

.pd_50 {
    padding: 50px 0px;
}

.pd_60 {
    padding: 60px 0;
}

.pd_70 {
    padding: 70px 0;
}

.pd_30 {
    padding: 30px 0;
}

.mb-30 {
    margin-bottom: 30px;
}

.gray-bg {
    background: #DDD8D3;
}

.clearfix::before,
.clearfix::after {
    content: "";
    display: table;
    width: 100%;
    clear: both;
}
.Highlight {
    background: #f2f3f8;
    padding: 40px 50px;
    clear: both;
}
.quotetitle {
    font-size: 30px;
    margin: 0;
    color: #023b56;
    font-family: 'Adobe Garamond Pro';
}
.Highlight p {
    margin-bottom: 20px
}
.Highlight .btns-wrap a {
    min-width: 270px;
}
.xs979 {
    display: none !important;
}

.xs767,
.xsVisible {
    display: none !important;
}

.xsHidden979 {
    display: block !important;
}

.xsHidden767,
.xsHidden {
    display: block !important;
}

.textUnderline {
    text-decoration: underline;
}

/***Header***/
.printHeader,
.printFooter {
    display: none;
}

.header {
    min-height: 116px;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 999;
    background: #ffffff;
}

.headerSpace {
    width: 100%;
    height: 180px;
    background-color: transparent;
}

.header .navbar {
    margin-bottom: 0;
    border-top: 1px solid #e5e7e9;
}
.main-navbar {
    background-color: #f1f3f7;
}

.header .navbar .nav li.dropdown .memberSection a.MAJButton {
    background: rgba(236, 169, 79, 0);
    border: 1px solid #ffffff;
    color: #ffffff;
    font-size: 16px;
    font-weight: 700;
    text-transform: uppercase;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 20px;
    background: #ed3943;
    text-align: center;
    margin-right: 15px;
}

header .navbar .nav li form .btn-flex {
    align-items: center;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.droplist-1.member-boxthree ul {
    margin: 0;
    display: block;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.droplist-1.member-boxthree ul li {
    display: block;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.droplist-1.member-boxthree ul li a {
    color: #ffffff;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.droplist-1.member-boxthree ul li a.MAJButton {
    margin-top: 10px;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.droplist-1.member-boxthree ul li a.MAJButton:before {
    display: none;
}

.header .navbar .nav li.dropdown .memberSection a.MAJButton:hover {
    background: #eca94f;
    border-color: #eca94f;
    color: #ffffff;
    text-decoration: none;
}

.header-drop-title a.readMoreButton {
    padding: 0 !important;
    color: #ed3943 !important;
}

.header .navbar .nav li.dropdown .droplist-1 .header-drop-title h2 {
    color: #121412;
}

.header .navbar-inner {
    border: none;
    -moz-border-radius: 0;
    -ms-border-radius: 0;
    -o-border-radius: 0;
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    padding: 0;
    min-height: inherit;
    border-bottom: 12px solid #b9892b;
    background: #ffffff;
}

.header .navbar-brand {
    margin-left: 0px;
    float: left;
    max-height: 100%;
    /* height: 115px; */
    padding: 10px 10px 10px 0px;
    width: auto;
}

.header .navbar .nav>li>a {
    font-family: 'Carbona Test';
    font-style: normal;
    font-weight: 500;
    font-size: 18px;
    line-height: 20px;
    /* text-align: center; */
    color: #023b56;
    text-shadow: none;
    padding: 5px 2px;
    border-bottom: 1px solid #ffffff00;
    display: block;
}

.header .navbar .nav>li.dropdown>a:after {
    width: 8px;
    height: 8px;
    content: "";
    position: relative;
    top: -3px;
    z-index: 1;
    left: 0;
    margin-left: 10px;
    bottom: 0px;
    border: 1px solid #ba892c;
    border-style: none solid solid none;
    display: inline-block;
    transform: rotate(45deg);
}

.header .navbar .nav>li:nth-last-child(5):focus>a,
.header .navbar .nav>li:nth-last-child(5):visited>a,
.header .navbar .nav>li:nth-last-child(5)>a:visited,
.header .navbar .nav>li:nth-last-child(3):focus>a,
.header .navbar .nav>li:nth-last-child(3):visited>a,
.header .navbar .nav>li:nth-last-child(3)>a:visited {
    background-color: #f1b828;
    color: #fff;
}

.header .navbar .nav li:nth-last-child(3) a img,
.header .navbar .nav li:nth-last-child(5) a img {
    width: 20px;
    height: 20;
    object-fit: contain;
    display: block;
    margin: 0 auto;
    margin-bottom: 15px;
}

.header .navbar .nav li:nth-last-child(3) a span {
    display: block;
    text-decoration: underline;
    font-family: 'PT Serif', serif;
}

.header .navbar .nav li:nth-last-child(3) {
}

.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.droplist-2 li {
    display: block;
    width: 100%;
    margin-bottom: 10px;
}

.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.droplist-2 li a {
    color: #fff;
}

.header .navbar .nav li.dropdown .memberSection li,
.header .navbar .nav li.dropdown .memberSection li p,
.header .navbar .nav li.dropdown .memberSection li a {
    color: #fff;
    display: inline-block;
    padding: 0;
    font-weight: 300;
    font-size: 16px;
}

.header .navbar .nav li.dropdown .memberSection li p {
    margin-bottom: 20px;
    font-size: 18px;
}

.header .navbar .nav li.dropdown .memberSection li a {
    text-decoration: underline;
}

.header .navbar .nav li.dropdown .memberSection li label {
    font-weight: 300;
    font-size: 16px;
}

.header .navbar .nav li.dropdown .memberSection li input {
    background-color: #fff;
    border: 0;
    height: 45px;
    border-radius: 0;
    width: 100%;
    margin-bottom: 15px;
    color: #121412;
    font-family: 'Montserrat', sans-seri;
    font-weight: 400;
    padding: 0 10px;
}

.header .navbar .nav li.dropdown .memberSection li input:focus {
    box-shadow: none;
}

.header .navbar .nav li.dropdown .memberSection li form a.btn {
    color: #fff;
    background: transparent;
    border: 2px solid #fff;
    font-size: 13px;
    font-weight: 500;
    height: 50px;
    min-width: auto;
    text-transform: uppercase;
    border-radius: 0px;
    font-family: 'Adobe Garamond Pro';
    line-height: 46px;
    padding: 0;
    margin: 0;
    box-shadow: none;
    text-shadow: none;
    padding: 0 25px;
    display: inline-block;
    width: auto;
    text-decoration: none;
}

.header .navbar .nav li.dropdown .memberSection li form a.btn:hover {
    background: #fff;
    color: #2d3e55;
}

.header .navbar .nav li.dropdown .memberSection li form a {
    width: 50%;
    float: left;
}

.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:hover,
.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:focus {
    background: #BA0C2F;
    color: #ffffff;
    border-color: #BA0C2F;
}

.header .navbar .nav li.dropdown .memberSection li form a:last-child {
    font-family: 'Adobe Garamond Pro';
    font-size: 14px;
    text-align: left;
    padding: 0px;
    text-decoration: none;
    margin-left: 15px;
    margin-top: 5px;
    text-transform: inherit;
}

.header .navbar .nav li.dropdown li a:hover,
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover,
.header .navbar .nav li.dropdown li a:focus,
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:focus,
.header .navbar .nav li .dropdown-menu>li:hover a {
    background: transparent;
}

.header .dropdown li {
    padding: 0 0px;
}

.header .navbar .nav ul.dropdown-menu li>a:hover {
    color: #ba892c;
}

.inline-link {
    font-size: 12px;
    font-family: 'Carbona Test';
}

.header .navbar .nav li.active>a {
    color: #ba892c;
    box-shadow: none;
    border-bottom: 2px solid #cca876;
    background: transparent;
}

.header .navbar .nav>li {
    display: inline-block;
    padding: 10px 0px;
}
.header .navbar .nav>li.top-btns {
    margin-left: 15px;
}

.header .navbar .nav li>a:hover,
.header .navbar .nav li>a:focus {
    background: transparent;
    color: #ba892c;
    box-shadow: none;
}

.header .nav-collapse.collapse {
    margin: 0;
}

.header .nav-collapse .nav {
    margin: 0;
    position: static;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0;
    padding-right: 0;
    width: 100%;
}

.searchBtnFn {
    position: initial;
}

.header .navbar .nav>li>.dropdown-menu::after,
.header .navbar .nav>li>.dropdown-menu::before {
    display: none;
}

.header .dropdown-menu>li>a {
    color: #3b3b3c;
}

.header .navbar .nav li .dropdown-menu li>a {
    border-right: none;
    text-align: left;
    white-space: normal;
    padding: 5px 20px;
    border: none;
    margin-bottom: 0px;
    color: #023b56;
    font-size: 15px;
    display: block;
    text-decoration: none;
    font-family: 'Carbona Test';
    font-style: normal;
    font-weight: 600;
}

.header .dropdown-menu {
    -moz-box-shadow: none;
    -ms-box-shadow: none;
    -o-box-shadow: none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.header .navbar .nav li .dropdown-menu>li:last-child a {
    border-bottom: none;
}

.header .dropdown-menu {
    width: 215px;
}
.header .dropdown-menu .droplist-1:not(:last-child) {
    border-bottom: 1px solid #aabbc7;
}
.header .navbar .nav>li>a {
    position: relative;
    background: transparent;
    z-index: 1;
}

.header .dropdown-submenu>.dropdown-menu {
    border: none;
    padding: 0;
    margin: 0;
}

.header .dropdown-submenu>a::after {
    /*display: none;*/
}

.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a {
    border: none;
    background: rgba(0, 0, 0, 0.1);
}

.header .navbar .nav li.dropdown.open>.dropdown-toggle,
.header .navbar .nav li.dropdown.active>.dropdown-toggle,
.header .navbar .nav li.dropdown.open.active>.dropdown-toggle,
.header .navbar .nav li.active>.dropdown-toggle {
    color: #121412;
    background-color: #ffffff;
    text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
    font-weight: 600;
    border-color: #eeeeee;
    box-shadow: none;
}

[data-toggle="dropdown"] {
    /* display: none; */
}

.header .navbar .nav li.dropdown .droplist-1 li a.active {
    color: #eca950;
    font-weight: 700;
}

.header .dropdown-menu {
    border-radius: 0;
    background: rgb(0, 107, 182);
}

.header .navbar .nav li.dropdown .droplist-1 .heading {
    max-width: 215px;
    margin: 0;
    top: 50%;
    transform: translateY(-50%);
    position: absolute;
}

.header .navbar .nav li.dropdown .droplist-1 .searchHeading {
    text-transform: uppercase;
    font-weight: 500;
    width: 100%;
    max-width: 308px;
    text-align: right;
}

.header .navbar .nav li.dropdown .droplist-1 .searchHeading p.TitleText {
    color: #fff;
    border: 0;
    text-shadow: none;
}

.header .formframe {
    width: 100%;
    padding: 0 25px 0 0;
    border-radius: 5px;
    display: block;
}

.header .formframe input {
    float: left;
    background: #f3f3f3 url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFmSURBVHgBjVI7TsNAEJ3ZJYoRjTtERbjBigQBnXMDSjrgBk46qsAFgnMCUkLJCeIuIH7mBqYBSlcoKNkdZjc/yzESI+1vdt6bL8JMKipUUsorAlII4CNgQkS90Uu3DwVBu3n11glfVz5n0h89d8/yCumpsAZCxFMG6gHSuRbUEwYfCCFg1oO1rUOcfA7jhSev3r7m87SM0WuEAZAYEEC2rs1OlkSZ1QtegbPQ5rIY1+gpYnaMbY7fUgZzvQXVnEESpVAiRObNGRL5C5B1bS++Cv0ykEDctqdBzJY6Lq3zJERYBNgiMemRM9Q6WYaHepoLQqe62w5zgACkGLgQge7y4U/71Ghf8E9nkQeHbJPPv40wzfFj5LxJu00+hjH34p2viml4GsAjYiDCDQNSfiskPK5s7t9Ovu4zLOZR2QuVPTfGkM77whPT56B4aiDl1jRXQH9Jtd565aJZwlT8F/SjqckFSWyCv0wrhb9anqj3AAAAAElFTkSuQmCC);
    border: 0;
    color: #33383A;
    background-position: left 20px center;
    width: calc(100% - 110px);
    background-repeat: no-repeat;
    font-size: 16px;
    display: inline-block;
    margin: 0;
    height: 36px;
    box-shadow: none;
    outline: none;
    padding: 0 15px 0 50px;
    font-weight: 400;
    font-family: 'Carbona Test';
    font-size: 14px;
}
.header .formframe form {
    margin: 0;
    padding: 5px 20px 5px 0px;
    height: auto;
    display: block;
    clear: both;
}
.header .formframe form:before, .header .formframe form:after {
    content: '';
    display: table;
    width: 100%;
    clear: both;
    height: 1px;
    margin-top: -1px;
}
.header .navbar .nav li.dropdown .droplist-1 .formframe input::-webkit-input-placeholder {
    color: #33383A;
}

.header .navbar .nav li.dropdown .droplist-1 .formframe input::-moz-placeholder {
    color: #33383A;
}

.header .navbar .nav li.dropdown .droplist-1 .formframe input:-ms-input-placeholder {
    color: #33383A;
}

.header .navbar .nav li.dropdown .droplist-1 .formframe input:-moz-placeholder {
    color: #33383A;
}

.header .formframe a {
    float: right;
    color: #fff;
    background: #0b0e2c;
    border: 2px solid #0b0e2c;
    font-size: 14px;
    font-weight: 700;
    height: 36px;
    min-width: auto;
    text-transform: uppercase;
    line-height: 32px;
    margin: 0;
    box-shadow: none;
    text-shadow: none;
    padding: 0px 22px;
    display: inline-block;
    width: auto;
    border-radius: 0;
}

.header .navbar .nav li.dropdown .droplist-1 .formframe a:hover {
    background: #ffffff;
    color: #0b0e2c;
    border: 2px solid #0b0e2c;
    text-decoration: none;
}
.header .navbar .row.flex-row {
    justify-content: space-between;
    padding: 0 15px;
}

.header .navbar .row.flex-row:before, 
.header .navbar .row.flex-row:after {
    display: none;
}
.header .navbar .row.flex-row .top-btns {
    align-self: center;
    padding-right: 0;
}
.quicklink-desktop .dropdown-menu {
    position: relative;
    z-index: 1;
    width: 100%;
    z-index: 999;
    background: #efe2ce;
    box-shadow: none;
    margin: 0 0 20px;
    clear: both;
    float: none;
    border-style: none;
    z-index: 1;
}

.DiamondBullets ul li.dropdown.open {
    margin: 0 0 0;
}

.DiamondBullets ul li.dropdown.open .dropdown-backdrop {
    display: none;
}

.quicklink-desktop .dropdown-menu li {
    margin: -1px 0 0;
}

.searchBtnFn .default {
    display: block;
}

.searchBtnFn li {
    display: inherit;
    width: 370px;
    margin: 0 !important;
    padding: 0;
}

.searchBtnFn li {
    display: inherit;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 15px 50px 15px 20px;
    position: relative;
}
.social-links ul li a {
    background: #b4b4b4;
    display: inline-flex;
    width: 25px;
    height: 25px;
    line-height: 30px;
    text-align: center;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    border-radius: 50%;
    font-size: 14px;
    padding-left: 3px;
}
.social-links ul li a:hover {
    background-color: #ba892c;
    text-decoration: none;
}
.social-links .searchBtnFn>a {
    background: transparent;
    color: #000000;
    font-size: 22px;
}
.social-links li.cart-icon>a {
    background: transparent;
    margin: 0 5px;
}
.social-links .searchBtnFn>a:hover ,
.social-links li.cart-icon>a:hover {
    background-color: transparent;
}
.social-mobile,
.mobile-links {
    display: none;
}

.top-strip .ts-left-box {
    display: inline-flex;
    align-items: center;
}

.top-strip .ts-left-box>span {
    font-size: 14px;
    font-family: 'Carbona Test';
    margin-right: 30px;
}

.header .navbar .nav li form a {
    padding: 0;
    color: #ffffff;
}

header .navbar .nav li form a.MAJButton:hover {
    background: #ffffff;
    border-color: #ffffff;
}

.searchBtnFn:hover .dropdown-menu,
body .header .navbar .nav>li.headerlogin:not(.show-form):hover .dropdown-menu {
    display: none !important;
}

.searchBtnFn:hover .dropdown-toggle:after {
    display: none !important;
}

.searchBtnFn.show-search-bar>ul {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 999;
}
.searchBtnFn >ul {
    display: none;
    background: #ffffff;
    position: absolute;
    z-index: 1;
    transform: translateX(-50%);
    left: 50%;
    width: 100%;
    max-width: 1200px;
    top: 0;
    right: 0;
    margin: 0;
    height: auto;
    padding: 0 15px;
}

.toplist a {
    font-size: 14px;
    font-family: 'Lato', sans-serif;
    color: #9b9b9b;
    text-decoration: none;
}
body .searchBtnFn.dropdown>ul.dropdown-menu {
    margin: 0;
    background: #ffffff;
    left: auto;
    right: 225px;
    padding: 0;
    height: auto;
    top: 19px;
    width: calc(100% - 580px);
    box-shadow: none;
}
.header .navbar .nav>li.active {}

.header .navbar .nav>li:hover>a {
    border-color: #d7912f;
}

.header .formframe a.searchclose {
    background: transparent !important;
    color: #9A8D83;
    padding: 0;
    border: none;
    display: inline-flex;
    align-items: center;
    position: absolute;
    right: 0;
    top: 24px;
    right: 5px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 1;
    text-transform: none;
}

.header .navbar .nav li.dropdown .droplist-1 .formframe a.searchclose svg {
    margin-left: 8px;
}

.header .navbar .nav>li.dropdown>a {
    z-index: 99;
    position: relative;
    display: flex;
    align-items: center;
}

.searchBtnFn>a:after {
    display: none;
}

.header .navbar .nav ul.dropdown-menu li>a:before {
    content: "\f105";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    padding-right: 0px;
    font-size: 0;
    transition: all 0.3s ease;
}

.header .navbar .nav ul.dropdown-menu li:hover>a:before {
    font-size: 12px;
    padding-right: 5px;
}

.header .navbar .nav ul.dropdown-menu li.dropdown>a:before {
    position: absolute;
    right: 15px;
    top: 10px;
    font-size: 12px;
    padding: 0;
    margin: 0;
}

.header .dropdown-menu ul {
    list-style: none;
}

.header .dropdown-menu ul {
    list-style: none;
    margin: 0;
}

.dropdown-menu .droplist-1 ul {
    position: absolute;
    left: 100%;
    width: 100%;
    top: -2px;
    background: #ffffff;
    display: none;
    padding: 2px;
}

.dropdown-menu .droplist-1>ul {
    top: 0 !important;
}

.dropdown-menu .droplist-1 {
    position: relative;
}

.top-btn-wrap {
    display: inline-flex;
    gap: 15px;
}

.top-btn-wrap>a {
    display: inline-block;
    width: auto;
}
.top-btn-wrap .MAJButton {
    color: #ba892c;
}

.top-btn-wrap .MAJButton:hover {
    color: #ffffff;
}

header .top-strip {
    display: flex;
    justify-content: space-between;
    padding: 10px 0px;
}

ul.toplist {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    gap: 20px;
    margin: 0;
}

ul.toplist li:not(:first-child) {
    /* margin-left: 30px; */
}
.toplist a i {
    color: #ba892c;
    margin-right: 8px;
    font-size: 16px;
}
.social-links>ul {
    margin: 0;
    display: inline-flex;
    gap: 8px;
    list-style: none;
    padding: 0;
}



/*-------Slider-----***/
.slider {
    position: relative;
}

.slider .owl-carousel .item {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    min-height: 400px;
    position: relative;
    overflow: hidden;
    padding: 40px 0;
/* Permalink - use to edit and share this gradient: https://colorzilla.com/gradient-editor/#46505d+0,46505d+100&0.66+26,0.9+89 */
    /* background: radial-gradient(ellipse at center,  rgba(70,80,93,0.66) 0%,rgba(70,80,93,0.66) 26%,rgba(70,80,93,0.9) 89%,rgba(70,80,93,0.9) 100%); */ /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
}
.slider .owl-carousel .item:before {
    content: "";
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    left: 50%;
    width: 100vw;
    height: 100%;
    /* background: radial-gradient(ellipse at center, rgba(70,80,93,0.65) 0%,rgba(70,80,93,0.65) 17%,rgba(70,80,93,1) 75%,rgba(70,80,93,1) 100%); */
    background: #023b56;
    opacity: 0.8;
}


.slider .owl-carousel .item img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: cover;
    top: 0;
    left: 0;
    opacity: 1;
}

.slider .owl-carousel .owl-dots {
    position: relative;
    margin: -26px auto 0 !important;
    bottom: auto;
    width: auto !important;
    display: inline-block !important;
    left: 0;
    transform: translateY(0%);
    display: flex !important;
    flex-direction: row;
    top: -45px;
    justify-content: center;
}

.owl-theme .owl-dots .owl-dot {
    outline: none;
    background: transparent;
    margin: 4px;
}

.owl-theme .owl-dots .owl-dot span {
    background: #ed3943;
    margin: 0;
}

.owl-theme .owl-dots .owl-dot.active span,
.owl-theme .owl-dots .owl-dot:hover span {
    background: #ba892c;
}
.slider .owl-carousel .owl-nav button.owl-prev {
    background: rgb(255 255 255 / 30%);
    color: #ffffff;
    left: 4%;
}

.slider .owl-carousel .owl-nav button.owl-next {
    background: rgb(255 255 255 / 30%);
    color: #ffffff;
    right: 4%;
}

.carousel-caption {
    background: transparent;
    max-width: 1170px;
    margin: 0 auto;
    position: relative;
    top: 0;
    left: 0;
    padding: 0;
    min-height: 650px;
    display: flex;
    align-items: center;
    z-index: 2;
}

.captionFrame {
    margin: 0px auto 40px;
    text-align: center;
    padding: 0 10%;
}

.captionFrame .MAJButton {
    color: #ffffff;
    min-width: 280px;
}

.captionFrame ul li:nth-child(2) {
    line-height: 1.4;    
    border: 0;
    text-shadow: none;
    text-align: center;
    margin-bottom: 25px;
    font-family: 'Adobe Garamond Pro';
    color: #ffffff;
    font-size: 30px;
}

.carousel-caption .TitleText {
    color: #ffffff;
    margin: 0px 0 15px;
    display: block;
}

.captionBtnBox {
    position: absolute;
    right: 0;
    width: 100%;
    max-width: 1920px;
    height: 100%;
    top: 0;
    left: 0;
    margin: 0 auto;
}

.captionBtnBox .captionBtnFrame ul {
    margin: 0px;
}

.captionBtnFrame {
    background-color: rgb(12 33 52 / 65%);
    position: absolute;
    right: 0;
    width: 100%;
    max-width: 423px;
    height: 100%;
    padding: 42px 34px;
    top: 0;
    z-index: 1;
}

.captionBtnBox ul li {
    width: 100%;
    overflow: hidden;
    position: relative;
    margin-bottom: 30px;
}

.captionBtnBox ul li:last-child {
    margin-bottom: 0px;
}

.captionBtnBox ul li a {
    padding: 15px 22px 15px 22px;
    display: flex;
    align-items: center;
    width: 100%;
    background: rgba(255, 255, 255, 0.0);
    border: 1px solid #ffffff;
    min-height: 98px;
}

.captionBtnBox ul li a:hover {
    background: rgba(255, 255, 255, 1.0);
}

.captionBtnBox ul li a .iconBox {
    width: 50px;
    float: left;
    margin: 0px 0px;
    text-align: center;
}

.captionBtnBox ul li a .iconBox img {
    margin: 0 auto;
    padding-top: 2px;
    filter: contrast(0)brightness(100);
    width: 43px;
    height: 43px;
    object-fit: contain;
}

.captionBtnBox ul li a .iconBox svg path {
    fill: #ffffff;
}

.captionBtnBox ul li a .iconBox img.default {
    display: block;
}

.captionBtnBox ul li a .textBox {
    position: absolute;
    left: 100px;
    top: 50%;
    transform: translateY(-50%);
    max-width: 200px;
    overflow: hidden;
}

.captionBtnBox ul li a .textBox h2 {
    margin: 0;
    padding: 0;
    color: #fff;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.3;
}

.captionBtnBox ul li a .arrow {
    float: right;
    padding: 19px 0;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffffff;
    font-size: 20px;
}

.captionBtnBox ul li a:hover .textBox h2,
.captionBtnBox ul li a:hover .arrow {
    color: #121412;
}

.captionBtnBox ul li a:hover .iconBox svg path {
    fill: #121412;
    
}

.captionBtnBox ul li a:hover .iconBox img {
    filter: none;
}

.captionBtnBox.captionBtnBox-mb {
    display: none;
}

/* Find a Layer  */
.find-layer-sec {
    background-color: #f1f3f7;
    padding: 0;
    margin-top: 80px;
}
.find-layer-sec .img-holder {
    margin-top: -80px;
    position: relative;
    z-index: 1;
}
.find-layer-sec .flex-row {
    align-items: end;
}

.slider .captionFrame li:nth-child(1), .slider .captionFrame li:nth-child(2), .slider .captionFrame li:nth-child(3) {
    transform: translateY(50px);
    opacity: 0;
    transition: all 0s ease-out;
}

.slider .active .captionFrame li:nth-child(1), 
.slider .active .captionFrame li:nth-child(2), 
.slider .active .captionFrame li:nth-child(3) {
    transform: translateY(0px);
    opacity: 1;
    transition: all 0.4s ease-out;
}

.slider .active .captionFrame li:nth-child(1) {
    transition-delay: 0.2s;
}
.slider .active .captionFrame li:nth-child(2) {
    transition-delay: 0.4s;
}
.slider .active .captionFrame li:nth-child(3) {
    transition-delay: 0.6s;
}

.owl-item.animated.owl-animated-in.fadeIn {}

/* End Find a Layer  */


/*-------FriendsLogoBox Css----------*/
.section-HeaderText {
    color: #08173A;
    font-size: 34px;
    font-weight: 700;
    margin-bottom: 60px;
}

.BlackLine,
.WhiteLine,
.GreenLine {
    position: relative;
}

.BlackLine:before,
.WhiteLine:before {
    content: '';
    position: absolute;
    bottom: -15px;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 200px;
    height: 3px;
    background: #0C1F4F;
}

.WhiteLine:before {
    background: #ffffff;
}

.friendsSliderBox .HeaderText {
    background: #fff;
    padding: 8px 14px;
    display: inline-block;
    font-size: 14px;
    text-transform: uppercase;
    color: #6C6C6C;
    margin-bottom: 40px;
}

.friendsSliderBox .owl-carousel .owl-item img {
    width: auto;
}

.friendsSliderBox .owl-carousel ul li {
    display: inline-block;
    vertical-align: middle;
    width: 30%;
    text-align: center;
    padding: 20px 20px;
}

.friendsSliderBox {
    margin-bottom: 40px;
    position: relative;
}

.friendsSliderBox .owl-carousel ul li a {
    display: inline-block;
}

.owl-carousel .owl-nav button.owl-prev span,
.owl-carousel .owl-nav button.owl-next span {
    color: #ffffff;
    font-size: 40px;
    padding: 0;
    width: 40px;
    display: inline-block;
    line-height: 1;
    height: 40px;
    margin-top: -3px;
}

.owl-carousel .owl-nav button.owl-prev {
    position: absolute;
    top: 50%;
    margin: -12px 0 0 0;
    left: 0;
    background: rgb(1 59 85 / 15%);
    color: #ffffff;
    height: 36px;
    display: inline-block;
    width: 36px;
    border-radius: 0px;
}

.owl-carousel .owl-nav button.owl-next {
    right: 0;
    position: absolute;
    top: 50%;
    margin: -12px 0 0 0;
    background: rgb(1 59 85 / 15%);
    color: #ffffff;
    height: 36px;
    display: inline-block;
    width: 36px;
    border-radius: 0px;
}
.owl-carousel .owl-nav button.owl-next:hover,
.owl-carousel .owl-nav button.owl-prev:hover {
    background-color: #ba892c;
}

.friendsSliderBox.friendsSliderBox-mobile {
    display: none;
}

.friendsLogoBox .tab-content .tab-pane {
    display: block;
}

.friendsLogoBox .tab-content {
    position: relative;
}

.friendsLogoBox .tab-content .tab-pane:not(.active) {
    opacity: 0;
    position: absolute;
    z-index: -999;
    width: 100%;
    height: 100%;
    overflow: hidden;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

/*--------Become A Member---------***/
.member-boxleft {
    display: inline-block;
    vertical-align: middle;
    width: 67%;
}

.member-boxright {
    display: inline-block;
    vertical-align: top;
    padding-left: 18px;
    margin-top: 70px;
}

.member-right {
    border-left: 1px solid #0C1F4F;
    padding-left: 50px;
}

.member-boxleft h3,
.member-boxright h3 {
    margin: 0 0 15px 0;
}

/**--------Footer---------***/
.footer-info p {
    font-size: 14px;
    line-height: 25px;
    margin: 25px 0;
    color: #ffffff;
}

ul.follow-us li {
    display: inline-block;
    vertical-align: middle;
    font-size: 19px;
    font-weight: 500;

    text-transform: uppercase;
    margin: 0 4px;
}

ul.follow-us li:first-child {
    margin-right: 20px;
}

ul.follow-us li a {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 2px solid transparent;
    line-height: 40px;
    color: #0BBA97;
    font-size: 20px;
    text-align: center;
}

ul.follow-us li a:hover,
ul.follow-us li a:focus {
    border-color: #BA0C2F;
    color: #BA0C2F;
}

.contact-links ul li {
    margin-bottom: 18px;
    color: #ffffff;
    position: relative;
    padding-left: 30px;
}

.contact-links ul li span {
    display: inline-block;
    vertical-align: top;
    width: calc(100% - 50px);
}

.contact-links ul li>i {
    position: absolute;
    left: 0;
    top: 4px;
    /* height: 25px; */
    /* width: 25px; */
    object-fit: contain;
    color: #7e9ba9;
}

.copyright-txt p,
.copyright-txt p a {
    color: #ffffff;
    font-size: 14px;
}

.copyright-txt p {
    margin: 0;
    letter-spacing: 0.025em;
    color: #ffffff;
}

img.footlogo {
    width: 100%;
    max-width: 175px;
}

.col1.footer-info {
    position: relative;
}

.footer-links h3,
.contact-links h3 {
    font-size: 16px;
    font-weight: 700;
    color: #ffffff;
    margin: 0 0 20px 0;
    line-height: 1.3;
    text-transform: uppercase;
    font-family: 'Carbona Test';
}

.footer-links ul li {
    position: relative;
    padding-left: 0;
    margin-bottom: 5px;
}

.footer-links ul li a,
.contact-links ul li a,
.contact-links ul li span {
    font-size: 14px;
    color: #FFFFFF;
    line-height: 1.3;
    background: transparent;
    text-shadow: none;
}

.footer-links ul li a:hover,
.footer-links ul li a:focus,
.contact-links ul li a:hover,
.contact-links ul li a:focus {
    text-decoration: none;
}

.zoneRWrap > ul > li > a i {
    display: inline-block;
    margin-right: 8px;
    font-weight: 900;
    font-size: 12px;
    color: #cca876;
    margin-top: 3px;
}

.d-flex-wrap {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.footer {
    background: #013b55;
    padding: 70px 0 0;
    overflow: hidden;
    border-top: 10px solid #b9892b;
}

.row.d-flex-wrap:before,
.row.d-flex-wrap:after {
    display: none;
}

.row.d-flex-wrap {
    margin-left: -15px;
    margin-right: -15px;
}

.footer .row.d-flex-wrap>div {
    padding-left: 15px;
    padding-right: 15px;
}

.footer .row.d-flex-wrap>div.col1 {
    -webkit-flex: 0 0 18%;
    flex: 0 0 18%;
    max-width: 18%;
    position: relative;
    z-index: 1;
    padding-right: 15px;
}

.footer .row.d-flex-wrap>div.col2 {
    -webkit-flex: 0 0 44%;
    flex: 0 0 44%;
    max-width: 44%;
    padding-left: 15px;
    padding-right: 15px;
}
.footer .row.d-flex-wrap>div.col3 {
    -webkit-flex: 0 0 18%;
    flex: 0 0 18%;
    max-width: 18%;
    padding-left: 15px;
}

.footer .row.d-flex-wrap>div.col4 {
    -webkit-flex: 0 0 75%;
    flex: 0 0 75%;
    max-width: 75%;
    /* margin-top: 30px; */
}

.footer .row.d-flex-wrap>div.col5 {
    -webkit-flex: 0 0 auto;
    flex: 0 0 auto;
    /* margin-top: 30px; */
}

.footer .row.d-flex-wrap>div.footer-info h3 {
    font-size: 25px;
    color: #DDDDDD;
    margin: 0 0 15px;
}

.footer .footer-links,
.footer .footer-info {}

.footer-links ul.social-list {
    display: flex;
    flex-wrap: wrap;
}

.footer-links ul.social-list li {
    margin-right: 20px;
}

.footer-links ul.social-list a {
    font-size: 20px;
    color: #ffffff;
}

.copyright-block {
    background: #1c2025;
    padding: 15px 0;
}

.footer-links ul li p {
    text-transform: uppercase;
    color: #ffffff;
    font-weight: 600;
    line-height: 1.3;
    font-size: 15px;
}

.footer .row.d-flex-wrap>div.col1 .MAJButton {
    margin-top: 25px;
}

.footer .row.d-flex-wrap>div.col1 .MAJButton {
    min-width: 164px;
    font-size: 14px;
    color: #ffffff;
}

.mt-20 {
    margin-top: 20px;
}
.mt-30 {
    margin-top: 30px;
}

.px_5 {
    padding-left: 8px;
    padding-right: 8px;
}
/* Member Highlight Sec */
.member-highlight-sec {
    padding: 100px 0 70px;
}
.member-card {
    position: relative;
    z-index: 1;
    margin-bottom: 70px;
}
.member-card .img-holder {
    position: relative;
    z-index: 1;
}
.member-card .img-holder img {
    width: 100%;
}
.member-card .hover-content {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.60);
    top: 0;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all 0.3s ease;
}
.member-card:hover .hover-content {
    opacity: 1;
}
.member-card .hover-content  .hc-inner-wrap a {
    color: #ffffff;
}
.member-card .hover-content  .hc-inner-wrap a:hover {
    text-decoration: none;
    color: #cba775;
}
.member-card .hover-content  .hc-inner-wrap a i {
    color: #cba775;
}
.member-card .member-info {
    padding: 0 20px;
    position: absolute;
    width: 100%;
    bottom: -50px;
    z-index: 1;
    left: 0;
}
.member-card .member-info .mi-wrapper {
    padding: 20px 20px;
    background: #cba775;
    text-align: center;
}
.member-card .member-info .mi-wrapper h2 {
    color: #ffffff;
    font-family: 'Adobe Garamond Pro';
    font-size: 20px;
    margin: 0 0 5px;
}

.member-card .member-info .mi-wrapper p {
    color: #ffffff;
    font-size: 14px;
    font-family: 'Carbona Test';
    margin: 0;
}
.member-list-slider .member-card {
    margin-bottom: 55px;
}

/* Member Highlight Sec */

.testimonial-block {

}
.testimonial-slider {
    padding: 0 40px;
}
.testimonial-slider .owl-stage {
    padding-top: 20px;
}
.testimonial-block .tb-inner-wrap {
    border: 1px solid #e5e7e9;
    padding: 35px 35px 20px;
    position: relative;
    z-index: 1;
}
.testimonial-slider.owl-carousel .owl-nav button.owl-prev {
    left: -80px;
}

.testimonial-slider.owl-carousel .owl-nav button.owl-next {
    right: -80px;
}
.about-sec .inner-sec-wrap .container {
    width: 100%;
    max-width: 1030px;
}
.testimonial-block .tb-inner-wrap h3 {
    font-size: 16px;
    font-weight: 600;
    font-family: 'Lato', sans-serif;
    color: #000000;
}
.testimonial-block .tb-inner-wrap p {
    margin: 0;
    color: #000000;
    font-size: 14px;
}
.testimonial-block .tb-inner-wrap:before {
    width: 38px;
    height: 27px;
    content: "";
    position: absolute;
    top: -10px;
    left: 35px;
    background-image: url('./../images/testimonial-icon.png');
    background-size: contain;
    background-repeat: no-repeat;
}
.testimonial-block .tb-from {
    padding-left: 35px;
    margin-top: 30px;
    position: relative;
    z-index: 1;
}

.testimonial-block .tb-from h4 {
    font-size: 17px;
    font-weight: 700;
    font-family: 'Carbona Test';
    margin: 0;
    color: #46505d;
}

.testimonial-block .tb-from p {
    margin: 0;
    font-size: 14px;
    color: #ba892c;
}
.testimonial-block .tb-from:before {
    content: "";
    width: 20px;
    height: 20px;
    border: 1px solid #e5e7e9;
    display: inline-block;
    border-style: none none solid solid;
    background: #ffffff;
    position: absolute;
    left: 35px;
    top: -41px;
    transform: rotate(-45deg);
}
/*--- Banner Inner --**/
.bannerInner {
    position: relative;
    background: rgba(1, 59, 85, 0.8);
    min-height: 220px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
}
.bannerInner .TitleText {
    color: #ffffff;
}
.bannerInner .fixed-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
}
.fixed-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.3;
    z-index: -1;
    object-fit: cover;
}
.bannerInner .banner-content {
    position: relative;
    z-index: 1;
    width: 100%;
}
/********************/
.quicklink-mobile {
    display: none;
}

.events {
    margin-top: 40px;
    display: block;
}
.events .side-title-center, .siedebar-blog .side-title-center {
    background: #023b56;
}

.events .eventbox-list,
.siedebar-blog .eventbox-list {
    padding: 25px 25px;
    background: #f2f3f8;
}

.event-box {
    color: #ffffff;
}

.event-box p {
    font-size: 20px;
    line-height: 1.5;
    font-weight: 700;
    color: #023b56;
    margin: 0;
}

.event-box span.e-date,
.event-box span.e-cle {
    text-align: center;
    font-size: 16px;
    color: #023b56;
    font-family: 'Carbona Test';
    font-weight: 400;
    line-height: 1.2;
}

.event-box h3 {
    color: #ffffff;
    margin: 0 0;
}

.event-box:not(:last-child) {
    border-bottom: 1px solid #aabbc7;
    padding-bottom: 15px;
    margin-bottom: 15px;
    padding-right: 0px;
    padding-left: 0px;
}
.member-highlights .side-title-center {
    background: #5f0d01;
}
.member-list-slider {
    padding: 30px 35px;
    background-color: #93140d;
}



.sponsors-box {
    background: #CFD2D9;
    text-align: center;
    padding: 23px 20px;
}

.sponsors-box span {
    background: #fff;
    padding: 4px 12px;
    color: #6C6C6C;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    position: relative;
    margin-top: 28px;
    display: inline-block;
}

.sponsors-box span small {
    font-size: 24px;
    color: #000;
    font-weight: 500;
    position: absolute;
    bottom: -22px;
    left: 0;
    right: 0;
    margin: 0 auto;
}

.sponsors-boxtwo {
    display: block;
    text-align: center;
    margin: 50px 0 30px 0;
    padding: 0 25px;
}

.sponsors-boxthree {
    background: #E9E9E9;
    display: block;
    text-align: center;
    padding: 38px 20px;
    margin: 0 25px;
}

.sponsors-link ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}

.sponsors-link ul a {
    display: inline-block;
    padding: 15px 40px;
    background: #f6f7fb;
    border: 1px solid #ddddde;
    margin-left: -1px;
    font-size: 20px;
    font-weight: 900;
    color: #41464b;
    text-decoration: none;
    position: relative;
}

.sponsors-link ul a:before {
    contain: "";
    width: 10px;
    height: 10px;
    background-color: #eca94f;
    -webkit-transform: translate(-50%, -50%)rotate(45deg);
    transform: translate(-50%, -50%)rotate(45deg);
    position: absolute;
    top: 100%;
    left: 50%;
    content: "";
    opacity: 0;
}

.sponsors-link ul li.active a {
    background: #eca94f;
    border-color: #eca94f;
}

.sponsors-link ul li.active a:before {
    opacity: 1;
}

.sponsors-link {
    margin-bottom: 40px;
}

.events .friendsLogoBox {
    display: none;
}

.inner-content-area>p {
    margin: 15px 0 20px 0;
}

.Highlight p {
    margin-bottom: 40px;
    /* color: #ffffff; */
}
.BulletList ul li {
    line-height: 1.6;
    position: relative;
    padding-left: 30px;
    list-style: none;
    color: #0c2c48;
}

.BulletList ul li i {
    font-weight: 900;
    position: absolute;
    left: 0;
    top: 4px;
    font-size: 10px;
    width: 20px;
    background: #ba892c;
    text-align: center;
    line-height: 20px;
    color: #ffffff;
    border-radius: 50%;
}
.BulletList ul ol {
    margin-left: 0;
    padding-left: 0;
    list-style: none;
}
.BulletList ul li:not(:last-child) {
    margin-bottom: 15px;
}

/* 
.BulletList ul {
    margin-bottom: 30px;
    margin-left: 0;
    list-style: none;
}

.BulletList ul li {
    padding-left: 20px;
    margin-bottom: 15px;
    font-size: 16px;
    position: relative;
    line-height: 1.6;
}

.BulletList ul li::before {
    content: '';
    position: absolute;
    top: 5px;
    left: 0;
    width: 6px;
    height: 6px;
    border: 1px solid #CC9933;
    border-style: none solid solid none;
    transform: rotate(-45deg);
}

.BulletList ul ul {
    margin-top: 12px;
    margin-bottom: 5px;
} */

/*---Main Content Div----***/

/* sbm Clases */

.img-card .img-holder {
    position: relative;
}
.img-card .img-holder img {
    width: 100%;
}

.img-card .img-holder span {
    font-size: 35px;
    color: #ffffff;
    display: inline-block;
    background: #eca94f;
    padding: 5px;
    position: absolute;
    width: 50px;
    bottom: 0;
    right: 0;
    line-height: 1;
    text-align: center;
    height: 50px;
}

.img-card h2 {
    font-size: 18px;
    line-height: 1.4;
    margin: 5px 0 5px;
    color: #0B0E2C;
    text-decoration: none;
    text-transform: uppercase;
    min-height: 52px;
    margin-bottom: 10px;
    font-weight: 700;
}

.img-card:hover h2 {
    color: #9F8F6A;
}

.img-card:hover {
    border-color: #000000;
}

.reg-here-link {
    font-size: 12px;
    font-weight: 700;
    color: #121412;
    text-transform: uppercase;
    position: relative;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFiSURBVHgBnVNBTgJBEOzuWRKP+4T9gYsKiTf4gb5AORnCQXgCL9ALIZ7AF8gPxJMJiswT9gfu0QjT7cxGCNmdXYx12GTSXdU1tT0IHqwmcbj5gpgAIndmgKTR1XNfL+aJvKFbYezbY5jrTRFh9m3M8Lyrk4LAYhTHFKgn+J1agcQOuWz23vRO4HUcRzVSK8/UMqRrNnXnhNzJkp/zZBSYusYSgbCm1CTrW45ProVw4puguBZSwAXxLWxWbRKFV94J1pWhdWrv2y51gtInEIjBj+iQCCEcE1QHF7m7usTFyDBfFFvH94fTz3IRSdbMbvo26CjXkJL9kfqfZGdBk7X24qMfJFuwyCMFR3wPxYDSQOiiiuwcNrsfU6p3tCN3ctUQFd5B5VrjwH2zTTy7Wc7sUgzhj2CmjuNkMvuF7E2gXVGElpcpMGdjBs2e3gWPvr7FqBEjmhYSRhmPJRFR8+0L3McPboCcJBIS7s8AAAAASUVORK5CYII=');
    background-position: left center;
    background-size: contain;
    background-repeat: no-repeat;
    padding: 1px 0 1px 22px;
}

.mt-40 {
    margin-top: 40px !important;
}

.newscard {
    display: block;
    position: relative;
    min-height: 225px;
    background: #ffffff;
    border: 1px solid #BCBCBC;
    padding-left: 95px;
}

.newscard .newstag {
    position: absolute;
    top: 0;
    left: 0;
    width: 95px;
    height: 100%;
    background-color: #121412;
    color: #ffffff;
}

.newscard .newstag>span {
    -webkit-transform: rotate(-90deg) translate(-50%, -50%);
    transform: rotate(-90deg) translate(-50%, -50%);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    text-align: center;
    width: auto;
    font-weight: 700;
}

.newscard .news-inner-wrap {
    position: relative;
    left: 0;
    padding: 30px 30px 30px 190px;
    min-height: 200px;
}

.newscard .news-inner-wrap img {
    position: absolute;
    left: 70px;
    top: 65px;
    z-index: 1;
}

.newscard .news-inner-wrap h2 {
    font-size: 30px;
    font-weight: 700;
    font-family: 'Raleway';
    text-transform: uppercase;
}

.newscard:not(:first-child) {
    margin-top: 70px;
}

.gray-bg2 {
    background: #F7F5F4;
}

.newscard .news-inner-wrap p {
    color: #33383A;
}

.searchnav-logo {
    padding: 24px 40px 14px 40px;
}

.nav-member-center p {
    color: #ffffff;
    margin: 10px 0 0;
}

.nav-member-center img {
    width: 35px;
}

.breadcrumd-list ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0px;
    justify-content: center;
    margin-top: 20px;
}

.breadcrumd-list ul li {
    list-style: none;
    color: #CC9933;
}

.breadcrumd-list ul li a {
    padding: 10px 0;
    font-size: 14px;
}

.breadcrumd-list ul li a {
    color: #FFFFFF;
}

.breadcrumd-list ul li:not(:last-child):after {
    display: inline-block;
    content: '|';
    margin: 0 15px;
    color: #ffffff;
}
.inner-page-content {
    position: relative;
    padding-bottom: 70px;
}
.inner-page-content p {
}
.inner-page-content>div>.row-fluid {
    display: flex;
    flex-direction: row-reverse;
    flex-wrap: wrap;
}
.inner-page-content>div>.row-fluid:before, .inner-page-content>div>.row-fluid:after {
    display: none;
}
.inner-page-content .inner-content-area {
    padding: 60px 0px 0px 30px;
    width: calc(100% - 375px);
    max-width: 100%;
}
.inner-page-content .sidebar {
    width: 375px;
    padding: 40px 25px 0 0px;
    position: relative;
    left: 0;
    top: 0;
    margin: 0;
    bottom: 0;
}
.side-title-center .ColumnHeader {
    color: #ffffff;
    margin-top: 10px;
    margin-bottom: 0;
}
.quicklink-mobile {
    display: none;
}
.event-list ul,.DiamondBullets ul {
    margin: 0px;padding: 0;list-style: none;
}


.ColumnHeader {
    text-transform: uppercase;
    color: #46505d;
    font-size: 30px;
}

.sidebar .eventbox-row {
    flex-direction: column;
}

.sidebar .eventbox-col {
    width: 100%;
    margin: 0 0 30px 0;
}

.events, .member-highlights {
    margin-top: 30px;
}

/*Left content*/
.content-info p {
    margin: 20px 0;
}
/* 
.Highlight {
    background: rgba(11, 14, 44, 0.90);
    padding: 45px 8%;
    flex-wrap: wrap;
    margin: 50px 0;
    position: relative;
}

.Highlight h3 {
    margin: 0 0 10px 0;
}

.Highlight:before,
.Highlight:after {
    position: absolute;
    width: 92%;
    height: 80%;
    left: 4%;
    border: 2px solid #ba892c;
    content: "";
}

.Highlight:before {
    top: -50px;
    border-bottom: none;
}

.Highlight:after {
    top: auto;
    border-top: none;
    bottom: -50px;
} */

.membership-headlinebox h5 {
    text-decoration-line: underline;
    color: #BA0C2F;
    font-size: 22px;
    font-weight: 600;
}

.membership-headlinebox p {
    margin: 20px 0;
}

.forgot-mb {
    display: none;
}

.primary-btnmb {
    display: none;
}

.headtitle {
    display: none;
}

/*-------eventbox Css----------*/
.eventbox {
    padding: 40px 0;
    background: #DBD5CD;
}

.eventbox-row {
    display: flex;
}

.eventbox-col {
    margin-right: 30px;
    width: 33.33%;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.25);
}

.eventbox-col:last-child {
    margin-right: 0px;
}

.eventbox-img {
    height: 180px;
    overflow: hidden;
    position: relative;
}

.eventbox-img span img {
    width: auto;
    height: 100%;
    max-width: inherit;
}

.event-head {
    position: absolute;
    z-index: 9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    top: 0;
    left: 0;
    height: 100%;
}

.event-head img {
    width: auto;
}

.event-head h4 {
    font-size: 34px;
    font-weight: 400;
    color: #ffffff;

    text-transform: uppercase;
}

.event-head h4 b {
    font-weight: 900;
}

.event-head h4::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 3px;
    background: #fff;
    bottom: -20px;
    left: 0;
    right: 0;
    margin: 0 auto;
}

.eventbox-info {
    padding: 15px;
}

.eventbox-item {
    text-align: center;
    margin: 10px 0 0 0;
    border-bottom: 1px solid #fff;
}

.eventbox-item:hover .eventbox-item-in {
    background: #fff;
}

.eventbox-item-in {
    padding: 15px 10px;
    margin-bottom: 10px;
}

.eventbox-item ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    opacity: 0.8;
}

.eventbox-item ul li {
    font-size: 14px;
    color: #FFFFFF;
    position: relative;
    padding: 0 20px;
    text-transform: uppercase;
}

.eventbox-item ul li i {
    margin-right: 5px;
}

.eventbox-item ul li:before {
    content: '|';
    position: absolute;
    height: 25px;
    right: 0;
    top: 0;
}

.eventbox-item ul li:last-child:before {
    display: none;
}

.eventbox-item ul li img {
    margin-right: 5px;
}

.eventbox-item ul li img.hover-img {
    display: none;
}

.eventbox-item:hover ul li img.active-img {
    display: none;
}

.eventbox-item:hover ul li img.hover-img {
    display: inline-block;
}

.eventbox-item p {
    color: #fff;
    font-size: 20px;
    margin: 10px 0 0 0;
}

.eventbox-item:hover ul li {
    color: #1B365D;
}

.eventbox-item:hover p {
    color: #1B365D;
}

.eventbox-item.eventbox-item-link {
    border: 0;
    margin: 15px 0;
}

.event-link {
    color: #fff;
    margin: 0 15px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: bold;
}

.event-link:hover,
.event-link:focus {
    color: #fff;
    text-decoration: underline;
}

.mb-20 {
    margin-bottom: 20px;
}
.mb-15 {
    margin-bottom: 15px;
}

.side-title-center {
    text-align: center;
    margin-bottom: 0;
    padding: 30px 15px;
    border-bottom: 1px solid rgba(87, 96, 102, 0.30);
    background: #ba892c;
}

.owl-theme .owl-dots .owl-dot span {
    background-color: #ffffff;
    width: 12px;
    height: 12px;
}

.info-iconbox h2>a {
    color: inherit;
    text-decoration: none;
}

.info-iconbox:hover span img {
    -webkit-filter: brightness(100);
    filter: brightness(100);
}

.d-inline-block {
    display: inline-block;
}

blockquote,
blockquote.pull-right {
    padding: 80px 90px;
    font-weight: 400;
    font-size: 24px;
    color: #023b56;
    border-style: solid;
    position: relative;
    font-family: 'Carbona Test';
    border-color: #bdcdd3;
    border-width: 2px;
    margin-top: 40px;
    margin-bottom: 40px;
    display: block;
}
blockquote:before,
blockquote:after {
    content: "";
    width: 100px;
    height: 84px;
    background-color: #ffffff;
    background-image: url('./../images/blockquote-sign.png');
    background-size: 74px 71px;
    background-repeat: no-repeat;
    display: inline-block;
    background-position: center;
    position: absolute;
}

blockquote:before {
    left: 50px;
    top: -42px;
}
blockquote:after {
    right: 50px;
    bottom: -42px;
    transform: scale(-1);
}
blockquote p {
    outline: none;
    font-size: inherit;
    color: inherit;
    font-family: inherit;
}
blockquote .QuoteAuthor {
    margin-top: 20px;
}
blockquote.pull-right:before {
    left: auto;
    right: 50px;
}

blockquote.pull-right:after {
    right: auto;
    left: 50px;
}

blockquote.pull-right:before,
blockquote.pull-right:after {
    margin: auto;
    -webkit-transform: scaleX(-1);
    transform: scaleX(-1);
}

blockquote.pull-right {
    text-align: right;
}

.fs22 {
    font-size: 22px;
}

.BulletList-row {
    display: flex;
    flex-wrap: wrap;
}

.BulletList-row .BulletList {
    -webkit-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}

.mr-10 {
    margin-right: 10px;
}

.Highlight .btns-wrap .MAJButton {
    margin-right: 30px;
}

.my-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}

.textLine-sec p {
    text-align: center;
    color: #33383A;
}

.img-card {
    background: #ffffff;
    border: 1px solid #e5e7e9;
    height: 100%;
}

.img-card .img-card-content {
    padding: 20px;
}
.latest-blogs-sec .btn-wrap {
    margin-top: 30px;
    text-align: center;
}
.upcoming-slider.owl-carousel .owl-nav button.owl-prev {
    left: -80px;
}

.upcoming-slider.owl-carousel .owl-nav button.owl-next {
    right: -80px;
}
.img-card .img-card-content p {
    font-size: 14px;
    line-height: 1.5;
    color: #000000;
    font-weight: 500;
}
.img-card .img-card-content .datebx {
    font-family: "Lato", sans-serif;
    font-weight: 400;
    font-size: 11px;
    text-transform: uppercase;
    color: #cca876;
}

.img-card .img-card-content a:hover {
    text-decoration: none;
}

.img-card .img-card-content span.datebx {
    color: #ba892c;
    font-size: 16px;
}
.img-card .img-card-content .datebx span:first-child {
    color: #aaaaaa;
}

.img-card .img-card-content .datebx span.px_5 {
    color: #aaaaaa;
}

.anouncebanner {
    display: none;
    background: #17432F;
    padding: 50px 20px;
}

.anouncebanner p {
    color: #ffffff;
    text-align: center;
    margin-bottom: 25px;
    font-size: 22px;
    font-weight: 400;
}

.anouncebanner .button-wrap {
    display: flex;
    justify-content: space-between;
}

.anouncebanner .button-wrap a {
    display: inline-block;
    padding: 10px 10px;
    width: 48%;
    color: #ffffff;
    border: 1px solid #ffffff;
    border-radius: 50px;
    text-align: center;
}

.latest-blogs-sec .flex-row>.span3 {
    margin: 0;
    width: 25%;
    padding: 0 15px;
    margin-bottom: 30px;
}
.latest-blogs-sec .flex-row>.span12 {
    margin: 0 !important;
    width: 100%;
    padding: 0 15px;
}
.latest-blogs-sec .row>.span-12 {
    padding-left: 15px;
    width: 100%;
    margin-left: 0px;
}

.footer img.bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    left: 0;
    top: 0;
}

.footer .for-mobile {
    position: relative;
    z-index: 2;
    padding: 30px 15px 20px;
    margin-top: 55px;
}

.footer .for-mobile h2 {
    color: #ffffff;
    font-size: 22px;
    margin: 0 0 15px;
    font-weight: 400;
    line-height: 1.3;
}

.footer .for-mobile .row-flex {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.footer .for-mobile .row-flex .col12 h2 {
    text-align: center;
}

.footer .for-mobile .row-flex .col6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}

.footer .for-mobile .row-flex .col12 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 30px;
}

.footer .for-mobile .row-flex .sbmrow {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    padding: 0 15px;
}

.footer .for-mobile .row-flex .sbmrow ul {
    flex: 0 0 calc(50% - 15px);
    max-width: calc(50% - 15px);
}

.footer .for-mobile ul li,
.footer .for-mobile ul li a {
    color: #ffffff;
    font-size: 15px;
    font-weight: 700;
}

.footer .for-mobile ul li {
    border: 1px solid rgb(255 255 255 / 30%);
    border-style: solid none;
    margin: -1px 0 0;
    padding: 4px 0;
    display: flex;
    align-items: center;
    min-height: 40px;
}

.footer .for-mobile ul li a:hover {
    color: #E1C783;
    text-decoration: none;
}

.footer .for-mobile ul li:before {
    content: "\f101";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin-right: 10px;
    color: #A2D5ED;
}

.footer .for-mobile .copyright a {
    color: #ffffff;
    text-decoration: underline;
}

.footer .for-mobile .copyright p {
    font-weight: 900;
    padding: 0 015px;
}

.footer .for-mobile .copyright p>span {
    margin: 0 5px;
}

.footsocial-list {
    background: #0B2239;
    padding: 8px 15px;
    width: 100%;
    position: absolute;
    top: -55px;
    z-index: 1;
}

.footsocial-list ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
}

.footsocial-list ul li:first-child {
    color: #ffffff;
    flex: 1 1 auto;
    font-size: 13px;
    font-weight: 900;
    font-family: 'Raleway';
}

.footer .for-mobile .footsocial-list>ul li a {
    display: inline-block;
    font-size: 18px;
    color: #ffffff;
    text-decoration: none;
    text-align: center;
}

.footer .footer-links p, .footer .footer-links p>a {
    color: #7e9ba9;
    font-size: 14px;
    font-family: 'Carbona Test';
}

.footer p>span {
    color: #666c84;
}

.footer .for-mobile .footsocial-list>ul li:before {
    display: none;
}

.footer .for-mobile .footsocial-list li {
    border-style: none;
    padding: 0;
    text-transform: uppercase;
}

.footer .for-mobile .footsocial-list li:not(:first-child) {
    margin-left: 25px;
}

.footer .for-mobile .row-flex .col6:first-child ul>li:before {
    display: none;
}

.footer .for-mobile ul li:hover:before {
    color: #E1C783;
}

.sponsors-img-list ul li {
    min-width: auto;
}

.whats-new-sec .flex-row {
    margin-left: -15px;
    margin-right: -15px;
}

.whats-new-sec .flex-row>div {
    margin: 0;
    padding: 0 15px;
}

.whats-new-sec .flex-row>div.sbm8 {
    padding-left: 60px;
}

.whats-new-sec .flex-row>div.span4 {
    flex: 0 0 35%;
    max-width: 35%;
}

.whats-new-sec .flex-row>div.span8 {
    flex: 0 0 65%;
    max-width: 65%;
}

.foot-logo:after {
    width: 126px;
}

.loggedinBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    margin: 0 auto;
    max-width: 250px;
    padding: 5px 0;
}


textarea,
input[type="text"],
input[type="password"],
input[type="datetime"],
input[type="datetime-local"],
input[type="date"],
input[type="month"],
input[type="time"],
input[type="week"],
input[type="number"],
input[type="email"],
input[type="url"],
input[type="search"],
input[type="tel"],
input[type="color"],
.uneditable-input {
    min-height: 30px;
    font-family: "Lato", sans-serif;
}

.footer .for-mobile ul, .captionFrame ul, .friendsSliderBox .item ul, .footer .footer-links ul, .footer .footer-info ul, .contact-links ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.footer-links ul li a {
    display: flex;
    padding: 5px 0;
}
.event-mobile,
.news-mobile {
    display: none;
}

.d-none {
    display: none !important;
}

ul.social-list {
    display: flex;
    list-style: none;
    margin: 0;
    /* justify-content: end; */
}

ul.social-list li a {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #366378;
    border-radius: 50%;
    color: #ffffff;
    text-decoration: none;
}
ul.social-list li a:hover, .mobile-contactus .dropdown .dropdown-menu ul.social-list li a:hover {
    background: #ba892c;
}

ul.social-list li {
    margin-right: 10px;
    color: #ffffff;
    font-size: 14px;
    align-self: center;
}

.foot-logo-wrap {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 10px;
}

.my-30 {
    margin-top: 30px;
    margin-bottom: 30px;
}

.titlewrap h1,
.titlewrap h2,
.titlewrap h3,
.titlewrap h4,
.titlewrap h5,
.titlewrap h6 {

    font-weight: 400;
    color: #ba892c;
    text-transform: uppercase;
}

.titlewrap h1 b,
.titlewrap h2 b,
.titlewrap h3 b,
.titlewrap h4 b,
.titlewrap h5 b,
.titlewrap h6 b {
    font-weight: 900;
}

.row.row-flex {
    margin: 0 -15px;
}

.row.row-flex>.span4 {
    flex: 0 0 25%;
    -webkit-flex: 0 0 25%;
    max-width: 25%;
    width: 25%;
    padding: 0 15px;
}

.row.row-flex>.span8 {
    flex: 0 0 75%;
    -webkit-flex: 0 0 75%;
    max-width: 75%;
    width: 75%;
    margin-left: 0;
    padding: 0 15px;
}

.event-list .sbm-event .sbm-e-head span:first-child:after {
    display: inline-block;
    content: "|";
    color: #33383A;
    opacity: 0.3;
    position: absolute;
    right: 0;
    top: 0;
}

.event-list .sbm-event .sbm-e-head {
    display: flex;
    justify-content: space-between;
    color: #A8462B;
    font-weight: 400;
    text-align: center;
}

.event-list .sbm-event .sbm-e-head span {
    min-width: 45%;
    text-align: center;
    position: relative;
}


.DiamondBullets ul li {
    position: relative;
    padding-left: 0;
    margin-bottom: 20px;
    float: none;
}
.DiamondBullets ul li:last-child {
    margin-bottom: 0;
}
.quicklink-desktop .DiamondBullets {
    padding: 15px 20px;
    background: #f5ede0;
}

.DiamondBullets ul li a {
    font-size: 18px;
    color: #ba892c;
    display: block;
    padding: 18px 20px;
    border: 1px solid #ba892c;
    width: 100%;
}

.DiamondBullets > ul > li.dropdown > a:after {
    content: "\f105";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: auto;
    height: auto;
    background-position: 0 0;
    position: absolute;
    top: 33px;
    right: 15px;
    line-height: 1;
    transform: translateY(-50%);
    font-size: 20px;
    color: #ba892c;
}

.DiamondBullets ul li a:hover {
    text-decoration: underline;
}

.DiamondBullets ul li a:hover:before {
    background-position: 0 -18px;
}

.btn-flex {
    display: flex;
}

.droptitle {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    display: block;
    color: #3a3d3f;
}

.sbm-icon-card-wrap {
    display: block;
    margin: 0 -15px;
}

.sbm-icon-card-wrap>ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    list-style: none;
    justify-content: space-between;
    width: 100%;
    margin: 0;
}

.sbm-icon-card-wrap>ul>li {
    flex: 0 0 33.33%;
    max-width: 33.33%;
    -webkit-flex: 0 0 33.33%;
    padding: 0 15px;
}

.sbm-icon-card-wrap>ul>li a {
    text-decoration: none;
}

.sbm-iconbox {
    border: 1px solid #b9892b;
    padding: 40px 35px 30px;
    transition: all 0.3s ease;
    text-align: center;
    height: 100%;
    position: relative;
    background: #b9892b;
}
.sbm-iconbox p {
    font-size: 14px;
    color: #ffffff;
    font-family: 'Carbona Test';
    text-align: center;
}
/* 
.sbm-iconbox:before, .sbm-iconbox:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 100%;
    display: block;
    border: 1px solid #e5e7e9;
    top: 0;
    left: 0;
    pointer-events: none;
}

.sbm-iconbox:before {
    border-style: solid none none solid;
    width: 80%;
    height: 80%;
}

.sbm-iconbox:after {
    border-style: none solid solid none;
    width: 80%;
    height: 80%;
    left: auto;
    right: 0;
    top: auto;
    bottom: 0;
} */
.link {
    display: inline-block;
}

.sbm-iconbox:hover p {
    /* color: #ba892c; */
}

.sbm-iconbox img {
    height: 55px;
    width: 80px;
    object-fit: contain;
    margin-bottom: 15px;
    filter: brightness(1);
    transition: all 0.3s ease;
}

/* .sbm-iconbox:hover {
    border-color: #0B0E2C;
    box-shadow: inset 0 0 0px 1px #0b0e2c;
} */

.sbm-iconbox .HighlightTitle {margin-left: -20px;margin-right: -20px;color: #ffffff;}

.sbm-iconbox:hover img {
    /* filter: brightness(0); */
}

.flex-row {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    margin: 0 -15px;
}
.flex-row>div {
    margin-left: 0;
    margin-right: 0;
    padding-left: 15px;
    padding-right: 15px;
}

.about-sec {
    padding-top: 0;
    padding-bottom: 0;
}

.about-sec .inner-sec-wrap {
    position: relative;
    z-index: 1;
    margin: 70px 0;
    padding: 0 50px;
    background: #000000;
}

.about-sec .inner-sec-wrap .fixed-bg {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    z-index: -1;
    object-fit: cover;
    opacity: 0.5;
}
.about-sec .SectionHeader {
    color: #ffffff;
}

.about-tflf-box {
    background: #013b55;
    padding: 55px 55px;
    display: flex;
    flex-wrap: wrap;
    max-width: 1130px;
    margin: -70px auto;
    align-items: self-start;
    border-bottom: 18px solid #b9892b;
}

.about-tflf-box .left-box {
    flex: 0 0 55%;
}
.border-card-box {
    padding: 45px 50px 60px 70px;
    position: relative;
    z-index: 2;
    font-family: 'Carbona Test';
}
.border-card-box p {
    margin-bottom: 30px;
}
.border-card-box .MAJButton {
    color: #ffffff;
}
.about-tflf-box .right-box {
    flex: 0 0 45%;
    max-width: 45%;
    text-align: center;
    justify-content: center;
    padding-left: 45px;
    display: flex;
    align-items: center;
    flex-direction: column;
    text-align: right;
}

.about-tflf-box h2,
.about-tflf-box p {
    color: #ffffff;
}

.about-tflf-box p {
    font-size: 14px;
    margin-bottom: 45px;
}

.border-card-box:before,
.border-card-box:after {
    content: "";
    display: block;
    position: absolute;
    width: 60px;
    height: 60px;
    border: 1px solid #ffffff;
    /* left: 0; */
    z-index: -1;
}

.border-card-box:before {
    top: 0;
    border-style: solid none none solid;
    left: 0;
}

.border-card-box:after {
    bottom: 0;
    border-style: none solid solid none;
    right: 0;
}

.border-card-bottom {
    padding-left: 69px;
}
.border-card-bottom .clr-1 {
    color: #ba892c;
}

.img-slider {
    width: 100%;
}

.sponsorSlider .owl-carousel {
	text-align: center;
	position: relative;
	padding: 0 40px;
}
.sponsorSlider ul { margin: 0; padding: 0; list-style: none; }

.sponsorSlider .item {
	text-align: center;
}
.sponsorSlider .item {
	vertical-align: top;
	text-align: center;
	width: 100%;
	margin: 0;
	justify-content: space-around;
	display: flex;
	align-items: center;
}
.sponsorSlider  li {
	display: inline-block;
	vertical-align: top;
	padding: 0 20px;
	text-align: center;
}
.sponsorSlider li a>img {
    height: 55px;
    object-fit: contain;
}

.sponsorSlider li a {
    display: inline-block;
    margin: 0 auto;
    /* max-width: 80%; */
}
.sponsorSlider .owl-nav {
	margin-top: 0;
	line-height: 0;
}
.sponsorSlider .owl-stage {
	margin: 0 auto;
}
.sponsorSlider .owl-carousel .owl-nav button.owl-prev {
    left: -80px;
}

.sponsorSlider .owl-carousel .owl-nav button.owl-next {
    right: -80px;
}
.sponsors-sec {
    background: #f2f3f8;
}
.readmore {
    color: #000000;
    font-size: 16px;
    font-family: 'PT Serif', serif;
}
.readmore i {
    color: #cca876;
    font-size: 14px;
}
.col12 {
    flex: 0 0 100%;
    max-width: 100%;
    width: 100%;
}
.row.d-flex-wrap.copyright-wrapper {
    align-items: center;
    padding: 10px 0;
    justify-content: space-between;
}
.copyright-wrapper hr {
    margin: 50px 0 15px;
    border-style: solid none none;
    border-color: #666c84;
}
.m-0 {
    margin: 0;
}

.PullQuote {
    padding-left: 40px;
    border-left: 15px solid #ba892c;
}
.event-box a:hover {
    text-decoration: none;
}
.siedebar-blog {
    margin-top: 30px;
}
.PullQuote .quotetitle {
    margin: 0;
}
.sbm-iconbox .btns {
    margin: 20px -15px 0;
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}
.sbm-iconbox .btns a {
    min-width: 150px;
}   
.img-card .img-card-content h3 {
    font-family: "Lato", sans-serif;
    font-size: 16px;
    margin: 0 0 5px;
}
.stay-conected-sec {
    background: #2b2f3f;
    position: relative;
    z-index: 1;
    border-bottom: 11px solid #b9892b;
    padding: 90px 0;
}
.stay-conected-sec .SectionHeader {
    color: #ffffff;
    text-align: center;
    margin-bottom: 30px;   
}
.stay-conected-sec p {
    color: #ffffff;
    text-align: center;
    font-size: 30px;
    font-family: 'Adobe Garamond Pro Regular';
    padding: 0 2%;
}
.stay-conected-sec .fixed-bg {
    opacity: 0.15;
    background-blend-mode: screen;
}
.counter-box {
    text-align: center;
}

.counter-box .counter {
    color: #ffffff;
    font-size: 55px;
    line-height: 1;
    font-weight: 700;
    margin: 15px 0 5px;
}

.stay-conected-sec .counter-box p {
    color: #9b9b9b;
    margin: 0;
    font-size: 18px;
    font-family: 'Carbona Test';
}

.contact-sec {
    background: #f2f3f8;
}

.form-design-1 input,
.form-design-1 textarea {
    background-color: #ffffff;
    width: 100%;
    border-color: #ffffff;
    border-radius: 0;
    padding: 10px 20px;
    height: auto;
    font-size: 14px;
    font-family: Lato;
    margin-bottom: 28px;
    font-family: "Lato", sans-serif;
}
.form-design-1 .MAJButton-2 {
    min-width: 300px;
}
.form-design-1 input {

}

.form-design-1 input:focus:invalid:focus,
.form-design-1 textarea:focus:invalid:focus,
.form-design-1 select:focus:invalid:focus {
    border-color: #ffffff;
    box-shadow: none;
}


.form-design-1 textarea:focus, 
.form-design-1 input[type="text"]:focus, 
.form-design-1 input[type="password"]:focus, 
.form-design-1 input[type="datetime"]:focus, 
.form-design-1 input[type="datetime-local"]:focus, 
.form-design-1 input[type="date"]:focus, 
.form-design-1 input[type="month"]:focus, 
.form-design-1 input[type="time"]:focus, 
.form-design-1 input[type="week"]:focus, 
.form-design-1 input[type="number"]:focus, 
.form-design-1 input[type="email"]:focus, 
.form-design-1 input[type="url"]:focus, 
.form-design-1 input[type="search"]:focus, 
.form-design-1 input[type="tel"]:focus, 
.form-design-1 input[type="color"]:focus, 
.form-design-1 .uneditable-input:focus {
    border-color: #ffffff;
    box-shadow: none;
}



.contact-sec .container {
    max-width: 925px;
    margin: 0 auto;
}
.contact-sec .row.row-flex {

}
.contact-sec .row.row-flex .span12 {
    flex: 0 0 100%;
    max-width: 100%;
    padding: 0 15px;
    margin: 0px;
}

.contact-sec .row.row-flex .span4 {
    flex: 0 0 33.33%;
    max-width: 33.33%;
    width: 33.33%;
    padding: 0 15px;
    margin: 0px;
}

.counter-box {
    margin-top: 80px;
}
.latest-blogs-sec {
    padding: 50px 0;
}
.Highlight .btns-wrap {
    display: inline-flex;
    flex-wrap: wrap;
    row-gap: 20px;
}

.DiamondBullets ul li ul>li  a:hover {
    background: #e5d6bf;
    color: #ba892c;
}
.hideNone{
    display: none !important;
}
#zoneToolBar form,#zoneToolBar select{margin:0;}
.span12.removeFlex.inner-content-area{
    width: 100% !important;
}
.contactFrmHolder .row.row-flex>.span4 {
    padding: 0 !important;
    margin-left: 20px !important;
    flex: 0 0 33.33%;
    max-width: 31.8%;
    width: 31.8%;
    padding: 0 15px;
}
.contactFrmHolder .row.row-flex>.span12{
   margin-left: 0 !important;
   width: 100% !important;
}
.contactFrmHolder .row.row-flex div:first-child {
    margin-left: 0px !important;
}