use membercentral;
GO

ALTER PROC dbo.cache_perms_updateSiteResourceFunctionRightPrintsForSiteResourcesBulk
@siteID int,
@processNewPrints bit = 1

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	-- expects temp table named #siteResourcesToProcess to already exist and be populated
	-- CREATE TABLE #siteResourcesToProcess (siteResourceID int PRIMARY KEY, resourceTypeID int);
	DECLARE @orgID int;

	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);

	if dbo.fn_cache_perms_getStatus(@orgID) = 'enabled' BEGIN

		IF OBJECT_ID('tempdb..#siteResourceFunctionRightPrintsToInsert') IS NOT NULL 
			DROP TABLE #siteResourceFunctionRightPrintsToInsert;
		IF OBJECT_ID('tempdb..#siteResourceFunctionRightPrintsToDelete') IS NOT NULL 
			DROP TABLE #siteResourceFunctionRightPrintsToDelete;
		IF OBJECT_ID('tempdb..#myRightPrintsToInsert') IS NOT NULL
			DROP TABLE #myRightPrintsToInsert;
		IF OBJECT_ID('tempdb..#rightPrintsToProcess') IS NOT NULL
			DROP TABLE #rightPrintsToProcess;
		IF OBJECT_ID('tempdb..#affectedResources') IS NOT NULL
			DROP TABLE #affectedResources;
		IF OBJECT_ID('tempdb..#holding_siteResourceFunctionRightPrints') IS NOT NULL
			DROP TABLE #holding_siteResourceFunctionRightPrints;
		IF OBJECT_ID('tempdb..#insertedRightPrints') IS NOT NULL 
			DROP TABLE #insertedRightPrints;




		CREATE TABLE #siteResourceFunctionRightPrintsToInsert (autoid int IDENTITY(1,1), siteResourceID int, functionID int, rightPrintID int INDEX IX_siteResourceFunctionRightPrintsToInsert_rightPrintID, groupList varchar(max));
		CREATE TABLE #siteResourceFunctionRightPrintsToDelete (siteResourceFunctionRightPrintID int PRIMARY KEY);
		CREATE TABLE #insertedRightPrints (rightPrintID int PRIMARY KEY);
		CREATE TABLE #myRightPrintsToInsert (rpiID int IDENTITY(1,1), siteID int, groupList varchar(max), hashGroupList varbinary(16));
		CREATE TABLE #rightPrintsToProcess (autoid int IDENTITY(1,1), rightPrintID int INDEX IX_rightPrintsToProcess_rightPrintID);
		CREATE TABLE #affectedResources (siteID int, siteResourceID int, resourceTypeID int, functionID int, groupID int, include bit, INDEX IX_affectedResources_siteResourceID_functionID (siteResourceID, functionID),  INDEX IX_affectedResources_resourceTypeID_siteResourceID (resourceTypeID, siteResourceID));
		CREATE TABLE #holding_siteResourceFunctionRightPrints (autoid int IDENTITY(1,1), siteID int, siteResourceID int, functionID int, siteResourceFunctionRightPrintID int, currentRightPrintID int INDEX IX_holding_siteResourceFunctionRightPrints_currentRightPrintID, newRightPrintID int, groupList varchar(max), hashGroupList varbinary(16), INDEX IX_holding_siteResourceFunctionRightPrints_siteResourceID_functionID (siteResourceID, functionID));

		insert into #affectedResources (siteID, siteResourceID , resourceTypeID, functionID , groupID, include )
		-- normal rights/roles
		select distinct @siteID, srrc.ResourceID as siteResourceID, siteResourcesToProcess.resourceTypeID, srrc.functionID, srrc.groupID, srrc.include
		from #siteResourcesToProcess siteResourcesToProcess
		inner join cms_siteResourceRightsCache srrc on siteResourcesToProcess.siteResourceID = srrc.resourceID
			and srrc.universalRoleResourceTypeID is null
			and srrc.siteID = @siteID
			union 
		-- universal role rights
		select distinct @siteID, siteResourcesToProcess.siteResourceID, siteResourcesToProcess.resourceTypeID, srrc.functionID, srrc.groupID, srrc.include
		from #siteResourcesToProcess siteResourcesToProcess
		inner join cms_siteResourceRightsCache srrc on siteResourcesToProcess.resourceTypeID = srrc.universalRoleResourceTypeID
			and srrc.siteID = @siteID
		inner join sites s on s.siteResourceID = srrc.ResourceID
			and s.siteID = @siteID

		insert into #holding_siteResourceFunctionRightPrints (siteID, siteResourceID, functionID, groupList, hashGroupList)
		select sr.siteID, sr.siteResourceID, srtf.functionID, 
			groupList = dbo.sortedIntList(distinct (case when ar.include > 0 then ar.groupID else (-1 * ar.groupID) end )),
			hashGroupList = HASHBYTES('MD5',cast(dbo.sortedIntList(distinct (case when ar.include > 0 then ar.groupID else (-1 * ar.groupID) end )) as varchar(max))) 
		from #affectedResources ar
		inner join dbo.cms_siteResources sr 
			on sr.siteID = @siteID 
			and sr.siteResourceID = ar.siteResourceID
		inner join dbo.cms_siteResourceTypeFunctions srtf 
			on sr.resourceTypeID = srtf.resourceTypeID
			and ar.functionID = srtf.functionID
		group by sr.siteID, sr.siteResourceID, srtf.functionID
		OPTION(RECOMPILE);

		-- get current rightPrints
		update hold 
		set hold.currentRightPrintID = srfrp.rightPrintID,
			hold.siteResourceFunctionRightPrintID = srfrp.siteResourceFunctionRightPrintID
		from #holding_siteResourceFunctionRightPrints hold
		inner join dbo.cache_perms_siteResourceFunctionRightPrints srfrp on srfrp.siteID = @siteID
			and srfrp.siteResourceID = hold.siteResourceID	
			and srfrp.functionID = hold.functionID;

		-- match to existing rightPrints
		update hold
		set hold.newRightPrintID = rp.rightPrintID
		from #holding_siteResourceFunctionRightPrints hold
		inner join dbo.cache_perms_rightPrints rp on rp.siteID = @siteID
			and rp.hashGroupList = hold.hashGroupList;

		insert into #myRightPrintsToInsert (siteID, groupList, hashGroupList)
		select distinct siteID, groupList, hashGroupList
		from #holding_siteResourceFunctionRightPrints
		where newRightPrintID is null and groupList is not null;

		if exists(select siteID from #myRightPrintsToInsert) BEGIN
			IF OBJECT_ID('tempdb..#myRightPrintsToInsertAndGroups') IS NOT NULL
				DROP TABLE #myRightPrintsToInsertAndGroups;
			CREATE TABLE #myRightPrintsToInsertAndGroups (autoID int IDENTITY(1,1), rpiID int INDEX IX_myRightPrintsToInsertAndGroups_rpiID, groupID int, include bit);

			insert into #myRightPrintsToInsertAndGroups (rpiID, groupID, include)
			select rpi.rpiID, abs(list.listitem) as groupID, include = case when list.listitem > 0 then cast(1 as bit) else cast(0 as bit) end
			from #myRightPrintsToInsert rpi
			cross apply dbo.fn_intListToTable(rpi.grouplist,',') as list
			order by abs(list.listitem);

			insert into dbo.cache_perms_rightPrints (siteID, isProcessed, groupList, hashGroupList)
			OUTPUT inserted.rightPrintID 
			INTO #insertedRightPrints
			select siteID, 0, groupList, hashGroupList
			from #myRightPrintsToInsert;

			insert into dbo.cache_perms_rightPrintsAndGroups (rightPrintID, groupID, include, siteID)
			SELECT rp.rightPrintID, rpig.groupID, rpig.include, @siteID
			FROM #myRightPrintsToInsert rpi
			inner join #myRightPrintsToInsertAndGroups rpig on rpig.rpiID = rpi.rpiID
			inner join dbo.cache_perms_rightPrints rp
				on rp.hashGroupList = rpi.hashGroupList
				and rp.siteID = rpi.siteID
			inner join #insertedRightPrints irp on irp.rightPrintID = rp.rightPrintID;

			IF OBJECT_ID('tempdb..#myRightPrintsToInsertAndGroups') IS NOT NULL
				DROP TABLE #myRightPrintsToInsertAndGroups;
		END

		IF OBJECT_ID('tempdb..#myRightPrintsToInsert') IS NOT NULL
			DROP TABLE #myRightPrintsToInsert;

		insert into #siteResourceFunctionRightPrintsToDelete (siteResourceFunctionRightPrintID)
		select siteResourceFunctionRightPrintID
		from #holding_siteResourceFunctionRightPrints
		where siteResourceFunctionRightPrintID is not null
		and currentRightPrintID <> isnull(newRightPrintID,0)
		
		
		-- find existing srfrp's that should be removed -- with no replacement
		insert into #siteResourceFunctionRightPrintsToDelete (siteResourceFunctionRightPrintID)
		select srfrp.siteResourceFunctionRightPrintID
		from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		inner join (
			SELECT ar.siteresourceID, srtf.functionID
			FROM #affectedResources ar
			inner join dbo.cms_siteResourceTypeFunctions srtf on ar.resourceTypeID = srtf.resourceTypeID
				except
			SELECT ar.siteresourceID, ar.functionID
			FROM #affectedResources ar
			) as newlyUnassignedFunctions 
			on srfrp.siteID = @siteID 
			and newlyUnassignedFunctions.siteResourceID = srfrp.siteResourceID
			and newlyUnassignedFunctions.functionID = srfrp.functionID
		
		except 

		select siteResourceFunctionRightPrintID
		from #siteResourceFunctionRightPrintsToDelete;
		
		-- srfrp's for preexisting rightPrints
		insert into #siteResourceFunctionRightPrintsToInsert (siteResourceID, functionID, rightPrintID)
		select siteResourceID, functionID, newRightPrintID
		from #holding_siteResourceFunctionRightPrints
		where newRightPrintID is not null
		and newRightPrintID <> isnull(currentRightPrintID,0);

		-- srfrp's for newly created rightPrints
		insert into #siteResourceFunctionRightPrintsToInsert (siteResourceID, functionID, rightPrintID)
		select hold.siteResourceID, hold.functionID, rp.rightPrintID
		from #holding_siteResourceFunctionRightPrints hold
		inner join dbo.cache_perms_rightPrints rp on hold.siteID = rp.siteID
			and hold.newRightPrintID is null
			and hold.groupList is not null
			and rp.hashGroupList = hold.hashGroupList;

		insert into dbo.cache_perms_siteResourceFunctionRightPrints (siteResourceID, functionID, rightPrintID, siteID)
		select siteResourceID, functionID, rightPrintID, @siteID
		from #siteResourceFunctionRightPrintsToInsert;

		delete srfrp
		from dbo.cache_perms_siteResourceFunctionRightPrints srfrp
		inner join #siteResourceFunctionRightPrintsToDelete temp on temp.siteResourceFunctionRightPrintID = srfrp.siteResourceFunctionRightPrintID
		where srfrp.siteID = @siteID;

		if @processNewPrints = 1 BEGIN
			insert into #rightPrintsToProcess (rightPrintID)
			select distinct rp.rightPrintID
			from dbo.cache_perms_rightPrints rp
			inner join #siteResourceFunctionRightPrintsToInsert insertlist on rp.rightPrintID = insertlist.rightPrintID
				and rp.isprocessed = 0

			if exists(select rightPrintID from #rightPrintsToProcess)
				exec dbo.cache_perms_processRightPrintsBulk @siteID=@siteID;

			-- mark all rightPrints as processed, if we processed all groupprints against it
			update rp set isProcessed = 1
			from dbo.cache_perms_rightPrints rp
			inner join #rightPrintsToProcess temp on temp.rightPrintID = rp.rightPrintID;
		END

		IF OBJECT_ID('tempdb..#siteResourceFunctionRightPrintsToInsert') IS NOT NULL 
			DROP TABLE #siteResourceFunctionRightPrintsToInsert;
		IF OBJECT_ID('tempdb..#siteResourceFunctionRightPrintsToDelete') IS NOT NULL 
			DROP TABLE #siteResourceFunctionRightPrintsToDelete;
		IF OBJECT_ID('tempdb..#myRightPrintsToInsert') IS NOT NULL
			DROP TABLE #myRightPrintsToInsert;
		IF OBJECT_ID('tempdb..#rightPrintsToProcess') IS NOT NULL
			DROP TABLE #rightPrintsToProcess;
		IF OBJECT_ID('tempdb..#affectedResources') IS NOT NULL
			DROP TABLE #affectedResources;
		IF OBJECT_ID('tempdb..#holding_siteResourceFunctionRightPrints') IS NOT NULL
			DROP TABLE #holding_siteResourceFunctionRightPrints;
		IF OBJECT_ID('tempdb..#insertedRightPrints') IS NOT NULL 
			DROP TABLE #insertedRightPrints;
	END

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
