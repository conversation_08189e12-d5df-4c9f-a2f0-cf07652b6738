<cfif structKeyExists(local.tmpMemSetRights,"ViewDues") and local.tmpMemSetRights.ViewDues neq 1>
	<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
</cfif>
<cfsavecontent variable="local.subJS">
<cfoutput>
<style>
	##subscriptions span.deleted{
		background: ##fcd9c4 !important;
	}
	.row-nonactivated{
		background: ##ffff00;
	}
	.rowPointer{
		cursor:pointer;
	}
	.actionTool a i{
		font-size: 13px !important;
		width: 15px !important;
	}
	.mcModalBodyCustom {
		min-height:120px;
	}
</style>
<script language="javascript">
	var mcma_currtab = "dues";
	let memberSubTable,memberSubHistoryTable;

	var #ToScript(local.subLink,"mcma_link_sub")#
	var #ToScript(local.subProcessGenerateRenewals,"mcma_link_procgenrenewals")#
	var #ToScript(local.subConfirmRenewLink,"mcma_link_confirmrenew")#
	var #ToScript(local.processGenerateOffers,"mcma_link_procgenoffers")#
	var #ToScript(local.startGenerateOffers,"mcma_link_startgenoffers")#
	var #ToScript(local.addPaymentURL,"mca_link_addpmt")#
	var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,"mcma_hasrights_allocpmt")#
	var #ToScript(local.allocatePaymentURL,"mca_link_allocpmt")#
	var #ToScript(local.subAssocCC,"mcma_link_subassoc")#
	var #ToScript(local.linkAcceptSubscription,"linkAcceptSubscription")#
	var #ToScript(local.subExpireLink,"mcma_link_expiresub")#
	var #ToScript(local.subRemoveLink,"mcma_link_removesub")#
	var #ToScript(local.cleanupInvoicesLink,"mcma_link_cleanupinv")#
	var #ToScript(local.showPaperStatementsForm,"mcma_link_generatePaperStatement")#
	var #ToScript(local.subRenewalLink,"mcma_link_subrenewal")#
	var #ToScript(arguments.event.getValue('mc_adminNav.adminHomeResource'),"mcma_link_adminhome")#
	var mcma_hasrights_refundpmt = "1"; /* Bypassing the JS method; a read-only form will be shown in this case */
	var #ToScript(local.refundPaymentURL,"mcma_link_refpayment")#

	$(document).ready(function(){
		$('body').on('change', '##fsubtype', function(e) {
			callChainedSelect('fsubtype','fsub','getSubscriptionsForSubType','typeid',1,'All',true,'subs');
			$('##fSubType2').val($('##fsubtype').val());
			$('##fSubscription2, ##fRate2').val('0');
			$('##frate').empty();
			$('##fRate2').val('0');
		});	
		$('body').on('change', '##fsub', function(e) {
			callChainedSelect('fsub','frate','getSubRatesForSub','subid',0,'All',true,'subs');
			$('##fSubscription2').val($('##fsub').val());
			$('##fRate2').val('0');
		});
		$('body').on('change', '##frate', function(e) {
			var r = $('##frate').val() || '';
			if (r.length > 0) { $('##fRate2').val(r.toString()); } else { $('##fRate2').val(''); }
		});
		mca_setupDatePickerRangeFields('ftermstartfrom','ftermstartto');
		mca_setupDatePickerRangeFields('ftermendfrom','ftermendto');
		mca_setupCalendarIcons('frmFilter');
		mca_setupSelect2();
		initMemberSubscriptions();
		initMemberSubscriptionsHistory();

		$(document).on('click', '##memberSubTable tbody > tr', function (e, dt, type, indexes) {
			
			subscriberid = $(this).attr('data-subscriberid');
			doSelectSubscriber(subscriberid);			
		});
		$(document).on('click', '##memberSubHistoryTable tbody > tr', function (e, dt, type, indexes) {
			
			subscriberid = $(this).attr('data-subscriberid');
			doSelectSubscriber(subscriberid);			
		});
	});
	function doc_download(u) {
		$('##divSubShowScreenLoading').hide();
		top.location.href='/tsdd/' + u;
	}

		/* called by both initMemberSubscriptions and initMemberSubscriptionsHistory to render the first column for each row*/
	function renderSubscriptionName ( data, type, row, meta ) {
		let renderData = '';
		if (type === 'display')	{
			let arrSubPathExpanded = data.thePath.split('.');
			let grpPadding = arrSubPathExpanded.length > 2 ? 10 * (arrSubPathExpanded.length-1) : 0;
			renderData += '<div style="padding-left:'+grpPadding+'px;">';
			if (data.hasChildSubs) {
				renderData += '<div><a data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'" href="javascript:toggleParentSubDisplay('+data.subscriberID+');" id="grpLabel_'+data.subscriberID+'"><i class="fas '+(data.expandSub ? 'fa-folder-minus' : 'fa-folder-plus')+' fa-fw subToggleBtn pr-2"></i> '+data.subscriptionName+'</a> <small data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'">('+data.rateName+')</small></div>';
			} else {
				renderData += '<div><ul class="pl-1 m-0"><li style="list-style:none;"><span data-toggle="tooltip" title="'+data.typeName+'; '+data.rateName+'; '+data.frequencyName+'">'+data.subscriptionName+' <small>('+data.rateName+')</small></span></li></ul></div>';
			}
			renderData += '</div>';
		}
		return type === 'display' ? renderData : data;
	}


	/* called by both initMemberSubscriptions and initMemberSubscriptionsHistory to render the action column for each row*/
	function renderSubscriptionActions( data, type, row, meta){
		let renderData = '';
		if (type === 'display')	{	
			renderData += '<div class="actionTool">';
			
			if (!('DX'.split('').includes(data.status))) {
				if(data.payMethod == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-dark p-1 mx-1 mb-1" onclick="assocCC('+data.subscriberID+');return false;" data-toggle="tooltip" title="Pay Method for Subscription"><i class="fa-solid fa-credit-card"></i></a>';
				}else if(data.payMethod == 2){
					renderData += '<a href="##" class="btn btn-xs btn-outline-warning  p-1 mx-1 mb-1" onclick="assocCC('+data.subscriberID+');return false;" data-toggle="tooltip" title="Pay Method for Subscription"><i class="fa-solid fa-credit-card"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-credit-card"></i></a>';
				}
			}
			if ('PAIED'.split('').includes(data.status)) {
				if(data.addPayment == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-green p-1 mx-1 mb-1" onclick="addPayment(\''+data.addPaymentEncString+'\');return false;" data-toggle="tooltip" title="Pay for Subscription"><i class="fa-solid fa-dollar-sign"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 mb-1 text-muted disabled"><i class="fa-solid fa-dollar-sign"></i></a>';
				}
			}

			if ('RO'.split('').includes(data.status)) {
				if(data.statusName == 'Billed' && data.parentSubscriberIDLen == 0){
					strdownload = "'download'";
					renderData += '<a href="javascript:generatePaperStatement('+data.subscriberID+','+strdownload+');" class="btn btn-xs btn-outline-primary p-1 mx-1 mb-1"  data-toggle="tooltip" title="Generate Paper Statement"><i class="fa-solid fa-file-pdf"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 mb-1 text-muted disabled" ><i class="fa-solid fa-file-pdf"></i></a>';
				}
			}

			if ('ROPAI'.split('').includes(data.status)) {
				if(data.canEditSub == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 mx-1 mb-1" onclick="editMemberSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Edit Subscription"><i class="fa-solid fa-pencil"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-pencil"></i></a>';
				}
			}

			if ('PAI'.split('').includes(data.status)) {
				if(data.markActive == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 mx-1 mb-1" onclick="markInactiveSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark Inactive"><i class="fa-solid fa-shuffle"></i></a>';
				}else if(data.markActive == 2){
					renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 mx-1 mb-1" onclick="markActiveSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark Active"><i class="fa-solid fa-shuffle"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-shuffle"></i></a>';
				}
			}

			if ('ROPAIE'.split('').includes(data.status)) {
				if(data.subCanRenew == 1 && data.parentSubscriberIDLen == 0){
					renderData += '<a href="##" class="btn btn-xs btn-outline-success p-1 mx-1 mb-1" onclick="renewMemberSub('+data.subscriberID+',#local.strMember.qryMember.memberid#);return false;" data-toggle="tooltip" title="Renew Subscription"><i class="fa-solid fa-rotate-right"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs  p-1 mx-1 mb-1 text-muted disabled"><i class="fa-solid fa-rotate-right"></i></a>';
				}
			}

			if ('RO'.split('').includes(data.status)) {
				if(data.acceptSub == 1 && data.parentSubscriberIDLen == 0){
					renderData += '<a href="##" class="btn btn-xs btn-outline-success p-1 mx-1 mb-1" onclick="acceptSubscription('+data.subscriberID+');return false;" data-toggle="tooltip" title="Accept Subscription"><i class="fa-solid fa-check-double"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-check-double"></i></a>';
				}
			}
			
			if ('RO'.split('').includes(data.status)) {
				if(data.emailOfferSub == 1 && data.parentSubscriberIDLen == 0){
					renderData += '<a href="##" class="btn btn-xs btn-outline-primary p-1 mx-1 mb-1" onclick="emailOfferSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Email Offer"><i class="fa-solid fa-envelope"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-envelope"></i></a>';
				}
			}

			if ('DE'.split('').includes(data.status)) {
				if(data.cleanUpInvoice == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-danger p-1 mx-1 mb-1" onclick="cleanupInvoices('+data.subscriberID+');return false;" data-toggle="tooltip" title="Cleanup Invoices"><i class="fa-solid fa-file-invoice-dollar"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-file-invoice-dollar"></i></a>';
				}
			}

			if ('ROPAI'.split('').includes(data.status)) {
				if(data.markBilledExpired == 1 && data.parentSubscriberIDLen == 0){
					renderData += '<a href="##" class="btn btn-xs btn-outline-info p-1 mx-1 mb-1" onclick="markBilledSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark as Billed"><i class="fa-solid fa-check"></i></a>';
				}else if(data.markBilledExpired == 2 ){
					renderData += '<a href="##" class="btn btn-xs btn-outline-danger p-1 mx-1 mb-1" onclick="markExpiredSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark as Expired"><i class="fa-solid fa-calendar-xmark"></i></a>';
				}else if(data.status == 'O' && data.parentSubscriberIDLen == 0){
					renderData += '<a href="##" class="btn btn-xs btn-outline-info p-1 mx-1 mb-1" onclick="showSubRenewalLink('+data.subscriberID+');return false;" data-toggle="tooltip" title="Subscription Renewal Link"><i class="fa-regular fa-copy"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs text-muted disabled p-1 mx-1 mb-1"><i class="fa-solid fa-calendar-xmark"></i></a>';
				}
			}
			if ('PAI'.split('').includes(data.status)) {
				if(data.markOverrideSub == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-success p-1 mx-1 mb-1" onclick="markOverrideActivationSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Mark Activated"><i class="fa-solid fa-play"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-play"></i></a>';
				}
			}
			if (!'D'.split('').includes(data.status)) {
				if(data.strRemoveSub == 1){
					renderData += '<a href="##" class="btn btn-xs btn-outline-danger p-1 mx-1 mb-1" onclick="removeMemberSub('+data.subscriberID+');return false;" data-toggle="tooltip" title="Remove Subscription"><i class="fa-solid fa-minus-circle"></i></a>';
				}else{
					renderData += '<a href="##" class="btn btn-xs p-1 mx-1 text-muted disabled"><i class="fa-solid fa-minus-circle"></i></a>';
				}
			}
			renderData += '</div>';
		}
		return type === 'display' ? renderData : data;

	}

	function initMemberSubscriptions(){
		memberSubTable = $('##memberSubTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"language": {
				"emptyTable": "No Subscriptions Found."
			},
			"ajax": { 
				"url": '#local.subscriptionListLink#',
				"type": "post",
				"data": function(d) {
					$.each($('##frmFilter').serializeArray(),function() {
						d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
					});
				}
			},
			"autoWidth": false,
			"columns": [
				{ "data": null,
					"render": renderSubscriptionName,
					"className": "align-top"
				},
				{ "data": "subStartDate", "width": "1", "className": "align-top text-nowrap px-2" },
				{ "data": "subEndDate", "width": "1", "className": "align-top text-nowrap px-2" },
				{ "data": "billedAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": "dueAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": "statusName", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": null,
					"render": renderSubscriptionActions,
					"width": "300px",
					"className": "align-top"
				}
			],
			"searching": false,
			"ordering": false,
			"createdRow": function (row, data, index) {
				$(row).attr('data-subscriberID',data.subscriberID); /* adding data-subscriberID here instead of DT_RowData due to the row data being lost while moving up/down grp */
				if(data.status == 'D'){
					$(row).addClass('table-warning');
				}else if(data.paymentStatus == 'N' && (data.status == 'A' || data.status == 'P')){
					$(row).addClass('row-nonactivated');
				}
				$(row).addClass('rowPointer');
			}, 
			"drawCallback": function (settings) {
				$("##memberSubTableStatusLegend").html(settings.json.subscriptionStatusLegendHtml);
			}
		});
	}
	function initMemberSubscriptionsHistory(){
		memberSubHistoryTable = $('##memberSubHistoryTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"info": false,
			"language": {
				"emptyTable": "No Subscriptions History Found."
			},
			"ajax": { 
				"url": '#local.subscriptionListLink#&hist=1',
				"type": "post",
				"data": function(d) {
					$.each($('##frmFilter').serializeArray(),function() {
						d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
					});
				}
			},
			"autoWidth": false,
			"columns": [
				{ "data": null,
					"render": renderSubscriptionName,
					"className": "align-top"
				},
				{ "data": "subStartDate", "width": "1", "className": "align-top text-nowrap px-2" },
				{ "data": "subEndDate", "width": "1", "className": "align-top text-nowrap px-2" },
				{ "data": "billedAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": "dueAmt", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": "statusName", "width": "1px", "className": "align-top text-nowrap px-2" },
				{ "data": null,
					"render": renderSubscriptionActions,
					"width": "300px",
					"className": "align-top"
				}
			],
			"searching": false,
			"ordering": false,
			"createdRow": function (row, data, index) {
				$(row).attr('data-subscriberID',data.subscriberID); /* adding data-subscriberID here instead of DT_RowData due to the row data being lost while moving up/down grp */
				if(data.status == 'D'){
					$(row).addClass('table-warning');
				}else if(data.paymentStatus == 'N' && (data.status == 'A' || data.status == 'P')){
					$(row).addClass('row-nonactivated');
				}
				$(row).addClass('rowPointer');
			}, 
			"drawCallback": function (settings) {
				$("##memberSubHistoryTableStatusLegend").html(settings.json.subscriptionStatusLegendHtml);
			}
		});
	}
	function toggleParentSubDisplay(sid) {
		let subToggleBtn = $('##grpLabel_'+sid+' i.subToggleBtn');		
		subToggleBtn.toggleClass('fa-folder-plus fa-folder-minus');

		if (subToggleBtn.hasClass('fa-folder-minus')) {
			showChildSubs(sid);
		} else {
			hideChildSubs(sid);
		}
	}
	function showChildSubs(sid) {
		$('tr.childSubOf'+sid).removeClass('d-none');
		$('tr.childSubOf'+sid).each(function(i,grpRow) {
			if ($(this).find('i.subToggleBtn').hasClass('fa-folder-minus')) showChildSubs($(this).attr('data-subscriberID'));
		});
	}
	function hideChildSubs(sid) {
		$('tr.childSubOf'+sid).addClass('d-none');
		$('tr.childSubOf'+sid).each(function(i,grpRow) {
			if ($(this).find('i.subToggleBtn').hasClass('fa-folder-minus')) hideChildSubs($(this).attr('data-subscriberID'));
		});
	}
	function showRefundPaymentOnSubRemoveSuccess(){
		$('##MCModal').on('hidden.bs.modal', function() { refundPayment(#local.strMember.qryMember.memberid#); });
		MCModalUtils.hideModal();
	}
</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.subJS)#">

<!--- button bar --->
<div class="toolButtonBar border-bottom-0">
	<div><a href="javascript:addSub();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new subscription."><i class="fa-solid fa-circle-plus"></i> Add Subscription</a></div>
	<cfif arguments.event.getValue('mc_siteInfo.sf_subsAddV2')>
		<div><a href="javascript:addSubV2();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to add a new subscription."><i class="fa-solid fa-circle-plus"></i> Add Subscription V2</a></div>
	</cfif>
	<div><a href="javascript:filterSubscriptionList();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter subscriptions."><i class="fa-solid fa-filter"></i> Filter Subscriptions</a></div>
	<div><a href="javascript:onChangeSubStatus(true);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to check activation status."><i class="fa-solid fa-table-cells"></i> Check Activation Status</a></div>
</div>

<div id="divFilterForm" style="display:none;">
	<form name="frmFilter" id="frmFilter">
		<div class="row mb-3">
			<div class="col-xl-12">
				<div class="card card-box mb-1">
					<div class="card-header py-1 bg-light">
						<div class="card-header--title font-weight-bold font-size-lg">
							Filter Subscriptions
						</div>
					</div>
					<div class="card-body pb-3">
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group">
												<input type="hidden" name="checkActivations" id="checkActivations" value="">
												<select name="fsubtype" id="fsubtype" class="form-control">
													<option value="0">Any Subscription Type</option>
													<cfoutput query="local.qrySubTypes">
														<option value="#local.qrySubTypes.typeID#">#local.qrySubTypes.typeName#</option>
													</cfoutput>
												</select>
												<label for="fsubtype">Subscription Type</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group">
												<select name="fsub" id="fsub" class="form-control">
													<option value="0">Any Subscription</option>
												</select>
												<label for="fsub">Subscription</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="ftermstartfrom" id="ftermstartfrom" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="ftermstartfrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('ftermstartfrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="ftermstartfrom">Start date from</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="ftermstartto" id="ftermstartto" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="ftermstartto"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('ftermstartto');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="ftermstartto">Start date to</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group">
												<select id="fstatus" name="fstatus" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2" >
													<cfoutput query="local.qrySubscriptionStatuses">
														<option value="#local.qrySubscriptionStatuses.statusCode#">#local.qrySubscriptionStatuses.statusName#</option>
													</cfoutput>
												</select>
												<label for="fstatus">Status</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group selectFieldHolderStatusRate">
												<select name="frate" id="frate" multiple="yes" data-toggle="custom-select2" class="form-control form-control-sm">
													<option></option>
												</select>
												<label for="frate">Rate</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="ftermendfrom" id="ftermendfrom" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="ftermendfrom"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('ftermendfrom');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="ftermendfrom">End date from</label>
												</div>
											</div>
										</div>
									</div>
									<div class="col-sm-6 col-xs-12">
										<div class="form-group">
											<div class="form-label-group mb-2">
												<div class="input-group dateFieldHolder">
													<input type="text" name="ftermendto" id="ftermendto" value="" class="form-control dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="ftermendto"><i class="fa-solid fa-calendar"></i></span>
														<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('ftermendto');"><i class="fa-solid fa-circle-xmark"></i></a></span>
													</div>
													<label for="ftermendto">End date to</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group">
												<select name="fcof" id="fcof" class="form-control">
													<option value="">With or Without Pay Method Associated</option>
													<option value="Y">With Pay Method Associated</option>
													<option value="N" >With no Pay Method Associated</option>
												</select>
												<label for="fcof">Pay Method</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group ">
												<select name="ffreq" id="ffreq" class="form-control">
													<option value="0">Any Frequency</option>
													<cfoutput query="local.qryFrequencies">
														<option value="#local.qryFrequencies.frequencyID#">#local.qryFrequencies.frequencyName#</option>
													</cfoutput>
												</select>
												<label for="ffreq">Frequency</label>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-label-group mb-2">
									<div class="input-group">
										<select id="fpaystatus" name="fpaystatus" class="form-control">
											<option value="">Any Activation Status</option>
											<cfoutput query="local.qrySubscriptionPaymentStatuses">
												<option value="#local.qrySubscriptionPaymentStatuses.statusCode#">#local.qrySubscriptionPaymentStatuses.statusName#</option>
											</cfoutput>
										</select>
										<label for="fpaystatus">Activation Status</label>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="card-footer p-2 text-right">
						<button type="button" name="btnClearSubsFilter" class="btn btn-sm btn-secondary" onclick="clearSubsFilter();">
							Clear Filters
						</button>
						<button type="button" name="btnFilterGrid" class="btn btn-sm btn-primary" onclick="onChangeSubStatus(false);">
							<i class="fa-light fa-filter"></i> Filter Subscriptions
						</button>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Subscriptions <img src="/assets/common/images/help-icon.jpg" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Lists subscriptions with Active, Accepted, Billed, Renewal Not Sent, or Inactive status." />
				</div>
			</div>
			<div class="card-body pb-3">
				<cfif local.qrySubscriberInqueue.recordcount gt 0>
					<div class="alert alert-info mb-1">This subscription is being processed</div>
				</cfif>
				<div class="my-2">
					<table id="memberSubTable" class="table table-sm table-bordered table-hover" style="width:100%;">
						<thead>
							<tr>
								<th>Subscription Name</th>
								<th>Start</th>
								<th>End</th>
								<th>Billed</th>
								<th>Due</th>
								<th>Status</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
				<div id="memberSubTableStatusLegend" style="width:100%"></div>
			</div>
		</div>
	</div>
</div>

<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Subscriptions History <img src="/assets/common/images/help-icon.jpg" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Lists subscriptions with Expired, Offer Expired or Deleted status." />
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="my-2">					
					<table id="memberSubHistoryTable" class="table table-sm table-bordered table-hover" style="width:100%;">
						<thead>
							<tr>
								<th>Subscription Name</th>
								<th>Start</th>
								<th>End</th>
								<th>Billed</th>
								<th>Due</th>
								<th>Status</th>
								<th>Actions</th>
							</tr>
						</thead>
					</table>
				</div>
				<div id="memberSubHistoryTableStatusLegend" style="width:100%"></div>
			</div>
		</div>
	</div>
</div>



<cfoutput>
<a id="subAuditT" name="subAuditT"></a>

<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="card-header--title font-weight-bold font-size-lg">
					Subscriptions Change Log <cfif local.memberSubAuditTrail.totalCount gt 0>(#local.memberSubAuditTrail.totalCount#)</cfif>
				</div>
			</div>
			<div class="card-body p-1">
				<cfif local.memberSubAuditTrail.totalCount gt 0>
					<div id="subAuditResultsShow" class="text-right" <cfif local.subAuditTrailFlag eq 'all'>style="display:none;"</cfif>>
						<input type="button" id="btnShowHistory" class="btn btn-primary btn-sm" onClick="toggleSubATGrid()" value="Show Changes" />
					</div>
					<div id="subAuditResultsHide" class="text-right" <cfif local.subAuditTrailFlag neq 'all'>style="display:none;"</cfif>>
						<input type="button" id="btnHideHistory" class="btn btn-secondary btn-sm" onClick="toggleSubATGrid()" value="Hide Changes" /> <cfif local.memberSubAuditTrail.totalCount neq local.memberSubAuditTrail.foundCount><input type="button" id="btnShowAllHistory" class="btn btn-primary btn-sm" onClick="document.location.href='#buildCurrentLink(arguments.event,"edit")#&memberID=#local.strMember.qryMember.memberid#&tab=subscriptions&subat=all##subAuditT'" value="Show All Changes" /></cfif>
					</div>
					<div class="row mt-3" id="mcg_gridboxSubAT" style="<cfif local.subAuditTrailFlag neq 'all'>display: none;</cfif>">
						<div class="col-md-12">
							<table id="showFullHist" class="table table-sm table-striped">
							<thead>
								<th>Description</th>
								<th>Previous Status</th>
								<th>Activity Date</th>
								<th>Updated By</th>
							</thead>
							<tbody>
								<cfset local.strActMemberInfo = {}>
								<cfloop query="local.memberSubAuditTrail.qryStatusHistory">
									<tr mc-data-subscriberid="#local.memberSubAuditTrail.qryStatusHistory.subscriberID#">
										<td class="align-top">
											#local.memberSubAuditTrail.qryStatusHistory.description#
											<div style="padding-top:2px;">#DateFormat(local.memberSubAuditTrail.qryStatusHistory.subStartDate, "m/d/yy")# - #DateFormat(local.memberSubAuditTrail.qryStatusHistory.subEndDate, "m/d/yy")#</div>
										</td>
										<td class="align-top">#local.memberSubAuditTrail.qryStatusHistory.previousStatus#</td>
										<td class="align-top">#DateTimeFormat(local.memberSubAuditTrail.qryStatusHistory.updateDate, "m/d/yy h:nn tt")#</td>							
										<td class="align-top">#encodeForHTML(local.memberSubAuditTrail.qryStatusHistory.firstname & ' ' & local.memberSubAuditTrail.qryStatusHistory.lastName)# <br/> #DateTimeFormat(local.memberSubAuditTrail.qryStatusHistory.dateRecorded, "m/d/yy h:nn tt")#</td>
									</tr>
								</cfloop>
							</tbody>
							</table>
						</div>
					</div>
				<cfelse>
					<div id="auditResultsDesc" class="col-12 my-2">
						No subscription history recorded.
					</div>
				</cfif>
			</div>
		</div>
	</div>
</div>
</cfoutput>