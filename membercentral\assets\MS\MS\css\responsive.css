@charset "utf-8";
@media print {
    * {
        -webkit-print-color-adjust: exact;
        -moz-print-color-adjust: exact;
        -o-print-color-adjust: exact;
        visibility: visible;
    }
    video,
    audio,
    object,
    embed {
        display: none;
    }
    img {
        max-width: 500px;
    }
    body {
        margin: 1cm auto;
    }
    .printHeader {
        text-align: center;
        padding: 15px;
        display: block;
        width: 100%;
    }
    .printHeader a {
        display: inline-block;
        width: 100%;
        text-align: center;
        margin-top: 10px;
    }
    .printHeader p {
        margin-top: 15px;
    }
    .headerSpace {
        display: none;
    }
    .header,
    .navbar,
    .footer,
    .bgBorder {
        display: none;
    }
    .copyrightText {
        display: block;
        text-align: center;
    }
    .copyrightText ul li {
        color: #000;
    }
    .printFooter {
        width: 100%;
        text-align: center;
        display: block;
        font-size: 13px;
        color: #fff;
        font-weight: 400;
    }
    .container {
        width: 100% !important;
    }
    .span4 {
        width: 33%;
    }
    .captionFrame ul li,
    .captionFrame ul li {
        color: white;
    }
    .captionFrame {
        margin-left: 0;
        max-width: 100%;
        padding: 0 15px;
    }
    .owl-carousel .owl-stage {
        display: block !important;
        transform: none !important;
        width: 100% !important;
        padding: 15px !important;
        height: auto;
        max-width: 100%;
    }
    .slider .owl-carousel .item:before,
    .slider .owl-carousel .item img {
        display: none;
    }
    .captionFrame ul li {
        word-break: break-all;
    }
    .captionBtnBox {
        position: static;
        max-width: 100%;
    }
    .captionBtnFrame {
        position: static;
        background-color: white;
        max-width: 100%;
    }
    .captionBtnBox ul li a {
        background: transparent;
        border: 1px solid #ba0c2f;
        display: block;
        height: 130px;
    }
    .captionBtnBox ul li a .iconBox img.default,
    .arrow img {
        filter: invert(1);
    }
    .captionBtnBox ul li a .textBox h2 {
        color: #2d2d2d;
    }

    .inner-page-content {
        position: relative;
    }
    .inner-page-content .sidebar {
        width: 280px;
        border-right: 1px solid #717171;
    }
    .inner-page-content .inner-content-area {
        padding: 20px;
    }
    .owl-carousel .owl-item.cloned {
        display: none !important;
    }

    .owl-carousel .owl-stage .owl-item {
        width: 100% !important;
    }
    .sbm-icon-card-wrap>ul {
        display: block;
        text-align: center;
        width: 100%;
        max-width: 100%;
    }
    
    .sbm-icon-card-wrap>ul>li {
        display: inline-block;
        width: 270px;
        margin: 0 auto 30px;
        max-width: 270px;
    }
    
    .sbm-iconbox {
        height: auto;
    }
    
    .sbm-icon-card-wrap {
        margin: 0;
    }
    
    .latest-blogs-sec .flex-row>div {
        width: 370px;
        margin: 0 auto 30px !important;
    }
    
    .flex-row {
        display: block;
    }
    
    .about-tflf-box {
        display: block;
        max-width: 100%;
    }
    
    .about-sec .inner-sec-wrap .fixed-bg {
        display: none;
    }
    
    .about-tflf-box .left-box {
        min-height: auto !important;
        padding: 0;
    }
    
    .about-tflf-box .left-box:before, .about-tflf-box .left-box:after {
        display: none;
    }
    .slider .owl-carousel .item img {
        display: none;
    }
    
    .carousel-caption {
        min-height: auto;
    }

.span12 {
    width: 100%;
}
.inner-page-content>div>.row-fluid>div {flex: 0 0 100%;max-width: 100%;width: 100%;}

}

@media screen and (min-width: 1300px) {
    .container {
        max-width: 1170px;
        width: 100%;
    }
}

@media screen and (min-width: 1800px) {
    .navbar .container {
        width: 100%;
        padding: 0px;
    }
}
@media screen and (min-width: 980px) {
    .nav-collapse.collapse {
        margin: 0 -15px 0;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu {
        position: absolute;
        width: 100%;
        max-width: 1920px;
        visibility: visible;
        background: #ffffff;
        opacity: 0.96;
        top: 100%;
        min-width: 200px;
        padding: 2px;
        border: none;
        display: block;
        visibility: hidden;
        opacity: 0;
        -moz-transition: top 0.5s ease 0s, visibility 0s ease 0s;
        -ms-transition: top 0.5s ease 0s, visibility 0s ease 0s;
        -o-transition: top 0.5s ease 0s, visibility 0s ease 0s;
        -webkit-transition: top 0.5s ease 0s, visibility 0s ease 0s;
        transition: top 0.5s ease 0s, visibility 0s ease 0s,
            z-index 0s ease 0.1s;
        z-index: 9;
        box-shadow: 0 2px 5px -3px rgba(0, 0, 0, 0.47);
    }
    /* .header .nav-collapse .nav .dropdown .dropdown-menu li {
        display: table;
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
    } */
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu
        li.droplist-1.textUl
        ul {
        padding-left: 0;
        display: block;
        vertical-align: middle;
        position: relative;
        list-style: none;
    }
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu
        li.droplist-1.cta
        ul
        li:first-child {
        float: left;
        margin-right: 45px;
    }
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu
        li.droplist-1.cta
        ul
        li
        p {
        margin-bottom: 15px;
        font-size: 16px;
    }

    .header .navbar .nav > li.dropdown:hover > .dropdown-menu {
        display: block !important;
        visibility: visible !important;
        z-index: 9;
        opacity: 1 !important;
        margin: 0;
        list-style: none;
    }
    .navbar .nav li.dropdown > .dropdown-toggle .caret {
        border-top-color: #eeeeee;
        border-bottom-color: #eeeeee;
    }
    .navbar .nav li.dropdown:hover > .dropdown-toggle .caret {
        border-top-color: #006eb3;
        border-bottom-color: #006eb3;
    }
    .header .nav li .dropdown-menu > li.dropdown-submenu a:hover .caret {
        border-top: 4px solid #000;
    }
    .navbar
        .nav
        li.dropdown
        .dropdown-menu
        .dropdown-submenu
        .dropdown-menu:before {
        display: none;
    }
    .header .dropdown-submenu li {
        padding: 0 20px;
    }
    .dropdown-submenu .dropdown-menu {
        padding: 20px 0;
    }
    .header .dropdown-submenu .dropdown-menu {
        background: #44687d;
    }
    .dropdown-submenu > .dropdown-menu {
        display: block !important;
        margin-left: -1px;
        left: 70%;
        opacity: 0;
        visibility: hidden;
        border-radius: 0;
        overflow: hidden;
    }
    .dropdown-submenu:hover > .dropdown-menu {
        display: block !important;
        left: 100%;
        visibility: visible;
        -moz-transition: all 0.3s ease 0s;
        -ms-transition: all 0.3s ease 0s;
        -o-transition: all 0.3s ease 0s;
        -webkit-transition: all 0.3s ease 0s;
        transition: all 0.3s ease 0s;
        opacity: 1;
    }
    .header .dropdown:hover>.dropdown-menu {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    .blockquote-row > div:first-child {
        padding-right: 25px;
    }
    .blockquote-row > div:last-child {
        padding-left: 25px;
    }

    .mobile-contactus {
        display: none;
    }
}
@media screen and (max-width: 1800px) {
    .navbar .container {
        max-width: 1170px;
        padding: 0;
    }
}
@media screen and (max-width: 1600px) {
    .navbar .container {
        padding: 0;
    }
    .dropdown-menu {
        width: 200px;
    }

    .header .navbar .nav li.dropdown .droplist-1 .searchHeading {
        max-width: 280px;
    }
    .sliderFrame .item ul li {
        padding-right: 60px;
    }
    .footer .row-fluid {
        padding: 0;
    }
    .sliderFrame {
        padding: 0;
    }
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu.memberSection
        li.droplist-1:last-child {
        padding-right: 0px;
    }
    blockquote,
    blockquote.pull-right {
        font-size: 28px;
    }

    .header .navbar .nav>li:last-child .droplist-1 .droplist-2 {
        left: -100%;
    }
    .header .navbar .nav>li:last-child .dropdown-menu {
        left: auto;
        right: 0
    }
    
}

@media screen and (max-width: 1399px) {
    .captionFrame h1 {
        font-size: 36px;
    }
    .header .navbar .nav li.dropdown .droplist-1 .searchHeading {
        max-width: 250px;
    }
    .header .navbar .nav > li.searchBtnFn a img {
        height: auto;
        width: 22px;
    }
    .header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder {
        min-width: 110px;
    }

}
@media screen and (max-width: 1330px) {
    .header .navbar .nav li > a {
    }
    .header .navbar .nav > li.searchBtnFn > a {
        padding: 28px 15px 28px 10px;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu {
    }
    .header .navbar .nav li.dropdown .droplist-1 .heading {
        left: 0;
    }
    .header .navbar .nav li.dropdown .droplist-1 .HeaderText {
        width: 360px;
    }
    .searchnav-logo {
        max-width: 200px;
        padding: 35px 0 35px 20px;
        height: 105px;
    }
    .header
        .nav-collapse
        .nav
        .searchBtnFn.dropdown
        .dropdown-menu
        li.droplist-1 {
        width: 200px;
    }
    .nav-member-center {
        width: 180px;
        padding: 10px;
    }
    .nav-member-center p {
        font-size: 14px;
    }
    .header
        .nav-collapse
        .nav
        .searchBtnFn.dropdown
        .dropdown-menu
        li.droplist-1.member-center-wrap:last-child {
        max-width: 180px;
    }
    .TitleText {
        /* font-size: 38px; */
    }
    .slider .owl-carousel .item {
        padding: 30px 0;
    }
    body .header .navbar .nav > li.searchBtnFn.dropdown > ul.dropdown-menu {
    }
}

@media screen and (max-width: 1199px) {
    .inner-page-content .sidebar {
        flex: 0 0 400px;
        width: 400px;
    }
    .inner-page-content .inner-content-area {
        flex: 0 0 calc(100% - 400px);
        max-width: calc(100% - 400px);
        padding: 50px 100px 0 40px;
    }
    .TitleText {
        font-size: 45px;
    }
    blockquote,
    blockquote.pull-right {
        font-size: 20px;
        padding: 40px;
        margin-top: 30px;
        margin-bottom: 30px;
    }
    blockquote:before,
    blockquote:after {
        width: 56px;
        height: 56px;
        background-size: 40px 34px;
    }
    blockquote:before {
        left: 14px;
        top: -30px;
    }
    blockquote:after {
        right: 14px;
        bottom: -30px;
    }
    blockquote.pull-right:after {
        left: 14px;
    }
    blockquote.pull-right:before {
        right: 14px;
    }

    .captionBtnBox ul li a .iconBox img {
        width: 40px;
        height: 40px;
    }
    .captionBtnBox ul li a .iconBox {
        width: 50px;
    }
    .captionFrame ul li:nth-child(1) {
        font-size: 14px;
    }
    .info-iconbox img {
        width: 50px;
        height: 50px;
        padding: 0;
    }

    .info-iconbox h2 {
        font-size: 30px;
        min-height: 40px;
    }
    .newscard .news-inner-wrap {
        padding-left: 120px;
    }

    .newscard .news-inner-wrap img {
        left: 30px;
        width: 70px;
        height: 70px;
        object-fit: contain;
    }

    .info-iconbox .iconlink {
        width: 50px;
        height: 50px;
        font-size: 30px;
    }

    body .header .navbar .nav > li.searchBtnFn.dropdown > ul.dropdown-menu {
    }
    .captionFrame {
        max-width: 80%;
    }
    .captionFrame h3 {
        font-size: 20px;
    }
    .captionFrame h1 {
        font-size: 36px;
        margin-bottom: 20px;
    }
    .captionBtnFrame {
        max-width: 351px;
        padding: 25px 15px;
    }
    .captionBtnBox ul li a {
        padding: 10px 15px;
        min-height: 80px;
    }
    .captionBtnBox ul li a .textBox {
        left: 80px;
        max-width: 180px;
    }
    .captionBtnBox ul li a .textBox h2 {
        font-size: 18px;
    }

    .HeaderText {
        font-size: 30px;
    }
    .BodyTextLarge {
        font-size: 14px;
    }

    .header .nav-collapse .nav {
        /*width: 734px;*/
        /* width: auto; */
        padding-right: 0;
    }
    .header .navbar .nav>li {
    }
    .header .navbar .nav li > a {
        font-size: 14px;
    }
    .header .navbar .nav > li.searchBtnFn > a {
        padding: 40px 30px 40px 5px;
    }
    .header .navbar .nav li .droplist-1 a {
        height: auto;
    }

    .header .navbar .nav li.dropdown .memberSection li,
    .header .navbar .nav li.dropdown .memberSection li p,
    .header .navbar .nav li.dropdown .memberSection li a {
        font-size: 14px;
    }
    .header .navbar .nav li.dropdown .memberSection li label {
        font-weight: 300;
        font-size: 14px;
        letter-spacing: 0.2px;
    }
    .header .navbar .nav li.dropdown .memberSection li input,
    .header .navbar .nav li.dropdown .memberSection li form a.btn,
    .header .navbar .nav li.dropdown .droplist-1 .heading .btn {
        height: 40px;
        line-height: 36px;
    }
    .header .navbar .nav li.dropdown .memberSection li form a {
        width: 100%;
    }
    .header .navbar .nav li.dropdown .droplist-1 .HeaderText {
        font-size: 24px;
    }
    .header .navbar .nav li.dropdown .droplist-1 .heading .TitleText {
        font-size: 24px;
    }
    .header .navbar .nav > li.dropdown:last-child:hover:hover > a::after,
    .header .navbar .nav > li.dropdown:last-child:hover:focus > a::after,
    .header .navbar .nav > li.dropdown:last-child:hover:visited > a::after {
    }
    .header .navbar .nav li.dropdown .droplist-1 li a {
        font-size: 14px;
    }
    .header .navbar .nav li.dropdown .droplist-1 .searchHeading {
        max-width: 180px;
    }
    .header .navbar .nav li.dropdown .droplist-1 .formframe {
        padding: 0;
    }
    .header .navbar .nav li.dropdown .droplist-1 .formframe input {
        height: 40px;
        padding: 0 15px 0 40px;
        background-position: left 15px center;
    }
    .header .navbar .nav li.dropdown .droplist-1 .formframe a {
        height: 40px;
        line-height: 36px;
        padding: 0 25px;
    }
    .header .navbar .nav li.dropdown .memberSection li form a:last-child {
        margin-left: 0px;
        margin-top: 10px;
        color: #ffffff;
    }
    .header .navbar .container,
    .container {
        width: 980px;
    }
    .header {
        min-height: 80px;
    }
    .headerSpace {
        height: 100px;
    }

    .header .navbar-brand {
        /* max-width: 65px; */
        /* padding: 0; */
    }
    body .header .navbar .nav > li.searchBtnFn.dropdown > ul.dropdown-menu {
        width: calc(100% - 498px);
        right: 220px;
    }
    .header .navbar .nav > li:nth-last-child(2) > a {
    }
    .header .navbar .nav li:nth-last-child(2) a img {
        margin-bottom: 2px;
    }
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu
        li.droplist-1.cta
        ul
        li:first-child {
        margin-left: 15px;
    }
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu
        li.droplist-1.cta
        ul
        li:first-child
        img {
        max-width: 200px;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu {
    }
    .header .navbar .nav li.dropdown .droplist-1 .HeaderText {
        width: 180px;
    }
    .header .navbar .nav li.dropdown .droplist-1 .heading {
        left: inherit;
    }
    .header .navbar .nav>li.dropdown>a {
    padding-right: 15px;
    }
    ul.follow-us li:first-child {
        margin-right: 10px;
    }
    /****************/
    .inner-page-content .sidebar {
        width: 350px;
    }
    .inner-page-content .inner-content-area {
        padding: 50px 50px 0;
    }
    header .navbar .nav li.headerlogin {
        width: 150px;
        padding: 0 10px;
        min-height: 100px;
    }
    .header-drop-title {
        width: 34%;
        padding-right: 20px;
    }

    .droplist-2Mob {
        width: 66%;
    }
  
    .footer {
        padding: 50px 15px 00px;
    }

    .footer-links ul.social-list li {
        margin-right: 15px;
    }
    .footer-links h3,
    .contact-links h3 {
        font-size: 16px;
    }
    img.footlogo {
        width: 350px;
    }
    ul.social-list li {
        margin-right: 10px;
    }
    .sbm-iconbox {
        padding: 30px 20px;
    }
    .header .navbar .nav>li.top-btns {
        margin-left: 5px;
    }
    .header .navbar .nav>li.dropdown>a:after {
        width: 5px;
        height: 5px;
        position: absolute;
        right: 2px;
        left: auto;
        top: 13px;
    }
    .top-btn-wrap>a {
        /* font-size: 14px; */
        padding: 5px 10px;
    }
    .top-btn-wrap {
        gap: 10px;
    }
    .captionFrame ul li:nth-child(2) {
        font-size: 24px;
    }
    .captionFrame .MAJButton {
        min-width: 200px;
    }
    .slider .owl-carousel .item, .carousel-caption {
        min-height: 450px;
    }
    .testimonial-slider.owl-carousel .owl-nav button.owl-next,
    .sponsorSlider .owl-carousel .owl-nav button.owl-next,
    .upcoming-slider.owl-carousel .owl-nav button.owl-next {
        right: 0;
    }
    
    .testimonial-slider.owl-carousel .owl-nav button.owl-prev,
    .sponsorSlider .owl-carousel .owl-nav button.owl-prev,
    .upcoming-slider.owl-carousel .owl-nav button.owl-prev {
        left: 0;
    }
    
    .testimonial-slider,
    .sponsorSlider .owl-carousel,
    .upcoming-slider {
        padding: 0 50px;
    }
    .sbm-icon-card-wrap>ul>li {
        flex: 0 0 33.33%;
        max-width: 33.33%;
    }
    
    .sbm-icon-card-wrap {
        margin: 0;
    }
    .sponsors-sec .container,
    .latest-blogs-sec .container,
    .member-highlight-sec .container,
    .find-layer-sec .container,
    .quick-links-sec .container {
        width: 100%;
        padding: 0 15px;
    }
    .quick-links-sec .span12 {
        width: 100%;
        margin: 0;
        padding: 0 15px;
    }
    
    .quick-links-sec .row {
        margin: 0 -15px;
    }
    .sbm-iconbox .SubHeading {
        margin-left: 0;
        margin-right: 0;
    }
    ul.toplist li:not(:first-child) {
        margin-left: 20px;
    }
    .footer .row.d-flex-wrap>div.col3 {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .footer .container {
        padding: 0 0px;
        margin: 0;
        width: 100%;
    }
    .border-card-box {
        padding: 15px 40px 00px 40px;
    }
    .border-card-bottom {
        padding-left: 40px;
    }
    .border-card-box:before, .border-card-box:after {
        width: 40px;
        height: 40px;
    }
    .about-tflf-box {
        padding: 40px 30px;
    }
    .SectionHeader {
        font-size: 35px !important;
        margin-bottom: 20px !important;
    }
}
/* 1199px */

@media (min-width: 980px) and (max-width: 1050px) {
    body .header .navbar .nav > li.searchBtnFn.dropdown > ul.dropdown-menu {
        width: calc(100% - 470px);
    }


    .header .navbar .nav li > a {
        font-size: 14px;
    }


    header .navbar .nav li.headerlogin,
    .header {
        min-height: 100px;
    }

    .headerSpace {
        height: 100px;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu {
    }
}

@media screen and (max-width: 979px) {
    header .navbar .nav li.headerlogin.show-form .nav-member-center {
        display: none !important;
    }

    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu.memberSection
        li.droplist-1.member-boxthree {
        width: 100%;
        padding: 15px;
        background: #ee3a43;
    }

    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu.memberSection
        li.droplist-1.member-boxone,
    .header
        .nav-collapse
        .nav
        .dropdown
        .dropdown-menu.memberSection
        li.droplist-1.member-boxtwo {
        display: none !important;
        background: transparent !important;
        padding: 0 !important;
    }

    .header .nav-collapse .nav .dropdown .dropdown-menu.memberSection {
        background: #ee3a43;
        padding: 0 !important;
        position: relative;
    }

    .header .navbar .nav li .memberSection a.toggle-form {
        font-size: 0;
        line-height: 1;
        padding: 0 2px 0 0;
        top: 5px;
        right: 5px;
        background: #ffffff;
        color: #ee3a43;
        display: inline-flex;
        width: 30px;
        height: 30px;
        align-items: center;
        justify-content: center;
    }

    .header .navbar .nav li .memberSection a.toggle-form i {
        font-size: 20px;
        margin: 0;
    }

    header .navbar .nav li.headerlogin span.menu-arrow {
        display: none;
    }
    .RedButton,
    .header .navbar .nav li.dropdown .memberSection a.RedButton {
        align-self: self-end;
    }

    header .navbar .nav li.headerlogin a.nav-member-center {
        background: #ee3a43;
        display: inline-flex;
        flex-wrap: nowrap;
        flex-direction: row;
        align-items: center;
        width: auto;
        height: auto;
        padding: 10px 10px;
        min-width: 250px;
        border-radius: 50px;
    }

    header .navbar .nav li.headerlogin a.nav-member-center p {
        margin: 0;
        padding-left: 15px;
    }

    header .navbar .nav li.headerlogin a.nav-member-center:after {
        display: none !important;
    }

    header .top-strip {
        display: none;
    }
    .headerSpace {
        height: 80px;
    }
    .header {
        background: #ffffff;
    }
    .inner-page-content > .row-fluid {
        display: block;
    }
    .droptitle {
        display: none;
    }
    .footer .row.d-flex-wrap > div.col2,
    .footer .row.d-flex-wrap > div.col3 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-left: 0;
        padding-left: 15px;
        padding-right: 15px;
    }
    .loggedinBox {
        width: 100%;
        max-width: 100%;
        padding-right: 0;
    }

    .row.d-flex-wrap:before,
    .row.d-flex-wrap:after {
        display: none;
    }

    .footer .row.d-flex-wrap > div.col1 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-left: 0;
    }
    .footer .footer-links,
    .footer .footer-info {
        padding: 0px 0;
    }

    .infoicon-sec .flex-row .col-6 {
        flex: 0 0 100%;
        -webkit-flex: 0 0 100%;
        max-width: 100%;
    }

    .row.d-flex-wrap {
        margin-left: -15px !important;
        margin-right: -15px !important;
    }
    .sbm-icon-card-wrap>ul>li {
        flex: 0 0 50%;
        max-width: 50%;
        -webkit-flex: 0 0 50%;
        margin-bottom: 30px;
    }
    
    .col1.footer-info:before {
        width: 100vw;
        left: 50%;
        right: auto;
        transform: translateX(-50%);
        margin-left: 0;
    }

    .footer .row.d-flex-wrap > div.col1 .footstar {
        width: 30vw;
    }

    .header .navbar .nav > li.mobile-links ul.dropdown-menu {
        display: flex !important;
        background: #ffffff !important;
        flex-wrap: wrap;
        justify-content: center;
    }

    .header .navbar .nav > li.mobile-links span.menu-arrow {
        display: none !important;
    }

    .header .navbar .nav > li.mobile-links ul.dropdown-menu a {
        padding: 10px !important;
        height: auto !important;
        display: inline-block;
        color: #ed3943 !important;
        font-size: 12px !important;
        border-style: none !important;
        height: auto !important;
        min-height: auto !important;
        line-height: 1.4;
    }

    .header .navbar .nav > li.mobile-links ul.dropdown-menu li {
        flex: 0 0 auto;
        width: auto;
        margin: 0;
        border-style: none;
        padding: 0;
    }

    .header
        .navbar
        .nav
        > li.mobile-links
        ul.dropdown-menu
        li:not(:first-child):before {
        content: "/";
        color: #d1d1d1;
    }

    .event-mobile .sbm-event {
        border-top: 1px solid #ebebeb;
        padding: 15px 0px;
        margin-bottom: 0;
        border-radius: 0;
        text-align: center;
    }
    .captionBtnBox ul li a .iconBox img {
        width: 30px;
        height: 30px;
    }
    .event-mobile .event-list {
        padding-top: 15px;
    }
    .event-mobile .event-list .sbm-event .sbm-e-head span {
        min-width: auto;
    }
    .event-mobile .event-list .sbm-event .sbm-e-head span:after {
        margin: 0 20px;
        position: relative;
    }
    .event-mobile .event-list .sbm-event .sbm-e-head {
        justify-content: center;
    }
    .header-member-form {
        display: none !important;
    }
    header .navbar .nav li.headerlogin {
        width: 100%;
        max-width: 100%;
        display: block;
        background: transparent;
        text-align: center;
        min-height: auto;
        border-bottom: none;
        padding-top: 30px;
        padding-bottom: 10px;
    }
    .home3 header .navbar .nav li.headerlogin,
    .home2 header .navbar .nav li.headerlogin {
        background: transparent;
    }
    header .navbar .nav li.headerlogin a.member-center-btn {
        text-align: center;
        display: inline-block;
        background: #a8462b;
        color: #ffffff;
        width: auto;
        margin: 0 auto;
        padding: 5px 40px !important;
        border-radius: 50px;
        min-height: auto !important;
    }
    .member-center-btn {
        display: block;
    }
    header .navbar .nav li.headerlogin a.member-center-btn img {
        width: 35px;
        height: 35px;
        margin-right: 10px;
    }
    .header-drop-title {
        display: none;
        padding: 0;
        width: 100%;
        padding: 15px 0;
    }

    .droplist-2Mob {
        display: block;
        width: 100%;
        background: transparent;
    }

    .droplist-2Mob .droplist-2Mob-col {
        display: block;
        width: 100%;
        padding: 0;
        float: none;
    }
    .droplist-2Mob .droplist-2Mob-col ul {
        margin: 0;
    }
    .header .navbar .nav li.dropdown .dropdown-menu li a {
        color: #3b3b3c;
        font-size: 18px;
    }
    .header .navbar .nav li.dropdown .dropdown-menu li a:hover {
    }
    .footer-links ul.social-list li {
        margin-right: 9px;
    }
    .footer-links h3,
    .contact-links h3 {
        /* font-size: 20px; */
    }

    img.footlogo {
        max-width: 70%;
        width: 130px;
    }
    .infoicon-sec .flex-row .col-3 {
        flex: 0 0 50%;
        -webkit-flex: 0 0 50%;
        max-width: 50%;
        margin-bottom: 30px;
    }

    .info-iconbox h2 {
        min-height: auto;
    }
    .header .navbar .nav li.open-droupdown a,.header .navbar .nav li.open-droupdown:hover a {
    background: #aabbc7;
    color: #ffffff;
    }
    .captionFrame ul li:nth-child(2) {
    }
    .whats-new-sec .flex-row > div.span8 {
        padding-left: 15px;
    }
    .whats-new-sec .flex-row > div.span8 {
        padding-left: 15px;
    }

    .newscard .news-inner-wrap {
        padding: 30px 30px 30px 100px;
    }

    .newscard .news-inner-wrap img {
        width: 50px;
        left: 15px;
    }

    .newscard .news-inner-wrap h2 {
        font-size: 25px;
        margin: 0 0 5px;
    }

    .magazine-block h2 {
        font-size: 25px;
        line-height: 1.5;
        margin-top: 20px;
    }
    .latest-blogs-sec .flex-row > div {
        width: 33.33%;
    }
    .img-card .img-holder span {
        width: 40px;
        height: 40px;
        font-size: 28px;
    }

    .img-card .img-holder span small {
        font-size: 16px;
    }
    .captionFrame ul li:nth-child(1) {
        margin: 0 0 10px;
    }

    .anouncebanner {
        display: block;
    }
    .droplist-2MobBtn {
        cursor: pointer;
        display: inline-block;
        font-size: 20px !important;
    }
    .droplist-2OnclickBtn {
        display: none;
        padding-left: 20px;
    }
    .droplist-1.closeBox ul.droplist-2Onclick {
        display: none !important;
    }
    .dropdown-menu > .droplist-1 {
        margin-left: 0px;
    }
    .header .navbar .nav li.memberFirst {
        padding: 0;
        margin: 20px 0 0 0;
        padding: 0 30px 20px 30px;
        background: #fff;
    }
    .header .navbar .nav li > .dropdown-menu>li {
        padding: 0px 0px 0px 40px;
    }
    .header .navbar .nav li > .dropdown-menu .dropdown-menu {
        margin-top: 1px;
    }
    .header .navbar .nav li.memberFirst > a {
        background: #0bba97;
        padding: 5px 20px;
        text-align: center;
        font-size: 14px;
        text-transform: uppercase;
    }
    .header .navbar .nav li.memberFirst .dropdown-menu li p a {
        padding: 0px;
        height: auto;
    }
    .header .navbar .nav li.memberFirst > a > img {
        margin-right: 20px;
    }
    .header .navbar .nav li.memberFirst > .dropdown-menu {
        margin: 0;
        padding: 0 !important;
        background: #2d2d2d;
    }
    .header .navbar .nav li.memberFirst > .dropdown-menu .droplist-1 {
        padding: 20px;
        background: #2d2d2d;
    }
    .header .navbar .nav li.memberFirst > .dropdown-menu .droplist-1.formDiv {
        padding: 0 20px 20px 20px !important;
    }
    .header .navbar .nav li.memberFirst.open-droupdown > a,
    .header .navbar .nav li.memberFirst.open-droupdown:hover > a,
    .header .navbar .nav li.memberFirst.open-droupdown:focus > a,
    .header .navbar .nav li.memberFirst.open-droupdown:visited > a {
        background-color: #ba0c2f;
        color: #fff;
    }
    .header .navbar .nav li.memberFirst.open-droupdown .menu-arrow {
        display: block;
        width: 100%;
        left: 0;
        height: 86px;
        top: 0px;
        opacity: 1;
        transform: none;
        text-align: center;
    }
    .header .navbar .nav li.memberFirst.open-droupdown .menu-arrow:after {
        color: #fff !important;
        left: 10px;
    }
    .header .navbar .nav li.memberFirst > .menu-arrow {
        top: 20px;
    }
    .social-mobile,
    .mobile-links {
        display: block;
    }
    .header .navbar .nav > li.social-mobile.dropdown {
        background: #fff;
        padding: 0px 0 30px 0;
        text-align: center;
    }
    .header .navbar .nav > li.social-mobile.dropdown .follow-us.dropdown-menu {
        display: block !important;
        padding: 20px 0 0 !important;
        background: #ffffff;
    }
    .header
        .navbar
        .nav
        > li.social-mobile.dropdown
        .follow-us.dropdown-menu
        .droplist-1 {
        font-size: 20px;
        font-weight: 600;
        display: inline-block;
        vertical-align: middle;
        width: auto;
        margin: 0 2px;
    }
    .header
        .navbar
        .nav
        > li.social-mobile.dropdown
        .follow-us.dropdown-menu
        .droplist-1:first-child {
        width: 100%;
        margin: 0 0 20px 0;
    }
    .header
        .navbar
        .nav
        > li.social-mobile.dropdown
        .follow-us.dropdown-menu
        .droplist-1
        a {
        width: 40px;
        height: 40px;
        color: #083372;
        line-height: 40px;
        font-size: 20px;
        text-align: center;
        border-radius: 50%;
        padding: 0;
    }
    .header
        .navbar
        .nav
        > li.social-mobile.dropdown
        .follow-us.dropdown-menu
        .droplist-1
        a:hover,
    .header
        .navbar
        .nav
        > li.social-mobile.dropdown
        .follow-us.dropdown-menu
        .droplist-1
        a:focus {
        border-color: #709ed1;
        color: #ffffff;
        background: #709ed1;
    }
    .header .navbar .nav > li.social-mobile.dropdown .menu-arrow {
        display: none;
    }
    .header .navbar .nav > li:nth-last-child(2) > a {
        width: 100%;
    }
    .TitleText {
    }
    .HeaderTextMediumLink {
        color: #008e89;
        font-size: 16px;
        text-decoration: none;
    }
    .HeaderTextSmall {
        font-size: 20px;
    }

    .HeaderTextMedium {
        font-size: 18px;
    }
    .HeaderTextMediumLink {
        font-size: 16px;
    }
    .captionBtnBox ul li a .iconBox {
        margin: 0px 0px;
    }
    .captionBtnFrame {
        max-width: 320px;
        padding: 25px 10px;
    }
    .captionBtnBox ul li a .arrow {
        float: right;
        padding: 9px 0px;
    }
    .captionBtnBox ul li a .textBox h2 {
        font-size: 16px;
    }
    .captionFrame {
        max-width: 370px;
        margin-left: 30px;
    }
    .captionFrame h1 {
        font-size: 28px;
        margin-top: 10px;
    }
    .captionFrame h3 {
        font-size: 18px;
    }
    .slider .owl-carousel .item img {
        height: 405px;
        object-fit: cover;
    }
    .slider .owl-carousel .owl-dots {
        bottom: 30px;
    }

    .xsHidden979 {
        display: none !important;
    }
    .header .navbar .nav > li {
        padding: 0 30px;
    }
    .xs979 {
        display: block !important;
    }
    .header .navbar .nav .searchBtnFn.xs979 {
        margin: 0px;
        padding: 0;
        margin-bottom: 0px;
    }
    .header .navbar .nav .searchBtnFn.xs979 ul.dropdown-menu {
        display: block !important;
        padding-left: 0px !important;
        padding: 0 !important;
    }
    .header .navbar .nav li.dropdown.searchBtnFn.xs979 .droplist-1 .formframe {
        display: block;
        vertical-align: top;
        width: 100%;
        margin: 0;
        background: #f3f3f3;
        height: 60px;
        padding: 10px;
        border-radius: 0;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        form {
        display: inline-block;
        width: 100%;
        margin: 0;
        padding: 0;
    }
    .header .navbar .nav li.dropdown.searchBtnFn.xs979 .menu-arrow {
        display: none;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        input {
        color: #33383a;
        font-size: 18px;
        font-weight: 400;
        background: #f3f3f3;
        font-family: "Open Sans";
        padding: 5px 15px;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        a {
        border: 0;
        margin: 0;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        a
        img {
        margin: 0 auto;
        margin-top: 0px;
        padding: 0;
        width: 20px;
        height: 20px;
        line-height: 35px;
        float: none;
        margin-top: 0;
    }

    .header .navbar .nav > li.dropdown > a.dropdown-toggle:after,
    .header .navbar .nav > li.dropdown:hover > a::after,
    .header .navbar .nav > li.dropdown:focus > a::after,
    .header .navbar .nav > li.dropdown:visited > a::after,
    .header .navbar .nav > li.dropdown:hover > a::after,
    .header .navbar .nav > li.dropdown:focus > a::after,
    .header .navbar .nav > li.dropdown:visited > a::after {
        content: "";
        top: 16px;
        left: auto;
        right: 15px;
        position: absolute;
    }
    .header .navbar-brand {
        padding: 5px 5px 5px 0px;
        line-height: 60px;
        position: relative;
        z-index: 2;
        max-width: 150px;
        left: 60px;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        a {
        background: #c5b081;
        border-radius: 50%;
        color: #fff;
        padding: 4px 10px;
        border: 0;
        margin: 0;
        line-height: normal;
        height: auto;
        position: relative;
        top: 0;
        width: 40px;
        height: 40px;
        display: inline-flex;
        align-items: center;
    }

    .btn.btn-navbar {
        min-width: auto;
    }
    .navbar .btn-navbar .icon-bar {
        width: 38px;
        margin: 0px auto 4px;
        height: 6px;
        border-radius: 2px;
    }
    .navbar .nav > li {
        width: 100%;
    }
    .header .nav-collapse.collapse {
        margin: 0;
        background: #fff;
        opacity: 1;
        position: absolute;
        top: 80px;
        width: 100%;
    }
    .header .nav-collapse.collapse .nav {
        padding: 0px;
    }
    .header
        .nav-collapse
        .nav
        .searchBtnFn.dropdown
        .dropdown-menu
        li.droplist-1.formDiv {
        width: 100%;
    }
    .header .navbar .nav > li {
        max-width: 100%;
        height: auto;
        position: relative;
        width: 100%;
        vertical-align: top;
        padding: 0;
        min-height: auto;
    }
    .header .navbar .nav li a,
    .header .navbar .nav li a,
    .header .navbar .nav li .dropdown-menu > li > a {
    }
    .header .navbar .nav li.dropdown .droplist-1 .HeaderText {
        display: none;
        width: 100%;
    }
    .header .navbar .nav {
        position: relative;
    }
    .header .navbar .nav > li > a {
        margin: 0;
        padding: 0;
        border: 0px solid;
        background-color: transparent;
        height: auto;
    }
    .header .navbar .nav li:last-child a img {
        margin-right: 20px;
        margin-bottom: 0px;
        margin-top: -5px;
    }
    .header .navbar .nav li:last-child {
        position: relative;
        border: 0;
    }
    .brand {
        margin-left: -45px;
        max-width: 250px;
    }
    .header .navbar .container {
        width: 750px;
    }
    .container {
        width: 750px;
    }
    .navMain {
        float: none;
        height: 40px;
        padding: 0;
        text-align: center;
    }
    .header .navbar-inner {
        width: 100%;
        border-bottom: none;
    }
    .header {
        border-bottom: 5px solid #C6B182;
    }
    .nav > .dropdown {
        padding-bottom: 0;
    }
    .navbar .btn-navbar .icon-bar {
        width: 30px;
        margin: 0px auto 4px;
        height: 4px;
        border-radius: 3px;
    }
    .navbar .btn-navbar .icon-bar:last-child {
        margin-bottom: 0;
    }
    .dropdown-menu {
        width: 100%;
    }
    .header .nav-collapse {
        float: none;
        padding: 0;
        width: 100%;
        z-index: 99;
        max-height: calc(100vh - 80px);
        overflow-y: auto;
    }
    .header .nav-collapse li {
        display: block;
        width: 100%;
        padding-bottom: 0px;
    }
    .header .navbar .nav li > a {
        border: none !important;
        margin: 0;
    }
    .header .navbar .nav li .dropdown-menu > li > a {
        padding: 15px 15px;
        font-size: 13px;
    }
    .header .navbar .btn-navbar {
        margin: 0;
        position: absolute;
        left: 15px;
        top: 15px;
        background: none;
        border: none;
        -moz-border-radius: 4px;
        -ms-border-radius: 4px;
        -o-border-radius: 4px;
        -webkit-border-radius: 4px;
        border-radius: 4px;
        box-shadow: none;
        line-height: 1.42857;
        margin: 0;
        padding: 10px 12px;
        z-index: 9999;
    }
    .header .navbar .btn-navbar.collapsed {
        border-radius: 2px;
        color: #ffffff;
        padding: 27px 0px;
        height: auto;
        line-height: normal;
        margin-right: 0;
        margin-top: 0;
        width: 30px;
        z-index: 100;
        text-align: center;
        border-radius: 3px;
        top: 0;
        margin: 0;
    }
    .header .navbar .btn-navbar {
        top: 7px;
        padding: 0;
        margin: 30px 0;
    }
    .header .navbar-inner {
        position: relative;
        top: 0;
        width: 750px;
        margin: 0 auto;
        background: transparent;
        box-shadow: none;
    }
    .navIcon {
        background: #0c1923;
        min-height: 52px;
        z-index: 9;
        width: 100%;
    }
    .header .navbar .btn-navbar .icon-bar {
        background: #043b56 !important;
        box-shadow: none;
    }
    .header .navbar .btn-navbar.collapsed .icon-bar {
        width: 30px;
        height: 4px;
        border-radius: 0;
        background: #043b56 !important;
        opacity: 1;
    }
    .header .navbar .btn-navbar .icon-bar:first-child {
        transform: rotate(45deg);
    }
    .header .navbar .btn-navbar .icon-bar:nth-child(2) {
        display: none;
    }
    .header .navbar .btn-navbar .icon-bar:last-child {
        transform: rotate(-45deg);
        margin-top: -8px;
    }
    .header .navbar .btn-navbar.collapsed .icon-bar:first-child,
    .header .navbar .btn-navbar.collapsed .icon-bar:nth-child(2),
    .header .navbar .btn-navbar.collapsed .icon-bar:last-child {
        transform: none;
        display: block;
        margin-top: 0;
    }
    .header .navbar .btn-navbar:hover .icon-bar {
        background: #043b56;
    }
    /*.header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled], .header .navbar .btn-navbar:hover, .header .navbar .btn-navbar:focus, .header .navbar .btn-navbar:active, .header .navbar .btn-navbar.active, .header .navbar .btn-navbar.disabled, .header .navbar .btn-navbar[disabled], .header .navbar .nav li .dropdown-menu>li>a:hover, .header .navbar .nav li .dropdown-menu>li:hover a { background: #fff; border-color: #fff; }*/
    .header .navbar .nav li > a,
    .header .navbar .nav li .dropdown-menu > li > a {
        text-align: center;
        color: #3b3b3c;
        border-radius: 0;
    }
    .header .navbar .nav li > a,
    .header .navbar .nav li > a,
    .header .navbar .nav li .dropdown-menu > li > a {
        font-size: 18px;
        border: none;
        border-top-width: medium;
        border-bottom-width: medium;
        border-top-style: none;
        border-bottom-style: none;
        border-top-color: currentcolor;
        border-bottom-color: currentcolor;
        border-bottom-width: medium;
        border-bottom-style: none;
        border-bottom-color: currentcolor;
        border-top: 0px solid rgba(255, 255, 255, 0.5);
        background: transparent;
        font-weight: 400;
        line-height: 1.42857;
        color: #3b3b3c;
        text-decoration: none;
        text-align: left;
        margin-bottom: 0px;
        box-shadow: none;
        line-height: 1.4;
        cursor: pointer;
        letter-spacing: 1px;
    }
    .header .navbar .nav > li:last-child > a {
        width: 100%;
        margin: 0;
        text-align: center;
    }
    .header .navbar .nav li:hover > a, .header .navbar .nav li:focus > a, .header .navbar .nav li > a:hover, .header .navbar .nav li > a:focus {
        background: #aabbc7;
        color: #ffffff;
        font-weight: 700;
        text-shadow: none;
        outline: none;
    }
    .header .navbar .nav li.memberFirst:hover a,
    .header .navbar .nav li.memberFirst:focus a {
        background: #ba0c2f;
        color: #fff;
    }
    .header .navbar .nav li:hover .menu-arrow::after,
    .header .navbar .nav li:focus .menu-arrow::after {
        color: #0bba97;
    }
    .header .navbar .nav li.dropdown .droplist-1 li a:focus {
        color: #083372;
        font-weight: 400;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu {
        background: #f2f3f8;
    }
    .navbar .nav li.dropdown > .dropdown-toggle .caret {
        float: right;
        border-top-color: #eeeeee;
        border-bottom-color: #eeeeee;
    }
    .navbar .nav li.dropdown > .dropdown-toggle:hover .caret {
        border-top-color: #006eb3;
        border-bottom-color: #006eb3;
    }
    .header .navbar .pull-right > li > .dropdown-menu,
    .header .navbar .nav > li > .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
        margin: 0px;
        padding: 0;
    }
    .header .dropdown .dropdown-menu {
        position: static;
        float: none;
        width: auto;
        margin-top: 0;
        background-color: transparent;
        border: 0;
        box-shadow: none;
        padding: 5px 0;
    }
    .dropdown .dropdown-menu li {
        padding: 0;
        background: transparent;
    }
    .header .navbar .nav li .dropdown-menu > li > a:hover {
        background: #c1d82f;
        color: #3b3b3c;
    }
    .dropdown-menu > li.active > a {
        color: #44687d;
    }
    .header .navbar .nav li.dropdown .dropdown-menu li a {
        border: 0;
        text-align: left;
        padding: 8px 0;
        background: transparent;
        color: #3b3b3c;
        font-weight: 400;
        font-size: 15px;
        margin-bottom: 0px;
        margin-top: -1px;
    }
    .header .nav li .dropdown-menu > li.dropdown-submenu li {
        padding: 0px 10px;
    }
    .header .nav li .dropdown-menu > li.dropdown-submenu li a {
        background: transparent;
        font-weight: normal;
    }
    .dropdown-submenu .caret {
        float: right;
        transform: rotate(-90deg);
        -webkit-transform: rotate(-90deg);
        -moz-transform: rotate(-90deg);
        -o-transform: rotate(-90deg);
        -ms-transform: rotate(-90deg);
        border-top-color: #eeeeee;
        border-bottom-color: #eeeeee;
        margin-top: 6px;
    }
    .dropdown-submenu a:hover .caret {
        border-top-color: #fff;
        border-bottom-color: #fff;
    }
    .header .navbar .nav li .dropdown-menu > li > a:hover {
        color: #3b3b3c;
    }
    .header .navbar .nav li .dropdown-menu > li.dropdown-submenu ul li a:hover,
    .header .navbar .nav li a:hover,
    .header .navbar .nav li a:focus,
    .navbar .nav li.dropdown.open > .dropdown-toggle,
    .navbar .nav li.dropdown.active > .dropdown-toggle,
    .navbar .nav li.dropdown.open.active > .dropdown-toggle,
    .dropdown:hover .dropdown-toggle {
        -moz-border-radius: 0;
        -ms-border-radius: 0;
        -o-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
    }
    .dropdown-menu {
        margin-left: 0 !important;
    }
    .header .nav-collapse li .menu-arrow::after {
        display: none;
        content: "\f107";
        font-family: "FontAwesome";
        position: absolute;
        right: 17px;
        top: 12px;
        color: #fff;
        font-size: 24px;
        z-index: 99999;
        width: 15px;
        height: 15px;
        line-height: 15px;
        opacity: 1;
        font-weight: bolder;
        transform: rotate(270deg);
        font-weight: 300;
    }
    .header .nav-collapse li.open-droupdown .menu-arrow {
        transform: none;
    }
    .header
        .nav-collapse
        li.dropdown.memberFirst.xs979.open-droupdown
        .menu-arrow::after {
        content: "\f00d";
        font-family: "FontAwesome";
        font-weight: 100;
        font-size: 18px;
    }
    .header .nav-collapse li .menu-arrow {
        cursor: pointer;
        width: 100%;
        background: transparent;
        left: 0;
        top: 0;
        position: absolute;
        height: 50px;
        z-index: 999;
    }
    .header .nav-collapse li.dropdown:hover:after,
    .header .nav-collapse li.dropdown.open::after {
        color: #9a0203;
    }
    .header .nav-collapse .nav {
        overflow-y: auto;
        margin: 0;
        width: 100%;
        float: none;
        padding: 0;
        display: block;
    }
    .navbar .btn-navbar .icon-bar {
        transition: all ease-in-out 0.3s;
    }
    .navMain {
        box-sizing: border-box;
        display: block;
        height: 100%;
        left: 0;
        max-height: 0;
        opacity: 0;
        overflow-x: hidden;
        overflow-y: auto;
        position: static;
        -moz-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
        -ms-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
        -o-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
        -webkit-transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
        transition: all 500ms cubic-bezier(0, 0.995, 0.99, 1) 0s;
        width: 100%;
        z-index: 999;
    }
    body.overlay {
        position: fixed;
    }
    body.overlay .navMain {
        max-height: 100vh;
        opacity: 1;
    }
    .overlay header {
        position: fixed;
        top: 0;
        width: 100%;
        background: #fcfeff;
    }

    .overlay .overlay div#semwebcatalog_application .browseLink-in li a,
    .overlay div#semwebcatalog_application .well,
    .overlay div#semwebcatalog_application .browseLink-in li a {
        position: static;
    }
    .navMain {
        border-bottom: none;
    }
    .header .navbar .nav li.dropdown.interestGroup .droplist-1 p.HeaderText {
        font-size: 18px !important;
        font-weight: 500;
        margin-bottom: 0px;
    }
    .header
        .navbar
        .nav
        li.dropdown.interestGroup
        .droplist-1
        p.HeaderText:hover,
    .header .navbar .nav li.dropdown.interestGroup .droplist-1 ul li a:hover {
        text-decoration: underline;
    }
    .header .navbar .nav li.dropdown.interestGroup .droplist-1 ul li a {
        padding-left: 20px;
    }
    .header .navbar .nav li.dropdown.interestGroup .droplist-1.xs979 .heading {
        position: static;
        text-align: left;
        margin-top: 20px;
    }
    .row-fluid .event_outer {
        width: 33.33%;
        margin: 0;
    }
    .eventimgText .HeaderText {
        font-size: 24px;
    }
    .eventimgText .HeaderText:after {
        bottom: -22px;
    }
    .event_outer:last-child .eventimgText .HeaderText:after {
        display: block;
    }
    .sliderFrame .item ul li {
        padding-right: 0;
    }
    .sliderFrame {
        padding: 0 15px;
    }

    .header .nav-collapse .nav .dropdown .dropdown-menu li.droplist-1.cta {
        display: none;
    }

    .header .navbar .nav li:nth-last-child(1) ul.memberSection ul li a {
        height: auto !important;
    }
    .header .navbar .nav li:nth-last-child(1).dropdown .droplist-1 .HeaderText {
        display: block;
    }

    .mainContent {
        width: calc(100% - 280px);
    }
    .pd_70 {
        padding: 50px 0;
    }

    .captionBtnBox ul li a .iconBox {
        width: 40px;
        height: 32px;
        position: relative;
        top: 1px;
    }
    .captionBtnBox ul li a .textBox {
        left: 70px;
    }
    .captionBtnBox ul li a .arrow {
        padding: 5px 0px;
    }
    .eventBoxFrame {
        padding: 15px 10px;
        margin-bottom: 50px;
    }
    .captionBtnBox ul li a .iconBox img.default {
        width: 30px;
    }
    .captionFrame ul {
        margin-bottom: 20px;
    }
    .carousel-caption {
        top: 44%;
    }
    .captionFrame ul li h1 {
        font-size: 32px;
        text-align: left;
    }
    .captionFrame ul li {
        line-height: 24px;
    }
    .captionFrame ul li small {
        font-size: 16px;
    }

    .footer-info a > img {
        width: 235px;
    }
    .footer .footer-info {
        width: 100%;
        margin-bottom: 15px;
        position: relative;
    }
    .follow-us {
        position: absolute;
        right: 0;
        top: 0;
    }
    ul.follow-us li {
        font-size: 16px;
    }
    .footer-info p {
        margin: 25px 0 0 0;
    }
    .footer .footer-links,
    .footer .contact-links {
        width: 50%;
        margin: 0;
        padding-right: 15px;
    }
    ul.follow-us li:first-child {
        margin: 0 0 5px 0;
        display: block;
    }
    ul.follow-us li {
        margin: 0 4px 0 0;
    }
    .footer-links ul li a,
    .contact-links ul li a,
    .contact-links ul li span,
    .footer-links.contact-links ul li {
        /* font-size: 16px; */
        /* line-height: 20px; */
    }
    .footer-links ul li {
        margin-bottom: 8px;
    }
    .contact-links ul li {
        margin-bottom: 10px;
    }
    .copyright p,
    .copyright p a {
        font-size: 14px;
    }
    .copyright p a {
        padding: 0 6px 0 10px;
    }
    .copyright p a:first-child {
        margin-left: 15px;
    }
    /*****************/
    .inner-page-content {
        padding-left: 0;
        min-height: inherit !important;
    }
    .inner-page-content .inner-content-area {
        padding: 30px 0 0;
        margin-bottom: 30px;
    }
    .content-info {
        padding: 30px;
    }
    .inner-page-content .sidebar,
    .inner-page-content .inner-content-area {
        width: 100%;
        position: static;
        padding: 30px;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .Highlight {
        margin: 50px 0;
    }
    .header
        .navbar
        .nav
        li.dropdown
        .memberSection
        .droplist-1
        .DiamondBullets
        ul {
        padding: 0;
    }
    .header
        .navbar
        .nav
        li.dropdown
        .memberSection
        .droplist-1
        .DiamondBullets
        ul
        li {
        margin-bottom: 8px;
        padding-left: 30px;
    }
    .header
        .navbar
        .nav
        li.dropdown
        .memberSection
        .droplist-1
        .DiamondBullets
        ul
        li
        a {
        padding: 0;
        font-size: 16px;
    }
    .header
        .navbar
        .nav
        li.dropdown
        .memberSection
        .droplist-1
        .DiamondBullets
        ul
        li
        a:before {
        top: 3px;
    }
    .carousel-caption {
        position: relative;
        top: 0;
        transform: none;
        padding: 30px 15px;
        max-width: 100%;
        justify-content: center;
    }

    .slider .owl-carousel .item img {
        position: absolute;
        width: 100%;
        height: 100%;
        z-index: -1;
    }

    .captionFrame {
        max-width: 100%;
        margin: 0;
        padding: 20px;
    }

    .captionBtnBox {
        position: relative;
        height: auto;
        background: #003485;
    }

    .captionBtnBox .captionBtnFrame {
        width: 100%;
        position: relative;
        max-width: 100%;
        padding: 20px 30px;
    }
    .slider .owl-carousel .owl-dots {
        position: absolute;
        transform: translateX(-50%);
        flex-direction: row;
        justify-content: center;
        height: auto;
        top: unset;
        left: 50%;
        bottom: 15px;
    }
    .captionBtnBox ul li a {
        min-height: 60px;
    }

    .captionBtnBox ul li a .iconBox svg {
        width: 30px;
        height: 30px;
    }
    .header
        .nav-collapse
        .nav
        .searchBtnFn.dropdown
        .dropdown-menu
        li.droplist-1.formDiv {
        padding: 0;
        width: 100%;
    }
    body .header .navbar .nav > li.searchBtnFn.dropdown > ul.dropdown-menu {
        height: auto;
        display: block !important;
        width: 100%;
        right: 0;
        left: 0;
        transform: translateX(0);
    }

    header .navbar .nav li.headerlogin.show-form .header-member-form {
        display: flex !important;
    }
    header .navbar .nav li.headerlogin .header-member-form {
        background: #a8462b;
        padding: 30px 20px 15px 20px;
    }

    .header .navbar .nav li form a:last-child {
        font-size: 14px;
        line-height: 1.2;
        margin-top: 10px;
    }

    .top-btn-wrap {
        width: 100%;
        padding: 15px;
    }
    .about-tflf-box .left-box {
        padding: 0;
    }
    
    .about-tflf-box {
        padding: 30px;
    }
    
    .about-sec .inner-sec-wrap {
        margin: 100px 0;
    }
    
    .about-tflf-box .left-box:before {
        top: -70px;
    }
    
    .about-tflf-box .left-box:after {
        top: 70px;
    }
    .header .navbar .nav > li.dropdown.open-droupdown > a.dropdown-toggle:after, .header .navbar .nav > li.dropdown:hover > a.dropdown-toggle:after {
        border-color: #ffffff;
        transform: rotate(45deg);
    }
    .header .navbar .nav li > a,
    .header .navbar .nav li > a,
    .header .navbar .nav li .dropdown-menu > li > a {
        font-size: 18px;
        padding: 13px 20px;
    }

    .dropdown-menu .droplist-1 ul {
        position: relative;
        top: 0;
        left: 0;
        background: transparent;
    }
    
    .dropdown-menu .droplist-1 ul li>a:hover {
        background: #ffffff;
    }
    .mobile-contactus {
        position: absolute;
        right: 0px;
        top: 15px;
        z-index: 999;
    }
    .mobile-contactus a.dropdown-toggle:hover, .mobile-contactus a.dropdown-toggle:focus {
    color: #ba892c;
}
    .mobile-contactus .dropdown .dropdown-menu {
        position: absolute;
        right: 15px;
        left: auto;
        background: #ffffff;
        padding: 20px;
        top: 100%;
        min-width: 280px;
        border-style: none solid solid;
        box-shadow: 0 1px 2px 0px #aabbc7;
    }
    
    .mobile-contactus a.dropdown-toggle {
        padding: 10px 20px;
        display: inline-block;
        font-size: 28px;
        line-height: 1;
        text-decoration: none;
    }
    
    .mobile-contactus .dropdown .dropdown-menu .social-list {
        justify-content: flex-start;
    }

    .mobile-contactus .dropdown.open .dropdown-menu {
        display: block !important;
    }
    .mobile-contactus .dropdown.open .dropdown-menu span {
        font-size: 14px;
        line-height: 1.5;
        display: block;
        padding-left: 50px;
        margin-bottom: 30px;
        color: #C6B182;
    }
    
    .mobile-contactus .dropdown.open .dropdown-menu span .img-ico {
        position: absolute;
        top: 15px;
        left: 20px;
        z-index: 1;
        width: 10px;
        color: #c6b182;
        font-size: 30px;
    }
    
    .mobile-contactus .dropdown .dropdown-menu ul.social-list li  a {
        background: #9b9b9b;
        color: #ffffff;
        padding-left: 3px;
    }
    
    /* .mobile-contactus .dropdown .dropdown-menu ul.social-list li:last-child a {
        font-size: 26px;
        padding: 0;
        text-decoration: none;
        background: transparent;
        color: #c6b182;
    } */
    .footer .row.d-flex-wrap>div.col4 {
        -webkit-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        text-align: center;
    }
    
    .footer .row.d-flex-wrap>div.col5 {
        -webkit-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
        margin-top: 10px;
    }
    
    .footer .row.d-flex-wrap>div.col5 ul.social-list {
        justify-content: center;
    }
    
    .footer .row.d-flex-wrap > div.col1 .foot-logo-wrap {
        text-align: center;
        justify-content: center;
    }

    .pd_50 {
        padding: 40px 0;
    }
    .header .navbar .nav ul.dropdown-menu li.dropdown>a:before {
        top: 13px;
        font-size: 22px;
        right: 16px;
        color: #c5b081;
    }
    .dropdown .dropdown-menu li>.dropdown-toggle:before {
        -webkit-transform: rotate(90deg);
        transform: rotate(90deg);
    }
    .header .navbar .nav>li.dropdown>a:after {
        width: 10px;
        height: 10px;
        border-width: 2px;
    }
    .top-btn-wrap a {
        width: 100%;
        padding: 10px;
        font-size: 16px;
    }
    
    .top-btn-wrap {
        flex-wrap: wrap;
    }
    .header .navbar .row.flex-row .top-btns {
        display: none;
    }
    .mobile-contactus .dropdown.open .dropdown-menu .item-link {
        color: #9b9b9b;
        font-size: 14px;
        display: block;
        display: flex;
        align-items: center;
        gap: 10px;
        font-family: "Lato", sans-serif;
        margin-bottom: 10px;
    }
    .mobile-contactus .dropdown.open .dropdown-menu .item-link i {
        color: #ba892c;
        font-size: 16px;
    }
    
    .mobile-contactus .dropdown .dropdown-menu .social-list {
        margin-top: 30px;
    }
    .latest-blogs-sec .flex-row>.span3 {
        flex: 0 0 33.33%;
        max-width: 33.33%;
    }
    .footer .row.d-flex-wrap {
        row-gap: 20px;
    }
    .footer-btns {
        display: flex !important;
        justify-content: center;
        gap: 20px;
        flex-wrap: wrap;
        align-items: center;
        margin-top: 20px;
    }
    .footer-btns .MAJButton {
        min-width: 160px;
        font-size: 14px;
        color: #ffffff;
    }
    
    .header .navbar .nav>li:last-child .droplist-1 .droplist-2 {
        left: 0;
    }
    .header .navbar .nav>li:last-child .dropdown-menu {
        left: 0;
        right: 0;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu>li:not(:last-child) {
        border-bottom: 1px solid #aabbc7;
    }
    
    .header .navbar .nav li > .dropdown-menu li.droplist-1>ul {
        border-top: 1px solid #aabbc7;
        margin: 0px;
    }
    .dropdown-menu > .dropdown.droplist-1.open-droupdown,
    .dropdown-menu > .dropdown.droplist-1.open-droupdown .dropdown-menu.droplist-2{
        background: #e9e9e9;
    }


    .dropdown-menu > .dropdown.droplist-1.open-droupdown {
        border-top: 1px solid #aabbc7 !important;
        margin-top: -1px;
    }
    .header .nav-collapse li li .menu-arrow {
        height: 37px;
    }
    
    .header .navbar .nav li > .dropdown-menu>li>.dropdown-toggle:before {
        top: 9px !important;
    }

    .dropdown-menu > .dropdown.droplist-1.open-droupdown .dropdown-menu.droplist-2 > li, .dropdown-menu > .dropdown.droplist-1.open-droupdown .dropdown-menu.droplist-2 {
        border-color: #d0d0d0;
        padding-left: 10px;
    }
    .header .nav-collapse .nav .dropdown .dropdown-menu.droplist-2 {
        border-top: 1px solid #d0d0d0;
    }
}

/* 979px */
@media only screen and (min-width: 768px) {
    .for-mobile {
        display: none;
    }
}
@media only screen and (max-width: 767px) {
    .BulletList-row .BulletList {
        -webkit-flex: 0 0 100%;
        flex: 0 0 100%;
        max-width: 100%;
    }
    .BulletList-row {
        flex-wrap: wrap;
    }
    .BulletList-row .BulletList ul {
        margin-bottom: 0;
    }
    .sponsors-link ul a {
        padding: 12px 20px;
        font-size: 16px;
    }
    .row.row-flex > .span4,
    .row.row-flex > .span8 {
        flex: 0 0 100%;
        -webkit-flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }

    .footer .row.d-flex-wrap > div.col2,
    .footer .row.d-flex-wrap > div.col3 {
    }
    .footer .row.d-flex-wrap {
        justify-content: center;
    }

    .footer .row.d-flex-wrap > div.col5 {
        text-align: center;
        flex: 0 0 100%;
        max-width: 100%;
        padding-top: 0px;
    }

    .footer .row.d-flex-wrap > div.col5.footer-links h3:after {
        margin: 15px auto 15px;
    }

    .footer .row.d-flex-wrap > div.col5.footer-links .social-list {
        justify-content: center;
        margin-left: 10px;
    }
    .SectionHeader {
    font-size: 30px !important;
    }
    .info-iconbox {
        padding: 80px 50px 30px 20px;
    }
    .info-iconbox .iconlink {
        width: 40px;
        height: 40px;
        font-size: 20px;
    }
    .info-iconbox img {
        width: 40px;
        height: 40px;
    }
    .mt-40 {
        margin-top: 25px !important;
    }

    blockquote,
    blockquote.pull-right {
        font-size: 20px;
        padding-left: 25px;
    }
    .latest-blogs-sec .flex-row > div {
        width: 100%;
        margin-bottom: 30px !important;
        height: auto;
    }
    .img-card {
        max-width: 400px;
        margin: 0 auto 0px;
    }
    .img-card img {
        width: 100%;
    }
    .img-card .img-holder {
        max-width: 100%;
        margin: 0 auto;
    }
    .img-card .img-holder img {
        width: 100%;
    }
    .infoicon-sec .flex-row .col-3 {
        flex: 0 0 50%;
        max-width: 50%;
        -webkit-flex: 0 0 50%;
        margin-bottom: 30px;
    }
    .for-desktop {
        display: none;
    }
    .captionFrame ul li:nth-child(2) {
        font-size: 17px;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        a {
        top: 3px;
    }
    .xs767 {
        display: block !important;
    }
    .xsHidden767,
    .xsHidden {
        display: none !important;
    }
    .pd_70 {
        padding: 30px 0px;
    }
    .header {
        /* min-height: 90px; */
    }

    .header .navbar .nav li.dropdown .droplist-1 .formframe input {
        font-size: 18px;
        height: 40px;
        padding: 0 15px;
        font-family: "Roboto", sans-serif;
    }

    .header .nav-collapse {
        max-height: calc(100vh - 80px);
    }
    .header .navbar .nav li > a,
    .header .navbar .nav li > a,
    .header .navbar .nav li .dropdown-menu > li > a {
        font-size: 18px;
        padding: 13px 20px;
    }
    .header .navbar .nav li.dropdown .memberSection li form a:last-child {
        margin-left: 0px;
        font-weight: normal;
        width: 100%;
        padding: 0px 0px 0 0px;
        margin-top: 15px;
        line-height: 1.6;
        height: auto;
    }
    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        input {
        height: 40px;
        width: calc(100% - 50px);
        margin: 0;
    }
    .header .nav-collapse.collapse {
        left: 0;
        top: 79px;
    }

    .sidebar {
        width: 100%;
        max-width: 100%;
    }
    .captionBtnBox,
    .captionBtnFrame {
        width: 100%;
        max-width: 100%;
    }
    .captionBtnFrame {
        padding: 20px 20px;
    }

    .top-inner h1 {
        width: 50%;
        font-size: 22px;
    }
    .captionFrame ul li h1 {
        text-align: center;
    }
    .captionBtnBox ul li a .textBox h2 {
        font-size: 18px;
        font-weight: 400;
        margin-bottom: 5px;
    }
    .captionFrame {
        max-width: 100%;
        margin-left: 0;
        padding: 20px 50px;
    }
    .captionFrame h1 {
        margin-top: 0px;
        font-size: 32px;
        line-height: 36px;
    }
    .header .navbar .container,
    .container {
        width: 100%;
        padding: 0px 15px;
        margin: 0 auto;
    }
    .header .navbar-inner {
        position: relative;
        top: 0;
        margin: 0 auto;
        width: 100%;
    }
    .header .navbar-brand {
        margin-left: 0px;
    }
    .navbar .navbar-brand img {
        margin-left: 0px;
    }
    .captionFrame h3 {
        font-size: 22px;
    }

    .sliderFrame {
        margin: 55px 0 35px;
    }
    .slider .owl-carousel .item, .carousel-caption {
    }
    .slider .owl-carousel .owl-dots {
        bottom: 20px;
    }

    .navbar .navbar-brand {
    }
    .captionBtnBox ul li {
        margin-bottom: 15px;
    }

    .header .navbar-brand {
        padding: 5px 0 5px 0px;
    }
    .header .navbar .btn-navbar.collapsed {
        padding: 28px 0;
    }
    .header .navbar .btn-navbar {
        padding: 25px 0px 25px;
        margin: 0;
    }

    .header
        .navbar
        .nav
        li.dropdown.searchBtnFn.xs979
        .droplist-1
        .formframe
        a {
        top: 0;
        right: 10px;
    }

    .headerSpace {
        height: 80px;
    }
    .Highlight {
        padding: 15px 15px;
        margin-bottom: 20px;
    }
    .section-HeaderText {
        font-size: 25px;
        margin-bottom: 30px;
    }
    .footer .footer-links,
    .footer .contact-links {
        float: left;
    }
    .footer-links h3,
    .contact-links h3 {
        font-size: 18px;
        margin: 0 0 10px 0;
    }
    .footer .footer-info .foot-logo:after {
        margin: 15px auto 14px;
    }
    ul.follow-us {
        margin-top: 20px;
        position: static;
    }
    ul.follow-us li:first-child {
        display: none;
    }
    .footer-links ul li::before {
        display: none;
    }
    .footer-links ul li {
        margin-bottom: 10px;
        padding-left: 0;
    }

    .friendsSliderBox .owl-carousel ul li a {
        padding: 0 25px;
    }
    .bannerInner h1 {
        font-size: 36px;
    }
    .quicklink-mobile,
    .event-mobile {
        padding: 10px 15px;
        margin: 15px 15px 0;
    }
    .DiamondBullets ul li a {
        font-size: 18px;
        padding: 15px 40px 15px 15px;
    }
    .content-info {
        padding: 20px 15px;
    }
    .content-info > h2 {
        font-size: 30px;
    }
    .content-info h3 {
        font-size: 25px;
        line-height: 1.3;
    }
    .Highlight {
        padding: 25px 30px;
        margin: 20px 0;
    }
    .Highlight p {
        margin-bottom: 30px;
    }
    .inner-page-content .sidebar,
    .inner-page-content .inner-content-area {
        margin-bottom: 0;
        padding: 30px 15px;
    }
    .membership-headlinebox {
        display: none;
    }
    .BlackBorder {
        padding: 14px 18px;
    }
    .membership-headlinebox h5 {
        font-size: 20px;
    }
    .inner-page-content .sidebar {
        padding: 20px 15px;
        background: #fff;
    }
    .sponsors-boxtwo {
        margin: 30px 0 20px 0;
        padding: 0;
    }
    .sponsors-boxtwo img {
        width: 100%;
    }
    .sponsors-boxthree {
        width: 100%;
    }
    .captionBtnBox {
    }
    .captionBtnBox.captionBtnBox-mb {
        display: block;
    }
    .eventBoxFrame .HeaderTextSmall {
        margin-bottom: 35px;
    }
    .footer-info a > img {
        width: 280px;
    }
    .events {
        margin-bottom: 5px;
    }
    .sponsors-box {
        display: none;
    }
    .events .friendsLogoBox {
        background: #fff;
    }
    .bannerInner {
        overflow: hidden;
        min-height: 200px;
    }
    .bannerInner img {
        width: auto;
        height: 100%;
        max-width: inherit;
    }
    .textBox p {
        color: #fff;
        margin: 0;
        line-height: 18px;
        font-size: 14px;
    }
    .friendsLogoBox {
        background: #fff;
    }
    .captionBtnBox ul li a .textBox {
        max-width: 100%;
    }
    .BlackBorder,
    .primary-btnmb {
        margin: 0px auto;
        width: 250px;
        display: block;
    }
    .events .friendsLogoBox {
        display: block;
    }
    .foot-logo-wrap {
        flex-flow: column;
        align-items: center;
    }
    .footer .footer-info ul.social-list {
        margin-top: 30px;
    }
    .SectionTitle {
        font-size: 35px;
    }
    
    .SectionTitleTop {
        font-size: 13px;
    }

    .about-tflf-box .right-box {
        flex: 0 0 100%;
        max-width: 100%;
        position: relative;
        z-index: 2;
        padding-top: 30px;
        padding-left: 0;
        padding-top: 0px;
        /* text-align: center; */
        /* justify-content: center; */
        display: block;
    }
    .about-tflf-box .right-box .img-slider {
    max-width: 400px;
    margin: 30px 0 0;
}
    
    .about-tflf-box .left-box {
        flex: 0 0 100%;
        padding-bottom: 0;
        min-height: auto;
        padding: 0;
    }
    .about-tflf-box .left-box:after {
        /* display: none; */
        border-style: none solid;
    }
    .about-tflf-box .right-box:before {
        /* content: ""; */
        display: block;
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid #C6B182;
        left: 0;
        z-index: -1;
        top: -30px;
        border-top: none;
    }
    .about-sec .inner-sec-wrap {
        margin: 80px 0;
        padding: 0 20px;
    }
    
    .about-tflf-box {
        padding: 20px 20px 0;
    }
    .about-tflf-box .left-box:before {
        top: -50px;
    }
    ul.social-list {
        justify-content: center;
    }
    .footer p, .footer p>a {
        font-size: 14px;
        text-align: center;
    }
    .contact-links ul li>img {
        display: none;
    }
    .footer .row.d-flex-wrap .col1 {
        order: 1;
    }
    .footer .row.d-flex-wrap .col2 {
        order: 2;
    }
    .footer .row.d-flex-wrap .col3 {
        order: 3;
    }
    .footer .row.d-flex-wrap .col4 {
        order: 5;
    }
    .footer .row.d-flex-wrap .col5 {
        order: 4;
    }
    .TitleText {
        font-size: 30px;
    }
    .header .navbar-brand {
        left: 50px;
    }
    .sbm-icon-card-wrap>ul {
        max-width: 550px;
        margin: 0 auto;
    }
    .footer .row.d-flex-wrap .contact-links:nth-child(2) h3:before {
        content: "";
        display: inline-block;
        background-image: url('../images/foot-icon-1.png');
        width: 30px;
        height: 30px;
        background-repeat: no-repeat;
        background-position: top 10px center;
        position: relative;
        top: 2px;
    }    
    .footer .row.d-flex-wrap .contact-links:nth-child(3) h3:before {
        content: "";
        display: inline-block;
        background-image: url('../images/foot-icon-2.png');
        width: 30px;
        height: 30px;
        background-repeat: no-repeat;
        background-position: top 10px center;
        position: relative;
        top: 2px;
        background-size: 30px;
    }
    .find-layer-sec .img-holder {
        margin: 0;
        text-align: center;
    }
    .find-layer-sec .img-holder img {
        max-width: 300px;
        margin: 0 auto;
    }
    .sbm-icon-card-wrap>ul>li {
        flex: 0 0 100%;
        max-width: 100%;
        -webkit-flex: 0 0 100%;
    }
    .find-layer-sec {
        margin-top: 0;
    }
    .about-sec {
        padding-top: 0;
    }
    .latest-blogs-sec .flex-row>.span3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
    .latest-blogs-sec {
        padding: 30px 0;
    }
    .stay-conected-sec p {
        font-size: 17px;
    }
    .contact-sec .row.row-flex .span4 {
        flex: 0 0 100%;
        max-width: 100%;
        width: 100%;
    }
    .form-design-1 .MAJButton-2 {
        width: 100%;
        min-width: auto;
        max-width: 300px;
        margin: 0 auto;
    }
    .footer .row.d-flex-wrap {
        
    }
    .Highlight .btns-wrap a {
        min-width: auto;
    }
    .BulletList ul {
        margin-left: 0;
    }
}
/* 767px */

@media only screen and (max-width: 600px) {
    .footer .container {
        max-width: 450px;
        margin: 0 auto;
    }
    .footer .row.d-flex-wrap > div.col2, .footer .row.d-flex-wrap > div.col3 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-top: 0;
    }
    .footer .row.d-flex-wrap>div.col5 {
        margin-bottom: 0;
    }
     .footer .row.d-flex-wrap>div.col4 {
         margin-bottom: 5px;
         margin-top: 25px;
     }
    .sbm-icon-card-wrap>ul>li {
        flex: 0 0 100%;
        max-width: 100%;
        -webkit-flex: 0 0 100%;
    }
    .latest-blogs-sec .flex-row>.span3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
