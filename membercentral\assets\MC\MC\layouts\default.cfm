<cfoutput>
	<cfset local.pageTitle=trim(event.getValue( 'mc_pageDefinition.pageTitle',event.getValue( 'mc_pageDefinition.pageName')))/>
	<cfset local.isSideEnabled = false />
	
	<cfset local.zoneK1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='K' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
			<cfset local.zoneK1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['K'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneB1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='B' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['B'],1)>
			<cfset local.zoneB1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['B'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneC1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='C' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['C'],1)>
			<cfset local.zoneC1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['C'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneD1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='D' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['D'],1)>
			<cfset local.zoneD1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['D'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneE1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='E' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['E'],1)>
			<cfset local.zoneE1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['E'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneF1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='F' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['F'],1)>
			<cfset local.zoneF1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['F'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneG1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='G' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['G'],1)>
			<cfset local.zoneG1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['G'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneI1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='I' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['I'],1)>
			<cfset local.zoneI1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['I'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneL1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='L' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
			<cfset local.zoneL1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['L'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneM1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='M' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
			<cfset local.zoneM1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['M'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneN1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='N' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
			<cfset local.zoneN1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['N'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<cfset local.zoneO1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='O' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['O'],1)>
			<cfset local.zoneO1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['O'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
			<body>
				<cfinclude template="jssalesscript.cfm">
				<div class="wrapper">
					<cfinclude template="header.cfm">
					
					<span class="zoneK1Wrapper hide">#local.zoneK1Content#</span>
					<span class="zoneK1Holder"></span>
					
					<cfif len(trim(local.zoneB1Content)) AND len(trim(local.zoneK1Content)) eq 0>
						<div class="pd_60 explore-links-sec no-top-banner" id="stickyHeader">
							<div class="explore-links-wrapper">
								<div class="container containerCustom w-1200">
									#local.zoneB1Content#
								</div>
							</div>
						</div>					
					<cfelseif len(trim(local.zoneB1Content))>
						<div class="pd_60 explore-links-sec" id="stickyHeader">
							<div class="container containerCustom w-1200">
								#local.zoneB1Content#
							</div>
						</div>
					</cfif>
					<cfif application.objCMS.getZoneItemCount(zone='Main' ,event=event)>
						<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Main'],1)>
							<cfif len(local.zoneG1Content) AND NOT structKeyExists(event.getValue("mc_pageDefinition").pageZones['Main'][1], "RESOURCENODEATTRIBUTES")>
								<cfset event.getValue("mc_pageDefinition").pageZones['Main'][1].data = "">
							</cfif>
							<cfset local.data = event.getValue("mc_pageDefinition").pageZones['Main'][1].data>
							<cfif NOT isSimpleValue(local.data) OR (isSimpleValue(local.data) AND len(trim(toString(local.data))))>
								<div class="contentArea mainHolder pd_30" id="mainHolder" <cfif len(trim(local.zoneK1Content)) AND len(trim(local.zoneB1Content)) eq 0> style="border-top-right-radius: 100px;margin-top: -100px;" </cfif> >
									<div class="container containerCustom w-1200">
										#application.objCMS.renderZone(zone='Main',event=event)# 
									</div>
								</div>
							</cfif>
						</cfif>
					</cfif>
					
					<span class="zoneL1Wrapper hide">#local.zoneL1Content#</span>
					<span class="zoneL1Holder"></span>

					<span class="zoneM1Wrapper hide">#local.zoneM1Content#</span>
					<span class="zoneM1Holder"></span>

					<span class="zoneN1Wrapper hide">#local.zoneN1Content#</span>
					<span class="zoneN1Holder"></span>
					
					<span class="zoneC1Wrapper hide">#local.zoneC1Content#</span>
					<span class="zoneC1Holder"></span>
					
					<cfif len(trim(local.zoneH1Content))>
					<span class="zoneH1Wrapper hide">#local.zoneH1Content#</span>
					</cfif>
					
					<cfif len(trim(local.zoneI1Content))>
						<span class="zoneH1Holder"></span>
					</cfif>

					<span class="zoneD1Wrapper hide">#local.zoneD1Content#</span>
					<span class="zoneD1Holder"></span>
					
					<span class="zoneE1Wrapper hide">#local.zoneE1Content#</span>
					<span class="zoneE1Holder"></span>
					
					<span class="zoneF1Wrapper hide">#local.zoneF1Content#</span>
					<span class="zoneF1Holder"></span>
					
					<span class="zoneG1Wrapper hide">#local.zoneG1Content#</span>
					<span class="zoneG1Holder"></span>
					
					<span class="zoneO1Wrapper hide">#local.zoneO1Content#</span>
					<span class="zoneO1Holder"></span>
					
					<span class="zoneI1Wrapper hide">#local.zoneI1Content#</span>
					<span class="zoneI1Holder"></span>
					
					<cfif len(trim(local.zoneI1Content)) EQ 0>
						<span class="zoneH1Holder"></span>
					</cfif>
					
					<!--Content End-->
					<cfinclude template="footer.cfm">
					<cfinclude template="toolBar.cfm">
				</div>
			</body>
		<cfelse>
			<cfinclude template="jssalesscript.cfm">
			<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
				#application.objCMS.renderZone(zone='Main',event=event)#
			</body>		
		</cfif>
		<cfinclude template="foot.cfm">
	</html>
</cfoutput>