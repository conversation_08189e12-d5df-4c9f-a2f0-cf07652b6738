ALTER PROC dbo.tr_invoiceData
@invoiceID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @ARGLAID int, @t_Sale int, @t_Adjustment int, @t_VO int, @t_SalesTax int, @t_WO int,
		@tr_AdjustTrans int, @tr_AdjustInvTrans int, @tr_OffsetTrans int, @tr_DITSaleTrans int, @tr_WriteOffSaleTrans int;
	declare @tblMessages TABLE (footnote smallint, messageContentVersionID int, messageContent varchar(max));

	select @orgID = orgID from dbo.tr_invoices where invoiceID = @invoiceID;
	set @t_Sale = dbo.fn_tr_getTypeID('Sale');
	set @t_Adjustment = dbo.fn_tr_getTypeID('Adjustment');
	set @t_VO = dbo.fn_tr_getTypeID('VoidOffset');
	set @t_SalesTax = dbo.fn_tr_getTypeID('Sales Tax');
	set @t_WO = dbo.fn_tr_getTypeID('Write Off');
	set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
	set @tr_AdjustInvTrans = dbo.fn_tr_getRelationshipTypeID('AdjustInvTrans');
	set @tr_OffsetTrans = dbo.fn_tr_getRelationshipTypeID('OffsetTrans');
	set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
	set @tr_WriteOffSaleTrans = dbo.fn_tr_getRelationshipTypeID('WriteOffSaleTrans');

	EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAID OUTPUT;

	/* invoice content in order of how transactions appear */
	insert into @tblMessages (footnote, messageContentVersionID, messageContent)
	select tmp2.footnote, tmp2.messageContentVersionID, cv.rawContent as messageContent
	from (
		select messageContentVersionID, row_number() OVER (order by min(row)) as footNote
		from (
			select it.messageContentVersionID, row_number() OVER (order by t.transactionDate, t.transactionID) as row
			from dbo.tr_invoiceTransactions as it
			inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
			where it.orgID = @orgID
			and it.invoiceID = @invoiceID
			and it.messageContentVersionID is not null
		) as tmp
		group by messageContentVersionID
	) as tmp2 
	inner join dbo.cms_contentVersions as cv on cv.contentVersionID = tmp2.messageContentVersionID;

	select footnote, messageContentVersionID, messageContent
	from @tblMessages
	order by footNote;

	/* section 1: sales */
	-- sales on inv
	SELECT s1.transactionID, s1.transactionDate, s1.detail, s1.amount, s1.type, s1.canVoid, s1.canAdjust, 
		msg.footnote, m.memberID as assignedToMemberID, 
		m.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m.middlename,''),'') else '' end 
		+ ' ' + m.lastname
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m.suffix,''),'') else '' end 
		as assignedToMember,
		m.company as assignedToMemberCompany
	from (
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, isnull(t.detail,'') as detail, t.amount, 'Sale' as [type], 
			canVoid = case when t.statusID in (1,3) then 1 else 0 end,
			canAdjust = case when t.statusID = 1 then 1 else 0 end,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_Sale
		AND t.statusID <> 4
			union all
		-- pos adj on inv to sales
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'ADJUSTMENT' + isnull(': ' + t.detail,'') as detail, t.amount, 'Adjustment' as [type], 
			canVoid = case when t.statusID in (1,3) then 1 else 0 end,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.debitGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
			union all
		-- neg adj on inv to sales when appliedToTID is on same invoice
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 
			'ADJUSTMENT' + isnull(': ' + t.detail,'') + isnull('<br/>' + nullif(c.invoiceDetail,''),'') as detail, 
			r2.amount*-1 as amount, 'Adjustment' as [type], 
			canVoid = case when t.statusID in (1,3) then 1 else 0 end,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID = it.invoiceID
		LEFT OUTER JOIN dbo.tr_transactionDiscounts as td 
			INNER JOIN dbo.tr_coupons as c on c.couponID = td.couponID
			on td.orgID = @orgID and td.transactionID = t.transactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
			union all
		-- voidoffset on inv when voidee (sale) is on same invoice
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID' + isnull(': ' + t.detail,'') as detail, t.amount*-1 as amount, 'VoidOffset' as [type], 
			canVoid = 0,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID = it.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
			union all
		-- voidoffset on inv when voidee (adj to sale) is on same invoice
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID ADJUSTMENT' + isnull(': ' + t.detail,'') as detail, 
			amount = case when t.creditGLAccountID = @ARGLAID then t.amount*-1 else t.amount end, 'VoidOffset' as [type], canVoid = 0,
			canAdjust = 0, it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID = it.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_Sale
	) as s1
	INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = s1.TAssignedToMemberID 
	inner join dbo.organizations as o on o.orgID = m.orgID
	left outer join @tblMessages as msg on msg.messageContentVersionID = s1.messageContentVersionID
	order by 2, 1;

	-- section 1 tax, pos adj to tax, neg adj to tax when appliedToTID is on same invoice, voidoffset when voidee is on same invoice
	select gl.GLAccountID as taxGLAccountID, gl.accountName as detail, sum(tmp.amount) as amount
	from (
		SELECT t.transactionID, t.amount, t.creditGLAccountID as taxGLAccountID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_SalesTax
		AND t.statusID <> 4
			union all
		SELECT t.transactionID, t.amount, t.creditGLAccountID as taxGLAccountID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		and t.debitGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, r2.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID = it.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		and t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, t.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID = it.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, 
			amount = case
				when t.creditGLAccountID = @ARGLAID then t.amount*-1
				else t.amount
				end, 
			taxGLAccountID = case
				when t.creditGLAccountID = @ARGLAID then t.debitGLAccountID
				else t.creditGLAccountID
				end
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID = it.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_SalesTax
	) as tmp
	INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = tmp.taxGLAccountID
	group by gl.GLAccountID, gl.accountName
	order by 2;

	-- section 2: neg adj to sales when appliedToTID is on diff invoice, voidoffset when voidee is on diff invoice
	SELECT s2.transactionID, s2.transactionDate, s2.detail, s2.amount, s2.invoiceid, s2.invoiceNumber, s2.type, s2.canVoid, s2.canAdjust, 
		msg.footnote, m.memberid as assignedToMemberID, 
		m.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m.middlename,''),'') else '' end 
		+ ' ' + m.lastname
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m.suffix,''),'') else '' end 
		as assignedToMember,
		m.company as assignedToMemberCompany
	from (
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 
			'ADJUSTMENT' + isnull(': ' + t.detail,'') + isnull('<br/>' + nullif(c.invoiceDetail,''),'') as detail, 
			sum(r2.amount*-1) as amount, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, 
			'Adjustment' as [type], 
			canVoid = case when t.statusID in (1,3) then 1 else 0 end,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		LEFT OUTER JOIN dbo.tr_transactionDiscounts as td 
			INNER JOIN dbo.tr_coupons as c on c.couponID = td.couponID
			on td.orgID = @orgID and td.transactionID = t.transactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		and t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
		group by t.transactionID, m.activememberid, t.transactionDate, 'ADJUSTMENT' + isnull(': ' + t.detail,'') + isnull('<br/>' + nullif(c.invoiceDetail,''),''), 
			i.fullInvoiceNumber, i.invoiceID, t.statusID, it.messageContentVersionID
			union all
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID' + isnull(': ' + t.detail,'') as detail, t.amount*-1 as amount, 
			i.fullInvoiceNumber as invoiceNumber, i.invoiceID,
			'VoidOffset' as [type],
			canVoid = 0,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
			union all
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID ADJUSTMENT' + isnull(': ' + t.detail,'') as detail, 
			amount = case
				when t.creditGLAccountID = @ARGLAID then t.amount*-1
				else t.amount
				end, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, 'VoidOffset' as [type], 			
			canVoid = 0,
			canAdjust = 0,
			it.messageContentVersionID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_Sale
	) as s2
	INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = s2.TAssignedToMemberID 
	inner join dbo.organizations as o on o.orgID = m.orgID
	left outer join @tblMessages as msg on msg.messageContentVersionID = s2.messageContentVersionID
	order by 2, 1;

	-- section 2 neg adj to tax when appliedToTID is on diff invoice, voidoffset when voidee is on diff invoice
	select gl.GLAccountID as taxGLAccountID, gl.accountName as detail, i.fullInvoiceNumber as invoiceNumber, i.invoiceid, sum(tmp.amount) as amount
	from (
		SELECT t.transactionID, r2.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID, i.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		and t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, t.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID, i.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, amount = case
			when t.creditGLAccountID = @ARGLAID then t.amount*-1
			else t.amount
			end, taxGLAccountID = case
				when t.creditGLAccountID = @ARGLAID then t.debitGLAccountID
				else t.creditGLAccountID
				end, i.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_SalesTax
	) as tmp
	INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = tmp.taxGLAccountID
	INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
	INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = i.assignedToMemberID 
	inner join dbo.organizations as o on o.orgID = m.orgID
	group by gl.GLAccountID, gl.accountName, i.fullInvoiceNumber, i.invoiceID
	order by 2, 3;

	-- section 3: current allocations to invoice transactions, neg adj to sales from other invoices, voidoffset from other invoices, writeoffs to invoice transactions
	select s3.transactionID, s3.transactionDate, s3.detail, s3.amount, s3.invoiceNumber, s3.invoiceID, s3.type, 
		m.firstname 
		+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m.middlename,''),'') else '' end 
		+ ' ' + m.lastname
		+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m.suffix,''),'') else '' end 
		as assignedToMember,
		m.company as assignedToMemberCompany
	from (
		select transactionID, TAssignedToMemberID, transactionDate, '$' + cast(payAmount as varchar(15)) + ' ' + detail as detail, 
			allocAmount*-1 as amount, '' as invoicenumber, 0 as invoiceID, [type]
		from (
			select transactionID, detail, payAmount, 'Allocation' as [type], transactionDate, TAssignedToMemberID, sum(allocAmount) as allocAmount
			from (
				-- allocations to invoice transactions
				select alloc.transactionID_cash as transactionID, tPay.detail, tPay.amount as payAmount, 
					tPay.transactionDate, m.activememberid as TAssignedToMemberID, 
					CASE WHEN alloc.debitGLAccountID_alloc = @ARGLAID then alloc.amount_alloc*-1 ELSE alloc.amount_alloc END as allocAmount
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceId = i.invoiceID
				inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = it.transactionID
				inner join dbo.tr_transactions as tAlloc on tAlloc.ownedByOrgID = @orgID and tAlloc.transactionID = alloc.transactionID_alloc and tAlloc.statusID = 1
				inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @orgID and tPay.transactionID = alloc.transactionID_cash and tPay.statusID = 1
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = alloc.assignedToMemberID_cash
				where i.orgID = @orgID
				and i.invoiceID = @invoiceID
					union all
				-- allocations to deferred of invoice transactions
				select tPay.transactionID, tPay.detail, tPay.amount as payAmount, 
					tPay.transactionDate, m.activememberid as TAssignedToMemberID, 
					CASE WHEN tAlloc.debitGLAccountID = @ARGLAID then tAlloc.amount*-1 ELSE tAlloc.amount END as allocAmount
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceId = i.invoiceID
				inner join dbo.tr_relationships as rDit on rDit.orgID = @orgID and rDit.typeID = @tr_DITSaleTrans and rDit.appliedToTransactionID = it.transactionID
				inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_rev = rDit.transactionID
				inner join dbo.tr_transactions as tAlloc on tAlloc.ownedByOrgID = @orgID and tAlloc.transactionID = alloc.transactionID_alloc and tAlloc.statusID = 1
				inner join dbo.tr_transactions as tPay on tPay.ownedByOrgID = @orgID and tPay.transactionID = alloc.transactionID_cash and tPay.statusID = 1
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = alloc.assignedToMemberID_cash
				where i.orgID = @orgID
				and i.invoiceID = @invoiceID
			) as tmpInner
			group by transactionID, detail, payAmount, transactionDate, TAssignedToMemberID
		) as tmp
		where allocAmount > 0
			union all
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 
			'ADJUSTMENT' + isnull(': ' + t.detail,'') + isnull('<br/>' + nullif(c.invoiceDetail,''),'') as detail, 
			sum(r2.amount*-1) as amount, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, 'Adjustment' as [type]
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID <> it.invoiceID
		LEFT OUTER JOIN dbo.tr_transactionDiscounts as td 
			INNER JOIN dbo.tr_coupons as c on c.couponID = td.couponID
			on td.orgID = @orgID and td.transactionID = t.transactionID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		and t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
		group by t.transactionID, m.activememberid, t.transactionDate, 'ADJUSTMENT' + isnull(': ' + t.detail,'') + isnull('<br/>' + nullif(c.invoiceDetail,''),''), 
			i.fullInvoiceNumber, i.invoiceID
			union all
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID' + isnull(': ' + t.detail,'') as detail, t.amount*-1 as amount, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, 'VoidOffset' as [type]
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Sale
			union all
		SELECT t.transactionID, m.activememberid as TAssignedToMemberID, t.transactionDate, 'VOID ADJUSTMENT' + isnull(': ' + t.detail,'') as detail, 
			amount = case
				when t.creditGLAccountID = @ARGLAID then t.amount*-1
				else t.amount
				end, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, 'VoidOffset' as [type]
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = t.assignedToMemberID 
		inner join dbo.organizations as o on o.orgID = m.orgID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_Sale
			union all
		select transactionID, TAssignedToMemberID, transactionDate, '$' + cast(WOAmount as varchar(15)) + ' ' + detail as detail, AllocAmount*-1 as amount, '' as invoicenumber, 0 as invoiceID, [type]
		from (
			select tWO.transactionID, '** WRITE-OFF ** ' + tWO.detail AS detail, tWO.amount as WOAmount, 
				tWO.transactionDate, m.activememberid as TAssignedToMemberID, 
				sum(tWO.amount) as AllocAmount, 'WriteOff' as [type]
			from dbo.tr_invoices as i
			inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceId = i.invoiceID
			INNER JOIN dbo.tr_transactions as tSale on tSale.ownedByOrgID = @orgID and tSale.transactionID = it.transactionID and tSale.statusID in (1,3)
			inner join dbo.tr_relationships as rS on rS.orgID = @orgID and rS.typeID = @tr_WriteOffSaleTrans and rS.appliedToTransactionID = tSale.transactionID
			inner join dbo.tr_transactions as tWO on tWO.ownedByOrgID = @orgID and tWO.transactionID = rS.transactionID and tWO.statusID = 1
			INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = tWO.assignedToMemberID 
			where i.orgID = @orgID
			and i.invoiceID = @invoiceID
			and tSale.typeID in (1,3)
			and tWO.typeID = @t_WO
			group by tWO.transactionID, tWO.detail, tWO.amount, tWO.transactionDate, m.activememberid
		) as tmp
		where allocAmount > 0
	) as s3
	INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = s3.TAssignedToMemberID 
	inner join dbo.organizations as o on o.orgID = m.orgID
	order by 2, 1;

	-- section 3 tax neg adj to tax from other invoices, voidoffset from other invoices, writeoff of tax
	select gl.GLAccountID as taxGLAccountID, gl.accountName as detail, i.fullInvoiceNumber as invoiceNumber, i.invoiceID, sum(tmp.amount) as amount
	from (
		SELECT t.transactionID, r2.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID, it.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_AdjustTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustInvTrans and r2.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t3.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		and t.creditGLAccountID = @ARGLAID
		AND t.typeID = @t_Adjustment
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, t.amount*-1 as amount, t.debitGLAccountID as taxGLAccountID, it.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_SalesTax
			union all
		SELECT t.transactionID, amount = case
			when t.creditGLAccountID = @ARGLAID then t.amount*-1
			else t.amount
			end, taxGLAccountID = case
				when t.creditGLAccountID = @ARGLAID then t.debitGLAccountID
				else t.creditGLAccountID
				end, it.invoiceID
		FROM dbo.tr_transactions AS t
		INNER JOIN dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = t.transactionID
		INNER JOIN dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = @tr_OffsetTrans and r.transactionID = t.transactionID
		INNER JOIN dbo.tr_transactions as t2 on t2.ownedByOrgID = @orgID and t2.transactionID = r.appliedToTransactionID
		INNER JOIN dbo.tr_invoiceTransactions as it2 on it2.orgID = @orgID and it2.transactionID = t2.transactionID and it2.invoiceID <> it.invoiceID
		INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it2.invoiceID
		INNER JOIN dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = @tr_AdjustTrans and r2.transactionID = t2.transactionID
		INNER JOIN dbo.tr_transactions as t3 on t3.ownedByOrgID = @orgID and t3.transactionID = r2.appliedToTransactionID
		WHERE t.ownedByOrgID = @orgID
		and it2.invoiceID = @invoiceID
		AND t.typeID = @t_VO
		AND t.statusID <> 4
		and t2.typeID = @t_Adjustment
		and t3.typeID = @t_SalesTax
			union all
		select tWO.transactionID, tWO.amount*-1 as amount, tTax.creditGLAccountID as taxGLAccountID, it.invoiceID
		from dbo.tr_invoices as i
		inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceId = i.invoiceID
		INNER JOIN dbo.tr_transactions as tTax on tTax.ownedByOrgID = @orgID and tTax.transactionID = it.transactionID and tTax.statusID IN (1,3)
		inner join dbo.tr_relationships as rS on rS.orgID = @orgID and rS.typeID = @tr_WriteOffSaleTrans and rS.appliedToTransactionID = tTax.transactionID
		inner join dbo.tr_transactions as tWO on tWO.ownedByOrgID = @orgID and tWO.transactionID = rS.transactionID and tWO.statusID = 1
		INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = tWO.assignedToMemberID 
		where i.orgID = @orgID
		and i.invoiceID = @invoiceID
		and tTax.typeID = @t_SalesTax
		and tWO.typeID = @t_WO
	) as tmp
	INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = tmp.taxGLAccountID
	INNER JOIN dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
	INNER JOIN dbo.ams_members m on m.orgID = @orgID and m.memberID = i.assignedToMemberID 
	inner join dbo.organizations as o on o.orgID = m.orgID
	group by gl.GLAccountID, gl.accountName, i.fullInvoiceNumber, i.invoiceID
	order by 2, 3;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
