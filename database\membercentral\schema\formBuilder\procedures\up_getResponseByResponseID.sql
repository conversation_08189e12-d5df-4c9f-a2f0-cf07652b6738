ALTER PROC dbo.up_getResponseByResponseID
@formID int,
@responseID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
		DROP TABLE #tmpQuestions;
	IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
		DROP TABLE #tmpOptions;
	IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
		DROP TABLE #tmpOptionsx;

	DECLARE @formTypeAbbr char(1);

	SELECT @formTypeAbbr = ft.formTypeAbbr
	FROM dbo.tblForms AS f
	INNER JOIN dbo.tblFormTypes AS ft ON ft.formTypeID = f.formTypeID
	WHERE f.formID = @formID;

	-- all questions and their versions
	WITH CTE AS (
		SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, q.questionID as useQuestionID
		FROM dbo.tblQuestions as q 
		inner join dbo.tblSections as s on s.sectionID = q.sectionID
		where s.formID = @formID
		and q.isEnabled = 1
			UNION ALL
		SELECT q.questionID, q.questionTypeID, q.questionText, q.formerQuestionID, CTE.useQuestionID
		FROM dbo.tblQuestions as q
		INNER JOIN CTE ON q.questionID = CTE.formerQuestionID
	)
	select CTE.questionID, CTE.useQuestionID, q.sectionID, CTE.questionTypeID, q.questionText as useQuestionText, 
		q.questionOrder, q.displayQuestionNumber, q.isEnabled
	into #tmpQuestions
	from CTE
	inner join dbo.tblQuestions as q on q.questionID = CTE.useQuestionID;

	-- all options and their versions
	WITH CTE 
	AS (
		SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
		FROM formBuilder.dbo.tblOptions o
		inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
		inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
		where s.formID = @formID
		and o.isEnabled = 1
				UNION ALL
		SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
		FROM formBuilder.dbo.tblOptions as o
		INNER JOIN CTE ON o.optionID = CTE.formerOptionID
	)
	select CTE.optionID, CTE.useOptionID, o.optionText as useOptionText, o.questionID, o.optionOrder, o.isEnabled
	into #tmpOptions
	from CTE
	inner join formBuilder.dbo.tblOptions as o on o.optionID = CTE.useOptionID;

	-- all optionsX and their versions
	WITH CTE 
	AS (
		SELECT o.optionID, o.optionText, o.formerOptionID, o.optionID as useOptionID
		FROM formBuilder.dbo.tblOptionsX o
		inner join formbuilder.dbo.tblQuestions as q on q.questionID = o.questionID
		inner join formbuilder.dbo.tblSections as s on s.sectionID = q.sectionID
		where s.formID = @formID
		and o.isEnabled = 1
				UNION ALL
		SELECT o.optionID, o.optionText, o.formerOptionID, CTE.useOptionID
		FROM formBuilder.dbo.tblOptionsX as o
		INNER JOIN CTE ON o.optionID = CTE.formerOptionID
	)
	select CTE.optionID, CTE.useOptionID, o.optionText as useOptionText, o.questionID, o.optionOrder, o.isEnabled
	into #tmpOptionsX
	from CTE
	inner join formBuilder.dbo.tblOptionsX as o on o.optionID = CTE.useOptionID;

	-- response
	IF @formTypeAbbr = 'V'
		select r.dateDelivered, r.dateCompleted, r.passingPct, m2.memberID, m2.lastName, m2.firstName, m2.memberNumber,
			m2.company, o.orgcode as signuporgcode, s.siteID as signupSiteID
		from dbo.tblResponses as r
		inner join membercentral.dbo.ev_eventsAndFormResponses as eafr on eafr.responseID = r.responseID
		inner join membercentral.dbo.ev_eventsAndForms as eaf on eaf.eventFormID = eafr.eventFormID
		inner join membercentral.dbo.ev_registrants as er on er.registrantID = eafr.registrantID
		inner join membercentral.dbo.ev_registration as evr ON evr.registrationID = er.registrationID
		inner join membercentral.dbo.ev_events as ev on ev.eventID = eaf.eventID
		inner join membercentral.dbo.sites as s on s.siteID = ev.siteID
		inner join membercentral.dbo.organizations as o on o.orgID = s.orgID
		inner join membercentral.dbo.ams_members as m ON er.memberID = m.memberID
		inner join membercentral.dbo.ams_members as m2 ON m2.memberID = m.activeMemberID
		where r.responseID = @responseID
		and r.formID = @formID;
	ELSE
		select r.dateDelivered, r.dateCompleted, r.passingPct, m2.memberID, m2.lastName, m2.firstName, m2.memberNumber,
			m2.company, p.orgcode as signuporgcode, s.siteID as signupSiteID
		from dbo.tblResponses as r
		inner join seminarweb.dbo.tblSeminarsAndFormResponses as safr on safr.responseID = r.responseID
		inner join seminarweb.dbo.tblSeminarsAndForms as saf on saf.seminarFormID = safr.seminarFormID
		inner join seminarWeb.dbo.tblEnrollments as e on e.enrollmentID = safr.enrollmentID
		inner join seminarweb.dbo.tblParticipants as p ON e.participantID = p.participantID
		inner join membercentral.dbo.sites as s on s.siteCode = p.orgcode
		inner join membercentral.dbo.ams_members as m ON e.MCMemberID = m.memberID
		inner join membercentral.dbo.ams_members as m2 ON m2.memberID = m.activeMemberID
		where r.responseID = @responseID
		and r.formID = @formID;

	IF @@ROWCOUNT <> 1
		GOTO on_done;

	-- sections
	select sectionid, sectiontitle, sectionDesc
	from dbo.tblSections
	where formID = @formID
	order by sectionOrder;

	-- questions
	select distinct q.sectionid, q.useQuestionID as questionid, q.questionTypeID, q.displayquestionnumber, 
		q.useQuestionText as questiontext, q.questionOrder
	from #tmpQuestions as q
	inner join dbo.tblResponseDetails rd ON q.questionID = rd.questionID
		and rd.responseID = @responseID
	inner join dbo.tblQuestionTypes as qt on q.questionTypeID = qt.questionTypeID
	order by q.questionOrder;

	-- response detail
	select distinct q.useQuestionID as questionID, rd.responseText, rd.isCorrect, o.useOptionText as optiontext, 
		ox.useOptionText as optionxtext, q.useQuestionID, o.optionOrder, ox.optionOrder
	from #tmpQuestions as q
	inner join dbo.tblResponseDetails rd ON q.questionID = rd.questionID
		and rd.responseID = @responseID
	left outer join #tmpOptions AS o ON o.questionid = q.useQuestionID AND o.isEnabled = 1 and rd.optionID = o.optionID
	left outer join #tmpOptionsX as ox on ox.questionid = q.useQuestionID AND ox.isEnabled = 1 and rd.optionXID = ox.optionID
	order by q.useQuestionID, o.optionOrder, ox.optionOrder;

	on_done:
	IF OBJECT_ID('tempdb..#tmpQuestions') IS NOT NULL 
		DROP TABLE #tmpQuestions;
	IF OBJECT_ID('tempdb..#tmpOptions') IS NOT NULL 
		DROP TABLE #tmpOptions;
	IF OBJECT_ID('tempdb..#tmpOptionsx') IS NOT NULL 
		DROP TABLE #tmpOptionsx;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
