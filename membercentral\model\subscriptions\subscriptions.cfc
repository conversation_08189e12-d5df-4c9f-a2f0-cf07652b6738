<cfcomponent extends="model.AppLoader" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset variables.instanceSettings = structNew()>

	<!--- check for bots --->
	<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
		<cfset variables.isBot = 1>
	<cfelse>
		<cfset variables.isBot = 0>
	</cfif>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<cfset arguments.event.paramValue('suba','list')>

		<cfset this.link.show = '/?pg=manageSubscriptions&suba=show'>
		<cfset this.link.message = '/?pg=manageSubscriptions&suba=message'>

		<!--- bots --->
		<cfif variables.isBot && arguments.event.getValue('suba') neq "message">
			<cflocation url="#this.link.message#&subM=1" addtoken="No">
		</cfif>

		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser) and session.cfcuser.memberdata.identifiedAsMemberID gt 0>
			<cfset arguments.event.setValue('memberid',session.cfcuser.memberdata.identifiedAsMemberID)>
		<cfelse>
			<cfset arguments.event.setValue('memberid',session.cfcuser.memberdata.memberid)>
		</cfif>

		<!--- protect against urlhacking of the suba --->
		<cfset local.validFuncNames = "list,show,lookupSubscription,message,renew,showAddOns,doConfirm,payDues,doPayDues,showReceipt,downloadReceipt">
		<cfif NOT listFindNoCase(local.validFuncNames, arguments.event.getValue('suba'))>
			<cfset local.redirectTo = "/?pg=manageSubscriptions">
			<cfif arguments.event.valueExists('subV') AND isSimpleValue(arguments.event.getValue('subV'))>
				<cfset local.redirectTo = local.redirectTo & "&subV=#arguments.event.getTrimValue('subV')#">
			</cfif>
			<cflocation url="#local.redirectTo#" addtoken="no">
		</cfif>

		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<cfset arguments.event.setValue('instanceSettings',getInstanceSettings(appInstanceID=this.appInstanceID))>

		<cfset local.methodToRun = this[arguments.event.getValue('suba')]>
		<cfreturn local.methodToRun(arguments.event)>
	</cffunction>
	
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.valueExists('subV')>
			<cfreturn show(event=arguments.event)>
		</cfif>

		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory')>
		<cfset local.viewToUse = 'subscriptions/#local.viewDirectory#/lookupSub'>

		<cfset local.dataStruct.actionStruct = {}>
		<cfset local.dataStruct.actionStruct.errorMessage = "">
		<cfif len(arguments.event.getTrimValue('subM',''))>
			<cfset local.dataStruct.actionStruct.errorMessage = message(event=arguments.event).data>
		</cfif>
		<cfset local.dataStruct.actionStruct.formlink = "/?pg=manageSubscriptions&suba=lookupSubscription">

		<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="show" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.encString = arguments.event.getValue('subV','')>

		<cftry>
			<!--- directLinkCode --->
			<cfif len(local.encString) eq 8>
				<cfquery name="local.qrySubscriber" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					select top 1 s.subscriberID, s.memberID, t.siteID
					from dbo.sub_subscribers as s
					inner join dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
					inner join dbo.sub_types as t on t.typeID = subs.typeID
					where s.directLinkCode = <cfqueryparam value="#local.encString#" cfsqltype="CF_SQL_VARCHAR">;
					
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
				<cfif local.qrySubscriber.recordcount>
					<cfset local.incomingStr = {
						'subscriberID': local.qrySubscriber.subscriberID,
						'siteID': local.qrySubscriber.siteID,
						'memberID': local.qrySubscriber.memberID,
					}>
				<cfelse>
					<cfthrow>
				</cfif>

			<!--- try to decrypt link: subid|siteid|memberid --->
			<cfelseif len(local.encString)>
				<cfset local.decString = Decrypt(ToString(ToBinary(URLDecode(Replace(local.encString,"xPcmKx","%","ALL")))),'M3mberC3ntr@l^_2012SUB@')>
				<cfif listLen(local.decString,'|') is 3>
					<cfset local.incomingStr = {
						'subscriberID': GetToken(local.decString,1,'|'),
						'siteID': GetToken(local.decString,2,'|'),
						'memberID': GetToken(local.decString,3,'|'),
					}>
				<cfelse>
					<cfthrow>
				</cfif>
			<cfelse>
				<cfthrow>
			</cfif>
		<cfcatch type="any">
			<cfset local.incomingStr = { siteid=arguments.event.getValue('mc_siteinfo.siteID'), memberid=0, subscriberID=0 }>
		</cfcatch>
		</cftry>

		<!--- site check --->
		<cfif local.incomingStr.siteid is not arguments.event.getValue('mc_siteinfo.siteID')>
			<cflocation url="/?pg=manageSubscriptions&subM=1" addtoken="no">
		</cfif>

		<!--- try to lookup subscriber --->
		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriber">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select subscriberID, subscriptionID, typeName, subscriptionName, status, subActivationCode, RFID, GLAccountID, subStartDate, subEndDate, graceEndDate,
					parentSubscriberID, PCFree, paymentOrder, thePath, thePathExpanded
				from dbo.fn_getRecursiveMemberSubscriptions(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.memberid#">, 
															<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.siteid#">, 
															<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.subscriberID#">)
				where parentSubscriberID is null;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfif local.qrySubscriber.recordCount is 0>
				<cfthrow>
			</cfif>
		<cfcatch type="any">
			<cflocation url="/?pg=manageSubscriptions&subM=2" addtoken="no">
		</cfcatch>
		</cftry>

		<!--- look at all transactions for this sub tree on the first invoice due date (may be multiple invoices). If any due, then redirect to buy now --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberTransactions">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.siteid#">;
			select @orgID = orgID from dbo.sites where siteID = @siteID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int, 
				INDEX IDX_mcSubscriberTransactions_subscriberID_amountdue (subscriberID, amountDue));

			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			select subscriberID
			FROM dbo.sub_subscribers
			WHERE orgID = @orgID
			AND rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.subscriberID#">;

			EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT top 1 sum(st.amountDue) as amountDue
			FROM dbo.sub_subscribers as s
			inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
			WHERE s.orgID = @orgID
			AND s.rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.incomingStr.subscriberID#">
			group by st.dateDue
			order by st.dateDue;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- if not logged-in, set identified by member --->
		<cfset local.memberID = application.objMember.getActiveMemberID(memberID=local.incomingStr.memberID)>
		<cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
			<cfset application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.memberID)>
		</cfif>
		<cfset local.useMID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>

		<!--- check status of sub --->
		<cfif listFindNoCase("A,P",local.qrySubscriber.status)>
			<cfif local.qrySubscriberTransactions.amountDue gt 0>
				<cflocation url="/?pg=manageSubscriptions&suba=payDues&rsid=#local.incomingStr.subscriberID#&mid=#local.memberID#" addtoken="false">
			<cfelse>
				<cflocation url="/?pg=manageSubscriptions&subM=3" addtoken="no">
			</cfif>
		<cfelseif listFindNoCase("X",local.qrySubscriber.status)>
			<cflocation url="/?pg=manageSubscriptions&subM=4" addtoken="no">
		<cfelseif NOT listFindNoCase("O",local.qrySubscriber.status)>
			<cflocation url="/?pg=manageSubscriptions&subM=5" addtoken="no">
		</cfif>
		
		<!--- common vars --->
		<cfset local.rootSubscriberID = local.incomingStr.subscriberID>
		<cfset local.formlink = '/?pg=manageSubscriptions&suba=doConfirm'>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory')>

		<!--- Top Sub Content --->
		<cfset local.topSubscriptionContent = getTopSubscriptionContent(rootSubscriberID=local.rootSubscriberID, memberID=local.incomingStr.memberid)>

		<!--- Parent Subscription Content --->
		<cfset local.parentSubscriptionContent = getParentSubscriptionContent(memberID=local.incomingStr.memberid, rootSubscriberID=local.rootSubscriberID, parentSubscriptionID=local.qrySubscriber.subscriptionID)>
	
		<!--- Member Card --->
		<cfset local.subsAppSettingsXML = arguments.event.getValue('instanceSettings.settingsXML')>
		<cfset local.freeRateDisplay = xmlSearch(local.subsAppSettingsXML,'string(/settings/setting[@name="freeRateDisplay"]/@value)')>
		<cfset local.showMemberPhoto = xmlSearch(local.subsAppSettingsXML,'string(/settings/setting[@name="showPhotosInSubsRenew"]/@value)') EQ 'true' ? true : false>
		<cfset local.qrySubRenewalsFieldSet = createObject("component","model.admin.memberFieldSets.memberFieldSets").getSettingsFieldsetID(siteResourceID=val(arguments.event.getValue('mc_siteInfo.subscriptionAdminSiteResourceID')), 
												area='SubRenewals', module="Subscriptions")>
		<cfset local.subRenewalsFieldsetID = val(local.qrySubRenewalsFieldSet.fieldsetID)>
		<cfset local.strMember = getSubMemberDetails(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.memberID, fieldsetID=local.subRenewalsFieldsetID)>

		<!--- Parent Subscription Card --->
		<cfset local.strRootSub = getRootSubscriberDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>

		<!--- Sub AddOn Cards --->
		<cfset local.loadSubAddOnsLink = "?event=cms.showResource&resID=#arguments.event.getValue('instanceSettings.siteResourceID')#&suba=showAddOns&mid=#local.memberID#&rsid=#local.rootSubscriberID#&mode=stream">
		
		<!--- Billing Tax Info --->
		<cfset local.qryMemberStateZipForTax = getStateZipForTax(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.memberID)>
		<cfset local.stateIDForTax = val(local.qryMemberStateZipForTax.stateIDforTax)>
		<cfset local.zipForTax = local.qryMemberStateZipForTax.zipForTax>
		<cfif len(local.zipForTax) AND NOT application.objCommon.checkBillingZIP(billingZip=local.zipForTax, billingStateID=local.stateIDForTax).isvalidzip>
			<cfset local.zipForTax = "">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/subscriptions/#local.viewDirectory#/frm_manageSubscriptions.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="lookupSubscription" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		
		<!--- clean code --->
		<cfset arguments.event.setValue('vc',ucase(ReReplace(arguments.event.getValue('vc',''),'[^A-Z]','','ALL')))>
		
		<cfquery name="local.qrySubscriber" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select directLinkCode
			from dbo.sub_subscribers
			where directLinkCode = <cfqueryparam value="#left(arguments.event.getValue('vc'),8)#" cfsqltype="CF_SQL_CHAR">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfif local.qrySubscriber.recordcount is 0>
			<cflocation url="/?pg=manageSubscriptions&subM=6" addtoken="no"> 
		<cfelse>
			<cflocation url="/?pg=manageSubscriptions&subV=#local.qrySubscriber.directLinkCode#" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="renew" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.siteID = arguments.event.getValue('mc_siteInfo.siteID')>

		<cfset local.memberid = arguments.event.getValue('memberid',0)>
		<cfif val(local.memberID) eq 0>
			<cflocation url="/?pg=login" addtoken="no">
		</cfif>
		
		<cfquery name="local.qryAdminApplicationSettings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">;

			select ai.settingsXML
			from dbo.cms_applicationInstances as ai
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID
				and sr.siteResourceID = ai.siteResourceID
			where ai.siteID = @siteID
			and ai.applicationInstanceName = 'admin'
			and sr.siteResourceStatusID = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.redirectSubTypeID = XMLSearch(local.qryAdminApplicationSettings.settingsXML,"string(/settings/setting[@name='renewRedirect']/@subscriptiontype)")>
		<cfset local.redirectSubIDs = XMLSearch(local.qryAdminApplicationSettings.settingsXML,"string(/settings/setting[@name='renewRedirect']/@subscriptions)")>
		<cfset local.redirectDefaultContentID = val(XMLSearch(local.qryAdminApplicationSettings.settingsXML,"string(/settings/setting[@name='renewRedirect']/@defaultcontentid)"))>

		<!--- type must be defined --->
		<cfif not len(local.redirectSubTypeID)>
			<cflocation url="/?pg=manageSubscriptions" addtoken="no">
		</cfif>

		<!--- look for a billed subscription --->
		<cfquery name="local.qrySubscription" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 s.subscriberID, s.directLinkCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_subscriptions as subs on subs.subscriptionID = s.subscriptionID
			inner join dbo.sub_statuses as ss on ss.statusID = s.statusID
			inner join dbo.sub_types as t on t.typeID = subs.typeID and t.siteID = #local.siteID#
			inner join dbo.ams_members m on m.memberID = s.memberID
			inner join dbo.ams_members m2 on m2.activeMemberID = m.activeMemberID  
				and m2.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberid#">			
			where
				s.parentSubscriberID is null		
				and t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.redirectSubTypeID#">
				<cfif len(local.redirectSubIDs) and local.redirectSubIDs neq 0>
					and s.subscriptionID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.redirectSubIDs#" list="true">)
				</cfif>
				and ss.statusCode = 'O'
			order by s.subscriberID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qrySubscription.recordcount is 1 and len(local.qrySubscription.directLinkCode)>
			<cflocation url="/renewsub/#local.qrySubscription.directLinkCode#" addtoken="no">
		<cfelseif local.qrySubscription.recordcount is 1>
			<cflocation url="/?pg=manageSubscriptions" addtoken="no">
		<cfelseif local.redirectDefaultContentID gt 0>
			<cfset local.data = CreateObject("component","model.admin.subscriptions.subscriptions").getRedirectMessage(local.redirectDefaultContentID)>
		<cfelse>
			<cflocation url="/?pg=manageSubscriptions" addtoken="no">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div class="tsAppHeading">Membership Renewal</div><br>
			<div class="tsAppBodyText">#local.data#</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="showAddOns" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
	
		<cfset arguments.event.setValue('loadMode',arguments.event.getTrimValue('loadMode','init'))>
		<cfset arguments.event.setValue('mid',int(val(arguments.event.getValue('mid',0))))>
		<cfset arguments.event.setValue('rsid',int(val(arguments.event.getValue('rsid',0))))>
	
		<cfset local.qryMemberSubscriptions = getMemberSubscriptions(memberID=arguments.event.getValue('mid'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), 
												rootSubscriberID=arguments.event.getValue('rsid'))>

		<cfif NOT local.qryMemberSubscriptions.recordCount>
			<cfreturn returnAppStruct("Invalid Subscriber","echo")>
		</cfif>

		<cfset local.topRFID = local.qryMemberSubscriptions.rfid[1]>
		<cfset local.topSubID = local.qryMemberSubscriptions.subscriptionID[1]>
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory')>
		<cfset local.freeRateDisplay = arguments.event.getTrimValue('freeratedisplay','')>
		<cfset local.isRenewalRate = arguments.event.getValue('userenewalrate',0) EQ 1 ? true : false>

		<cfset local.qryAllAddOnSubs = getRecursiveAddOnSubs(siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=arguments.event.getValue('mid'), 
										rootSubscriberID=arguments.event.getValue('rsid'), isRenewalRate=local.isRenewalRate, topSubscriptionID=local.topSubID)>

		<cfif arguments.event.getValue('loadMode') EQ 'reload'>
			<cfset local.topRFID = arguments.event.getValue('parentRFID',0)>

			<cfquery name="local.qrySelectedAddOnSubs" dbtype="query">
				SELECT DISTINCT subscriptionID
				FROM [local].qryAllAddOnSubs
				WHERE subscriptionID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.event.getTrimValue('selectedAddOnSubs','')#">)
				AND addonID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.event.getTrimValue('selectedAddOns','')#">)
			</cfquery>

			<cfset local.freeSubs = "">
			<cfloop list="#arguments.event.getTrimValue('selectedAddOns','')#" index="local.thisAddOnID">
				<cfif len(arguments.event.getTrimValue('addOn#local.thisAddOnID#_freeSubs',''))>
					<cfset local.freeSubs = listAppend(local.freeSubs,arguments.event.getTrimValue('addOn#local.thisAddOnID#_freeSubs'))>
				</cfif>
			</cfloop>

			<cfloop query="local.qrySelectedAddOnSubs">
				<cfset local.tmpStr = {
					'RFID': int(val(arguments.event.getValue('sub#local.qrySelectedAddOnSubs.subscriptionID#_currRFID',0))),
					'rateID': int(val(arguments.event.getValue('sub#local.qrySelectedAddOnSubs.subscriptionID#_currRateID',0))),
					'frequencyID': int(val(arguments.event.getValue('sub#local.qrySelectedAddOnSubs.subscriptionID#_currFreqID',0))),
					"modifiedRate": 0,
					"isModifiedRate": 0,
					'PCFree': listFind(local.freeSubs,local.qrySelectedAddOnSubs.subscriptionID) ? 1 : 0
				}>
				<cfif arguments.event.valueExists('sub#local.qrySelectedAddOnSubs.subscriptionID#_newRateAmt')>
					<cfset local.tmpStr['newRateAmt'] = val(arguments.event.getValue('sub#local.qrySelectedAddOnSubs.subscriptionID#_newRateAmt'))>
				</cfif>

				<cfif local.tmpStr.rateID>
					<cfset local.strMemberSubs[local.qrySelectedAddOnSubs.subscriptionID] = duplicate(local.tmpStr)>
				</cfif>
			</cfloop>
		<cfelse>
			<cfquery name="local.strMemberSubs" dbtype="query" returntype="struct" columnkey="subscriptionID">
				SELECT DISTINCT subscriptionID, rateID, frequencyID, RFID, PCFree, modifiedRate, isModifiedRate, lastPrice
				FROM [local].qryMemberSubscriptions
				WHERE subscriptionID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topSubID#">
			</cfquery>
		</cfif>
		
		<cfif NOT local.keyExists('strMemberSubs')>
			<cfset local.strMemberSubs = {}>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryParentRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT f.frequencyID, f.frequencyName, f.frequencyShortName, rf.numInstallments
			FROM dbo.sub_rates AS r
			INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rateID = r.rateID
			INNER JOIN dbo.sub_frequencies AS f ON f.frequencyID = rf.frequencyID
			WHERE rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">;
		
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strParentFreq = {
			"frequencyID": local.qryParentRate.frequencyID,
			"frequencyName": local.qryParentRate.frequencyName,
			"frequencyShortName": local.qryParentRate.frequencyShortName,
			"numPaymentsToUse": local.qryParentRate.numInstallments
		}>

		<cfset local.strSubDef = structNew('ordered')>
		<cfset local.strSubAddOns = getSubAddOnsStruct(siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=arguments.event.getValue('mid'), 
										subscriptionID=local.topSubID, strSubDef=local.strSubDef, isRenewalRate=local.isRenewalRate, recursionLevel=1, 
										strMemberSubs=local.strMemberSubs, qryAllAddOnSubs=local.qryAllAddOnSubs, selectedParentFreqID=local.qryParentRate.frequencyID)>

		<cfset local.data = renderSubscriptionAddOnForm(strSubAddOns=local.strSubAddOns.addOns, subscriptionID=local.topSubID, strParentFreq=local.strParentFreq,
								freeRateDisplay=local.freeRateDisplay, strEditSubs=local.strMemberSubs, viewDirectory=local.viewDirectory, inlineAddOn=false)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRootSubscriberDetails" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		
		<cfset local.qryMemberSubscriptions = getMemberSubscriptions(memberID=arguments.memberID, siteID=arguments.siteID, rootSubscriberID=arguments.rootSubscriberID)>
	
		<cfif NOT local.qryMemberSubscriptions.recordCount>
			<cfthrow message="Invalid Subscriber">
		</cfif>
	
		<cfset local.topRFID = local.qryMemberSubscriptions.rfid[1]>
		<cfset local.topStatus = local.qryMemberSubscriptions.status[1]>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryParentRate">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			SELECT r.isRenewalRate, rf.numInstallments, f.frequencyID, f.frequencyName, f.frequencyShortName
			FROM dbo.sub_rates AS r
			INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rateID = r.rateID
			INNER JOIN dbo.sub_frequencies AS f ON f.frequencyID = rf.frequencyID
			WHERE rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">;
		
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfif listFindNoCase("R,O",local.topStatus) AND val(local.qryParentRate.isRenewalRate) eq 1>
			<cfset local.isRenewalRate = true>
		<cfelse>
			<cfset local.isRenewalRate = false>
		</cfif>
		
		<cfquery dbtype="query" name="local.qryRootSubscriber">
			SELECT subscriberID, subscriptionID, RFID, rateID, frequencyID, invoiceID, parentSubscriberID, 
				parentSubscriptionID, typeName, thePathExpanded, lastPrice, subStartDate, subEndDate, 
				PCFree, modifiedRate, isModifiedRate
			FROM [local].qryMemberSubscriptions
			WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">
		</cfquery>

		<cfquery name="local.strEditSub" dbtype="query" returntype="struct" columnkey="subscriptionID">
			SELECT subscriptionID, rateID, frequencyID, RFID, PCFree, modifiedRate, isModifiedRate, lastPrice
			FROM [local].qryRootSubscriber
		</cfquery>

		<cfset local.rootSubscription = getSubscriptionDetails(siteID=arguments.siteID, memberID=arguments.memberID, isRoot=true, subscriptionID=local.qryRootSubscriber.subscriptionID,
					parentSubRateFreqID=local.qryRootSubscriber.frequencyID, isRenewalRate=local.isRenewalRate, selectedSubscriptionID=local.qryRootSubscriber.subscriptionID,
					selectedRFID=local.qryRootSubscriber.RFID)>

		<cfset local.strRootSub = { 
			"subscriberID": local.qryRootSubscriber.subscriberID,
			"memberID": arguments.memberID,
			"qryRootSubscriber": local.qryRootSubscriber,
			"subscription": local.rootSubscription,
			"isRenewalRate": local.isRenewalRate,
			"strEditSub": local.strEditSub,
			"strParentFreq": {
				"frequencyID": local.qryParentRate.frequencyID,
				"frequencyName": local.qryParentRate.frequencyName,
				"frequencyShortName": local.qryParentRate.frequencyShortName,
				"numPaymentsToUse": local.qryParentRate.numInstallments
			}
		}>
		
		<cfreturn local.strRootSub>
	</cffunction>

	<cffunction name="getMemberSubscriptions" access="private" output="false" returntype="query">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="siteID" type="numeric" required="true">		
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="hideDeleted" type="boolean" required="false" default="true">

		<cfset var qryMemberSubscriptions = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryMemberSubscriptions">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int, 
				INDEX IDX_mcSubscriberTransactions_subscriberID_invoiceID_payprofileID (subscriberID, invoiceID, payProfileID));

			DECLARE @siteID int, @orgID int, @memberID int, @rootSubscriberID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SELECT @orgID = orgID from dbo.sites where siteID = @siteID;

			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			SET @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;

			DECLARE @subscriptionTable TABLE (subscriberID int, subscriptionID int, typeName varchar(100), subscriptionName varchar(300),
				status varchar(1), statusName varchar(50), paymentStatus varchar(1), paymentStatusName varchar(50), RFID int, rateTermDateFlag varchar(1),
				GLAccountID int, subStartDate datetime, subEndDate datetime, graceEndDate datetime, parentSubscriberID int, rootSubscriberID int, PCFree bit,
				modifiedRate decimal(18,2), paymentOrder int, subActivationCode varchar(1), allowRateGLAccountOverride bit, topRow varchar(max), thePath varchar(max),
				thePathExpanded varchar(max), lastPrice decimal(18,2), linkedNonRenewalRateID int, fallbackRenewalRateID int, keepChangedPriceOnRenewal bit,
				frontEndAllowChangePrice int, frontEndChangePriceMin decimal(18,2), frontEndChangePriceMax decimal(18,2), recogStartDate datetime, recogEndDate datetime);

			INSERT INTO @subscriptionTable (subscriberID, subscriptionID, typeName, subscriptionName, status, statusName, paymentStatus, paymentStatusName,
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, 
				paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastPrice, linkedNonRenewalRateID, 
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, 
				recogStartDate, recogEndDate)
			SELECT subscriberID, subscriptionID, typeName, subscriptionName, status, statusName, paymentStatus, paymentStatusName,
				RFID, rateTermDateFlag, GLAccountID, subStartDate, subEndDate, graceEndDate, parentSubscriberID, rootSubscriberID, PCFree, modifiedRate, 
				paymentOrder, subActivationCode, allowRateGLAccountOverride, topRow, thePath, thePathExpanded, lastPrice, linkedNonRenewalRateID, 
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, frontEndChangePriceMin, frontEndChangePriceMax, 
				recogStartDate, recogEndDate
			FROM dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @rootSubscriberID);

			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			select subscriberID
			from @subscriptionTable;

			EXEC dbo.sub_getSubscriberTransactions_NoTax @orgID=@orgID;

			select rms.subscriberID, rms.subscriptionID, rms.typeName, rms.subscriptionName, rms.status, rms.RFID, 
				rms.rateTermDateFlag, rms.GLAccountID, rms.subStartDate, rms.subEndDate, rms.graceEndDate, rms.parentSubscriberID, 
				rms.subActivationCode, rms.allowRateGLAccountOverride, rms.PCFree, rms.modifiedRate, rms.lastPrice, 
				case when rms.modifiedRate is null then 0 else 1 end as isModifiedRate,
				rms.paymentOrder as subPayOrder, rms.thePath, rms.thePathExpanded, rms.recogStartDate, rms.recogEndDate, 
				rms.linkedNonRenewalRateID,rms.fallbackRenewalRateID, rms.keepChangedPriceOnRenewal, rms.frontEndAllowChangePrice, 
				rms.frontEndChangePriceMin, rms.frontEndChangePriceMax, s.subscriptionID as parentSubscriptionID, rf.rateID, 
				r.rateName, r.isRenewalRate, rf.frequencyID, f.frequencyName, f.frequencyShortName, r.forceUpfront, 
				st.invoiceID, st.payProfileID
			from @subscriptionTable as rms
			inner join dbo.sub_rateFrequencies as rf on rf.rfid = rms.rfid
			inner join dbo.sub_rates as r on r.rateID = rf.rateID
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
				and f.siteID = @siteID
			left outer join dbo.sub_subscribers as s on s.subscriberID = rms.parentSubscriberID
			left outer join ##mcSubscriberTransactions as st on st.subscriberID = rms.subscriberID
			<cfif arguments.hideDeleted>
				where rms.[status] <> 'D'
			</cfif>
			order by rms.thePath;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
		</cfquery>

		<cfreturn qryMemberSubscriptions>
	</cffunction>

	<cffunction name="getAddOnSubscriptions" access="public" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="addonID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @siteID int, @addonID int, @memberID int, @FID int, @groupPrintID int;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @addonID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.addonID#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			SET @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;
			
			-- ensure active member is considered
			SELECT @memberID = activeMemberID FROM dbo.ams_members WHERE memberID = @memberID;
			SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = @memberID;

			SELECT sub.subscriptionID, sub.[uid], sub.subscriptionName, sub.rateTermDateFlag,
				sub.GLAccountID, sub.allowRateGLAccountOverride, sub.paymentOrder, o.subActivationCode,
				o2.subActivationCode AS subAlternateActivationCode, rs.scheduleID AS rateScheduleID,
				rs.scheduleName AS rateScheduleName, rs.status AS rateScheduleStatus, 
				rs.[uid] AS rateScheduleUID
			FROM dbo.sub_addons AS ao
			INNER JOIN dbo.sub_sets AS subsets on subsets.setID = ao.childSetID
				AND ao.addonID = @addonID
			INNER JOIN dbo.sub_subscriptionSets AS ss on subsets.setID = ss.setID
			INNER JOIN dbo.sub_subscriptions AS sub on ss.subscriptionID = sub.subscriptionID 
				AND sub.[status] = 'A'
			INNER JOIN dbo.sub_rateSchedules AS rs on rs.scheduleID = sub.scheduleID
				AND rs.[status] = 'A'
			INNER JOIN dbo.sub_rates AS r on r.scheduleID = rs.scheduleID 
				AND r.[status] = 'A' 
				AND r.isRenewalRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRenewalRate#">
				AND GETDATE() BETWEEN r.rateAFStartDate AND DATEADD(DAY, DATEDIFF(DAY, 0, r.rateAFEndDate)+1, 0)
			INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
				AND srfrp.siteResourceID = r.siteResourceID
			    AND srfrp.functionID = @FID
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
				AND gprp.rightPrintID = srfrp.rightPrintID
				AND gprp.groupPrintID = @groupPrintID
			INNER JOIN dbo.sub_activationOptions AS o ON o.subActivationID = sub.subActivationID
			INNER JOIN dbo.sub_activationOptions AS o2 ON o2.subActivationID = sub.subAlternateActivationID
			GROUP BY sub.subscriptionID, sub.uid, sub.subscriptionName, sub.rateTermDateFlag,
				sub.GLAccountID, sub.allowRateGLAccountOverride, sub.paymentOrder, o.subActivationCode,
				o2.subActivationCode, rs.scheduleID, rs.scheduleName, rs.status, rs.uid, ss.orderNum
			ORDER BY ss.orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrSubs = arrayNew(1)>
		<cfloop query="local.subs">
			<cfset arrayAppend(local.arrSubs, {
				"subscriptionID": local.subs.subscriptionID,
				"uid": local.subs.uid,
				"subscriptionName": local.subs.subscriptionName,
				"rateTermDateFlag": local.subs.rateTermDateFlag,
				"GLAccountID": local.subs.GLAccountID,
				"allowRateGLAccountOverride": local.subs.allowRateGLAccountOverride,
				"paymentOrder": local.subs.paymentOrder,
				"subActivationCode": local.subs.subActivationCode,
				"subAlternateActivationCode": local.subs.subAlternateActivationCode,
				"rateScheduleID": local.subs.rateScheduleID,
				"rateScheduleName": local.subs.rateScheduleName,
				"rateScheduleStatus": local.subs.rateScheduleStatus,
				"rateScheduleUID": local.subs.rateScheduleUID
			})>
		</cfloop>

		<cfreturn local.arrSubs>
	</cffunction>

	<cffunction name="getRecursiveAddOnSubs" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="topSubscriptionID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAddOnSubs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			DECLARE @siteID int, @orgID int, @memberID int, @rootSubscriberID int, @topSubscriptionID int, 
				@FID int, @groupPrintID int, @isRenewalRate bit;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			SET @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;
			SET @topSubscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.topSubscriptionID#">;
			SET @isRenewalRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRenewalRate#">;
			SET @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;
	
			SELECT @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	
			-- ensure active member is considered
			SELECT @memberID = activeMemberID FROM dbo.ams_members WHERE memberID = @memberID;
			SELECT @groupPrintID = groupPrintID FROM dbo.ams_members WHERE memberID = @memberID;
													
			WITH addOnSubs AS (
				SELECT subscriptionID, subscriptionName, addonID, setid, setName, addOnSubscriptionID, frontEndAllowSelect,
					CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath, 1 AS recursionLevel
				FROM (
					SELECT s.subscriptionID, s.subscriptionName, ao.addonID, sets.setid, sets.setName, ao.subscriptionID as addOnSubscriptionID,
						CONVERT(integer, ao.frontEndAllowSelect) as frontEndAllowSelect,
						ROW_NUMBER() OVER (ORDER BY ao.orderNum, ss.orderNum) AS theRow
					FROM dbo.sub_addons as ao
					INNER JOIN dbo.sub_sets as sets on sets.setID = ao.childSetID
					INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
					INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID 
						AND s.[status] = 'A'
					INNER JOIN dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID 
						AND rs.status = 'A'
					WHERE ao.subscriptionID = @topSubscriptionID
					AND EXISTS (
						SELECT 1
						FROM dbo.sub_rates as r
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteID
							AND srfrp.siteResourceID = r.siteResourceID
							AND srfrp.functionID = @FID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
							AND gprp.rightPrintID = srfrp.rightPrintID
							AND gprp.groupPrintID = @groupPrintID
						WHERE r.scheduleID = rs.scheduleID 
						AND r.[status] = 'A' 
						AND r.isRenewalRate = @isRenewalRate
						AND GETDATE() BETWEEN r.rateAFStartDate AND dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
					)
					GROUP BY ao.orderNum, ss.orderNum, s.subscriptionID, s.subscriptionName, ao.addonID, sets.setid, sets.setName, ao.subscriptionID, ao.frontEndAllowSelect
				) as x
	
				UNION ALL
	
				SELECT subscriptionID, subscriptionName, addonID, setid, setName, addOnSubscriptionID, frontEndAllowSelect,
						thePath + '.' + CAST(RIGHT('100000'+theRow,4) as varchar(max)) AS thePath, recursionLevel
				FROM (
					SELECT s.subscriptionID, s.subscriptionName, ao.addonID, sets.setid, sets.setName, ao.subscriptionID as addOnSubscriptionID,
						CONVERT(integer, ao.frontEndAllowSelect) as frontEndAllowSelect,
						scte.thePath, scte.recursionLevel + 1 AS recursionLevel,
						ROW_NUMBER() OVER (ORDER BY ao.orderNum, ss.orderNum) AS theRow
					FROM dbo.sub_addons as ao
					INNER JOIN dbo.sub_sets as sets on sets.setID = ao.childSetID
					INNER JOIN dbo.sub_subscriptionSets as ss on sets.setID = ss.setID
					INNER JOIN dbo.sub_subscriptions as s on s.subscriptionID = ss.subscriptionID and s.status = 'A'
					INNER JOIN dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID and rs.status = 'A'
					INNER JOIN addOnSubs as scte on ao.subscriptionID = scte.subscriptionID
					WHERE EXISTS (
						SELECT 1
						FROM dbo.sub_rates as r
						INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp ON srfrp.siteID = @siteID
							AND srfrp.siteResourceID = r.siteResourceID
							AND srfrp.functionID = @FID
						INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON gprp.siteID = @siteID
							AND gprp.rightPrintID = srfrp.rightPrintID
							AND gprp.groupPrintID = @groupPrintID
						WHERE r.scheduleID = rs.scheduleID
						AND r.[status] = 'A' 
						AND r.isRenewalRate = @isRenewalRate
						AND GETDATE() BETWEEN r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
					)
				) as y
			)
			SELECT ao.subscriptionID, ao.subscriptionName, ao.addonID, ao.setID, ao.setName, ao.addOnSubscriptionID, 
				ao.frontEndAllowSelect, ao.thePath, ao.recursionLevel
			FROM addOnSubs as ao
			LEFT OUTER JOIN dbo.sub_subscribers AS ss 
				INNER JOIN sub_statuses AS st ON st.statusID = ss.statusID 
					AND st.canEdit = 1
				ON ss.orgID = @orgID
					AND ss.rootSubscriberID = @rootSubscriberID
					AND ss.subscriptionID = ao.subscriptionID
			WHERE (ao.frontEndAllowSelect = 1 OR ss.subscriberID IS NOT NULL)
			GROUP BY ao.subscriptionID, ao.subscriptionName, ao.addonID, ao.setID, ao.setName, ao.addOnSubscriptionID, 
				ao.frontEndAllowSelect, ao.thePath, ao.recursionLevel
			ORDER BY ao.thePath
			OPTION(RECOMPILE);
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
	
		<cfreturn local.qryAddOnSubs>
	</cffunction>

	<cffunction name="getSubscriptionAddOns" access="public" output="no" returntype="array">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="qryAllAddOnSubs" type="query" required="true">

		<cfset var local = structNew()>
		<cfset local.arrSubAddOns = []>

		<cfquery name="local.qryAddOnIDs" dbtype="query">
			SELECT DISTINCT addOnID
			FROM arguments.qryAllAddOnSubs
			WHERE addOnSubscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">
		</cfquery>

		<cfif NOT local.qryAddOnIDs.recordCount>
			<cfreturn local.arrSubAddOns>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subAddonsSet">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @subscriptionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">;

			SELECT sets.setID, sets.uid as setUID, ao.addOnID, sets.setName, ao.useAcctCodeInSet, ao.PCnum, ao.PCPctOffEach, 
				ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, isnull(ao.minAllowed,0) as minAllowed,
				isnull(ao.maxAllowed,0) as maxAllowed, ao.frontEndAddAdditional
			FROM dbo.sub_addons as ao
			INNER JOIN dbo.sub_sets as sets on sets.setID = ao.childSetID 
				AND ao.subscriptionID = @subscriptionID
				AND ao.addOnID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#valueList(local.qryAddOnIDs.addOnID)#">)
			ORDER BY ao.orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfloop query="local.subAddonsSet">
			<cfset arrayAppend(local.arrSubAddOns, {
				"setID": local.subAddonsSet.setID,
				"setUID": local.subAddonsSet.setUID,
				"addOnID": local.subAddonsSet.addOnID,
				"setName": local.subAddonsSet.setName,
				"useAcctCodeInSet": local.subAddonsSet.useAcctCodeInSet,
				"PCnum": local.subAddonsSet.PCnum,
				"PCPctOffEach": local.subAddonsSet.PCPctOffEach,
				"frontEndAllowSelect": local.subAddonsSet.frontEndAllowSelect,
				"frontEndAllowChangePrice": local.subAddonsSet.frontEndAllowChangePrice,
				"frontEndContentID": local.subAddonsSet.frontEndContentID,
				"minAllowed": local.subAddonsSet.minAllowed,
				"maxAllowed": local.subAddonsSet.maxAllowed,
				"frontEndAddAdditional": local.subAddonsSet.frontEndAddAdditional,
				"frontEndContent": application.objCMS.getStaticContent(contentID=local.subAddonsSet.frontEndContentID, languageID=1, skipMerge=1).rawcontent
			})>
		</cfloop>

		<cfreturn local.arrSubAddOns>
	</cffunction>

	<cffunction name="renderSubscriptionAddOnForm" access="private" output="false" returntype="string">
		<cfargument name="strSubAddOns" type="struct" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="strParentFreq" type="struct" required="true">
		<cfargument name="freeRateDisplay" type="string" required="true">
		<cfargument name="strEditSubs" type="struct" required="true">
		<cfargument name="viewDirectory" type="string" required="true">
		<cfargument name="inlineAddOn" type="boolean" required="true">

		<cfset var local = structNew()>
		
		<cfset var strSubAddOns = duplicate(arguments.strSubAddOns)>
		<cfset local.addOns = strSubAddOns.filter(
								function(addOnID) {
									var thisSubAddOn = duplicate(strSubAddOns[addOnID])
									return thisSubAddOn.subscriptions.len() GT 0;
								})>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="/views/subscriptions/#arguments.viewDirectory#/frm_addons.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="renderSubscriptionsForm" access="private" output="false" returntype="string">
		<cfargument name="arrSubs" type="array" required="true">
		<cfargument name="strAddOn" type="struct" required="true">
		<cfargument name="parentSubscriptionID" type="numeric" required="true">
		<cfargument name="strParentFreq" type="struct" required="true">
		<cfargument name="recursionLevel" type="numeric" required="true">
		<cfargument name="freeRateDisplay" type="string" required="true">
		<cfargument name="strEditSubs" type="struct" required="true">
		<cfargument name="viewDirectory" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.arrSubs = arguments.arrSubs.filter(
								function(thisRow) {
									return arguments.thisRow.qryRates.recordCount GT 0;
								})>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/subscriptions/#arguments.viewDirectory#/frm_subscriptions.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getTopSubscriptionContent" access="private" output="no" returntype="string">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">

		<cfset var qryTopSubscriptionContent = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTopSubscriptionContent">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @subFrontEndContentID int, @typeFrontEndContentID int, @subRawContent varchar(max), @typeRawContent varchar(max);

			select @siteID = t.siteID, @subFrontEndContentID = s.frontEndContentID, @typeFrontEndContentID = t.frontEndContentID
			from dbo.sub_subscriptions as s
			inner join dbo.sub_subscribers as ss on ss.subscriptionID = s.subscriptionID
				and ss.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">
			inner join dbo.sub_types as t on s.typeID = t.typeID;
			
			select @subRawContent = rtrim(ltrim(cv.rawContent))
			from dbo.cms_contentLanguages as cl
			inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentID = @subFrontEndContentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			where cl.siteID = @siteID
			and cl.contentID = @subFrontEndContentID
			and cl.languageID = 1;

			select @typeRawContent = rtrim(ltrim(cv.rawContent))
			from dbo.cms_contentLanguages as cl
			inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentID = @typeFrontEndContentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			where cl.siteID = @siteID
			and cl.contentID = @typeFrontEndContentID
			and cl.languageID = 1;

			SELECT isnull(nullif(@subRawContent,''),@typeRawContent) as subRawContent;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var topSubscriptionContent = CreateObject("component","model.admin.subscriptions.subscriptions").parseContentWithMergeCodes(content=qryTopSubscriptionContent.subRawContent, 
			memberID=arguments.memberID, subscriberID=arguments.rootSubscriberID).content>

		<cfreturn topSubscriptionContent>
	</cffunction>

	<cffunction name="getTopSubscriptionCompletedContent" access="private" output="no" returntype="string">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="topSubscriptionID" type="numeric" required="true">

		<cfset var qryTopSubscriptionCompletedContent = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryTopSubscriptionCompletedContent">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @subFrontEndContentID int, @typeFrontEndContentID int, @subRawContent varchar(max), @typeRawContent varchar(max);

			select @siteID = t.siteID, @subFrontEndContentID = s.frontEndCompletedContentID, @typeFrontEndContentID = t.frontEndCompletedContentID
			from dbo.sub_subscriptions as s
			inner join dbo.sub_types as t on s.typeID = t.typeID
			where s.subscriptionID = <cfqueryparam value="#arguments.topSubscriptionID#" cfsqltype="CF_SQL_INTEGER">;
			
			select @subRawContent = rtrim(ltrim(cv.rawContent))
			from dbo.cms_contentLanguages as cl
			inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentID = @subFrontEndContentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			where cl.siteID = @siteID
			and cl.contentID = @subFrontEndContentID
			and cl.languageID = 1;

			select @typeRawContent = rtrim(ltrim(cv.rawContent))
			from dbo.cms_contentLanguages as cl
			inner join dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentID = @typeFrontEndContentID
				and cv.contentLanguageID = cl.contentLanguageID 
				and cv.isActive = 1
			where cl.siteID = @siteID
			and cl.contentID = @typeFrontEndContentID
			and cl.languageID = 1;

			SELECT isnull(nullif(@subRawContent,''),@typeRawContent) as subRawContent;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var topSubscriptionCompletedContent = CreateObject("component","model.admin.subscriptions.subscriptions").parseContentWithMergeCodes(content=qryTopSubscriptionCompletedContent.subRawContent, 
			memberID=arguments.memberID, subscriberID=arguments.rootSubscriberID).content>

		<cfreturn topSubscriptionCompletedContent>
	</cffunction>

	<cffunction name="getParentSubscriptionContent" access="private" output="no" returntype="string">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="parentSubscriptionID" type="numeric" required="true">

		<cfset var qryParentSubscriptionContent = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryParentSubscriptionContent">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select frontEndParentSubContent.rawContent as frontEndParentSubContent
			from dbo.sub_subscriptions s
			cross apply dbo.fn_getContent(s.frontEndParentSubContentID,1) as frontEndParentSubContent
			where s.subscriptionID = <cfqueryparam value="#arguments.parentSubscriptionID#" cfsqltype="CF_SQL_INTEGER">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset var parentSubscriptionContent = CreateObject("component","model.admin.subscriptions.subscriptions").parseContentWithMergeCodes(content=qryParentSubscriptionContent.frontEndParentSubContent, 
												memberID=arguments.memberID, subscriberID=arguments.rootSubscriberID).content>

		<cfreturn parentSubscriptionContent>
	</cffunction>

	<cffunction name="getSubMemberDetails" access="public" output="false" returntype="struct">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="fieldsetID" type="numeric" required="true">
	
		<cfset var local = structNew()>
	
		<cfquery name="local.qryMember" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
			IF OBJECT_ID('tempdb..##tmpMember') IS NOT NULL
				DROP TABLE ##tmpMember;
			IF OBJECT_ID('tempdb..##tmp_memberForFS') IS NOT NULL
				DROP TABLE ##tmp_memberForFS;
			CREATE TABLE ##tmpMember (MFSAutoID int IDENTITY(1,1) not null);
			CREATE TABLE ##tmp_memberForFS (memberID int PRIMARY KEY, lastName varchar(75), firstName varchar(75), 
				memberNumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200));
	
			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">,
				@fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldsetID#">,
				@outputFieldsXML xml;
	
			-- get fieldset data
			INSERT INTO ##tmp_memberForFS (memberID, lastName, firstName, memberNumber, MCAccountStatus, hasMemberPhotoThumb, company)
			SELECT mActive.memberID, mActive.lastName, mActive.firstName, mActive.memberNumber, mActive.[status], mActive.hasMemberPhotoThumb, mActive.company
			FROM dbo.ams_members AS m
			INNER JOIN dbo.ams_members AS mActive ON mActive.orgID = @orgID
				AND mActive.memberID = m.activeMemberID
			WHERE m.orgID = @orgID
			AND m.memberID = @memberID
			AND mActive.[status] IN ('A','I');
	
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_memberForFS', @membersResultTableName='##tmpMember',
					@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
	
			SELECT tmp.lastName, tmp.firstName, tmp.memberNumber, tmp.MCAccountStatus, tmp.hasMemberPhotoThumb, tmp.company, 
				tmpM.*, @outputFieldsXML AS mc_outputFieldsXML
			FROM ##tmp_memberForFS as tmp
			INNER JOIN ##tmpMember as tmpM on tmpM.memberID = tmp.memberID;
			
			IF OBJECT_ID('tempdb..##tmpMember') IS NOT NULL
				DROP TABLE ##tmpMember;
			IF OBJECT_ID('tempdb..##tmp_memberForFS') IS NOT NULL
				DROP TABLE ##tmp_memberForFS;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strMember = structNew()>
		<cfif NOT local.qryMember.recordCount>
			<cfreturn local.strMember>
		</cfif>
	
		<cfset local.xmlResultFields = local.qryMember.mc_outputFieldsXML[1]>
		<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
		
		<!--- remove fields from qryOutputFields that are handled manually --->
		<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
			SELECT *
			FROM [local].qryOutputFields
			WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
		</cfquery>

		<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.orgID, includeTags=1)>
		<cfset local.strOrgAddressTypes = structNew()>
		<cfloop query="local.orgAddressTypes">
			<cfif local.orgAddressTypes.isTag is 1>
				<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
			<cfelse>
				<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
			</cfif>
		</cfloop>
	
		<cfset local.strMember['memberID'] = local.qryMember.memberID>
		<cfset local.strMember['memberNumber'] = local.qryMember.memberNumber>
		<cfset local.strMember['company'] = local.qryMember.company>
		<cfset local.strMember['mcaccountstatus'] = local.qryMember.MCAccountStatus>
		<cfset local.strMember['hasPhoto'] = val(local.qryMember.hasMemberPhotoThumb)>
		<cfif val(local.qryMember.hasMemberPhotoThumb)>
			<cfset local.strMember['memberphoto'] = local.qryMember.membernumber & ".jpg">
		<cfelse>
			<cfset local.strMember['memberphoto'] = "">
		</cfif>
	
		<cfset local.strMember['mc_combinedName'] = local.qryMember['Extended Name'][local.qryMember.currentrow]>
	
		<!--- combine address fields if there are any --->
		<cfset local.mc_combinedAddresses = structNew()>
		<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
		<cfloop array="#local.tmp#" index="local.thisField">
			<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
			<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
				<cfset local.strKey = "t#local.thisATID#">
			<cfelse>
				<cfset local.strKey = local.thisATID>
			</cfif>
			<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
				<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
			</cfif>
		</cfloop>
		<cfloop collection="#local.mc_combinedAddresses#" item="local.thisATID">
			<cfsavecontent variable="local.thisATFull">
				<cfoutput>
				<cfif left(local.thisATID,1) eq "t">
					<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
					<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
				<cfelse>
					<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
					<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
				</cfif>
	
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>#local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#<br/> </cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>#local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#<br/> </cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>#local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#<br/></cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>#local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#</cfif>
				<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
				<cfif arrayLen(local.tmp2) is 1 and len(local.qryMember[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>, #local.qryMember[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]# </cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])> #local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#<br/></cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>#local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]# County<br/></cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
				<cfif arrayLen(local.tmp) is 1 and len(local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])> #local.qryMember[local.tmp[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]#<br/></cfif>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
				<cfloop array="#local.tmp#" index="local.thisPT">
					<cfif len(local.qryMember[local.thisPT.xmlAttributes.FieldLabel][local.qryMember.currentrow])>
						<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryMember[local.thisPT.xmlAttributes.FieldLabel][local.qryMember.currentrow]#</div>
					</cfif>
				</cfloop>
				</cfoutput>
			</cfsavecontent>
			<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
			<cfif left(local.thisATfull,2) eq ", ">
				<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
			</cfif>
			<cfif len(local.thisATfull)>
				<cfset local.mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
			<cfelse>
				<cfset structDelete(local.mc_combinedAddresses,local.thisATID,false)>
			</cfif>
		</cfloop>
	
		<!--- get recordtype if available --->
		<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
		<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryMember[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>
			<cfset local.strMember['mc_recordType'] = local.qryMember[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]>
		<cfelse>
			<cfset local.strMember['mc_recordType'] = "">
		</cfif>
		
		<!--- get membertypeid if available --->
		<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
		<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryMember[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>
			<cfset local.strMember['mc_memberType'] = local.qryMember[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]>
		<cfelse>
			<cfset local.strMember['mc_memberType'] = "">
		</cfif>
		
		<!--- get status if available --->
		<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
		<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryMember[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>
			<cfset local.strMember['mc_memberStatus'] = local.qryMember[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow]>
		<cfelse>
			<cfset local.strMember['mc_memberStatus'] = "">
		</cfif>
	
		<!--- get last login date if available --->
		<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
		<cfif arrayLen(local.LastLoginDateInFS) is 1>
			<cfif len(local.qryMember[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow])>
				<cfset local.strMember['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryMember[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryMember.currentrow],"m/d/yy")#'>
			<cfelse>
				<cfset local.strMember['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
			</cfif>
		<cfelse>
			<cfset local.strMember['mc_lastlogin'] = "">
		</cfif>				
	
		<cfif StructCount(local.mc_combinedAddresses)>
			<cfsavecontent variable="local.thisMemCombinedAddress">
				<cfoutput>
				<cfloop collection="#local.mc_combinedAddresses#" item="local.thisATID">
					<div><b style="font-size:95%; margin-right:15px;">#local.mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.mc_combinedAddresses[local.thisATID]['addr']#</div>
				</cfloop>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strMember['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
		<cfelse>
			<cfset local.strMember['mc_combinedAddresses'] = "">
		</cfif>
	
		<cfif local.qryOutputFieldsForLoop.recordCount>
			<cfsavecontent variable="local.thisMemExtraInfo">
				<cfoutput>
				<cfloop query="local.qryOutputFieldsForLoop">
					<cfset local.currValue = local.qryMember[local.qryOutputFieldsForLoop.fieldLabel][local.qryMember.currentrow]>
					<cfif len(local.currValue)>
						<div>
							#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
							<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
								#dollarFormat(local.currValue)#
							<cfelse>
								<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
									<cfcase value="DATE">
										#dateFormat(local.currValue,"m/d/yyyy")#
									</cfcase>
									<cfcase value="STRING,DECIMAL2,INTEGER">
										#local.currValue#
									</cfcase>
									<cfcase value="BIT">
										#YesNoFormat(local.currValue)#
									</cfcase>
								</cfswitch>
							</cfif>
						</div>
					</cfif>
				</cfloop>
				</cfoutput>
			</cfsavecontent>
			<cfset local.strMember['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
		<cfelse>
			<cfset local.strMember['mc_extraInfo'] = "">
		</cfif>
	
		<cfreturn local.strMember>
	</cffunction>

	<cffunction name="getSubAddOnsStruct" access="public" output="no" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="strSubDef" type="struct" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="strMemberSubs" type="struct" required="true">
		<cfargument name="qryAllAddOnSubs" type="query" required="true">
		<cfargument name="selectedParentFreqID" type="numeric" required="true">
		<cfargument name="isAdminListing" type="boolean" required="false" default="0">
		
		<cfset var local = structNew()>
		<cfset local.strMemberSubs = duplicate(arguments.strMemberSubs)>
		<cfset local.arrAddOns = getSubscriptionAddOns(subscriptionID=arguments.subscriptionID, qryAllAddOnSubs=arguments.qryAllAddOnSubs)>

		<cfset arguments.strSubDef['addOns'] = structNew('ordered')>
		<cfloop from="1" to="#ArrayLen(local.arrAddOns)#" index="local.i">
			<cfset local.thisAddOn = duplicate(local.arrAddOns[local.i])>

			<cfquery name="local.qryThisAddOnRecursionLevel" dbtype="query" maxrows="1">
				SELECT recursionLevel
				FROM arguments.qryAllAddOnSubs
				WHERE addOnID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisAddOn.addonID#">
			</cfquery>
	
			<cfset local.arrSubs = getAddOnSubscriptions(siteID=arguments.siteID, addonID=local.thisAddOn.addonID, memberID=arguments.memberID, isRenewalRate=arguments.isRenewalRate)>
			<cfset arguments.strSubDef.addOns[local.thisAddOn.addonID]['subscriptions'] = arrayNew(1)>
			<cfset arguments.strSubDef.addOns[local.thisAddOn.addonID]['strAddOn'] = duplicate(local.thisAddOn)> 
			<cfset arguments.strSubDef.addOns[local.thisAddOn.addonID]['recursionLevel'] = local.qryThisAddOnRecursionLevel.recursionLevel>
			
			<cfloop from="1" to="#ArrayLen(local.arrSubs)#" index="local.j">
				<cfset local.thisSub = duplicate(local.arrSubs[local.j])>
				
				<cfquery name="local.qryThisAddOnSubPath" dbtype="query">
					SELECT thePath
					FROM arguments.qryAllAddOnSubs
					WHERE addOnID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisAddOn.addonID#">
					AND subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisSub.subscriptionID#">
				</cfquery>
				<cfquery name="local.qryThisAddOnSubMaxRecursionLevelAddOns" dbtype="query">
					SELECT MAX(recursionLevel) AS recursionLevel
					FROM arguments.qryAllAddOnSubs
					WHERE thePath LIKE <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryThisAddOnSubPath.thePath#.%">
				</cfquery>
				
				<cfset local.thisSubSelectedRateID = local.strMemberSubs.keyExists(local.thisSub.subscriptionID) ? local.strMemberSubs[local.thisSub.subscriptionID].rateID : 0>
				
				<cfset local.thisSub['qryRates'] = getSubRates(siteID=arguments.siteID, memberID=arguments.memberID, isRoot=false, 
													subscriptionID=local.thisSub.subscriptionID, parentSubRateFreqID=arguments.selectedParentFreqID, 
													isRenewalRate=arguments.isRenewalRate, selectedRateID=local.thisSubSelectedRateID,
													isAdminListing=arguments.isAdminListing)>

				<cfset local.subscription = getSubAddOnsStruct(siteID=arguments.siteID, memberID=arguments.memberID, subscriptionID=local.thisSub.subscriptionID, 
												strSubDef=local.thisSub, isRenewalRate=arguments.isRenewalRate, strMemberSubs=local.strMemberSubs, 
												qryAllAddOnSubs=arguments.qryAllAddOnSubs, selectedParentFreqID=arguments.selectedParentFreqID)>
				
				<cfset local.subscription['isSelected'] = local.thisSubSelectedRateID GT 0>
				<cfset local.subscription['currAddOnRecursionLevel'] = local.qryThisAddOnRecursionLevel.recursionLevel>
				<cfset local.subscription['maxAddOnRecursionLevel'] = local.qryThisAddOnSubMaxRecursionLevelAddOns.recordCount 
																		? local.qryThisAddOnSubMaxRecursionLevelAddOns.recursionLevel
																		: local.qryThisAddOnRecursionLevel.recursionLevel>
				
				<cfset arrayAppend(arguments.strSubDef.addOns[local.thisAddOn.addonID]['subscriptions'], local.subscription)>
			</cfloop>
		</cfloop>
	
		<cfreturn arguments.strSubDef>
	</cffunction>

	<cffunction name="getSubscriptionDetails" access="public" output="false" returntype="array">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="isRoot" type="boolean" required="true">
		<cfargument name="subscriptionID" type="numeric" required="true">
		<cfargument name="parentSubRateFreqID" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="selectedSubscriptionID" type="numeric" required="false" default="0">
		<cfargument name="selectedRFID" type="numeric" required="false" default="0">
		<cfargument name="isAdminListing" type="boolean" required="false" default="0">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			DECLARE @subscriptionID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">;
			
			SELECT sub.subscriptionID, sub.[uid], sub.subscriptionName, t.typeName, sub.rateTermDateFlag,
				sub.GLAccountID, sub.allowRateGLAccountOverride, sub.paymentOrder, ao.subActivationCode,
				ao_alt.subActivationCode as subAlternateActivationCode, rs.scheduleID as rateScheduleID,
				rs.scheduleName as rateScheduleName, rs.status as rateScheduleStatus, rs.uid as rateScheduleUid
			FROM dbo.sub_subscriptions AS sub
			INNER JOIN dbo.sub_rateSchedules AS rs on rs.scheduleID = sub.scheduleID 
				and sub.subscriptionID = @subscriptionID 
				and sub.[status] = 'A'
			INNER JOIN dbo.sub_Types AS t on t.typeID = sub.typeID			
			INNER JOIN dbo.sub_activationOptions AS ao on ao.subActivationID = sub.subActivationID
			INNER JOIN dbo.sub_activationOptions AS ao_alt on ao_alt.subActivationID = sub.subAlternateActivationID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrSubs = arrayNew(1)>
		<cfloop query="local.subs">			
			<cfset arrayAppend(local.arrSubs, {
				"subscriptionID": local.subs.subscriptionID,
				"uid": local.subs.uid,
				"subscriptionName": local.subs.subscriptionName,
				"typeName": local.subs.typeName,
				"rateTermDateFlag": local.subs.rateTermDateFlag,
				"GLAccountID": local.subs.GLAccountID,
				"allowRateGLAccountOverride": local.subs.allowRateGLAccountOverride,
				"paymentOrder": local.subs.paymentOrder,
				"subActivationCode": local.subs.subActivationCode,
				"subAlternateActivationCode": local.subs.subAlternateActivationCode,
				"rateScheduleID": local.subs.rateScheduleID,
				"rateScheduleName": local.subs.rateScheduleName,
				"rateScheduleStatus": local.subs.rateScheduleStatus,
				"rateScheduleUID": local.subs.rateScheduleUID,
				"qryRates": getSubRates(siteID=arguments.siteID, memberID=arguments.memberID, isRoot=arguments.isRoot, subscriptionID=local.subs.subscriptionID,
								parentSubRateFreqID=arguments.parentSubRateFreqID, isRenewalRate=arguments.isRenewalRate, selectedRFID=arguments.selectedRFID,
								isAdminListing=arguments.isAdminListing),
				"isSelected": arguments.selectedSubscriptionID EQ local.subs.subscriptionID
			})>
		</cfloop>

		<cfreturn local.arrSubs>
	</cffunction>
	
	<cffunction name="getSubRates" access="public" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="isRoot" type="boolean" required="true">
		<cfargument name="subscriptionID" type="string" required="true">
		<cfargument name="parentSubRateFreqID" type="numeric" required="true">
		<cfargument name="isRenewalRate" type="boolean" required="true">
		<cfargument name="selectedRFID" type="numeric" required="false" default="0">
		<cfargument name="selectedRateID" type="numeric" required="false" default="0">
		<cfargument name="isAdminListing" type="boolean" required="false" default="0">
	
		<cfset var local = structNew()>
		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRatesAndFreqs">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpSubRates') IS NOT NULL
				DROP TABLE ##tmpSubRates;
			CREATE TABLE ##tmpSubRates (rfid int PRIMARY KEY, rateID int);
	
			declare @FID int, @subscriptionID int, @memberID int, @isRenewalRate bit, @siteID int, @groupPrintID int,
				@selectedRFID int, @selectedRateID int;
			
			set @FID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.qualifySubRateRFID#">;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			set @subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.subscriptionID#">;
			set @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			set @isRenewalRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.isRenewalRate#">;
			set @selectedRFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedRFID#">;
			set @selectedRateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.selectedRateID#">;
	
			-- ensure active member is considered
			select @memberID = activeMemberID from dbo.ams_members where memberID = @memberID;
			select @groupPrintID = groupPrintID from dbo.ams_members where memberID = @memberID;

			INSERT INTO ##tmpSubRates (rfid, rateID)
			select distinct rf.rfid, r.rateID
			from dbo.sub_subscriptions as s
			inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID
				and rs.[status] = 'A'
			inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
				and r.isRenewalRate = @isRenewalRate
				and r.[status] = 'A'
				and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
			inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
				and srfrp.siteResourceID = r.siteResourceID
				and srfrp.functionID = @FID
			inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
				and gprp.rightPrintID = srfrp.rightPrintID
				and gprp.groupPrintID = @groupPrintID
			inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
				and rf.rateAmt >= 0 
				and rf.[status] = 'A'
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID 
				and f.siteID = @siteID 
				and f.[status] = 'A'
			where s.subscriptionID = @subscriptionID
			<cfif arguments.isRoot and not arguments.isAdminListing>
				<cfif arguments.selectedRFID>
					and (rf.allowFrontEnd = 1 or rf.rfid = @selectedRFID)
				<cfelseif arguments.selectedRateID>
					and (rf.allowFrontEnd = 1 or r.rateID = @selectedRateID)
				<cfelse>
					and rf.allowFrontEnd = 1
				</cfif>
			</cfif>;

			-- override perms/availability dates for the selectedRateID/selectedRFID
			<cfif arguments.selectedRFID OR arguments.selectedRateID>
				IF NOT EXISTS (SELECT 1 FROM ##tmpSubRates 
								WHERE <cfif arguments.selectedRFID>
											rfid = @selectedRFID
										<cfelseif arguments.selectedRateID>
											rateID = @selectedRateID
										</cfif>
								)
					INSERT INTO ##tmpSubRates (rfid, rateID)
					select distinct rf.rfid, r.rateID
					from dbo.sub_subscriptions as s
					inner join dbo.sub_rateSchedules as rs on rs.scheduleID = s.scheduleID
						and rs.[status] = 'A'
					inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
						and r.isRenewalRate = @isRenewalRate
						and r.[status] = 'A'
					inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
						and rf.rateAmt >= 0 
						and rf.[status] = 'A'
					inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID 
						and f.siteID = @siteID 
						and f.[status] = 'A'
					where s.subscriptionID = @subscriptionID
					<cfif arguments.selectedRFID>
						and rf.rfid = @selectedRFID
					<cfelseif arguments.selectedRateID>
						and r.rateID = @selectedRateID
					</cfif>
						except
					select rfid, rateID
					from ##tmpSubRates;
			</cfif>

			<cfif arguments.isAdminListing>
				DELETE
				FROM ##tmpSubRates
				WHERE rateID NOT IN (
					SELECT DISTINCT tmp.rateID
					FROM ##tmpSubRates AS tmp
					INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles AS rfmp ON rfmp.rfid = tmp.rfid 
						AND rfmp.status = 'A'
				);
			</cfif>
			
			select r.rateID, r.rateName, r.uid, r.isRenewalRate, r.siteResourceID, r.status, 
				r.rateAFStartDate, r.rateAFEndDate,  r.termAFStartDate,  r.termAFEndDate, r.graceEndDate, 
				r.recogAFStartDate, r.recogAFEndDate, r.forceUpfront, r.frontEndAllowChangePrice,
				rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, rf.status as ratestatus, 
				rf.rfid, f.frequencyID, f.frequencyName, f.frequencyShortName, f.uid as rateuid, f.hasInstallments, 
				case when f.hasInstallments = 1 then f.monthlyInterval else 1 end as monthlyInterval,
				r.frontEndChangePriceMin, r.frontEndChangePriceMax, r.rateOrder,
				ROW_NUMBER() OVER (PARTITION BY r.rateID ORDER BY r.rateName, rf.numInstallments) AS rateFreqOrder,
				<cfif arguments.selectedRFID>
					CASE WHEN @selectedRFID = rf.rfid THEN 1 ELSE 0 END
				<cfelseif arguments.selectedRateID>
					CASE WHEN @selectedRateID = r.rateID THEN 1 ELSE 0 END
				<cfelse>
					0
				</cfif>
				AS isSelected
			from ##tmpSubRates as tmp
			inner join dbo.sub_rates as r on r.rateID = tmp.rateID
			inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
				and rf.rfid = tmp.rfid
			inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID 
				and f.siteID = @siteID 
				and f.[status] = 'A'
			order by r.rateOrder, f.frequencyName;

			IF OBJECT_ID('tempdb..##tmpSubRates') IS NOT NULL
				DROP TABLE ##tmpSubRates;
	
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif arguments.isRoot>
			<cfset local.qryRates = local.qryRatesAndFreqs>
		<cfelse>
			<cfquery dbtype="query" name="local.qryFreqRates">
				select *
				from [local].qryRatesAndFreqs
				where frequencyID = #val(arguments.parentSubRateFreqID)#
				order by rateOrder, frequencyName
			</cfquery>
	
			<cfif local.qryFreqRates.recordCount>
				<cfset local.qryRates = local.qryFreqRates>
			<cfelse>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFullRate" cachedwithin="#createTimeSpan(0,0,1,0)#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					select frequencyID
					from dbo.sub_frequencies
					where siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="CF_SQL_INTEGER">
					and frequencyShortName = 'F'
					and isSystemRate = 1
					and [status] = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
		
				<cfquery dbtype="query" name="local.qryFullRates">
					select *
					from [local].qryRatesAndFreqs
					where frequencyID = #val(local.qryFullRate.frequencyID)#
					order by rateOrder, frequencyName
				</cfquery>
				<cfset local.qryRates = local.qryFullRates>
			</cfif>	
		</cfif>
		
		<cfreturn local.qryRates>
	</cffunction>

	<cffunction name="getRateToUse" access="private" output="false" returntype="struct">
		<cfargument name="rateAmt" type="numeric" required="true">
		<cfargument name="rateInstallments" type="numeric" required="true">
		<cfargument name="numPaymentsToUse" type="numeric" required="true">
		<cfargument name="pcPctOff" type="numeric" required="true">
		<cfargument name="modifiedRateTotal" type="numeric" required="true">
		<cfargument name="isModifiedRate" type="boolean" required="true">
	
		<cfset var local = structNew()>
		<cfset local.useRates = NOT arguments.isModifiedRate>

		<cfif local.useRates>
			<cfset local.strReturn = { "rateTotal": numberFormat(arguments.rateAmt * arguments.rateInstallments,"0.00"), "rateToUse": 0 }>
		
			<cfif arguments.pcPctOff GT 0>
				<cfset local.strReturn.rateTotal = numberFormat(local.strReturn.rateTotal - (local.strReturn.rateTotal * (arguments.pcPctOff * 0.01)),"0.00")>
			</cfif>
		<cfelse>
			<cfset local.strReturn = { "rateTotal": numberFormat(arguments.modifiedRateTotal,"0.00"), "rateToUse": 0 }>
		</cfif>
	
		<cfset local.strReturn.rateToUse = numberFormat(local.strReturn.rateTotal / arguments.numPaymentsToUse,"0.00")>
	
		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="renderPrice" access="private" output="false" returntype="string">
		<cfargument name="amount" type="numeric" required="true">
		<cfargument name="freeRateDisplay" type="string" required="true">
		<cfreturn arguments.amount EQ 0 ? arguments.freeRateDisplay : DollarFormat(arguments.amount)>
	</cffunction>

	<cffunction name="doConfirm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSubscriptionReg = CreateObject("component","model.admin.subscriptions.subscriptionReg")>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>

		<cfsetting requesttimeout="1800">

		<cftry>
			<cfset local.rootSubscriberID = val(arguments.event.getValue('rootSubscriberID',0))>
			<cfset local.memberID = val(arguments.event.getValue('mid',0))>
			<cfset local.actorMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'))>
			<cfif local.actorMemberID EQ 0>
				<cfset local.actorMemberID = local.memberID>
				<cfset application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.actorMemberID)>
			</cfif>

			<cfset local.qryMemberSubscriptions = getMemberSubscriptions(memberID=local.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), rootSubscriberID=local.rootSubscriberID, hideDeleted=false)>
			<cfset local.qryActiveMemberSubscriptions = QueryFilter(local.qryMemberSubscriptions, function(thisRow) { return arguments.thisRow.status NEQ 'D'; })>
			<cfset local.qryDeletedMemberSubscriptions = QueryFilter(local.qryMemberSubscriptions, function(thisRow) { return arguments.thisRow.status EQ 'D'; })>

			<cfif NOT local.qryActiveMemberSubscriptions.recordCount>
				<cfreturn returnAppStruct("Invalid Subscriber","echo")>
			</cfif>

			<cfquery name="local.qryRootSubscriber" dbtype="query">
				SELECT subscriberID, subscriptionID, typeName, subscriptionName, status, RFID, rateTermDateFlag, GLAccountID,
					subStartDate, subEndDate, graceEndDate, parentSubscriberID, subActivationCode, allowRateGLAccountOverride,
					PCFree, modifiedRate, isModifiedRate, lastPrice, subPayOrder, thePath, thePathExpanded, recogStartDate, recogEndDate, 
					linkedNonRenewalRateID,fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndAllowChangePrice, 
					frontEndChangePriceMin, frontEndChangePriceMax, parentSubscriptionID, rateID, frequencyID, invoiceID,
					payProfileID
				FROM [local].qryActiveMemberSubscriptions
				WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">
			</cfquery>

			<!--- check status of sub --->
			<cfif listFindNoCase("A,P",local.qryRootSubscriber.status)>
				<cflocation url="/?pg=manageSubscriptions&subM=3" addtoken="no">
			<cfelseif listFindNoCase("X",local.qryRootSubscriber.status)>
				<cflocation url="/?pg=manageSubscriptions&subM=4" addtoken="no">
			<cfelseif NOT listFindNoCase("O",local.qryRootSubscriber.status)>
				<cflocation url="/?pg=manageSubscriptions&subM=5" addtoken="no">
			</cfif>

			<cfset local.rootSubStartDate = local.qryRootSubscriber.subStartDate>
			<cfset local.rootSubEndDate = local.qryRootSubscriber.subEndDate>
			<cfset local.rootSubGraceEndDate = local.qryRootSubscriber.graceEndDate>

			<cfset local.topSubID = local.qryRootSubscriber.subscriptionID>
			<cfset local.topStatus = local.qryRootSubscriber.status>
			<cfset local.topRFID = arguments.event.getValue('sub#local.topSubID#_rfid',0)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryParentRate">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
				
				SELECT r.rateID, r.rateName, r.isRenewalRate, rf.numInstallments, f.frequencyID, f.frequencyName, 
					f.frequencyShortName, f.monthlyInterval, rf.rateAmt, r.forceUpfront, r.frontEndAllowChangePrice, 
					r.glaccountID AS rateGLAccountID, sub.GLAccountID AS subGLAccountID, sub.allowRateGLAccountOverride, 
					gl_subs.invoiceProfileID AS subInvoiceProfileID, gl_rates.invoiceProfileID AS rateInvoiceProfileID, 
					sub.paymentOrder, o.subActivationCode, o2.subActivationCode AS subAlternateActivationCode
				FROM dbo.sub_rates AS r
				INNER JOIN dbo.sub_rateFrequencies AS rf ON rf.rateID = r.rateID
				INNER JOIN dbo.sub_frequencies AS f ON f.frequencyID = rf.frequencyID
				INNER JOIN dbo.sub_rateSchedules AS rs on rs.scheduleID = r.scheduleID
					AND rs.[status] = 'A'
				INNER JOIN dbo.sub_subscriptions AS sub on sub.scheduleID = rs.scheduleID 
					AND sub.[status] = 'A'
				INNER JOIN dbo.sub_activationOptions AS o ON o.subActivationID = sub.subActivationID
				INNER JOIN dbo.sub_activationOptions AS o2 ON o2.subActivationID = sub.subAlternateActivationID
				INNER JOIN dbo.tr_GLAccounts AS gl_subs on gl_subs.orgID = @orgID 
					AND gl_subs.GLAccountID = sub.GLAccountID
				LEFT OUTER JOIN dbo.tr_GLAccounts AS gl_rates on gl_rates.orgID = @orgID 
					AND gl_rates.GLAccountID = r.GLAccountID
				WHERE rf.rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">
				AND sub.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topSubID#">;
			
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset local.rootRateNumInstallments = local.qryParentRate.numInstallments>
			<cfset local.isRenewalRate = val(local.qryParentRate.isRenewalRate) eq 1 ? true : false>

			<cfset local.qryAllAddOnSubs = getRecursiveAddOnSubs(siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID, 
											rootSubscriberID=local.rootSubscriberID, isRenewalRate=local.isRenewalRate, topSubscriptionID=local.topSubID)>

			<cfset local.subPaymentDates = local.objSubscriptionReg.getSubPaymentDates(subStartDate=local.rootSubStartDate, subEndDate=local.rootSubEndDate, 
												numPayments=local.qryParentRate.numInstallments, monthlyInterval=local.qryParentRate.monthlyInterval)>
			<cfset local.numPaymentsToUse = val(local.subPaymentDates.numPayments)>

			<cfset local.strMemberSubs = structNew('ordered')>
			
			<cfset local.strRootSub = {
				"subscriberID": local.rootSubscriberID,
				"rfid": local.topRFID,
				"subscriptionID": local.topSubID,
				"parentSubscriptionID": 0,
				"rateID": local.qryParentRate.rateID,
				"rateName": local.qryParentRate.rateName,
				"rateAmt": local.qryParentRate.rateAmt,
				"numPaymentsToUse": local.numPaymentsToUse,
				"rateToUse": local.qryParentRate.rateAmt,
				"rateInstallments": local.qryParentRate.numInstallments,
				"freqName": local.qryParentRate.frequencyName,
				"priceChanged": false,
				"forceUpFront": local.qryParentRate.forceUpfront,
				"rateGLAccountID": val(local.qryParentRate.rateGLAccountID),
				"subGLAccountID": local.qryParentRate.subGLAccountID,
				"GLAccountIDToUse": val(local.qryParentRate.allowRateGLAccountOverride) EQ 1 AND val(local.qryParentRate.rateGLAccountID) GT 0
										? val(local.qryParentRate.rateGLAccountID)
										: val(local.qryParentRate.subGLAccountID),
				"invProfileIDToUse": val(local.qryParentRate.allowRateGLAccountOverride) EQ 1 AND val(local.qryParentRate.rateInvoiceProfileID) GT 0
										? val(local.qryParentRate.rateInvoiceProfileID)
										: val(local.qryParentRate.subInvoiceProfileID),
				"paymentOrder": val(local.qryParentRate.paymentOrder),
				"activationoptioncode": local.qryParentRate.subActivationCode,
				"altactivationoptioncode": local.qryParentRate.subAlternateActivationCode,
				"setID": 0,
				"setName": "",
				"subTermFlag": local.qryRootSubscriber.rateTermDateFlag,
				"PCFree": 0,
				"currStatus": local.qryRootSubscriber.status,
				"subName": local.qryRootSubscriber.subscriptionName,
				"alreadySub": true,
				"thePath": "0000",
				"recursionLevel": 1
			}>

			<!--- Must Pay on 1st invoice ---->
			<cfif local.qryParentRate.forceUpfront>
				<cfset local.strRootSub.numPaymentsToUse = 1>
			</cfif>

			<cfset local.modifiedRateTotal = 0>

			<cfif local.qryParentRate.frontEndAllowChangePrice AND arguments.event.getValue('newRateTotal_#local.topSubID#_#local.topRFID#',0) GTE 0>
				<cfset local.strRootSub["rateToUse"] = numberFormat(val(ReReplace(arguments.event.getValue('newRateTotal_#local.topSubID#_#local.topRFID#'),'[^0-9\.]','','ALL')),"0.00")>
				<cfset local.strRootSub["priceChanged"] = local.strRootSub["rateToUse"] NEQ local.qryParentRate.rateAmt>
			<cfelseif local.qryRootSubscriber.RFID EQ local.topRFID AND val(local.qryRootSubscriber.isModifiedRate) EQ 1>
				<cfset local.modifiedRateTotal = val(local.qryRootSubscriber.lastPrice)>
				<cfset local.strRootSub["priceChanged"] = true>
			</cfif>

			<cfif local.qryParentRate.frontEndAllowChangePrice>
				<cfset local.strRootSub["rateTotal"] = numberFormat(local.strRootSub['rateToUse'] * local.strRootSub.numPaymentsToUse,"0.00")>
			<cfelse>
				<cfset local.strRateAmt = getRateToUse(rateAmt=local.strRootSub["rateToUse"], rateInstallments=local.strRootSub.rateInstallments, 
					numPaymentsToUse=local.strRootSub.numPaymentsToUse, pcPctOff=0, modifiedRateTotal=local.modifiedRateTotal, 
					isModifiedRate=local.strRootSub.priceChanged)>
				<cfset local.strRootSub["rateTotal"] = local.strRateAmt.rateTotal>
			</cfif>
			
			<cfset structInsert(local.strMemberSubs, local.topSubID, local.strRootSub)>

			<cfquery name="local.qryDistinctSubAddOns" dbtype="query">
				SELECT DISTINCT addonID
				FROM [local].qryAllAddOnSubs
			</cfquery>

			<cfset local.selectedSubs = "">
			<cfloop query="local.qryDistinctSubAddOns">
				<cfset local.subInputFieldName = "subAddOn#local.qryDistinctSubAddOns.addOnID#">
				<cfif len(arguments.event.getTrimValue('#local.subInputFieldName#',''))>
					<cfset local.selectedSubs = listAppend(local.selectedSubs,arguments.event.getTrimValue('#local.subInputFieldName#'))>
				</cfif>
			</cfloop>

			<cfif listLen(local.selectedSubs)>
				<cfquery name="local.qrySelectedAddOnSubs" dbtype="query">
					SELECT subscriptionID, subscriptionName, addonID, setID, setName, addOnSubscriptionID, frontEndAllowSelect, thePath, recursionLevel
					FROM [local].qryAllAddOnSubs
					WHERE subscriptionID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.selectedSubs#">)
					ORDER BY thePath
				</cfquery>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySelectedSubAddOns">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
		
					SELECT sets.setID, sets.uid as setUID, ao.addOnID, sets.setName, ao.useAcctCodeInSet, ao.PCnum, ao.PCPctOffEach, 
						ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, ao.frontEndContentID, isnull(ao.minAllowed,0) as minAllowed,
						isnull(ao.maxAllowed,0) as maxAllowed, ao.frontEndAddAdditional
					FROM dbo.sub_addons as ao
					INNER JOIN dbo.sub_sets as sets on sets.setID = ao.childSetID 
					WHERE ao.addOnID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="yes" value="#valueList(local.qrySelectedAddOnSubs.addOnID)#">);
		
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubs">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

					SELECT sub.subscriptionID, sub.[uid], sub.subscriptionName, sub.rateTermDateFlag,
						sub.GLAccountID, sub.allowRateGLAccountOverride, sub.paymentOrder, o.subActivationCode,
						o2.subActivationCode AS subAlternateActivationCode, rs.scheduleID AS rateScheduleID,
						rs.scheduleName AS rateScheduleName, rs.status AS rateScheduleStatus, 
						rs.[uid] AS rateScheduleUID, gl.invoiceProfileID
					FROM dbo.sub_addons AS ao
					INNER JOIN dbo.sub_sets AS subsets on subsets.setID = ao.childSetID
						AND ao.addonID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qrySelectedSubAddOns.addOnID)#">)
					INNER JOIN dbo.sub_subscriptionSets AS ss on subsets.setID = ss.setID
					INNER JOIN dbo.sub_subscriptions AS sub on ss.subscriptionID = sub.subscriptionID 
						AND sub.[status] = 'A'
					INNER JOIN dbo.sub_rateSchedules AS rs on rs.scheduleID = sub.scheduleID
						AND rs.[status] = 'A'
					INNER JOIN dbo.sub_activationOptions AS o ON o.subActivationID = sub.subActivationID
					INNER JOIN dbo.sub_activationOptions AS o2 ON o2.subActivationID = sub.subAlternateActivationID
					INNER JOIN dbo.tr_GLAccounts AS gl on gl.orgID = @orgID 
						AND gl.GLAccountID = sub.GLAccountID
					WHERE sub.subscriptionID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#valueList(local.qrySelectedAddOnSubs.subscriptionID)#">)
					GROUP BY sub.subscriptionID, sub.uid, sub.subscriptionName, sub.rateTermDateFlag,
						sub.GLAccountID, gl.invoiceProfileID, sub.allowRateGLAccountOverride, sub.paymentOrder, o.subActivationCode,
						o2.subActivationCode, rs.scheduleID, rs.scheduleName, rs.status, rs.uid, ss.orderNum;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfloop query="local.qrySelectedAddOnSubs">
					<cfquery name="local.qryThisSubAddOn" dbtype="query">
						SELECT setID, setUID, setName, useAcctCodeInSet, PCnum, PCPctOffEach, frontEndAllowChangePrice
						FROM [local].qrySelectedSubAddOns
						WHERE addOnID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySelectedAddOnSubs.addOnID#">
					</cfquery>

					<cfquery name="local.qryThisSub" dbtype="query">
						SELECT subscriptionID, uid, subscriptionName, rateTermDateFlag, GLAccountID, allowRateGLAccountOverride, paymentOrder, subActivationCode,
							subAlternateActivationCode, rateScheduleID, rateScheduleName, rateScheduleStatus, rateScheduleUID, invoiceProfileID
						FROM [local].qrySubs
						WHERE subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySelectedAddOnSubs.subscriptionID#">
					</cfquery>

					<cfset local.ratePctOff = val(local.qryThisSubAddOn.PCPctOffEach)>
					<cfset local.addOnAllowChangePrice = local.qryThisSubAddOn.frontEndAllowCHangePrice>
					<cfset local.selectedRFID = val(arguments.event.getValue('sub#local.qrySelectedAddOnSubs.subscriptionID#_rfid',0))>
					<cfif NOT structKeyExists(local,"strFreeSubAddons")>
						<cfset local.strFreeSubAddons = {}>
					</cfif>
					<cfif NOT structKeyExists(local.strFreeSubAddons,local.qrySelectedAddOnSubs.addOnID)>
						<cfset local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID] = { 
							"freeSubsCount":val(local.qryThisSubAddOn.PCNum), 
							"freeSubsCounter":0,
							"selectedFreeSubIDList": arguments.event.getTrimValue('addOn#local.qrySelectedAddOnSubs.addOnID#_freeSubs','')
						}>
					</cfif>
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryThisSubRate">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
						declare @siteID int, @orgID int, @RFID int;
						set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
						set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
						set @RFID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.selectedRFID#">;
						
						select r.rateID, r.rateName, r.uid, r.isRenewalRate, r.siteResourceID, r.status, 
							r.rateAFStartDate, r.rateAFEndDate,  r.termAFStartDate,  r.termAFEndDate, r.graceEndDate, 
							r.recogAFStartDate, r.recogAFEndDate, r.forceUpfront, r.frontEndAllowChangePrice,
							rf.rateAmt, rf.numInstallments, rf.allowFrontEnd, rf.status as ratestatus, r.glaccountID,
							rf.rfid, f.frequencyID, f.frequencyName, f.frequencyShortName, f.uid as rateuid, f.hasInstallments, 
							case when f.hasInstallments = 1 then f.monthlyInterval else 1 end as monthlyInterval,
							r.frontEndChangePriceMin, r.frontEndChangePriceMax, r.rateOrder, gl.invoiceProfileID
						from dbo.sub_rates as r
						inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID 
							and rf.rateAmt >= 0 
							and rf.[status] = 'A'
						inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID 
							and f.siteID = @siteID 
							and f.[status] = 'A'
						left outer join dbo.tr_GLAccounts as gl on gl.orgID = @orgID 
							and gl.GLAccountID = r.GLAccountID
						where rf.rfid = @RFID;
				
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfif local.qryThisSubRate.recordCount>
						<cfquery name="local.qryThisExistingSubscriber" dbtype="query">
							SELECT subscriberID, status, subscriptionName, RFID, modifiedRate, isModifiedRate, lastPrice, invoiceID
							FROM [local].qryActiveMemberSubscriptions
							WHERE subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qrySelectedAddOnSubs.subscriptionID#">
						</cfquery>

						<cfset local.thisSubscriberID = 0>
						<cfset local.thisSubStatus = "">
						<cfset local.alreadySub = false>

						<cfif local.qryThisExistingSubscriber.recordCount>
							<cfset local.thisSubscriberID = val(local.qryThisExistingSubscriber.subscriberID)>
							<cfset local.thisSubStatus = local.qryThisExistingSubscriber.status>
							<cfset local.alreadySub = true>
						</cfif>

						<cfset local.strThisSub = {
							"subscriberID": local.thisSubscriberID,
							"rfid": local.qryThisSubRate.rfid,
							"subscriptionID": local.qrySelectedAddOnSubs.subscriptionID,
							"parentSubscriptionID": val(local.qrySelectedAddOnSubs.addOnSubscriptionID),
							"rateID": local.qryThisSubRate.rateID,
							"rateName": local.qryThisSubRate.rateName,
							"rateAmt": local.qryThisSubRate.rateAmt,
							"numPaymentsToUse": local.numPaymentsToUse,
							"rateToUse": local.qryThisSubRate.rateAmt,
							"rateInstallments": local.qryThisSubRate.numInstallments,
							"freqName": local.qryThisSubRate.frequencyName,
							"priceChanged": false,
							"forceUpFront": local.qryThisSubRate.forceUpfront,
							"rateGLAccountID": val(local.qryThisSubRate.GLAccountID),
							"subGLAccountID": local.qryThisSub.GLAccountID,
							"GLAccountIDToUse": val(local.qryThisSub.allowRateGLAccountOverride) EQ 1 AND val(local.qryThisSubRate.GLAccountID) GT 0
													? val(local.qryThisSubRate.GLAccountID)
													: val(local.qryThisSub.GLAccountID),
							"invProfileIDToUse": val(local.qryThisSub.allowRateGLAccountOverride) EQ 1 AND val(local.qryThisSubRate.invoiceProfileID) GT 0
													? val(local.qryThisSubRate.invoiceProfileID)
													: val(local.qryThisSub.invoiceProfileID),
							"paymentOrder": val(local.qryThisSub.paymentOrder),
							"activationoptioncode": local.qryThisSub.subActivationCode,
							"altactivationoptioncode": local.qryThisSub.subAlternateActivationCode,
							"setID": local.qryThisSubAddOn.setID,
							"setName": local.qryThisSubAddOn.setName,
							"subTermFlag": local.qryThisSub.rateTermDateFlag,
							"PCFree": 0,
							"currStatus": local.thisSubStatus,
							"subName": local.qryThisSub.subscriptionName,
							"alreadySub": local.alreadySub,
							"thePath": local.qrySelectedAddOnSubs.thePath,
							"recursionLevel": local.qrySelectedAddOnSubs.recursionLevel
						}>

						<cfif local.addOnAllowChangePrice OR local.qryThisSubRate.frontEndAllowChangePrice>
							<cfset local.rateCanEditPrice = true>
						<cfelse>
							<cfset local.rateCanEditPrice = false>
						</cfif>

						<!--- Must Pay on 1st invoice ---->
						<cfif local.qryThisSubRate.forceUpfront>
							<cfset local.strThisSub.numPaymentsToUse = 1>
						</cfif>

						<cfset local.modifiedRateTotal = 0>

						<cfif local.rateCanEditPrice AND arguments.event.getValue('newRateTotal_#local.strThisSub.subscriptionID#_#local.strThisSub.rfid#',0) GTE 0>
							<cfset local.strThisSub['rateToUse'] = numberFormat(val(ReReplace(arguments.event.getValue('newRateTotal_#local.strThisSub.subscriptionID#_#local.strThisSub.rfid#'),'[^0-9\.]','','ALL')),"0.00")>
							<cfset local.strThisSub["priceChanged"] = local.qryThisSubRate.rateAmt NEQ local.strThisSub['rateToUse']>
						<cfelseif val(local.qryThisExistingSubscriber.RFID) EQ local.qryThisSubRate.rfid AND val(local.qryThisExistingSubscriber.isModifiedRate) EQ 1>
							<cfset local.modifiedRateTotal = val(local.qryThisExistingSubscriber.lastPrice)>
							<cfset local.strThisSub["priceChanged"] = true>
						</cfif>

						<cfif local.rateCanEditPrice>
							<cfset local.strThisSub["rateTotal"] = numberFormat(local.strThisSub['rateToUse'] * local.strThisSub.numPaymentsToUse,"0.00")>
						<cfelse>
							<cfset local.strRateAmt = getRateToUse(rateAmt=local.strThisSub["rateToUse"], rateInstallments=local.strThisSub.rateInstallments, 
								numPaymentsToUse=local.strThisSub.numPaymentsToUse, pcPctOff=local.ratePctOff, modifiedRateTotal=local.modifiedRateTotal, 
								isModifiedRate=local.strThisSub.priceChanged)>
							<cfset local.strThisSub["rateTotal"] = local.strRateAmt.rateTotal>
						</cfif>

						<cfif local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID].freeSubsCount GT 0 
								AND local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID].freeSubsCounter LT local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID].freeSubsCount 
								AND listFind(local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID].selectedFreeSubIDList,local.strThisSub.subscriptionID)
								AND NOT local.rateCanEditPrice>
							<cfset local.strThisSub['PCFree'] = 1>
							<cfset local.strFreeSubAddons[local.qrySelectedAddOnSubs.addOnID].freeSubsCounter++>
						</cfif>

						<cfset structInsert(local.strMemberSubs, local.strThisSub.subscriptionID, local.strThisSub)>
					</cfif>
					
				</cfloop>
			</cfif>

			<cfset local.subAmts = getSubscriptionAmounts(strMemberSubs=local.strMemberSubs, qryAllAddOnSubs=local.qryAllAddOnSubs)>
			<cfset local.amountToCharge = local.subAmts.qryTotals.totalAmt>
			<cfset local.firstPaymentMinimum = 0>

			<cfset local.maxFrequencyInstallments = local.objSubscriptionReg.getMaxFrequencyInstallments(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			<cfset local.arrPaySchedule = arrayNew(1)>
			<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
				<cfset local.strPS = { "date"='', "amount"='' }>
				<cfset arrayAppend(local.arrPaySchedule,local.strPS)>
			</cfloop>

			<cfif local.subAmts.qryUpFrontAmt.totalAmt gt 0>
				<cfset local.arrPaySchedule[1] = { "date"=now(), "amount"=numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__") }>
				<cfset local.firstPaymentMinimum = numberformat(local.subAmts.qryUpFrontAmt.totalAmt, "_.__")>
			</cfif>

			<cfset local.distribAmt = local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse>
			<cfset local.distribLeftOver = numberformat(PrecisionEvaluate(local.subAmts.qryNonUpFrontAmt.totalAmt - (numberformat((local.subAmts.qryNonUpFrontAmt.totalAmt / local.numPaymentsToUse), "_.__") * local.numPaymentsToUse)), "_.__")>

			<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopCnt">
				<cfset local.loopAmt = 0>
				<cfset local.loopAmt = local.loopAmt + val(local.arrPaySchedule[local.loopCnt].amount)>
				<cfset local.arrPaySchedule[local.loopCnt] = { "date"=now(), "amount"=numberformat(local.loopAmt + val(local.distribAmt), "_.__") }>
			</cfloop>
			<cfset local.arrPaySchedule[1].amount = numberformat(val(local.arrPaySchedule[1].amount) + val(local.distribLeftOver), "_.__") >

			<cfloop from="1" to="#ArrayLen(local.subPaymentDates.arrDates)#" index="local.thisP">
				<cfset local.arrPaySchedule[local.thisP].date = local.subPaymentDates.arrDates[local.thisP]>
			</cfloop>

			<cfset local.payOrderIndex = 1>
			<!--- Invoices --->
			<cfset arguments.event.setValue("hInvoiceProfileIDs", valueList(local.subAmts.qryInvProfiles.invoiceProfileID))>
			
			<cfloop from="1" to="#local.numPaymentsToUse#" index="local.loopIndex">

				<cfset local.isPayOrderIndexInitialized = false>
				<cfset local.thisInvAmt = local.arrPaySchedule[local.loopIndex].amount>

				<cfset arguments.event.setValue("ps_#local.loopIndex#_date", "#Month(local.arrPaySchedule[local.loopIndex].date)#/#Day(local.arrPaySchedule[local.loopIndex].date)#/#Year(local.arrPaySchedule[local.loopIndex].date)#")>
				<cfset arguments.event.setValue("ps_#local.loopIndex#_amt", "#local.thisInvAmt#")>
				<cfset arguments.event.setValue("ps_#local.loopIndex#_subscriptionWithSalesNeeded", "")>
				
				<cfset local.strProfile = structNew()>
				
				<cfset local.invoiceAmtApplied = 0>
				<cfloop condition="(local.thisInvAmt-local.invoiceAmtApplied) gt 0.0 and local.payOrderIndex lte arrayLen(local.subAmts.payOrderArray)">

					<cfif not local.isPayOrderIndexInitialized>
						<!--- All invoice profiles with any subscriptions set to current payorder will be affected by current payment --->
						<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
							<cfset arguments.event.setValue("hps_#local.loopIndex#_#local.thisProfileEntry.id#_appearOnThisInvoice", "true")>
						</cfloop>
						<cfset local.isPayOrderIndexInitialized = true />
					</cfif>									

					<cfset local.currAmount = (local.subAmts.payOrderArray[local.payOrderIndex].total - local.subAmts.payOrderArray[local.payOrderIndex].totalapplied)>

					<cfif ((local.thisInvAmt-local.invoiceAmtApplied) gte local.currAmount)>
						<cfset local.invoiceAmtApplied = local.invoiceAmtApplied + local.currAmount>
						<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = local.subAmts.payOrderArray[local.payOrderIndex].total>

						<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
							<cfset local.innerCurrAmount = (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied)>
							<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.total>
							<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
								<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
							</cfif>
							<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
						</cfloop>
					<cfelseif ((local.thisInvAmt-local.invoiceAmtApplied) lt local.currAmount)>
						<cfset local.pctToUse = ((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount)>
						<cfset local.leftOver = numberformat((local.thisInvAmt-local.invoiceAmtApplied) - numberformat((((local.thisInvAmt-local.invoiceAmtApplied) / local.currAmount) * local.currAmount), "_.__"), "_.__")>
										
						<cfloop array="#local.subAmts.payOrderArray[local.payOrderIndex].profiles#" index="local.thisProfileEntry">
							<cfset local.innerCurrAmount = numberformat((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied) * local.pctToUse, "_.__")>
							<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
								
							<cfif ((local.leftOver gt 0) AND (local.thisProfileEntry.totalapplied lt local.thisProfileEntry.total))>
								<cfif (local.leftOver gt (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
									<cfset local.innerCurrAmount = local.innerCurrAmount + (local.leftOver - (local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
									<cfset local.leftOver = local.leftOver - ((local.thisProfileEntry.total - local.thisProfileEntry.totalapplied))>
								<cfelse>
									<cfset local.innerCurrAmount += local.leftOver>
									<cfset local.leftOver = 0>
								</cfif>
								<cfset local.thisProfileEntry.totalapplied = local.thisProfileEntry.totalapplied + local.innerCurrAmount>
							</cfif>
								
							<cfif not StructKeyExists(local.strProfile, "#local.thisProfileEntry.id#")>
								<cfset local.strProfile["#local.thisProfileEntry.id#"] = 0>
							</cfif>
							<cfset local.strProfile["#local.thisProfileEntry.id#"] = local.strProfile["#local.thisProfileEntry.id#"] + local.innerCurrAmount>
						</cfloop>
						
						<cfset local.subAmts.payOrderArray[local.payOrderIndex].totalapplied = numberformat(local.subAmts.payOrderArray[local.payOrderIndex].totalapplied + local.thisInvAmt-local.invoiceAmtApplied, "_.__")>
						<cfset local.invoiceAmtApplied = local.thisInvAmt>
					</cfif>

					<cfif ((local.subAmts.payOrderArray[local.payOrderIndex].total-local.subAmts.payOrderArray[local.payOrderIndex].totalapplied) lte 0.0)>
						<cfset local.payOrderIndex = local.payOrderIndex + 1>
						<cfset local.isPayOrderIndexInitialized = false>
					</cfif>
				
				</cfloop>

				<cfloop array="#StructKeyArray(local.strProfile)#" index="local.thisProfileKey">
					<cfset local.loopCurrAmt = local.strProfile["#local.thisProfileKey#"]>
					<cfset arguments.event.setValue("hps_#local.loopIndex#_#local.thisProfileKey#_amt", "#numberformat(local.loopCurrAmt, "_.__")#")>
				</cfloop>

			</cfloop>

			<cfset local.sidList = "">
			<cfset local.addSubsList = "">

			<cfloop collection="#local.strMemberSubs#" item="local.thisSubID">
				<cfif local.strMemberSubs[local.thisSubID].subscriberID GT 0 AND local.strMemberSubs[local.thisSubID].currStatus NEQ 'D'>
					<cfset local.sidList = listAppend(local.sidList,local.strMemberSubs[local.thisSubID].subscriberID)>
				</cfif>
				<cfif local.strMemberSubs[local.thisSubID].subscriberID EQ 0>
					<cfset local.addSubsList = listAppend(local.addSubsList,local.strMemberSubs[local.thisSubID].subscriptionID)>
				</cfif>
			</cfloop>

			<cfset local.listInvoiceProfileIDs = arguments.event.getValue('hInvoiceProfileIDs','')>
			<cfset local.ttlUpfrontAmt = val(arguments.event.getValue('ps_upfront_amt',0))>
			
			<!--- Build a structure of dates and amounts --->
			<cfset local.dateAmts = ArrayNew(1)>
			<cfset local.pretaxTotal = 0>
			<cfloop from="1" to="#local.maxFrequencyInstallments#" index="local.thisP">
				<cfif val(arguments.event.getValue('ps_#local.thisP#_amt',0)) gt 0 OR arguments.event.getValue('ps_#local.thisP#_hasiid',0) gt 0>
					<cfset local.currPS = StructNew()>
					<cfset local.currPS.paymentNumber = local.thisP>
					<cfset local.currPS.date = arguments.event.getValue('ps_#local.thisP#_date','')>
					<cfset local.currPS.amt = val(arguments.event.getValue('ps_#local.thisP#_amt',0))>
					<cfset local.currPS.profiles = StructNew()>
					<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
						<cfif arguments.event.getValue('hps_#local.thisP#_#local.thisInvProfileID#_appearOnThisInvoice',0)
								OR val(arguments.event.getValue('hps_#local.thisP#_#local.thisInvProfileID#_amt',0)) gte 0>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"] = StructNew()>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].amt = val(arguments.event.getValue('hps_#local.thisP#_#local.thisInvProfileID#_amt',0))>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceNumber = 0>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactions = ArrayNew(1)>
							<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactionTotal = 0>
						</cfif>
					</cfloop>
					<cfset ArrayAppend(local.dateAmts, local.currPS)>
					<cfset local.pretaxTotal = local.pretaxTotal + val(local.currPS.amt)>
				</cfif>
			</cfloop>
			<cfset local.amtForCalc = local.pretaxTotal - local.ttlUpfrontAmt>

			<cfif local.pretaxTotal eq 0 AND ArrayLen(local.dateAmts) eq 0>	
				<!--- there were no existing Invoice IDs and the total was 0 
					It has to be an add with no cost.  So, add an entry for the date and cost of 0 to attach the invoice and transactions
				--->
				<cfset local.currPS = StructNew()>
				<cfset local.currPS.date = DateFormat(now(), "m/d/yyyy")>
				<cfset local.currPS.amt = 0>
				<cfset local.currPS.paymentNumber = 1>
				<cfset local.currPS.profiles = StructNew()>
				<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
					<cfset local.currPS.profiles["#local.thisInvProfileID#"] = StructNew()>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].amt = 0>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].invoiceNumber = 0>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactions = ArrayNew(1)>
					<cfset local.currPS.profiles["#local.thisInvProfileID#"].transactionTotal = 0>
				</cfloop>
				<cfset ArrayAppend(local.dateAmts, local.currPS)>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTopProfileID">
				select profileID
				from sub_rateFrequenciesMerchantProfiles
				where rfid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.topRFID#">
				and [status] <> 'D'
			</cfquery>
			<cfset local.topProfileIDList = valueList(local.qryTopProfileID.profileID)>

			<cfquery name="local.qryDeleteSubs" dbtype="query">
				SELECT subscriberID
				FROM [local].qryActiveMemberSubscriptions
				WHERE subscriberID NOT IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.sidList#">)
			</cfquery>
			<cfset local.removeSubList = valueList(local.qryDeleteSubs.subscriberID)>
			

			<cfset local.thisInvoiceIDList = "">
			<cfset local.saleTransactionDate = "#dateformat(now(),'m/d/yyyy')# #timeformat(now(),'h:mm tt')#">
			
			<cfif arguments.event.getValue('stateIDForTax',0) gt 0 and len(arguments.event.getTrimValue('zipForTax',''))>
				<cfset local.qryAssignee = structNew()>
				<cfset local.qryAssignee.stateIDForTax = arguments.event.getValue('stateIDForTax')>
				<cfset local.qryAssignee.zipForTax = arguments.event.getTrimValue('zipForTax')>
				<cfset local.strBillingZip = application.objCommon.checkBillingZIP(billingZip=local.qryAssignee.zipForTax, billingStateID=local.qryAssignee.stateIDForTax)>
				<cfif local.strBillingZip.isvalidzip>
					<cfset local.qryAssignee.zipForTax = local.strBillingZip.billingzip>
				<cfelse>
					<cfthrow message="Invalid State/Zip.">
				</cfif>
			<cfelse>
				<cfset local.qryAssignee = getStateZipForTax(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID)>
			</cfif>

			<cfif listLen(local.removeSubList)>
				<cfloop list="#local.removeSubList#" index="local.removeSubscriberID">		
					<cfset local.objSubs.removeMemberSubscription(actorMemberID=local.actorMemberID, actorStatsSessionID=session.cfcuser.statsSessionID, memberID=local.memberID, 
								subscriberID=local.removeSubscriberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), AROption='B')>
				</cfloop>
			</cfif>

			<!--- create sql to record everything --->
			<cfsavecontent variable="local.addSubscriberSQL">
				<cfoutput>
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @rc int, @invoiceID int, @subscriberID int, @loggedInMemberID int, @orgID int, @siteID int, @statsSessionID int, 
						@trashID int, @assignedToMemberID int, @subTransactionID int, @parentSubscriberID int, 
						@topParentSubscriberID int, @pendingGroupID int, @activeGroupID int, @renewGroupID int, @invstatusID int, 
						@offerDate datetime, @detail varchar(max), @GLAccountID int, @transDate datetime, @tempDate datetime, 
						@tempCompareDate datetime, @stateIDForTax int, @zipForTax varchar(25), @couponID int, @redemptionCount int,
						@discountAmount decimal(18,2), @adjTransactionID int, @merchantProfileID int, @subPayProfileID int, 
						@payProcessFee bit, @processFeePercent decimal(5,2);

					SET @loggedInMemberID = #val(local.actorMemberID)#;
					SET @assignedToMemberID = #val(local.memberID)#;
					SET @statsSessionID = #val(session.cfcuser.statsSessionID)#;
					SET @siteID = #arguments.event.getValue('mc_siteInfo.siteID')#;
					SET @orgID = #arguments.event.getValue('mc_siteInfo.orgID')#;
					SET @transDate = '#local.saleTransactionDate#';
					SET @tempDate = GETDATE();

					SET @parentSubscriberID = NULL;
					SET @topParentSubscriberID = #val(local.rootSubscriberID)#;

					SELECT @merchantProfileID = MPProfileID, @subPayProfileID = payProfileID, @payProcessFee = payProcessFee, @processFeePercent = processFeePercent
					FROM dbo.sub_subscribers
					WHERE subscriberID = @topParentSubscriberID;

					BEGIN TRAN;

				<!--- create invoice for all dates --->
				<cfset local.arrCount = 0>
				-- local.dateAmts length: #arrayLen(local.dateAmts)#
				-- local.listInvoiceProfileIDs: #local.listInvoiceProfileIDs#
				<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
					<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
						<cfif StructKeyExists(local.thisDateAmt, "profiles") AND StructKeyExists(local.thisDateAmt.profiles,"#local.thisInvProfileID#")>
							<cfset arguments.event.paramValue('hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice',false)>
							-- local.thisDateAmt.profiles[#local.thisInvProfileID#] exists
							-- amt: #local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt#
							-- hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice: #arguments.event.getValue('hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice')#
							-- local.rootSubscriberID: #local.rootSubscriberID#
							-- local.addSubsList length: #listLen(local.addSubsList)#
							
							<cfif local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt gt 0 
									OR arguments.event.getValue('hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice')
									OR (ArrayLen(local.dateAmts) eq 1 
										AND (local.rootSubscriberID neq 0 OR listLen(local.addSubsList))
										)>
								<cfset local.arrCount = local.arrCount + 1>
								<cfset local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber = local.arrCount>
								<cfset local.thisInvoiceIDList = listAppend(local.thisInvoiceIDList,local.arrCount)>
								
								DECLARE @invoiceID_#local.arrCount# int, @invoiceNumber_#local.arrCount# varchar(19);
								select @tempDate = convert(datetime,'#local.thisDateAmt.date#');

								EXEC dbo.tr_createInvoice @invoiceProfileID=#local.thisInvProfileID#, @enteredByMemberID=@loggedInMemberID, 
									@assignedToMemberID=@assignedToMemberID, @dateBilled=@transDate, @dateDue=@tempDate, 
									@invoiceID=@invoiceID_#local.arrCount# OUTPUT, @invoiceNumber=@invoiceNumber_#local.arrCount# OUTPUT;
								EXEC dbo.tr_createInvoiceMerchantProfile @invoiceID=@invoiceID_#local.arrCount#, @profileIDList='#local.topProfileIDList#';

								IF @subPayProfileID IS NOT NULL BEGIN
									UPDATE dbo.tr_invoices
									set payProfileID = @subPayProfileID,
										MPProfileID = @merchantProfileID,
										payProcessFee = @payProcessFee,
										processFeePercent = @processFeePercent
									where invoiceID = @invoiceID_#local.arrCount#
									and isnull(payProfileID,0) <> @subPayProfileID;

									IF @@ROWCOUNT > 0 BEGIN
										INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
										SELECT '{ "c":"auditLog", "d": {
											"AUDITCODE":"INV",
											"ORGID":' + cast(@orgID as varchar(10)) + ',
											"SITEID":' + cast(@siteID as varchar(10)) + ',
											"ACTORMEMBERID":' + cast(@loggedInMemberID as varchar(20)) + ',
											"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
											"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars('Pay Profile ' + mpp.detail + ' associated to Invoice ' + i.fullInvoiceNumber),'"','\"') + '" } }'
										FROM dbo.tr_invoices as i
										INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
										WHERE i.invoiceID = @invoiceID_#local.arrCount#;
									END
								END
								
							</cfif>
						</cfif>
					</cfloop>
				</cfloop>

				<cfset local.retDates = getDatesToUseForExistingSubs(subStartDate=local.rootSubStartDate, subEndDate=local.rootSubEndDate, subGraceEndDate=local.rootSubGraceEndDate)>
				<cfset local.subStartDate = local.retDates.subStartDate>
				<cfset local.subEndDate = local.retDates.subEndDate>
				<cfset local.graceEndDate = local.retDates.subGraceEndDate>

				<!--- for each subscription, record subscriber and sale --->
				<cfloop collection="#local.strMemberSubs#" item="local.thisSubscriptionID">
					<cfset local.thisSub = duplicate(local.strMemberSubs[local.thisSubscriptionID])>
					<cfset local.subscriptionID = local.thisSubscriptionID>
					<cfset local.setID = local.thisSub.setID>
					<cfset local.RFID = local.thisSub.rfid>
					<cfset local.subTermFlag = local.thisSub.subTermFlag>
					<cfset local.subPCFree = local.thisSub.PCFree>
					<cfset local.subSubscriberID = local.thisSub.subscriberID>
					<cfset local.subStatus = local.thisSub.currStatus>
					<cfset local.subGLAccountID = local.thisSub.GLAccountIDToUse>

					<cfif Len(local.subStatus) eq 0>
						<cfset local.subStatus = 'A'>
					</cfif>
					<cfset local.subName = local.thisSub.subName>
					<cfset local.useRFID = local.RFID>

					<cfset local.recogDateStruct = getRecognitionDates(useRFID=local.useRFID, saleTransactionDate=local.saleTransactionDate, subStartDate=local.subStartDate, 
														subEndDate=local.subEndDate, rootsubTermFlag=local.subTermFlag)>
					<cfset local.recogStartDate = local.recogDateStruct.recogStartDate>
					<cfset local.recogEndDate = local.recogDateStruct.recogEndDate>
					
					<cfif len(local.subStartDate) AND len(local.subEndDate)>
						<cfset local.parentSubscriberID = 0>
						
						<cfif local.setID neq 0>
							<cfset local.parentSubscriptionID = local.thisSub.parentSubscriptionID>

							<!--- this is an edit, so the parent subscriber ID might have been previously created so check for it. --->
							<!--- if found put the result down. if not found, trust that it was created in this SQL code, so can reference it. --->
							<cfset local.parentSubscriberID = local.strMemberSubs.keyExists(local.parentSubscriptionID) ? val(local.strMemberSubs[local.parentSubscriptionID].subscriberID) : 0>
							<cfif local.parentSubscriberID eq 0>							
								select @parentSubscriberID = @subscriberIDForSub#local.parentSubscriptionID#;
							<cfelse>
								select @parentSubscriberID = #local.parentSubscriberID#;
							</cfif>
						<cfelse>
							select @parentSubscriberID = NULL;
						</cfif>
					
						<cfif NOT local.thisSub.alreadysub>
						
							<cfset local.subActivationOptionCode = local.thisSub.altactivationoptioncode>
							
							<cfif Now() lte local.subStartDate>
								<cfset local.subStatus = 'P'>
							</cfif>
							<cfset local.subStatusToUse = local.subStatus>

							<!--- add subscriber --->
							select @parentSubscriberID = nullif(@parentSubscriberID,0);
							EXEC dbo.sub_addSubscriber @orgID=@orgID, @memberID=@assignedToMemberID, @subscriptionID=#local.subscriptionID#,
								@parentSubscriberID=@parentSubscriberID, @RFID=#local.RFID#, @GLAccountID=#local.subGLAccountID#,
								@status='#local.subStatusToUse#', 
								@subStartDate='#dateFormat(local.subStartDate,"yyyy-mm-dd")# #timeFormat(local.subStartDate, "HH:mm:ss")#', 
								@subEndDate='#dateFormat(local.subEndDate,"yyyy-mm-dd")# #timeFormat(local.subEndDate, "HH:mm:ss")#', 
								@graceEndDate=<cfif len(local.graceEndDate) gt 0>'#dateFormat(local.graceEndDate,"yyyy-mm-dd")# #timeFormat(local.graceEndDate, "HH:mm:ss")#'<cfelse>NULL</cfif>,
								@recogStartDate='#local.recogStartDate#', @recogEndDate='#local.recogEndDate#', 
								@pcfree=<cfif local.subPCFree>1<cfelse>0</cfif>,
								@activationOptionCode='#local.subActivationOptionCode#',
								@recordedByMemberID=@loggedInMemberID, @bypassQueue=1, @subscriberID=@subscriberID OUTPUT;
							
							<cfif local.subStatus eq "A">
								EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID, 
									@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
								IF @rc = 1
									EXEC dbo.sub_updateSubscriberStatus	@subscriberID=@subscriberID, @newStatusCode='A', @siteID=@siteID,
										@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
							<cfelseif local.subStatus eq "P">
								EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='P', @siteID=@siteID,
									@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@rc OUTPUT;
							</cfif>


						<cfelse>
							<cfif local.thisSub.currStatus eq "O">
								update dbo.sub_subscribers
								set RFID = #local.RFID#,
									PCFree = <cfif local.subPCFree>1<cfelse>0</cfif>,
									GLAccountID = #local.subGLAccountID#
								where subscriberID = #local.subSubscriberID#;
							</cfif>
						
							select @subscriberID = #local.subSubscriberID#;
						</cfif>

						update dbo.sub_subscribers
						set lastPrice = convert(decimal(18,2), '#local.thisSub.rateTotal#'),
							modifiedRate = <cfif local.thisSub.priceChanged>
												convert(decimal(18,2), '#local.thisSub.rateTotal#')
											<cfelse>
												null
											</cfif>
						where subscriberID = @subscriberID;
						
						declare @subscriberIDForSub#local.subscriptionID# int;
						select @subscriberIDForSub#local.subscriptionID# = @subscriberID;
					</cfif>

					<cfif local.thisSub.currStatus eq "O">
						<cfif local.thisSub.subscriberID GT 0>
							select @subscriberID = #local.thisSub.subscriberID#;
						</cfif>

						<!--- change the status based on start time.  If start time is after today, status is P, otherwise, it's A --->
						<cfif Now() lte local.subStartDate>
							<cfset local.subUpdateStatus = 'P'>
						<cfelse>
							<cfset local.subUpdateStatus = 'A'>
						</cfif>

						EXEC dbo.sub_updateSubscriberStatus @subscriberID=@subscriberID, @newStatusCode='#local.subUpdateStatus#', @siteID=@siteID, 
							@enteredByMemberID=@loggedInMemberID, @bypassQueue=1, @result=@trashID OUTPUT;
					</cfif>
				</cfloop>

					EXEC dbo.sub_fixSubscriberTreeOrder @rootSubscriberID=@topParentSubscriberID;

					<cfset local.dateAmts = getSubscriptionTransactions(strMemberSubs=local.strMemberSubs, dateAmts=local.dateAmts, subEndDate=local.subEndDate, 
												qryAllAddOnSubs=local.qryAllAddOnSubs)>

					<!--- prepare invoices --->
					<cfset local.profilesAdjusts = StructNew()>
					<cfloop list="#local.listInvoiceProfileIDs#" index="local.thisInvProfileID">
						<cfset local.profilesAdjusts["#local.thisInvProfileID#"] = StructNew()>
						<cfset local.profilesAdjusts["#local.thisInvProfileID#"].invoiceAdjustArray = ArrayNew(1)>
					</cfloop>
				
					<cfset local.arrIndex = 0>
					<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
						<cfset local.arrIndex = local.arrIndex + 1>
						<cfset local.arrProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
						<cfloop array="#local.arrProfileKeys#" index="local.thisProfileKey">
							<!--- check that payments equal the invoice amount --->
							<cfif (Len(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) gt 0) AND (local.thisDateAmt.profiles["#local.thisProfileKey#"].amt neq 0)>
								<cfif decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal - local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) neq 0>
									<!--- need to adjust numbers before committing transactions --->
									<cfset local.invoiceAdjustStruct = StructNew()>
									<cfset local.invoiceAdjustStruct.difference = decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt - local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal)>
									<cfset local.invoiceAdjustStruct.arrIndex = local.arrIndex>
									<cfset ArrayAppend(local.profilesAdjusts["#local.thisProfileKey#"].invoiceAdjustArray, local.invoiceAdjustStruct)>
								</cfif> <!--- decimalFormat(local.thisDateAmt.profiles["#local.thisProfileKey#"].transactionTotal --->
							</cfif> <!--- end if (Len(local.thisDateAmt.profiles["#local.thisProfileKey#"].amt) gt 0)  --->
						</cfloop> <!--- end local.arrProfileKeys loop --->
					</cfloop> <!--- end local.dateAmts loop --->
						
					<cfset local.profileAdjustKeys = StructKeyArray(local.profilesAdjusts)>
					<cfloop array="#local.profileAdjustKeys#" index="local.thisProfileKey">
						<cfset local.invoiceAdjustArray = local.profilesAdjusts["#local.thisProfileKey#"].invoiceAdjustArray>
						<cfif ArrayLen(local.invoiceAdjustArray) gt 1> <!--- have to have at least 2 to adjust or leave it alone --->
							<!--- loop through all the subs in the first one, currently, the number of transactions should line up --->
							<cfset local.thisSubIDWillWork = 0>
							<cfloop array="#local.dateAmts[local.invoiceAdjustArray[1].arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisSubTransactionInfo">
								<cfset local.loopCurrSubID = local.thisSubTransactionInfo.subscriptionID>
							
								<cfset local.currArrLoop = 0>
								<cfloop array="#local.invoiceAdjustArray#" index="local.thisArrIndex">
									<cfset local.currArrLoop = local.currArrLoop + 1>
									<!--- find the subscription, check the test, break if no good --->
									<cfset local.loopAmtWorks = false>
									<cfloop array="#local.dateAmts[local.thisArrIndex.arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisLoopTransactionInfo">
										<cfif local.thisLoopTransactionInfo.subscriptionID eq local.loopCurrSubID>
											<cfset local.loopSubFound = true>
											<cfif local.thisLoopTransactionInfo.action eq "Add">
												<cfif decimalFormat(local.thisLoopTransactionInfo.amount + local.thisArrIndex.difference) gte 0>
													<cfset local.loopAmtWorks = true>
													<cfbreak />
												</cfif> <!--- end if decimalFormat(local.thisLoopTransactionInfo.amount + local.thisArrIndex.difference) gte 0 --->
											<cfelse>
												<!--- unknown action --->
												<cfbreak />
											</cfif> <!--- end if/elseif local.thisLoopTransactionInfo.action eq "Add" --->
										</cfif> <!--- end if local.thisLoopTransactionInfo.subscriptionID eq local.loopCurrSubID --->
									</cfloop> <!--- local.dateAmts[local.thisArrIndex.arrIndex].profiles[local.thisProfileKey].transactions --->

									<cfif local.loopAmtWorks neq true>
										<cfbreak /> <!--- go to the next sub --->
									<cfelseif local.currArrLoop eq ArrayLen(local.invoiceAdjustArray)> <!--- last one and if here it all worked --->
										<cfset local.thisSubIDWillWork = local.loopCurrSubID>
										<cfbreak />
									</cfif> <!--- end if/elseif local.loopAmtWorks neq true --->

								</cfloop> <!--- end local.invoiceAdjustArray loop --->
								
								<cfif local.thisSubIDWillWork neq 0> 
									<!--- do the work and get out --->
									<cfloop array="#local.invoiceAdjustArray#" index="local.thisAdjustIndex">
										<cfloop array="#local.dateAmts[local.thisAdjustIndex.arrIndex].profiles[local.thisProfileKey].transactions#" index="local.thisLoopAdjustTransactionInfo">
											<cfif local.thisLoopAdjustTransactionInfo.subscriptionID eq local.loopCurrSubID>
												<cfif local.thisLoopAdjustTransactionInfo.action eq "Add">
													<cfset local.thisLoopAdjustTransactionInfo.amount = decimalFormat(local.thisLoopAdjustTransactionInfo.amount + local.thisAdjustIndex.difference)>
												</cfif> <!--- end if/elseif local.thisLoopAdjustTransactionInfo.action eq "Add" --->
											</cfif> <!--- end if local.thisLoopAdjustTransactionInfo.subscriptionID eq local.loopCurrSubID --->
										</cfloop> <!--- end local.dateAmts[local.thisAdjustIndex.arrIndex].profiles[local.thisProfileKey].transactions loop --->
									</cfloop> <!--- end local.invoiceAdjustArray loop --->
									<cfbreak /> <!--- done, we've got our sub adjust --->
								</cfif> <!--- end if local.thisSubIDWillWork neq 0 --->
							</cfloop>	<!--- end local.dateAmts[local.invoiceAdjustArray[1].arrIndex].profiles[local.thisProfileKey].transactions loop --->				
						
						</cfif> <!--- end if ArrayLen(local.invoiceAdjustArray) gt 1 ---> 
					</cfloop> <!--- end local.profileAdjustKeys loop --->

					<!--- create recognition schedules --->
					<cfset local.recogSchedCode = getRecognitionScheduleCode(dateAmts=local.dateAmts)>
					#local.recogSchedCode#

					<!--- enter transactions. trust amounts after this point --->
					<cfset local.transInvoiceCode = getTransactionsAndInvoiceCode(event=arguments.event, rootSubscriberID=local.rootSubscriberID,
						rootSubscriptionID=local.topSubID, dateAmts=local.dateAmts, subMemberID=local.memberID, listInvoiceProfileIDs=local.listInvoiceProfileIDs, 
						thisInvoiceIDList=local.thisInvoiceIDList, transDate=local.saleTransactionDate, 
						stateIDforTax=local.qryAssignee.stateIDForTax, zipForTax=local.qryAssignee.zipForTax)>
					#local.transInvoiceCode#

					COMMIT TRAN;

					-- check activations for all subs for the member
					EXEC dbo.sub_checkActivationsByMember @orgID=@orgID, @memberid=@assignedToMemberID, @subscriberID=null, @bypassQueue=1;
				
					-- reprocess any applicable conditions. ConditionsAndGroups here not ConditionsAndGroupsChanged because of the manual subscription groups
					BEGIN TRY
						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
						CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

						INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
						SELECT @orgID, #local.memberID#, conditionID
						FROM dbo.ams_virtualGroupConditions
						where orgID = @orgID
						and fieldCode = 'sub_entry';

						EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroups';

						IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
							DROP TABLE ##tblMCQRun;
					END TRY
					BEGIN CATCH
						EXEC dbo.up_MCErrorHandler @raise=0, @email=1;
					END CATCH

					SELECT 1 as success, @topParentSubscriberID as topParentSubscriberID;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH					
				</cfoutput>
			</cfsavecontent>

			<!--- run generated sql --->
			<cftry>
				<cfquery name="local.qryAddSubscribers" datasource="#application.dsn.membercentral.dsn#" timeout="1800">
					#preserveSingleQuotes(local.addSubscriberSQL)#
				</cfquery>
				<cfif local.qryAddSubscribers.success is not 1>
					<cfthrow message="local.qryAddSubscribers was not successful">
				</cfif>
			<cfcatch type="any">				
				<cfset application.objError.extendRequestTimeout()>	
				<cfset local.addSubscriberSQL = replace(local.addSubscriberSQL,chr(10),"<br/>","ALL")>
				<cfset local.argsEventCollection = duplicate(arguments.event.getCollection())>
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local, customMessage="model.subscriptions.subscriptions.doConfirm Run SQL")>
				<cfrethrow>
			</cfcatch>
			</cftry>

		<cfcatch type="any">
			<cfset var errorStruct = { "localscope":local, "event":arguments.event.getCollection() }>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=errorStruct, customMessage="model.subscriptions.subscriptions.doConfirm Wrapper")>
			<cfrethrow>
		</cfcatch>
		</cftry>

		<cftry>
			<!--- Send Accepted via front end --->
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryNotification">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				DECLARE @siteID int, @memberID int, @rootSubscriberID int;
				SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;
				SET @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">;

				select distinct t.feEmailNotification
				from dbo.fn_getRecursiveMemberSubscriptions(@memberID, @siteID, @rootSubscriberID) as rms
				inner join dbo.sub_subscriptions sc on sc.subscriptionID = rms.subscriptionID
				inner join dbo.sub_types t on t.typeID = sc.typeID and t.siteID = @siteID
				where rms.status <> 'D'
				and rms.parentSubscriberID is NULL;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>			

			<!--- get distinct list of emails --->
			<cfset local.lstEmails = listRemoveDuplicates(replace(ValueList(local.qryNotification.feEmailNotification),',',';','ALL'),';')>
			
			<cfif local.lstEmails NEQ "">
				<!--- get member info --->
				<cfset local.qryMember = CreateObject("component","model.admin.members.members").getMember_demo(memberid=local.memberID)>
									
				<cfset local.fullname = "">
				<cfif len(local.qryMember.prefix)>
					<cfset local.fullname = local.fullname & " " & local.qryMember.prefix> 
				</cfif>
				<cfset local.fullname = local.fullname & " " & local.qryMember.firstname>  
					<cfif len(local.qryMember.middlename)>
					<cfset local.fullname = local.fullname & " " & local.qryMember.middlename>  
				</cfif> 
				<cfset local.fullname = local.fullname & " " & local.qryMember.lastname>  
				<cfif len(local.qryMember.suffix)>
					<cfset local.fullname = local.fullname & " " & local.qryMember.suffix>  
				</cfif> 
				<cfif len(local.qryMember.professionalsuffix)>
					<cfset local.fullname = local.fullname & " " & local.qryMember.professionalsuffix>  
				</cfif>	
				<cfset local.company = "">
				<cfif len(local.qryMember.company)>
					<cfset local.company = local.qryMember.company> 
				</cfif>
				<cfset local.termDateString = "#DateFormat(local.subStartDate,'m/d/yyyy')# - #DateFormat(local.subEndDate,'m/d/yyyy')#">
				
				<cfset local.subTreeVerbose = getSubTreeVerbose(strMemberSubs=local.strMemberSubs, termDateString=local.termDateString)>

				<cfset sendFrontEndEmailNotification(lstEmails=local.lstEmails, qryRootSubscriber=local.qryRootSubscriber, 
					fullName=local.fullName, preTaxTotal=local.preTaxTotal, subTreeVerbose=local.subTreeVerbose,
					networkEmailFrom=arguments.event.getValue('mc_siteInfo.networkEmailFrom'),
					supportProviderEmail=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
					orgname=arguments.event.getValue('mc_siteInfo.orgname'), sitename=arguments.event.getValue('mc_siteInfo.sitename'),
					sitecode=arguments.event.getValue('mc_siteInfo.sitecode'),company=local.company)>
			</cfif>
		<cfcatch type="any">
			<cfset var errorStruct = { "localscope":local, "event":arguments.event.getCollection() }>
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=errorStruct, customMessage="model.subscriptions.subscriptions.doConfirm Wrapper")>
			<cfrethrow>
		</cfcatch>
		</cftry>
		
		<!--- Redirect To Pay Dues --->
		<cfset application.mcCacheManager.sessionSetValue(keyname='showSubReceipt#local.rootSubscriberID#', value=1)>
		<cfif application.mcCacheManager.sessionValueExists('subCoupon#local.rootSubscriberID#')>
			<cfset application.mcCacheManager.sessionDeleteValue('subCoupon#local.rootSubscriberID#')>
		</cfif>
		<cflocation url="/?pg=manageSubscriptions&suba=payDues&rsid=#local.rootSubscriberID#&mid=#local.memberID#" addtoken="false">
	</cffunction>

	<cffunction name="getSubscriptionAmounts" access="private" output="false" returntype="struct">
		<cfargument name="strMemberSubs" type="struct" required="true">
		<cfargument name="qryAllAddOnSubs" type="query" required="true">
		<cfargument name="includeExtraSubInfo" type="boolean" required="false" default="false">
		
		<cfset var local = structNew()>

		<cfset local.payOrderList = ''>

		<cfsavecontent variable="local.sqlSubAmts">
			<cfoutput>
				<cfloop collection="#arguments.strMemberSubs#" index="local.thisSubscriptionID">
					<cfset local.thisSub = duplicate(arguments.strMemberSubs[local.thisSubscriptionID])>
					<cfif local.thisSub.keyExists("PCFree") AND local.thisSub.PCFree>
						<cfset local.thisRateTotal = 0>
					<cfelse>
						<cfset local.thisRateTotal = val(local.thisSub.rateTotal)>
					</cfif>

					<cfif local.thisRateTotal EQ 0 OR local.thisSub.forceUpFront EQ 1>
						<cfset local.loopPayOrder = "0000.">
					<cfelse>
						<cfset local.loopPayOrder = "#numberformat(local.thisSub.paymentOrder, "0000.")#">

						<cfquery name="local.qryThisAddOnSubPath" dbtype="query">
							SELECT thePath
							FROM [arguments].qryAllAddOnSubs
							WHERE subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisSub.subscriptionID)#">
						</cfquery>

						<cfif local.qryThisAddOnSubPath.recordCount>
							<cfset local.thisSubAddOnParentPath = listFirst(local.qryThisAddOnSubPath.thePath,".")>
							<cfquery name="local.qryThisPathSub" dbtype="query">
								SELECT subscriptionID
								FROM [arguments].qryAllAddOnSubs
								WHERE thePath = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisSubAddOnParentPath#">
							</cfquery>

							<cfif local.qryThisPathSub.recordCount AND arguments.strMemberSubs.keyExists(local.qryThisPathSub.subscriptionID)>
								<cfset local.loopPayOrder = "#numberformat(arguments.strMemberSubs[local.qryThisPathSub.subscriptionID].paymentOrder, "0000.")#">
							</cfif>
						</cfif>
					</cfif>

					<cfif ListContains(local.payOrderList, local.loopPayOrder) eq 0>
						<cfset local.payOrderList = ListAppend(local.payOrderList, local.loopPayOrder)>
					</cfif>
					
					INSERT INTO @tblAmts (amount, payOrder, forceUpFront, subscriptionID, invoiceProfileID, rateID)
					VALUES (#local.thisRateTotal#, '#local.loopPayOrder#', #val(local.thisSub.forceUpFront)#, #val(local.thisSub.subscriptionID)#, 
							#val(local.thisSub.invProfileIDToUse)#, #val(local.thisSub.rateID)#);
				</cfloop>
			</cfoutput>
		</cfsavecontent>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubAmts">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @rateNameOverride varchar(30) = 'Rate Overridden';
			DECLARE @tblAmts TABLE (rowID int identity(1,1), amount decimal(18,2), payOrder varchar(100), forceUpFront bit,
				subscriptionID int, subscriptionName varchar(100), invoiceProfileID int, invoiceProfileName varchar(100), 
				rateID int, rateName varchar(200));

			#preserveSingleQuotes(local.sqlSubAmts)#

			UPDATE tbl
			SET tbl.subscriptionName = s.subscriptionName
			FROM @tblAmts tbl
			INNER JOIN dbo.sub_subscriptions s on s.subscriptionID = tbl.subscriptionID;

			UPDATE tbl
			SET tbl.rateName = r.rateName
			FROM @tblAmts tbl
			INNER JOIN dbo.sub_rates r on r.rateID = tbl.rateID;

			UPDATE @tblAmts
			SET rateName = @rateNameOverride
			WHERE rateID IS NULL;

			UPDATE tbl
			SET tbl.invoiceProfileName = ip.profileName
			FROM @tblAmts tbl
			INNER JOIN dbo.tr_invoiceProfiles ip on ip.profileID = tbl.invoiceProfileID;

			SELECT rowID, amount, payOrder, forceUpFront, subscriptionID, subscriptionName, invoiceProfileID, 
				invoiceProfileName, rateID, rateName
			FROM @tblAmts;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.payOrderList = ListSort(local.payOrderList, "Text")>

		<cfquery dbtype="query" name="local.qryInvProfileAmts">
			select sum(amount) as totalAmt, payOrder, forceUpFront, invoiceProfileID, invoiceProfileName
			from [local].qrySubAmts
			group by payOrder, forceUpFront, invoiceProfileID, invoiceProfileName
			order by payOrder, forceUpFront DESC, invoiceProfileID, invoiceProfileName
		</cfquery>

		<cfset local.payOrderArray = ArrayNew(1)>
		<cfloop list="#local.payOrderList#" index="local.thisPayOrderPath">
			<cfset local.payOrderStructEntry = StructNew()>
			<cfset local.payOrderStructEntry.payOrder = "#local.thisPayOrderPath#">
			<cfset local.payOrderStructEntry.total = 0.00>
			<cfset local.payOrderStructEntry.rate = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.diff = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.totalApplied = 0.00 + 0.00>
			<cfset local.payOrderStructEntry.profiles = ArrayNew(1)>

			<cfquery dbtype="query" name="local.qryProfileAmt">
				select sum(totalAmt) as totalAmt, invoiceProfileName, invoiceProfileID
				from [local].qryInvProfileAmts
				where payOrder = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisPayOrderPath#">
				group by invoiceProfileName, invoiceProfileID
			</cfquery>
			<cfloop query="local.qryProfileAmt">
				<cfset local.profileEntry = StructNew()>
				<cfset local.profileEntry.name = local.qryProfileAmt.invoiceProfileName>
				<cfset local.profileEntry.id = local.qryProfileAmt.invoiceProfileID>
				<cfset local.profileEntry.total = local.qryProfileAmt.totalAmt>
				<cfset local.profileEntry.totalApplied = 0.00 + 0.00>
				<cfset local.profileEntry.currAmount = 0.00 + 0.00>

				<cfset local.profileEntry.subs = ArrayNew(1)>
				
				<cfquery dbtype="query" name="local.qryInvProfileSubs">
					select amount, subscriptionName + ' - ' + rateName as subscriptionName, subscriptionID
					from [local].qrySubAmts
					where invoiceProfileID = #local.qryProfileAmt.invoiceProfileID#
					and payOrder = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisPayOrderPath#">
					order by rowID
				</cfquery>
				<cfloop query="local.qryInvProfileSubs">
					<cfset local.subEntry = StructNew()>
					<cfset local.subEntry.name = replaceNoCase(local.qryInvProfileSubs.subscriptionName,' - Rate Overridden','','ALL')>
					<cfset local.subEntry.id = local.qryInvProfileSubs.subscriptionID>
					<cfset local.subEntry.total = local.qryInvProfileSubs.amount>
					<cfset local.subEntry.totalApplied = 0.00 + 0.00>

					<cfif arguments.includeExtraSubInfo>
						<cfset local.subInfo = duplicate(arguments.strMemberSubs[local.qryInvProfileSubs.subscriptionID])>
						
						<cfset local.subEntry.GLAccountID = int(val(local.subInfo.GLAccountIDToUse))>
						<cfset local.subEntry.invProfileID = local.thisSub.invProfileIDToUse>
						<cfset local.subEntry.subscriptionID = int(val(local.subInfo.subscriptionID))>
						<cfset local.subEntry.subscriberID = int(val(local.subInfo.subscriberID))>
						<cfset local.subEntry.useRate = 0.00>
						<cfset local.subEntry.rateApplied = 0.00>
						<cfset local.subStatus = local.subInfo.currStatus>
						<cfif Len(local.subStatus) eq 0>
							<cfset local.subStatus = 'A'>
						</cfif>
						<cfset local.subEntry.subStatus = local.subStatus>
						<cfset local.subEntry.isSinglePayment = true>
					</cfif>

					<cfset ArrayAppend(local.profileEntry.subs, local.subEntry)>
				</cfloop>
				
				<cfset local.payOrderStructEntry.total = local.payOrderStructEntry.total + local.qryProfileAmt.totalAmt>
				<cfset ArrayAppend(local.payOrderStructEntry.profiles, local.profileEntry)>
			</cfloop>

			<cfset ArrayAppend(local.payOrderArray, local.payOrderStructEntry)>
		</cfloop>

		<cfquery dbtype="query" name="local.qryTotals">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
		</cfquery>
		<cfset local.amountToCharge = local.qryTotals.totalAmt>

		<cfquery dbtype="query" name="local.qryUpFrontAmt">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
			where forceUpFront = 1
		</cfquery>

		<cfquery dbtype="query" name="local.qryNonUpFrontAmt">
			select sum(totalAmt) as totalAmt
			from [local].qryInvProfileAmts
			where forceUpFront = 0
		</cfquery>
		
		<cfif not local.qryNonUpFrontAmt.recordcount>
			<cfset local.qryNonUpFrontAmt = QueryNew("totalAmt", "Double")>
			<cfset QueryAddRow(local.qryNonUpFrontAmt, 1)>
			<cfset QuerySetCell(local.qryNonUpFrontAmt, "totalAmt", "0", 1)>
		</cfif>
		
		<cfquery dbtype="query" name="local.qryInvProfiles">
			select distinct invoiceProfileID, invoiceProfileName
			from [local].qryInvProfileAmts
			order by invoiceProfileName
		</cfquery>

		<cfset local.retStruct = {
			"qrySubAmts": local.qrySubAmts,
			"payOrderList": local.payOrderList,
			"payOrderArray": local.payOrderArray,
			"qryTotals": local.qryTotals,
			"qryUpFrontAmt": local.qryUpFrontAmt,
			"qryNonUpFrontAmt": local.qryNonUpFrontAmt,
			"qryInvProfiles": local.qryInvProfiles
		}>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSubscriptionTransactions" access="private" output="false" returntype="array">
		<cfargument name="strMemberSubs" type="struct" required="true">
		<cfargument name="subEndDate" type="string" required="true">
		<cfargument name="dateAmts" type="array" required="true">
		<cfargument name="qryAllAddOnSubs" type="query" required="true">
	
		<cfset var local = structNew()>
		<cfset local.retVal = structNew()>		
	
		<cftry>
			<cfset local.retVal.dateAmts = arguments.dateAmts>
			<cfset local.subAmts = getSubscriptionAmounts(strMemberSubs=arguments.strMemberSubs, qryAllAddOnSubs=arguments.qryAllAddOnSubs, includeExtraSubInfo=true)>
			<cfloop array="#local.subAmts.payOrderArray#" index="local.thisPayOrderEntry">
				<cfif local.thisPayOrderEntry.payOrder eq "0000."> <!--- must be put on first available invoice for invoice profile --->
					<cfif ArrayLen(local.retVal.dateAmts) gt 0>
						<cfset local.thisDateAmt = local.retVal.dateAmts[1]>
						
						<cfloop array="#local.thisPayOrderEntry.profiles#" index="local.thisPayOrderProfileEntry">
							<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
								<cfset local.currDetail = local.thisSubEntry.name>
								<cfset local.currTransaction = StructNew()>
								<cfset local.currTransaction.action = 'Add'>
								<cfset local.currTransaction.subEndDate = arguments.subEndDate>
								<cfset local.currTransaction.detail = local.currDetail>
								<cfset local.currTransaction.amount = local.thisSubEntry.total>
								<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
								<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
								<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>

								<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
								<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.thisSubEntry.total>	
								<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.thisSubEntry.total>
								<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.thisSubEntry.total>
								<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.thisSubEntry.total>
							</cfloop>
						</cfloop>
						
					</cfif>
				<cfelse> <!--- work the invoices in order --->
				
					<cfloop array="#local.retVal.dateAmts#" index="local.thisDateAmt">
						<cfloop array="#local.thisPayOrderEntry.profiles#" index="local.thisPayOrderProfileEntry">
							
							<cfif StructKeyExists(local.thisDateAmt.profiles, "#local.thisPayOrderProfileEntry.ID#")>
								
								<cfset local.currPayOrderProfileAmountRemaining = local.thisPayOrderProfileEntry.total - local.thisPayOrderProfileEntry.totalApplied>
								
								<cfset local.currInvoiceAmountRemaining = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].amt - local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal>
								
								<cfif local.currInvoiceAmountRemaining gt 0>
	
									<cfset local.poSubsAmtApplied = 0.00>
									<cfset local.subArrayCount = 0>
									<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
										<cfset local.subArrayCount = local.subArrayCount + 1>
			
										<cfset local.currSubAmountRemaining = local.thisSubEntry.total - local.thisSubEntry.totalApplied>
	
										<cfif local.currInvoiceAmountRemaining gte local.currPayOrderProfileAmountRemaining>
											<cfset local.currUsePayment = local.currSubAmountRemaining>
										<cfelse>
											<!--- partial payments --->
											<cfset local.thisSubEntry.isSinglePayment = false>
											<cfset local.currUsePayment = numberformat((local.thisSubEntry.total / local.thisPayOrderProfileEntry.total) * local.currInvoiceAmountRemaining, "_.__")>
											<cfif local.currInvoiceAmountRemaining lt (local.currUsePayment + local.poSubsAmtApplied)>
												<cfset local.currUsePayment = local.currUsePayment - ((local.currUsePayment + local.poSubsAmtApplied) - local.currInvoiceAmountRemaining)>
											<cfelseif (ArrayLen(local.thisPayOrderProfileEntry.subs) eq local.subArrayCount) AND
																(local.currPayOrderProfileAmountRemaining gt (local.currUsePayment + local.poSubsAmtApplied)) AND
																(local.currInvoiceAmountRemaining gt (local.currUsePayment + local.poSubsAmtApplied)) AND
																(local.currSubAmountRemaining gt (local.currUsePayment + (local.currInvoiceAmountRemaining - (local.currUsePayment + local.poSubsAmtApplied))))>
												<cfset local.currUsePayment = local.currUsePayment + (local.currInvoiceAmountRemaining - (local.currUsePayment + local.poSubsAmtApplied))>
											</cfif> <!--- end if/elseif local.currInvoiceAmountRemaining lt (local.currUsePayment + local.poSubsAmtApplied) --->
										</cfif> <!--- end if/else local.currInvoiceAmountRemaining gte local.currPayOrderProfileAmountRemaining --->
	
										<cfif local.thisSubEntry.isSinglePayment>
											<cfset local.currDetail = local.thisSubEntry.name>
										<cfelse>
											<cfset local.currDetail = "#local.thisSubEntry.name# Installment">
										</cfif> <!--- end if/else local.thisSubEntry.isSinglePayment eq "true" --->
										
										<cfif (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0)>
											<cfset local.currTransaction = StructNew()>
											<cfset local.currTransaction.action = 'Add'>
											<cfset local.currTransaction.subEndDate = arguments.subEndDate>
											<cfset local.currTransaction.detail = local.currDetail>
											<cfset local.currTransaction.amount = local.currUsePayment>
											<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
											<cfset local.currTransaction.invoiceCount = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
											<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
			
											<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
											<cfset local.poSubsAmtApplied = local.poSubsAmtApplied + local.currUsePayment>
											<cfset local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactionTotal + local.currUsePayment>	
											<cfset local.thisPayOrderEntry.totalApplied = local.thisPayOrderEntry.totalApplied + local.currUsePayment>
											<cfset local.thisPayOrderProfileEntry.totalApplied = local.thisPayOrderProfileEntry.totalApplied + local.currUsePayment>
											<cfset local.thisSubEntry.totalApplied = local.thisSubEntry.totalApplied + local.currUsePayment>

										<cfelse>
											<cfset local.currDetail = local.thisSubEntry.name>
											<cfset local.thisInvoiceCountToUse = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
											
											<cfset local.currTransaction = StructNew()>
											<cfset local.currTransaction.action = 'Add'>
											<cfset local.currTransaction.subEndDate = arguments.subEndDate>
											<cfset local.currTransaction.detail = local.currDetail>
											<cfset local.currTransaction.amount = 0>
											<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
											<cfset local.currTransaction.invoiceCount = local.thisInvoiceCountToUse>
											<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
			
											<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>		
										</cfif> <!--- end if/else (local.currPayOrderProfileAmountRemaining gt 0) AND (local.currUsePayment gt 0) --->
										
									</cfloop> <!--- end local.thisPayOrderProfileEntry.subs loop --->							
								<cfelse>
									<!--- invoice amt has been met, if there is a previous amount, it needs to be removed  --->
									<cfloop array="#local.thisPayOrderProfileEntry.subs#" index="local.thisSubEntry">
										<cfset local.currDetail = local.thisSubEntry.name>
										<cfset local.thisInvoiceCountToUse = local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].invoiceNumber>
										
										<cfset local.currTransaction = StructNew()>
										<cfset local.currTransaction.action = 'Add'>
										<cfset local.currTransaction.subEndDate = arguments.subEndDate>
										<cfset local.currTransaction.detail = local.currDetail>
										<cfset local.currTransaction.amount = 0>
										<cfset local.currTransaction.GLAccountID = local.thisSubEntry.GLAccountID>
										<cfset local.currTransaction.invoiceCount = local.thisInvoiceCountToUse>
										<cfset local.currTransaction.subscriptionID = local.thisSubEntry.subscriptionID>
		
										<cfset ArrayAppend(local.thisDateAmt.profiles["#local.thisPayOrderProfileEntry.ID#"].transactions, local.currTransaction)>
									</cfloop> <!--- end local.thisPayOrderProfileEntry.subs loop --->							
									
								</cfif> <!--- end if/else local.currInvoiceAmountRemaining gt 0 --->
								
							</cfif> <!--- if StructKeyExists(local.thisDateAmt.profiles, "#local.thisPayOrderProfileEntry.ID#") --->
						
						</cfloop> <!--- end local.thisPayOrderEntry.profiles loop --->
					</cfloop>	<!--- end local.retVal.dateAmts loop --->
				
				</cfif> <!--- end if/else local.thisPayOrderEntry.payOrder eq "0000." --->
			</cfloop> <!--- end local.subAmts.payOrderArray loop --->
	
			<!--- get the deferred GLs if there are any --->
			<!--- For this to work, all sales and positive adjustments must have a GLAccountID identified above --->
			<cfset local.retVal.dateAmts = updateSubscriptionTransactionsDeferred(dateAmts=local.retVal.dateAmts)>
	
		<cfcatch type="any">
			<cfset local.arguments = arguments />
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local,customMessage="exception in model.subscriptions.subscriptions.getSubscriptionTransactions + rethrow")>
			<cfrethrow>
		</cfcatch>
		</cftry>
		<cfreturn local.retVal.dateAmts>
	</cffunction>
	
	<cffunction name="updateSubscriptionTransactionsDeferred" access="private" output="false" returntype="array">
		<cfargument name="dateAmts" type="array" required="true">
	
		<cfset var local = structNew()>
		<cfset local.dateAmts = arguments.dateAmts>
		<cfset local.strDeferredAccounts = structNew()>
	
		<cftry>
			<cfloop array="#local.dateAmts#" index="local.thisDateAmt">
				<cfloop collection="#local.thisDateAmt.profiles#" item="local.thisProfileKey">
					<cfloop array="#local.thisDateAmt.profiles[local.thisProfileKey].transactions#" index="local.thisCurrTransaction">
						<cfif structKeyExists(local.thisCurrTransaction,"GLAccountID") and local.thisCurrTransaction.GLAccountID gt 0>
							<cfif structKeyExists(local.strDeferredAccounts,local.thisCurrTransaction.GLAccountID)>
								<cfset local.thisCurrTransaction.deferredGLAccountID = local.strDeferredAccounts[local.thisCurrTransaction.GLAccountID]>
							<cfelse>	
								<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeferred">
									select dbo.fn_tr_getDeferredGLAccountID(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisCurrTransaction.GLAccountID#">) as deferredGLAccountID
								</cfquery>
								<cfset local.thisCurrTransaction.deferredGLAccountID = val(local.qryDeferred.deferredGLAccountID)>
								<cfset structInsert(local.strDeferredAccounts, local.thisCurrTransaction.GLAccountID, val(local.qryDeferred.deferredGLAccountID))>
							</cfif>
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>
	
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfrethrow>
		</cfcatch>
		</cftry>
	
		<cfreturn local.dateAmts>
	</cffunction>

	<cffunction name="getRecognitionScheduleCode" access="private" output="false" returntype="string">
		<cfargument name="dateAmts" type="array" required="true">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.retString">
			<cfoutput>
			-- create recognition schedules
			declare @tblRecognitions TABLE (autoID int IDENTITY(1,1), dateamtInvNum int, dateamtTransNumber int, dueDate datetime, amount decimal(18,2), subscriptionID int, subscriptionTransOrder int);
			declare @tblSubscriberID TABLE (subscriptionID int, subscriberID int);
			declare @minSubIDDeferred int, @defStartDate datetime, @defEndDate datetime, @minSubTransOrder int, @recogThisMonth datetime, 
				@subAmtSum decimal(18,2), @spreadSum decimal(18,2), @subScheduleCount int, @spreadFirstAutoID int, @spreadDiff decimal(18,2),
				@transAmtToRecog decimal(18,2), @recogAmt decimal(18,2), @defSchAmt decimal(18,2), @recogAutoID int, @XMLSchedule xml;
			declare @tblDefSchedule TABLE (autoID int IDENTITY(1,1), subscriptionID int, dt datetime, amt decimal(18,2));
			declare @tblFinalDefSchedule TABLE (subscriptionID int, subscriptionTransOrder int, dt datetime, amt decimal(18,2));

			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfloop collection="#local.thisDateAmt.profiles#" item="local.thisProfileKey">
					<cfset local.thisArrTransactions = local.thisDateAmt.profiles[local.thisProfileKey].transactions>
					<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
						<cfif structKeyExists(local.thisArrTransactions[local.thisCurrTransactionNum],"deferredGLAccountID") 
							and local.thisArrTransactions[local.thisCurrTransactionNum].deferredGLAccountID gt 0
							and local.thisArrTransactions[local.thisCurrTransactionNum].action eq "Add">
							insert into @tblRecognitions (dateamtInvNum, dateamtTransNumber, dueDate, amount, subscriptionID) 
							values (#local.thisDateAmt.profiles[local.thisProfileKey].invoiceNumber#, #local.thisCurrTransactionNum#, '#local.thisDateAmt.date#', 
								#local.thisArrTransactions[local.thisCurrTransactionNum].amount#, 
								#local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#);

							IF NOT EXISTS (select subscriptionID from @tblSubscriberID where subscriptionID = #local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#)
								insert into @tblSubscriberID (subscriptionID, subscriberID)
								VALUES (#local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#, @subscriberIDForSub#local.thisArrTransactions[local.thisCurrTransactionNum].subscriptionID#);
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>		

			-- update order we need to use below (invduedate asc)
			update tbl
			set tbl.subscriptionTransOrder = tmp.row
			from @tblRecognitions as tbl
			inner join (
				select autoID, ROW_NUMBER() OVER(PARTITION BY subscriptionID ORDER BY dueDate, dateAmtInvNum, dateamtTransNumber) as row
				from @tblRecognitions
			) as tmp on tmp.autoID = tbl.autoID;

			-- loop over each subscription in deferred
			select @minSubIDDeferred = min(subscriptionID) from @tblRecognitions;
			while @minSubIDDeferred is not null BEGIN
				select @defStartDate = s.recogStartDate, @defEndDate = s.recogEndDate
					from dbo.sub_subscribers as s
					inner join @tblSubscriberID as tbl on tbl.subscriberID = s.subscriberID
					where tbl.subscriptionID = @minSubIDDeferred;

				-- create recog schedule for sub
				set @recogThisMonth = DATEADD(dd,DATEDIFF(dd,0,@defStartDate),0);
				while @recogThisMonth <= @defEndDate BEGIN
					insert into @tblDefSchedule (subscriptionID, dt, amt) values (@minSubIDDeferred, @recogThisMonth, 0);
					select @recogThisMonth = DATEADD(mm,1,@recogThisMonth);
				END

				-- spread amount of sub over dates evenly and put remainder on 1st schedule item
				select @subAmtSum = sum(amount) from @tblRecognitions where subscriptionID = @minSubIDDeferred;
				select @subScheduleCount = count(*) from @tblDefSchedule where subscriptionID = @minSubIDDeferred;
				update @tblDefSchedule set amt = cast(@subAmtSum/@subScheduleCount as decimal(18,2)) where subscriptionID = @minSubIDDeferred;
				select @spreadSum = sum(amt), @spreadFirstAutoID = min(autoid) from @tblDefSchedule where subscriptionID = @minSubIDDeferred;
				set @spreadDiff = @subAmtSum - @spreadSum;
				if @spreadDiff <> 0
					update @tblDefSchedule set amt = amt + @spreadDiff where autoID = @spreadFirstAutoID;

				-- loop over transactions in order to determine the schedule needed for each transaction
				set @minSubTransOrder = null;
				select @minSubTransOrder = min(subscriptionTransOrder) from @tblRecognitions where subscriptionID = @minSubIDDeferred;
				while @minSubTransOrder is not null BEGIN
					select @transAmtToRecog = amount from @tblRecognitions where subscriptionID = @minSubIDDeferred and subscriptionTransOrder = @minSubTransOrder;

					while @transAmtToRecog > 0 BEGIN
						select top 1 @recogAutoID = autoID, @recogThisMonth = dt, @recogAmt = amt
							from @tblDefSchedule 
							where subscriptionID = @minSubIDDeferred
							and amt > 0
							order by autoID;

						if @recogAmt <= @transAmtToRecog
							set @defSchAmt = @recogAmt;
						else 									
							set @defSchAmt = @transAmtToRecog;

						insert into @tblFinalDefSchedule (subscriptionID, subscriptionTransOrder, dt, amt)
						values (@minSubIDDeferred, @minSubTransOrder, @recogThisMonth, @defSchAmt);

						update @tblDefSchedule 
						set amt = amt - @defSchAmt 
						where subscriptionID = @minSubIDDeferred
						and autoID = @recogAutoID;

						set @transAmtToRecog = @transAmtToRecog - @defSchAmt;
					END

					select @minSubTransOrder = min(subscriptionTransOrder) from @tblRecognitions where subscriptionID = @minSubIDDeferred and subscriptionTransOrder > @minSubTransOrder;
				END

				select @minSubIDDeferred = min(subscriptionID) from @tblRecognitions where subscriptionID > @minSubIDDeferred;
			END
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retString>
	</cffunction>

	<cffunction name="getTransactionsAndInvoiceCode" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="rootSubscriptionID" type="numeric" required="true">
		<cfargument name="dateAmts" type="array" required="true">
		<cfargument name="subMemberID" type="numeric" required="true">
		<cfargument name="listInvoiceProfileIDs" type="string" required="true">
		<cfargument name="thisInvoiceIDList" type="string" required="true">
		<cfargument name="transdate" type="date" required="true">
		<cfargument name="stateIDforTax" type="numeric" required="true">
		<cfargument name="zipForTax" type="string" required="true">

		<cfset var local = structNew()>

		<cfset local.strSubDiscounts = structNew('ordered')>
		<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname='subCoupon#arguments.rootSubscriberID#', defaultValue={})>
		
		<!--- coupon applied --->
		<cfif structCount(local.strCoupon) AND val(local.strCoupon.couponID)>
			<cfset local.totalRedemptionCount = 0>

			<cfloop collection="#local.strCoupon.strsubprice#" item="local.subID">
				<cfset local.thisSub = duplicate(local.strCoupon.strsubprice[local.subID])>
				<cfset local.discount = val(local.thisSub.discount)>

				<!--- no discount --->
				<cfif local.discount EQ 0>
					<cfcontinue>
				</cfif>

				<cfset local.tmpStr = { "discount":local.discount, "discountRemaining":local.discount, "discountPerInstallment":local.thisSub.discountperinstallment, "strDates":structNew('ordered') }>
				<cfset local.thisSubTotalSaleAmt = 0>

				<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
					<cfset local.dateProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
					<cfloop array="#local.dateProfileKeys#" index="local.thisProfileKey">
						<cfset local.thisArrTransactions = duplicate(local.thisDateAmt.profiles[local.thisProfileKey].transactions)>
						<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
							<cfset local.thisCurrTransaction = local.thisArrTransactions[local.thisCurrTransactionNum]>
							<cfif local.thisCurrTransaction.subscriptionID EQ local.subID AND local.thisCurrTransaction.action eq "Add" AND local.thisCurrTransaction.amount GT 0>
								<cfset local.thisSubTotalSaleAmt = local.thisSubTotalSaleAmt + local.thisCurrTransaction.amount>
								<cfset local.discountStr = { "amt":local.thisCurrTransaction.amount, "discount":0, "redemptionCount":0 }>
								<cfif local.thisDateAmt.date EQ arguments.dateAmts[1].date
									AND (
										local.strCoupon.applyto EQ 'sub' 
										OR
										(local.strCoupon.applyto EQ 'subtree' AND local.totalRedemptionCount EQ 0)
									)>
									<cfset local.discountStr.redemptionCount = 1>
									<cfset local.totalRedemptionCount++>
								</cfif>
								<cfset structInsert(local.tmpStr.strDates, DateFormat(local.thisDateAmt.date,'m_d_yyyy'), local.discountStr)>
							</cfif>
						</cfloop>
					</cfloop>
				</cfloop>

				<!--- no sales --->
				<cfif local.thisSubTotalSaleAmt EQ 0>
					<cfcontinue>
				</cfif>

				<cfif local.tmpStr.discount GT local.thisSubTotalSaleAmt>
					<cfset local.tmpStr.discount = local.thisSubTotalSaleAmt>
					<cfset local.tmpStr.discountRemaining = local.thisSubTotalSaleAmt>
				</cfif>

				<cfloop condition="local.tmpStr.discountRemaining GT 0">
					<cfloop collection="#local.tmpStr.strDates#" item="local.thisDate">
						<cfset local.thisDateSub = duplicate(local.tmpStr.strDates[local.thisDate])>

						<!--- max discount applied to this sale --->
						<cfif local.thisDateSub.amt EQ local.thisDateSub.discount>
							<cfcontinue>
						</cfif>

						<cfset local.thisSubSaleDiscount = MIN((local.thisDateSub.amt - local.thisDateSub.discount),MIN(local.tmpStr.discountPerInstallment,local.tmpStr.discountRemaining))>
						<cfset local.tmpStr.strDates[local.thisDate].discount = NumberFormat(precisionEvaluate(local.thisDateSub.discount + local.thisSubSaleDiscount),"0.00")>
						<cfset local.tmpStr.discountRemaining = NumberFormat(precisionEvaluate(local.tmpStr.discountRemaining - local.thisSubSaleDiscount),"0.00")>

						<!--- discount fully applied --->
						<cfif local.tmpStr.discountRemaining EQ 0>
							<cfbreak>
						</cfif>
					</cfloop>
				</cfloop>

				<cfset structInsert(local.strSubDiscounts, local.subID, local.tmpStr)>
			</cfloop>
		</cfif>
		
		<cfsavecontent variable="local.retString">
			<cfoutput>
			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfset local.dateProfileKeys = StructKeyArray(local.thisDateAmt.profiles)>
				<cfloop array="#local.dateProfileKeys#" index="local.thisProfileKey">
					<cfset local.thisArrTransactions = local.thisDateAmt.profiles[local.thisProfileKey].transactions>
					<cfloop from="1" to="#ArrayLen(local.thisArrTransactions)#" index="local.thisCurrTransactionNum">
						<cfset local.thisCurrTransaction = local.thisArrTransactions[local.thisCurrTransactionNum]>
						<cfif local.thisCurrTransaction.action eq "Add">
							<cfset local.strTaxIndiv = CreateObject("component","model.system.platform.accounting").getTaxForUncommittedSale(saleGLAccountID=local.thisCurrTransaction.GLAccountID, 
															saleAmount=local.thisCurrTransaction.amount, transactionDate=arguments.transdate, stateIDForTax=val(arguments.stateIDforTax),
															zipForTax=arguments.zipForTax)>
							set @XMLSchedule = null;
							SELECT @XMLSchedule = (
								select row.amt, convert(varchar(10),row.dt,101) as dt
								from @tblFinalDefSchedule as row
								inner join @tblRecognitions as r on r.subscriptionID = row.subscriptionID and r.subscriptionTransOrder = row.subscriptionTransOrder
								where r.subscriptionID = #local.thisCurrTransaction.subscriptionID#
								and r.dateamtInvNum = #local.thisDateAmt.profiles[local.thisProfileKey].invoicenumber#
								and r.dateAmtTransNumber = #local.thisCurrTransactionNum#
								for XML AUTO, ROOT('rows'), TYPE
							);

							EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID, @assignedToMemberID=@assignedToMemberID, @recordedByMemberID=@loggedInMemberID,
								@statsSessionID=@statsSessionID, @status='Active', @detail='#replace(left(local.thisCurrTransaction.detail,500),"'","''","ALL")#', @amount=#local.thisCurrTransaction.amount#,
								@transactionDate=@transDate, @creditGLAccountID=#local.thisCurrTransaction.GLAccountID#, 
								@invoiceID=@invoiceID_#local.thisCurrTransaction.invoiceCount#,
								@parentTransactionID=null, 
								@stateIDForTax=<cfif val(arguments.stateIDforTax)>#arguments.stateIDforTax#<cfelse>null</cfif>, 
								@zipForTax=<cfif len(arguments.zipForTax)>'#arguments.zipForTax#'<cfelse>null</cfif>, 
								@taxAmount=#val(local.strTaxIndiv.totalTaxAmt)#, @byPassTax=0, @bypassInvoiceMessage=0, @bypassAccrual=0, 
								@xmlSchedule=@XMLSchedule, @transactionID=@subTransactionID OUTPUT;

							EXEC dbo.tr_createApplication @orgID=@orgID, @siteID=@siteID, @applicationType='Admin', @transactionID=@subTransactionID, 
								@itemType='Dues', @itemID=@subscriberIDForSub#local.thisCurrTransaction.subscriptionID#, @subItemID=null;
						
							<!--- Coupon applied --->
							<cfif local.strSubDiscounts.keyExists(local.thisCurrTransaction.subscriptionID) 
								AND local.strSubDiscounts[local.thisCurrTransaction.subscriptionID].strDates.keyExists(DateFormat(local.thisDateAmt.date,'m_d_yyyy'))>
								<cfset local.thisSubDiscount = duplicate(local.strSubDiscounts[local.thisCurrTransaction.subscriptionID].strDates["#DateFormat(local.thisDateAmt.date,'m_d_yyyy')#"])>

								SET @couponID = #int(val(local.strCoupon.couponID))#;
								SET @discountAmount = #local.thisSubDiscount.discount# * - 1;
								SET @redemptionCount = #int(val(local.thisSubDiscount.redemptionCount))#;
								
								EXEC dbo.tr_createTransaction_discount @recordedOnSiteID=@siteid, @recordedByMemberID=@loggedInMemberID, 
									@statsSessionID=@statsSessionID, @amount=@discountAmount, @transactionDate=@transDate, 
									@saleTransactionID=@subTransactionID, @invoiceID=@invoiceID_#local.thisCurrTransaction.invoiceCount#, 
									@couponID=@couponID, @itemType='SubReg', @itemID=@subscriberIDForSub#local.thisCurrTransaction.subscriptionID#, 
									@redemptionCount=@redemptionCount, @transactionID=@adjTransactionID OUTPUT;
							</cfif>
						</cfif>
					</cfloop>
				</cfloop>
			</cfloop>

			<cfset local.arrCount = 0>
			<cfset local.isFirstPaymentDate = true>
			<cfloop array="#arguments.dateAmts#" index="local.thisDateAmt">
				<cfloop list="#arguments.listInvoiceProfileIDs#" index="local.thisInvProfileID">
					<cfif StructKeyExists(local.thisDateAmt, "profiles") AND StructKeyExists(local.thisDateAmt.profiles, "#local.thisInvProfileID#")>
						<cfif Len(local.thisDateAmt.profiles["#local.thisInvProfileID#"].amt) gt 0 
								and not local.isFirstPaymentDate 
								and arguments.event.getValue('hps_#local.thisDateAmt.paymentNumber#_#local.thisInvProfileID#_appearOnThisInvoice',false) eq true>
							<cfset local.arrCount = local.arrCount + 1>
		
							update dbo.tr_invoices
							set statusID = 2
							where invoiceID = @invoiceID_#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber#;
	
							insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
							values (@invoiceID_#local.thisDateAmt.profiles["#local.thisInvProfileID#"].invoiceNumber#, getdate(), 2, 1, @loggedInMemberID);
						<cfelse>
							<cfset local.arrCount = local.arrCount + 1>
							<cfif listFind(arguments.thisInvoiceIDList,local.arrCount)>
								exec dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@loggedInMemberID, @invoiceIDList=@invoiceID_#local.arrCount#;
							</cfif>
						</cfif>
					</cfif>
				</cfloop>
				<cfset local.isFirstPaymentDate = false>
			</cfloop>	
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.retString>
	</cffunction>

	<cffunction name="getDatesToUseForExistingSubs" access="private" output="false" returntype="struct">
		<cfargument name="subStartDate" type="string" required="true">
		<cfargument name="subEndDate" type="string" required="true">
		<cfargument name="subGraceEndDate" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = {
			"subStartDate": arguments.subStartDate,
			"subEndDate": arguments.subEndDate,
			"subGraceEndDate": arguments.subGraceEndDate
		}>
		
		<!--- preserves the hour, thus the TZ --->
		<cfset local.daysDiff = DateDiff("d", local.returnStruct.subStartDate, Now())>
		<cfif local.daysDiff gt 0>
			<cfset local.subCurrDate = DateAdd("d", local.daysDiff, local.returnStruct.subStartDate)>
			<cfset local.subCurrDate = DateFormat(local.subCurrDate, "yyyy-mm-dd") & " " & TimeFormat(local.subCurrDate, "HH:mm:ss.l")>
			<cfset local.returnStruct.subStartDate = local.subCurrDate>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="getRecognitionDates" access="private" output="false" returntype="struct">
		<cfargument name="useRFID" type="numeric" required="true">
		<cfargument name="saleTransactionDate" type="date" required="true">
		<cfargument name="subStartDate" type="date" required="true">
		<cfargument name="subEndDate" type="date" required="true">
		<cfargument name="rootsubTermFlag" type="string" required="true">
		

		<cfset var local = structNew()>
		<cfset local.retStruct = structNew()>

		<cfquery name="local.qryGetRecogDate" datasource="#application.dsn.membercentral.dsn#">
			select top 1 r.recogAFStartDate, r.recogAFEndDate
			from dbo.sub_rates as r
			inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
			where rf.rfid = <cfqueryparam value="#arguments.useRFID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset local.retStruct.recogStartDate = DateFormat(local.qryGetRecogDate.recogAFStartDate,'m/d/yyyy')>
		<cfset local.retStruct.recogEndDate = "#dateformat(local.qryGetRecogDate.recogAFEndDate,'m/d/yyyy')# 23:59:59.997">
		
		<!--- if blank, default to sub dates --->
		<cfif NOT len(local.retStruct.recogStartDate)>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.subStartDate,'m/d/yyyy')>
		</cfif>
		<cfif NOT len(local.retStruct.recogEndDate)>
			<cfset local.retStruct.recogEndDate = "#dateformat(arguments.subEndDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<!--- recognition start date cannot be before sale transaction date --->
		<cfif dateCompare(arguments.saleTransactionDate,local.retStruct.recogStartDate) is 1>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.saleTransactionDate,'m/d/yyyy')>
		</cfif>

		<!--- recognition start date cannot be before subscription start date --->
		<cfif dateCompare(arguments.subStartDate,local.retStruct.recogStartDate) is 1>
			<cfset local.retStruct.recogStartDate = dateformat(arguments.subStartDate,'m/d/yyyy')>
		</cfif>

		<!--- If true anniversary mode --->
		<cfif arguments.rootsubTermFlag eq "C">
			<!--- Attempt to spread recognition over original number of days as configured --->
			<cfset local.recognitionDays = DateDiff("d", local.qryGetRecogDate.recogAFStartDate, local.qryGetRecogDate.recogAFEndDate)>
			<cfset local.retStruct.recogEndDate = DateAdd("d", local.recognitionDays, local.retStruct.recogStartDate)>
		</cfif>

		<!--- recognition end date cannot be after sub end date --->
		<cfif dateCompare(arguments.subEndDate,local.retStruct.recogEndDate) is -1>
			<cfset local.retStruct.recogEndDate = "#dateformat(arguments.subEndDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<!--- recognition start date must be before recognition end date --->
		<cfif DateCompare(local.retStruct.recogStartDate,local.retStruct.recogEndDate) is 1>
			<cfset local.retStruct.recogEndDate = "#dateformat(local.retStruct.recogStartDate,'m/d/yyyy')# 23:59:59.997">
		</cfif>

		<cfset local.retStruct.recogStartDate = dateformat(local.retStruct.recogStartDate,'m/d/yyyy')>
		<cfset local.retStruct.recogEndDate = "#dateformat(local.retStruct.recogEndDate,'m/d/yyyy')# 23:59:59.997">

		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="getSubTreeVerbose" access="private" output="false" returntype="string">
		<cfargument name="strMemberSubs" type="struct" required="true">
		<cfargument name="termDateString" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.setIDsList = "">
		<cfset local.strInvProfile = {}>
		<cfset local.tdStyle = "font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;">

		<cfsavecontent variable="local.subTreeVerbose">
			<cfoutput>
			<div style="font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;padding:6px;margin-bottom:15px;">Subscription Term: #arguments.termDateString#</div>
			<table>
			<cfloop collection="#arguments.strMemberSubs#" item="local.thisSubscriptionID">
				<cfset local.thisSub = duplicate(arguments.strMemberSubs[local.thisSubscriptionID])>
				<cfif local.thisSub.setID GT 0 >
					<cfif NOT listFind(local.setIDsList,local.thisSub.setID)>
						<tr><td colspan="3">&nbsp;</td></tr>
						<tr>
							<td style="#local.tdStyle#" colspan="3"><b>#local.thisSub.setName#</b></td>
						</tr>
						<cfset local.setIDsList = listAppend(local.setIDsList,local.thisSub.setID)>
					</cfif>
				</cfif>
				<tr>
					<td style="#local.tdStyle#">#local.thisSub.subName#</td>
					<cfif local.thisSub.PCFree>
						<td style="#local.tdStyle#">#DollarFormat(0)#</td>
						<td style="#local.tdStyle#">Free</td>
					<cfelse>
						<td style="#local.tdStyle#"><cfif local.thisSub.priceChanged>#DollarFormat(local.thisSub.rateTotal)#<cfelse>#DollarFormat(local.thisSub.rateToUse)#</cfif></td>
						<td style="#local.tdStyle#"><cfif local.thisSub.rateToUse GT 0 AND NOT local.thisSub.priceChanged>#local.thisSub.freqName#</cfif></td>
					</cfif>
				</tr>
				<tr>
					<td style="#local.tdStyle#" colspan="3">
						<div style="padding-left:30px;">
							<cfif local.thisSub.priceChanged>
								Rate Overridden
							<cfelse>
								Rate: #local.thisSub.rateName#
							</cfif>
							<br/>
							<cfif val(local.thisSub.invProfileIDToUse)>
								<cfif NOT structKeyExists(local.strInvProfile,local.thisSub.invProfileIDToUse)>
									<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoiceProfile">
										SET NOCOUNT ON;
										SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

										select profileName
										from dbo.tr_invoiceProfiles
										where profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.thisSub.invProfileIDToUse)#">

										SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
									</cfquery>
									<cfset local.strInvProfile[local.thisSub.invProfileIDToUse] = local.qryInvoiceProfile.profileName>
								</cfif>
								Invoice Profile: #local.strInvProfile[local.thisSub.invProfileIDToUse]#
							</cfif>
						</div>
					</td>
				</tr>
			</cfloop>
			</table>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.subTreeVerbose>
	</cffunction>

	<cffunction name="sendFrontEndEmailNotification" access="private" output="false" returntype="void">
		<cfargument name="lstEmails" type="string" required="true">
		<cfargument name="qryRootSubscriber" type="query" required="true">
		<cfargument name="fullName" type="string" required="true">
		<cfargument name="preTaxTotal" type="string" required="true">
		<cfargument name="subTreeVerbose" type="string" required="true">
		<cfargument name="networkEmailFrom" type="string" required="true">
		<cfargument name="supportProviderEmail" type="string" required="true">
		<cfargument name="orgname" type="string" required="true">
		<cfargument name="sitename" type="string" required="true">
		<cfargument name="sitecode" type="string" required="true">
		<cfargument name="company" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname='subCoupon#arguments.qryRootSubscriber.subscriberID#', defaultValue={})>
		
		<cfset local.tdStyle = "font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">

		<cfif arguments.lstEmails NEQ "">
			<cfsavecontent variable="local.emailBody">
				<cfoutput>
				#arguments.fullName#<br/>
				#arguments.company#<br/><br/>
				Here are the details for your subscription:<br/><br/><br/>

				<table style="border:1px solid ##999;border-collapse:collapse;" cellspacing="0" cellpadding="0" width="100%" border="0">
				<tr>
					<td style="font:bold 12px Verdana,Helvetica,Arial,sans-serif;border-bottom:1px solid ##999;background-color:##eee;padding:6px;color:##333;">Subscription Details</td>
				</tr>
				<tr>
					<td style="#local.tdStyle#padding:6px;">
						<!--- coupon applied --->
						<cfif structCount(local.strCoupon) AND val(local.strCoupon.couponID)>
							<table style="width:150px;border:0px;">
								<tr>
									<td><b>Total:</b></td>
									<td style="text-align:right;"><b>#DollarFormat(local.strCoupon.discountappliedtotal)#</b></td>
								</tr>
								<tr>
									<td></td>
									<td style="text-align:right;"><del>#DollarFormat(local.strCoupon.totalamt)#</del></td>
								</tr>
							</table>
							<div>#DollarFormat(local.strCoupon.discount)# discount applied</div>
							<div>#local.strCoupon.redeemDetail#</div>
						<cfelse>
							Total: <b>#dollarFormat(arguments.preTaxTotal)#</b>
						</cfif>
						<br/><br/>
						#arguments.subTreeVerbose#
					</td>
				</tr>
				</table>
				<br/><br/>
				#arguments.orgName#
				</cfoutput>
			</cfsavecontent>
			
			<cfset local.arrEmailTo = []>
			<cfset local.toEmailArr = listToArray(arguments.lstEmails,';')>
			<cfloop array="#local.toEmailArr#" item="local.thisEmail">
				<cfset ArrayAppend( local.arrEmailTo, { name:"", email:local.thisEmail })>
			</cfloop>

			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.orgName, email=arguments.networkEmailFrom},
				emailto=local.arrEmailTo,
				emailreplyto=arguments.supportProviderEmail,
				emailsubject="#arguments.qryRootSubscriber.subscriptionName# Notification",
				emailtitle="#arguments.sitename# Subscription Notification",
				emailhtmlcontent=local.emailBody,
				emailAttachments=[],
				siteID=local.mc_siteInfo.siteID,
				memberID=local.mc_siteInfo.sysMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SUBSNOTIFY"),
				sendingSiteResourceID=val(local.mc_siteInfo.subscriptionAdminSiteResourceID))>
		</cfif>		
	</cffunction>

	<cffunction name="getStateZipForTax" access="private" output="no" returntype="query">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="memberid" type="numeric" required="yes">

		<cfset var qryStateZipDetails = "">

		<cfquery name="qryStateZipDetails" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="CF_SQL_INTEGER">,
				@memberID int = <cfqueryparam value="#arguments.memberid#" cfsqltype="CF_SQL_INTEGER">

			SELECT isnull(ma.stateID,0) as stateIDForTax, isnull(ma.postalCode,'') as zipForTax
			FROM dbo.ams_members as m
			INNER JOIN dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
			LEFT OUTER JOIN dbo.ams_memberAddresses as ma 
				INNER JOIN dbo.ams_memberAddressTags as matag on matag.orgID = @orgID and matag.memberID = ma.memberID and matag.addressTypeID = ma.addressTypeID
				INNER JOIN dbo.ams_memberAddressTagTypes as matagt on matagt.orgID = @orgID and matagt.addressTagTypeID = matag.addressTagTypeID and matagt.addressTagType = 'Billing'
				ON ma.orgID = @orgID and ma.memberid = m2.memberID
			WHERE m.orgID = @orgID
			AND m.memberID = @memberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryStateZipDetails>
	</cffunction>

	<cffunction name="getPaymentGateways" access="private" output="no" returntype="query">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="invoiceID" type="numeric" required="yes">
		<cfargument name="useInvProfileProcFeeSettings" type="boolean" required="yes">

		<cfset var qryPaymentGateways = "">

		<!--- merchant profiles allowed --->
		<cfstoredproc procedure="tr_getMerchantProfilesForInvoice" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.invoiceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.useInvProfileProcFeeSettings#">
			<cfprocresult name="qryPaymentGateways" resultset="1">
		</cfstoredproc>

		<cfset qryPaymentGateways = QueryFilter(qryPaymentGateways, function(thisRow) { return NOT listFindNoCase("2,13,14", arguments.thisRow.gatewayID); })>

		<cfreturn qryPaymentGateways>
	</cffunction>

	<cffunction name="payDues" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.memberID = application.objMember.getActiveMemberID(memberID=int(val(arguments.event.getValue('mid',0))))>
		<cfset local.rootSubscriberID = int(val(arguments.event.getValue('rsid',0)))>

		<cfset local.qryInvoices = getSubscriberInvoices(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>

		<!--- If no payment is due redirect to confirmation page or message --->
		<cfif NOT local.qryInvoices.recordCount>
			<cfif application.mcCacheManager.sessionValueExists(keyname='showSubReceipt#local.rootSubscriberID#')>
				<cfset application.mcCacheManager.sessionDeleteValue(keyname='showSubReceipt#local.rootSubscriberID#')>
				<cfset local.strSubs = getNoPaymentSubDetailsForReceipt(event=arguments.event, memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>
				<cfset local.receiptKeyEnc = prepareReceipt(event=arguments.event, strSubs=local.strSubs)>
				<cflocation url="/?pg=manageSubscriptions&suba=showReceipt&rk=#local.receiptKeyEnc#" addtoken="false">
			<cfelse>
				<cflocation url="#this.link.message#&subM=3" addtoken="No">
			</cfif>
		</cfif>
		
		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.paymentGateways = getPaymentGateways(siteID=arguments.event.getValue('mc_siteinfo.siteID'), invoiceID=val(local.qryInvoices.invoiceID), useInvProfileProcFeeSettings=val(local.qryInvoices.invProfilesCount) EQ 1)>

		<cfset local.topDueDate = local.qryInvoices.dateDue[1]>
		<cfif not len(trim(local.topDueDate))>
			<cfset local.topDueDate = "01/01/1900">
		</cfif>

		<cfquery dbtype="query" name="local.qryTodayInvoices">
			select invoiceID, dateDue, sumRevTotal, sumTaxTotal, sumTotal, invoiceNumber, invoiceCode
			from [local].qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			order by invoiceID
		</cfquery>

		<cfquery dbtype="query" name="local.qryTodaySum">
			select sum(sumRevTotal) as totalRevAmt, sum(sumTaxTotal) as totalTaxAmt
			from [local].qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
		</cfquery>

		<cfquery dbtype="query" name="local.qryOtherInvoices">
			select dateDue, sum(sumRevTotal) as sumRevTotal, sum(sumTaxTotal) as sumTaxTotal, sum(sumTotal) as sumTotal
			from [local].qryInvoices
			where dateDue <> <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			group by dateDue
			order by dateDue
		</cfquery>

		<cfset local.amtDueToday = 0>
		<cfset local.amtDueTodayTax = 0>
		<cfset local.invoiceList = "">
		<cfset local.showPaymentArea = true>
		<cfset local.noPaymentStatement = "No payment is due.">

		<cfif local.qryTodayInvoices.recordCount>
			<cfif local.qryTodayInvoices.recordCount GT 1>
				<cfloop query="local.qryTodayInvoices">
					<cfif val(local.qryTodayInvoices.sumTotal) gt 0>
						<cfset local.amtDueToday += val(local.qryTodayInvoices.sumRevTotal)>
						<cfset local.amtDueTodayTax += val(local.qryTodayInvoices.sumTaxTotal)>
						<cfset local.invoiceList = listAppend(local.invoiceList, "#local.qryTodayInvoices.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryTodayInvoices.invoiceCode#")>
					</cfif>
				</cfloop>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.invoiceList#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.invoiceLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/invoices/#local.stInvEnc#'>
			<cfelse>
				<cfset local.amtDueToday += val(local.qryTodayInvoices.sumRevTotal)>
				<cfset local.amtDueTodayTax += val(local.qryTodayInvoices.sumTaxTotal)>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryTodayInvoices.invoicenumber#|#right(GetTickCount(),5)#|#local.qryTodayInvoices.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.invoiceLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/?pg=invoices&va=show&item=#local.stInvEnc#'>
			</cfif>
		<cfelse>
			<cfset local.showPaymentArea = false>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="/views/subscriptions/#arguments.event.getValue('viewDirectory')#/frm_payDues.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doPayDues" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.memberID = int(val(arguments.event.getValue('mid',0)));
			local.rootSubscriberID = int(val(arguments.event.getValue('rsid',0)));
			local.activeMemberID = application.objMember.getActiveMemberID(memberID=local.memberID);
			local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));
			
			// If NotLoggedIn
			if (NOT local.useMemberID)  {
				local.useMemberID = local.activeMemberID;
				application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.useMemberID);
			}
		</cfscript>
		
		<cfset local.strResponse = chargeAndAllocatePaymentToInvoices(event=arguments.event, memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>
		<cfif local.strResponse.success>
			<cfset application.mcCacheManager.sessionDeleteValue(keyname='showSubReceipt#local.rootSubscriberID#')>
			<cfset local.receiptKeyEnc = prepareReceipt(event=arguments.event, strSubs=local.strResponse)>
			<cflocation url="/?pg=manageSubscriptions&suba=showReceipt&rk=#local.receiptKeyEnc#" addtoken="false">
		<cfelse>
			<cflocation url="/?pg=manageSubscriptions&suba=payDues&rsid=#local.rootSubscriberID#&mid=#local.memberID#&perr=#urlEncodedFormat(local.strResponse.response)#" addtoken="false">
		</cfif>
	</cffunction>

	<cffunction name="chargeAndAllocatePaymentToInvoices" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objAccounting = CreateObject("component","model.system.platform.accounting")>
		<cfset local.strResponse = { "success"=false, "response"='' }>

		<cfset local.memberID = int(val(arguments.memberID))>
		<cfset local.rootSubscriberID = int(val(arguments.rootSubscriberID))>
		<cfset local.activeMemberID = application.objMember.getActiveMemberID(memberID=local.memberID)>

		<cfset local.qryMemberSubscriptions = getMemberSubscriptions(memberID=local.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), rootSubscriberID=local.rootSubscriberID)>

		<cfif NOT local.qryMemberSubscriptions.recordCount>
			<cfset local.strResponse.response = "Invalid Subscriber.">
			<cfreturn local.strResponse>
		</cfif>

		<cfset local.qryInvoices = getSubscriberInvoices(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>

		<cfif NOT local.qryInvoices.recordCount>
			<cfset local.strResponse.response = "No payment is due.">
			<cfreturn local.strResponse>
		</cfif>

		<cfquery name="local.qryStateZipForTax" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@invoiceID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryInvoices.invoiceID[1]#">;

			SELECT TOP 1 ts.stateIDForTax, ts.zipForTax
			FROM dbo.tr_invoiceTransactions AS it
			INNER JOIN dbo.tr_transactionSales AS ts ON ts.orgID = @orgID
				AND ts.transactionID = it.transactionID
			WHERE it.invoiceID = @invoiceID
			AND it.orgID = @orgID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.topDueDate = local.qryInvoices.dateDue[1]>
		<cfif not len(trim(local.topDueDate))>
			<cfset local.topDueDate = "01/01/1900">
		</cfif>

		<cfquery dbtype="query" name="local.qryTodayInvoices">
			select invoiceID, dateDue, sumRevTotal, sumTaxTotal, sumTotal, invoiceNumber, invoiceCode
			from [local].qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			order by invoiceID
		</cfquery>

		<cfquery dbtype="query" name="local.qryTodaySum">
			select sum(sumRevTotal) as totalRevAmt, sum(sumTaxTotal) as totalTaxAmt
			from [local].qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
		</cfquery>

		<cfquery dbtype="query" name="local.qryOtherInvoices">
			select dateDue, sum(sumRevTotal) as sumRevTotal, sum(sumTaxTotal) as sumTaxTotal, sum(sumTotal) as sumTotal
			from [local].qryInvoices
			where dateDue <> <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			group by dateDue
			order by dateDue
		</cfquery>

		<cfset local.amtDueToday = 0>
		<cfset local.amtDueTodayTax = 0>
		<cfset local.invoiceList = "">
		<cfset local.hasDues = true>

		<cfif local.qryTodayInvoices.recordCount>
			<cfif local.qryTodayInvoices.recordCount GT 1>
				<cfloop query="local.qryTodayInvoices">
					<cfif val(local.qryTodayInvoices.sumTotal) gt 0>
						<cfset local.amtDueToday += val(local.qryTodayInvoices.sumRevTotal)>
						<cfset local.amtDueTodayTax += val(local.qryTodayInvoices.sumTaxTotal)>
						<cfset local.invoiceList = listAppend(local.invoiceList, "#local.qryTodayInvoices.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryTodayInvoices.invoiceCode#")>
					</cfif>
				</cfloop>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.invoiceList#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.invoiceLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/invoices/#local.stInvEnc#'>
			<cfelse>
				<cfset local.amtDueToday += val(local.qryTodayInvoices.sumRevTotal)>
				<cfset local.amtDueTodayTax += val(local.qryTodayInvoices.sumTaxTotal)>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryTodayInvoices.invoicenumber#|#right(GetTickCount(),5)#|#local.qryTodayInvoices.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.invoiceLink = '#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).scheme#://#application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).mainhostname#/?pg=invoices&va=show&item=#local.stInvEnc#'>
			</cfif>
		<cfelse>
			<cfset local.hasDues = false>
		</cfif>

		<cfif NOT local.hasDues>
			<cfset local.strResponse.response = "No payment is due.">
			<cfreturn local.strResponse>
		</cfif>

		<cfset local.paymentGateways = getPaymentGateways(siteID=arguments.event.getValue('mc_siteinfo.siteID'), invoiceID=val(local.qryInvoices.invoiceID), useInvProfileProcFeeSettings=val(local.qryInvoices.invProfilesCount) EQ 1)>
		
		<!--- determine payment profileID and profileCode --->
		<cfset arguments.event.paramValue('profileid',0)>
		<cfquery name="local.qryMerchantProfile" dbtype="query">
			SELECT profileid, profileCode, gatewayid, gatewayType, tabTitle, enableProcessingFeeDonation, processFeeDonationFeePercent, 
				processFeeDonationRenevueGLAccountID, processFeeDonationRevTransDesc, processingFeeLabel, 
				enableSurcharge, surchargePercent, surchargeRevenueGLAccountID
			FROM [local].paymentGateways
			WHERE profileid = <cfqueryparam value="#arguments.event.getValue('profileid')#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfset local.transactionID = 0>
		<cfset local.totalAmountToCharge = local.amtDueToday + local.amtDueTodayTax>

		<!--- prepare fields for gateway and send --->
		<cfif val(local.totalAmountToCharge) gt 0>

			<!--- get fields --->
			<cfset local.qryGatewayProfileFields = application.objPayments.getGatewayProfileFields(siteid=arguments.event.getValue('mc_siteinfo.siteid'), profilecode=local.qryMerchantProfile.profilecode)>
			<cfset local.tmpFields = structNew()>
			<cfloop query="local.qryGatewayProfileFields">
				<cfset structInsert(local.tmpFields,'fld_#local.qryGatewayProfileFields.fieldid#_',arguments.event.getTrimValue('p_#local.qryMerchantProfile.profileID#_fld_#local.qryGatewayProfileFields.fieldid#_',''))>
			</cfloop>

			<!--- get info on file if applicable --->
			<cfset arguments.event.setValue('p_#local.qryMerchantProfile.profileID#_mppid',int(val(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0))))>
			<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') gt 0>
				<cfset local.qrySavedInfoOnFile = application.objPayments.getSavedInfoOnFile(mppid=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid'), memberID=local.activeMemberID, profileID=local.qryMerchantProfile.profileID)>
				<cfset structInsert(local.tmpFields,'qryInfoOnFile',local.qrySavedInfoOnFile)>
			</cfif>

			<!--- Surcharge / Processing Fee Donation --->
			<cfset local.additionalFeesInfo = application.objPayments.getAdditionalFeesInfo(qryMerchantProfile=local.qryMerchantProfile, amt=local.totalAmountToCharge, 
				stateIDForTax=val(local.qryStateZipForTax.stateIDForTax), zipForTax=local.qryStateZipForTax.zipForTax,
				processingFeeOpted=arguments.event.getValue('processFeeDonation#arguments.event.getValue('profileid')#',0) EQ 1,
				surchargeEligibleCard=arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid') GT 0 AND local.qrySavedInfoOnFile.surchargeEligible EQ 1)>

			<!--- failed --->
			<cfif NOT local.additionalFeesInfo.success>
				<cfset local.strResponse.response = "Unable to get additional payment fees info.">
				<cfreturn local.strResponse>
			</cfif>

			<cfset local.totalAmountToCharge = local.additionalFeesInfo.finalAmountToCharge>

			<!--- prepare fields for gateway and send --->
			<cfset local.strTemp = { orgID=arguments.event.getValue('mc_siteinfo.orgID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), 
									 profileCode=local.qryMerchantProfile.profileCode, assignedToMemberID=local.activeMemberID, recordedByMemberID=local.activeMemberID, 
									 statsSessionID=val(session.cfcUser.statsSessionID), x_amount=local.totalAmountToCharge, 
									 x_description='#arguments.event.getValue('mc_siteinfo.sitename')# #local.qryMemberSubscriptions.subscriptionName[1]#', 
									 offeredPaymentFee=local.additionalFeesInfo.offeredPaymentFee }>
			
			<!--- apple or google pay token --->
			<cfif arguments.event.valueExists('p_#local.qryMerchantProfile.profileID#_tokenData') AND len(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
				<cfset local.strTemp["tokenData"] = deSerializeJSON(arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_tokenData'))>
			</cfif>

			<cfset structAppend(local.strTemp,local.tmpFields)>
			<cfif listFindNoCase("AuthorizeCCCIM",local.qryMerchantProfile.gatewayType)>
				<cfset local.qryLevel3Data = QueryNew("name,desc,itemPriceExcDiscount,itemPriceIncDiscount,discount,qty,total","varchar,varchar,decimal,decimal,decimal,decimal,decimal")>
				<cfset local.thisItemPrice = precisionEvaluate((local.totalAmountToCharge - local.additionalFeesInfo.additionalFees))>
				<cfset QueryAddRow(local.qryLevel3Data, {
					"name": "Subscription Payment",
					"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.qryMemberSubscriptions.subscriptionName[1]#",
					"itemPriceExcDiscount": local.thisItemPrice,
					"itemPriceIncDiscount": local.thisItemPrice,
					"discount": 0,
					"qty": 1,
					"total": local.thisItemPrice
				})>
				<cfif local.additionalFeesInfo.additionalFees GT 0>
					<cfset QueryAddRow(local.qryLevel3Data, {
						"name": "#local.additionalFeesInfo.additionalFeesLabel#",
						"desc": "#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#",
						"itemPriceExcDiscount": local.additionalFeesInfo.additionalFees,
						"itemPriceIncDiscount": local.additionalFeesInfo.additionalFees,
						"discount": 0,
						"qty": 1,
						"total": local.additionalFeesInfo.additionalFees
					})>
					<!--- Surcharge --->
					<cfif local.additionalFeesInfo.paymentFeeTypeID EQ 2>
						<cfset local.strTemp['x_surcharge'] = { "amount":local.additionalFeesInfo.additionalFees, "description":"#arguments.event.getValue('mc_siteinfo.sitename')# #local.additionalFeesInfo.additionalFeesLabel#" }>
					</cfif>
				</cfif>
				<cfset local.strTemp["x_items"] = application.objPayments.getLevel3Data(qryLevel3Data=local.qryLevel3Data, gatewayType=local.qryMerchantProfile.gatewayType)>
			</cfif>
			<cfinvoke component="#application.objPayments#" method="chargeAdHoc" argumentcollection="#local.strTemp#" returnvariable="local.paymentResponse">

			<!--- if payment not successful --->
			<cfif local.paymentResponse.responseCode is not 1>
				<cfset local.strResponse.response = local.paymentResponse.publicResponseReasonText>
				<cfreturn local.strResponse>
			</cfif>

			<!--- Record Surcharge / Processing Fee Donation --->
			<cfif local.additionalFeesInfo.additionalFees GT 0 AND local.paymentResponse.keyExists("mc_transactionID") AND val(local.paymentResponse.mc_transactionID)>
				<cfset local.strRecordAdditionalPmtFees = local.objAccounting.recordAdditionalPaymentFees(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
					siteID=arguments.event.getValue('mc_siteinfo.siteID'), assignedToMemberID=local.activeMemberID, recordedByMemberID=local.activeMemberID, 
					statsSessionID=val(session.cfcuser.statsSessionID), paymentTransactionID=local.paymentResponse.mc_transactionID, 
					GLAccountID=local.additionalFeesInfo.gl, qryAdditionalFees=local.additionalFeesInfo.qryAdditionalFees, 
					paymentFeeTypeID=local.additionalFeesInfo.paymentFeeTypeID)>
				
				<!--- if not successful --->
				<cfif NOT local.strRecordAdditionalPmtFees.success>
					<cfset local.strResponse.response = "Unable to record additional payment fees.">
					<cfreturn local.strResponse>
				</cfif>

				<cfset local.additionalPaymentFeeInvoiceID = local.strRecordAdditionalPmtFees.invoiceID>
			</cfif>

			<!--- allocate payment --->
			<cftry>
				<cftransaction>
	
					<!--- close invoice --->
					<cfloop query="local.qryTodayInvoices">
						<cfset local.strAccInv = local.objAccounting.closeInvoice(invoiceID=local.qryTodayInvoices.invoiceid)>
						<cfif local.strAccInv.rc is not 0>
							<cfthrow message="Failed to close invoice">
						</cfif>
					</cfloop>
					
					<cfif val(local.totalAmountToCharge) gt 0 and local.paymentResponse.mc_transactionID gt 0>
						<cfset local.transactionID = local.paymentResponse.mc_transactionID>
		
						<cfloop query="local.qryTodayInvoices">
							<cfset local.strACCTemp = { recordedOnSiteID=arguments.event.getValue('mc_siteinfo.siteID'), recordedByMemberID=local.activeMemberID, 
														statsSessionID=val(session.cfcUser.statsSessionID), amount=val(local.qryTodayInvoices.sumTotal),
														transactionDate=now(), paymentTransactionID=local.transactionID, invoiceID=local.qryTodayInvoices.invoiceid }>
							<cfset local.strACCAllocate = local.objAccounting.allocateToInvoice(argumentcollection=local.strACCTemp)>
							<cfif local.strACCAllocate.rc is not 0>
								<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.strACCTemp)>
								<cfthrow message="Failed to allocate payment to invoice">
							</cfif>
						</cfloop>
					</cfif>
	
				</cftransaction>
			<cfcatch type="any">
				<cftransaction action="rollback" />
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.strResponse.response = cfcatch.message>
				<cfreturn local.strResponse>
			</cfcatch>
			</cftry>

			<cfif arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0) is 0>
				<cfif StructKeyExists(local, "qrySavedInfoOnFile")>
					<cfset local.usepayProfileID = val(local.qrySavedInfoOnFile.payProfileID)>
				<cfelse>
					<cfset local.usepayProfileID = 0>
				</cfif>
			<cfelse>
				<cfset local.usepayProfileID = arguments.event.getValue('p_#local.qryMerchantProfile.profileID#_mppid',0)>
			</cfif>

			<cfif local.usepayProfileID gt 0>
				<cfquery name="local.qryUpdateCPPayProfile" datasource="#application.dsn.membercentral.dsn#">
					EXEC dbo.sub_updatePaymentProfile
						@siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
						@subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">,
						@MPProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMerchantProfile.profileID#">,
						@payProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.usepayProfileID#">,
						<cfif local.keyExists("payProcessFee")>
							@payProcessFee = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.payProcessFee#">,
						<cfelse>
							@payProcessFee = 0,
						</cfif>
						@retainCurrentFeePercent = 0,
						@recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.activeMemberID#">;
				</cfquery>
			</cfif>

			<cfif local.additionalFeesInfo.keyExists("qryAdditionalFees") AND local.keyExists("additionalPaymentFeeInvoiceID")>
				<cfset local.strResponse.qryAdditionalFees = local.additionalFeesInfo.qryAdditionalFees>

				<cfquery name="local.strResponse.qryAdditionalPaymentFeeInvoice" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
					
					select i.invoiceID, i.invoiceCode, i.fullInvoiceNumber as invoiceNumber
					from dbo.tr_invoices as i
					inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
					inner join dbo.organizations as o on o.orgID = m.orgID
					where i.invoiceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.additionalPaymentFeeInvoiceID#">;
	
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>
		</cfif>

		<cfquery name="local.strResponse.qryPaymentGateway" datasource="#application.dsn.membercentral.dsn#">
			SELECT ga.gatewayID, mpContent.rawContent as paymentInstructions
			FROM dbo.mp_profiles as pr
			INNER JOIN dbo.mp_gateways as ga on ga.gatewayid = pr.gatewayID
			OUTER APPLY dbo.fn_getContent(pr.paymentInstructionsContentID,1) as mpContent
			WHERE ga.gatewayID = <cfqueryparam value="#val(local.qryMerchantProfile.gatewayID)#" cfsqltype="cf_sql_integer">
			AND pr.profileID = <cfqueryparam value="#val(local.qryMerchantProfile.profileID)#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfquery name="local.strResponse.qryPaymentTransaction" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;

			select t.detail, t.transactionDate, t.amount, ph.gatewayID, ph.gatewayTransactionID, ph.gatewayApprovalCode
			from dbo.tr_transactions as t
			inner join dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = t.transactionID
			inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tp.historyID
			where t.ownedByOrgID = @orgID 
			and t.transactionID = <cfqueryparam value="#local.transactionID#" cfsqltype="CF_SQL_INTEGER">
			and t.typeID = 2
			and t.statusID not in (2,4);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.strResponse['memberID'] = local.memberID>
		<cfset local.strResponse['rootSubscriberID'] = local.rootSubscriberID>
		<cfset local.strResponse['qryInvoices'] = local.qryInvoices>
		<cfset local.strResponse.success = true>
		
		<cfreturn local.strResponse>
	</cffunction>

	<cffunction name="getNoPaymentSubDetailsForReceipt" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfscript>
			var local = structNew();
			local.memberID = int(val(arguments.event.getValue('mid',0)));
			local.rootSubscriberID = int(val(arguments.event.getValue('rsid',0)));
			local.activeMemberID = application.objMember.getActiveMemberID(memberID=local.memberID);
			local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));
			
			// If NotLoggedIn
			if (NOT local.useMemberID)  {
				local.useMemberID = local.activeMemberID;
				application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=local.useMemberID);
			}
	
			local.returnStruct = {};
		</cfscript>
	
		<cfset local.qryInvoices = getSubscriberPaidInvoices(orgID=arguments.event.getValue('mc_siteInfo.orgID'), memberID=local.memberID, rootSubscriberID=local.rootSubscriberID)>
	
		<cfif NOT local.qryInvoices.recordCount>
			<cfthrow message="Invalid Subscriber">
		</cfif>
	
		<cfset local.strResponse['memberID'] = local.memberID>
		<cfset local.strResponse['rootSubscriberID'] = local.rootSubscriberID>
		<cfset local.strResponse['qryInvoices'] = local.qryInvoices>
		<cfset local.strResponse['qryPaymentGateway'] = QueryNew("gatewayID,paymentInstructions","integer,varchar")>
		<cfset local.strResponse['qryPaymentTransaction'] = QueryNew("detail,transactionDate,amount,gatewayID,gatewayTransactionID,gatewayApprovalCode","varchar,date,decimal,integer,integer,varchar")>
		<cfset local.strResponse.success = true>
		
		<cfreturn local.strResponse>
	</cffunction>

	<!--- receipt --->
	<cffunction name="prepareReceipt" access="private" output="false" returntype="string">
		<cfargument name="event" type="any" required="true">
		<cfargument name="strSubs" type="struct" required="true">

		<cfscript>
			var local = structNew();
			local.useMemberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteinfo.orgID'));
			local.strSubs = duplicate(arguments.strSubs);
			local.qryMemberSubscriptions = getMemberSubscriptions(memberID=local.strSubs.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'), rootSubscriberID=local.strSubs.rootSubscriberID);
		</cfscript>

		<!--- Get Purchaser Info --->
		<cfset local.qryPurchaser = application.objMember.getMemberInfo(memberID=local.useMemberID)>
		<cfset local.qryPurchaserAddr = application.objMember.getMemberAddressByBillingAddressType(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.qryPurchaser.memberID)>
		<cfset local.qryMainEmail = application.objMember.getMainEmail(memberID=local.qryPurchaser.memberID)>
		
		<cfset local.PurchaserMemberID = local.qryPurchaser.memberID>
		<cfset local.PurchaserName = "#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#">
		<cfset local.PurchaserCompany = local.qryPurchaser.company>
		<cfset local.PurchaserEmail = local.qryMainEmail.email>

		<cfsavecontent variable="local.PurchaserAddress">
			<cfoutput>
			<cfif len(local.qryPurchaserAddr.address1)>#local.qryPurchaserAddr.address1#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress2 is 1 and len(local.qryPurchaserAddr.address2)>#local.qryPurchaserAddr.address2#<br/></cfif>
			<cfif local.qryPurchaserAddr.hasAddress3 is 1 and len(local.qryPurchaserAddr.address3)>#local.qryPurchaserAddr.address3#<br/></cfif>
			#local.qryPurchaserAddr.city# #local.qryPurchaserAddr.stateCode# #local.qryPurchaserAddr.postalCode#
			</cfoutput>
		</cfsavecontent>

		<cfset local.memberID = local.strSubs.memberID>
		<cfset local.rootSubscriberID = local.strSubs.rootSubscriberID>
		
		<cfset local.displayedCurrencyType = application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).showCurrencyType is 1 
												? " #application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultCurrencyType#"
												: "">

		<cfset local.subsAppSettingsXML = arguments.event.getValue('instanceSettings.settingsXML')>
		<cfset local.freeRateDisplay = xmlSearch(local.subsAppSettingsXML,'string(/settings/setting[@name="freeRateDisplay"]/@value)')>
		<cfset local.topSubscriptionContent = getTopSubscriptionCompletedContent(memberID=local.memberID, rootSubscriberID=local.rootSubscriberID, topSubscriptionID=local.qryMemberSubscriptions.subscriptionID[1])>
		<cfset local.qryMember = application.objMember.getMemberInfo(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteInfo.orgid'))>
		<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.defaultOrgIdentityID'))>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberTransactions">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int, @orgID int, @rootSubscriberID int;
			set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgid')#">;
			set @rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int INDEX IDX_mcSubscriberTransactions_subscriberID, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int);

			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			select subscriberID
			FROM dbo.sub_subscribers
			WHERE orgID = @orgID
			AND rootSubscriberID = @rootSubscriberID;

			EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT s.subscriberID, s.subscriptionID, s.subStartDate, s.subEndDate, s.GLAccountID, s.RFID, s.parentSubscriberID, s.PCFree,
				s.modifiedRate, s.lastPrice, CASE WHEN s.modifiedRate IS NULL THEN 0 ELSE 1 END AS isModifiedRate, s.subscriberPath, 
				st.invoiceID, st.dateDue, st.invoiceProfileID, st.transactionID, st.amount, st.amountDue, st.amountToConsider
			FROM dbo.sub_subscribers as s
			inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
			WHERE s.orgID = @orgID
			AND s.rootSubscriberID = @rootSubscriberID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryTotalAmount" dbtype="query">
			SELECT SUM(amount) AS totalAmount
			FROM [local].qrySubscriberTransactions
		</cfquery>
		<cfset local.totalAmount = val(local.qryTotalAmount.totalAmount)>

		<cfquery name="local.qryRootSubscriber" dbtype="query">
			SELECT DISTINCT subscriberID, subscriptionID, subscriptionName, status, RFID, GLAccountID,
				subStartDate, subEndDate, graceEndDate, modifiedRate, isModifiedRate, lastPrice, rateID, rateName,
				isRenewalRate, frequencyName, frequencyShortName, forceUpfront, thePath
			FROM [local].qryMemberSubscriptions
			WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">
		</cfquery>

		<cfquery name="local.qryAddOnSubs" dbtype="query">
			SELECT DISTINCT subscriberID, subscriptionID, subscriptionName, status, RFID, GLAccountID,
				subStartDate, subEndDate, graceEndDate, modifiedRate, isModifiedRate, lastPrice, rateID, rateName,
				frequencyName, frequencyShortName, forceUpfront, thePath
			FROM [local].qryMemberSubscriptions
			WHERE subscriberID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">
		</cfquery>

		<cfset local.topDueDate = local.strSubs.qryInvoices.dateDue[1]>
		<cfif not len(trim(local.topDueDate))>
			<cfset local.topDueDate = "01/01/1900">
		</cfif>

		<cfquery dbtype="query" name="local.qryTodayInvoices">
			select invoiceID, dateDue, sumRevTotal, sumTaxTotal, sumTotal, invoiceNumber, invoiceCode
			from [local].strSubs.qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			order by invoiceID
		</cfquery>

		<cfquery dbtype="query" name="local.qryTodaySum">
			select sum(sumTotal) as totalAmt
			from [local].strSubs.qryInvoices
			where dateDue = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
		</cfquery>

		<cfquery dbtype="query" name="local.qryOtherInvoices">
			select dateDue, sum(sumRevTotal) as sumRevTotal, sum(sumTaxTotal) as sumTaxTotal, sum(sumTotal) as sumTotal
			from [local].strSubs.qryInvoices
			where dateDue <> <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.topDueDate#">
			group by dateDue
			order by dateDue
		</cfquery>

		<cfset local.qryAllAddOnSubs = getRecursiveAddOnSubs(siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID, 
										rootSubscriberID=local.rootSubscriberID, isRenewalRate=local.qryRootSubscriber.isRenewalRate, 
										topSubscriptionID=local.qryRootSubscriber.subscriptionID)>

		<cfquery name="local.qrySelectedAddOnSubs" dbtype="query">
			SELECT subscriptionID, subscriptionName, addonID, setID, setName, addOnSubscriptionID, frontEndAllowSelect, thePath, recursionLevel
			FROM [local].qryAllAddOnSubs
			WHERE subscriptionID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#valueList(local.qryAddOnSubs.subscriptionID)#">)
			ORDER BY thePath
		</cfquery>

		<!--- store receipt in session so it can be sent and resent --->
		<cfset local.resendKey = createUUID()>
		<cfset local.tmpStr = { 
			"memberID": local.memberID,
			"rootSubscriberID": local.rootSubscriberID,
			"displayedCurrencyType": local.displayedCurrencyType,
			"freeRateDisplay": local.freeRateDisplay,
			"topSubscriptionContent": local.topSubscriptionContent,
			"qryMember": local.qryMember,
			"totalAmount": local.totalAmount,
			"qryRootSubscriber": local.qryRootSubscriber,
			"qryAddOnSubs": local.qryAddOnSubs,
			"topDueDate": local.topDueDate,
			"qrySubscriberTransactions": local.qrySubscriberTransactions,
			"qryTodayInvoices": local.qryTodayInvoices,
			"qryTodaySum": local.qryTodaySum,
			"qryOtherInvoices": local.qryOtherInvoices,
			"qryAllAddOnSubs": local.qryAllAddOnSubs,
			"qrySelectedAddOnSubs": local.qrySelectedAddOnSubs,
			"strSubs": local.strSubs,
			"qryMemberSubscriptions": local.qryMemberSubscriptions,
			"purchaserMemberID"=local.purchaserMemberID,
			"purchaserName"=local.PurchaserName,
			"purchaserCompany"=local.PurchaserCompany,
			"purchaserAddress"=local.PurchaserAddress,
			"PurchaserEmail": local.PurchaserEmail
		}>

		<cfset local.strSubReceipt = { "#local.resendKey#": duplicate(local.tmpStr) }>
		<cfset application.mcCacheManager.sessionSetValue(keyname='strSubReceipt', value=local.strSubReceipt)>

		<cfif len(local.PurchaserEmail)>
			<cfsavecontent variable="local.receiptData">
				<cfinclude template="/views/subscriptions/subDuesReceiptForEmail.cfm">
			</cfsavecontent>

			<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=local.qryOrgIdentity.organizationName, email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
				emailto=[{ name="#local.qryPurchaser.firstname# #local.qryPurchaser.lastname#", email=local.PurchaserEmail }],
				emailreplyto=local.qryOrgIdentity.email,
				emailsubject="#local.qryRootSubscriber.subscriptionName# Payment Receipt",
				emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# Payment Receipt",
				emailhtmlcontent=local.receiptData,
				emailAttachments=[],
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=local.useMemberID,
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SUBSPAY"),
				sendingSiteResourceID=val(arguments.event.getValue('mc_siteInfo.subscriptionsSiteResourceID'))
			)>
		</cfif>

		<!--- clear remembered payer when not logged in --->
		<cfset application.objUser.setIdentifiedMemberIDfromID(cfcuser=session.cfcuser, memberID=0)>

		<!--- return encrypted receipt uid --->
		<cfreturn Replace(URLEncodedFormat(ToBase64(Encrypt(local.resendKey,"M3m18eR_CenTR@l"))),"%","xPcmKx","ALL")>
	</cffunction>

	<cffunction name="showReceipt" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.receiptUUID = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('rk',''),"xPcmKx","%","ALL")))),"M3m18eR_CenTR@l")>
		<cfset local.strSubReceipt = application.mcCacheManager.sessionGetValue(keyname='strSubReceipt', defaultValue={})>
		
		<cfif len(local.receiptUUID) AND structKeyExists(local.strSubReceipt,local.receiptUUID)>
			<cfset structAppend(local, duplicate(local.strSubReceipt[local.receiptUUID]))>
			
			<cfsavecontent variable="local.data">
				<cfinclude template="/views/subscriptions/#arguments.event.getValue('viewDirectory','default')#/subDuesPaymentReceipt.cfm">
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<h4>Unable to display confirmation.</h4>
					<div class="alert">The confirmation you are trying to view is invalid or has expired.</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="downloadReceipt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.receiptUUID = decrypt(toString(toBinary(URLDecode(replace(arguments.event.getTrimValue('rk',''),"xPcmKx","%","ALL")))),"M3m18eR_CenTR@l")>
		<cfset local.strSubReceipt = application.mcCacheManager.sessionGetValue(keyname='strSubReceipt', defaultValue={})>
		
		<cfif len(local.receiptUUID) AND structKeyExists(local.strSubReceipt,local.receiptUUID)>
			<cfset structAppend(local, duplicate(local.strSubReceipt[local.receiptUUID]))>

			<cfset local.qryOrgIdentity = application.objOrgInfo.getOrgIdentity(orgIdentityID=arguments.event.getValue('mc_siteInfo.defaultOrgIdentityID'))>

			<cfquery name="local.qryRootSubInvProfileID" dbtype="query">
				SELECT TOP 1 invoiceProfileID
				FROM [local].qrySubscriberTransactions
				WHERE subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rootSubscriberID#">
			</cfquery>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryInvoiceProfile">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT profileID, imageExt
				FROM dbo.tr_invoiceProfiles
				WHERE profileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRootSubInvProfileID.invoiceProfileID)#">

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfsavecontent variable="local.receiptData">
				<cfoutput>
					<html>
					<head>
						<style type="text/css">
							<cfinclude template="/assets/admin/css/pdfstylesheet.css">
						</style>
					</head>
					<body>
						<cfinclude template="/views/subscriptions/subDuesPaymentReceiptPDF.cfm">
					</body>
					</html>
				</cfoutput>
			</cfsavecontent>

			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.siteCode'))>
			<cfset local.reportFileName = "#rereplaceNoCase("#local.qryMember.lastName#_#local.qryMember.firstName#_#local.qryMember.memberNumber#",'[^A-Z0-9_]','','ALL')#.pdf">

			<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
				<cfoutput>
				<cfdocumentsection>
					#local.receiptData#
				</cfdocumentsection>
				</cfoutput>
			</cfdocument>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script type="text/javascript">doDownloadPaymentReceipt('#local.stDownloadURL#');</script>
				</cfoutput>
			</cfsavecontent>

		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<script type="text/javascript">alert('The payment receipt pdf you are trying to view is invalid or has expired.');</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getSubscriberInvoices" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">

		<cfset var qryInvoices = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInvoices">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;

			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int, 
				INDEX IDX_mcSubscriberTransactions_subscriberID_invoiceID (subscriberID, invoiceID));

			DECLARE @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">,
				@invProfilesCount int;

			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			SELECT subscriberID
			FROM dbo.sub_subscribers
			WHERE orgID = @orgID
			AND rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;

			EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;

			SELECT @invProfilesCount = COUNT(DISTINCT invoiceProfileID)
			FROM ##mcSubscriberTransactions;

			select i.invoiceID, i.dateDue, inv.sumRevTotal, inv.sumTaxTotal, inv.sumRevTotal + inv.sumTaxTotal as sumTotal, 
				i.fullInvoiceNumber as invoiceNumber, i.invoiceCode, @invProfilesCount as invProfilesCount
			from (
				select st.invoiceID, sum(case when st.typeID <> 7 then st.amount else 0 end) as sumRevTotal, sum(case when st.typeID = 7 then st.amount else 0 end) as sumTaxTotal
				from dbo.sub_subscribers as s
				inner join ##mcSubscriberTransactions as st on st.subscriberID = s.subscriberID
				inner join dbo.tr_invoiceStatuses as ins on ins.statusID = st.invoiceStatusID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = s.memberID
				where s.orgID = @orgID
				and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				and ins.status in ('Pending','Closed','Delinquent')
				group by st.invoiceID
				having sum(case when st.typeID <> 7 then st.amount else 0 end) > 0
			) as inv			
			inner join dbo.tr_invoices as i on i.invoiceID = inv.invoiceID
			inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			order by i.dateDue, i.invoiceID;

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
		</cfquery>

		<cfreturn qryInvoices>
	</cffunction>

	<cffunction name="getSubscriberPaidInvoices" access="private" output="false" returntype="query">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
	
		<cfset var qryInvoices = "">
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryInvoices">
			SET NOCOUNT ON;
	
			declare @orgID int = <cfqueryparam value="#arguments.orgID#" cfsqltype="cf_sql_integer">;
			declare @paidInvoiceStatusID int;
	
			select @paidInvoiceStatusID = statusID
			from dbo.tr_invoiceStatuses
			where status = 'Paid';

			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
	
			CREATE TABLE ##mcSubscribersForAcct (subscriberID int PRIMARY KEY);
			CREATE TABLE ##mcSubscriberTransactions (subscriberID int, invoiceID int, dateDue date, invoiceStatusID int, payProfileID int,
				invoiceProfileID int, mainTransactionID int, transactionID int, statusID int, detail varchar(500), parentTransactionID int,
				amount decimal(18,2), amountDue decimal(18,2), amountToConsider decimal(18,2), dateRecorded datetime, transactionDate datetime, 
				assignedToMemberID int, recordedByMemberID int, statsSessionID int, typeID int, debitGLAccountID int, creditGLAccountID int, 
				INDEX IDX_mcSubscriberTransactions_invoiceStatusID_subscriberID_invoiceID (invoiceStatusID, subscriberID, invoiceID));			
	
			INSERT INTO ##mcSubscribersForAcct (subscriberID)
			SELECT subscriberID
			FROM dbo.sub_subscribers
			WHERE orgID = @orgID
			AND rootSubscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.rootSubscriberID#">;
	
			EXEC dbo.sub_getSubscriberTransactions @orgID=@orgID;
	
			select i.invoiceID, i.dateDue, inv.sumRevTotal, inv.sumTaxTotal, inv.sumRevTotal + inv.sumTaxTotal as sumTotal, 
				i.fullInvoiceNumber as invoiceNumber, i.invoiceCode
			from (
				select st.invoiceID, sum(case when st.typeID <> 7 then st.amount else 0 end) as sumRevTotal, sum(case when st.typeID = 7 then st.amount else 0 end) as sumTaxTotal
				from dbo.sub_subscribers as s
				inner join ##mcSubscriberTransactions as st on st.invoiceStatusID = @paidInvoiceStatusID
					and st.subscriberID = s.subscriberID
				inner join dbo.ams_members as m on m.orgID = @orgID 
					and m.memberID = s.memberID
				where s.orgID = @orgID
				and m.activeMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
				group by st.invoiceID
				having sum(case when st.typeID <> 7 then st.amount else 0 end) >= 0
			) as inv			
			inner join dbo.tr_invoices as i on i.invoiceID = inv.invoiceID
			inner join dbo.ams_members as m on m.memberID = i.assignedToMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			order by i.dateDue, i.invoiceID;
	
			IF OBJECT_ID('tempdb..##mcSubscribersForAcct') IS NOT NULL
				DROP TABLE ##mcSubscribersForAcct;
			IF OBJECT_ID('tempdb..##mcSubscriberTransactions') IS NOT NULL
				DROP TABLE ##mcSubscriberTransactions;
		</cfquery>
	
		<cfreturn qryInvoices>
	</cffunction>

	<cffunction name="renderConfirmationPrice" access="private" output="false" returntype="string">
		<cfargument name="amount" type="numeric" required="true">
		<cfargument name="displayedCurrencyType" type="string" required="true">
		<cfargument name="freqName" type="string" required="true">
		<cfset var amountForDisplay = "#DollarFormat(arguments.amount)##arguments.displayedCurrencyType#">
		<cfif arguments.freqName.len() AND arguments.amount GT 0>
			<cfset amountForDisplay = "#amountForDisplay# #arguments.freqName#">
		</cfif>
		<cfreturn amountForDisplay>
	</cffunction>

	<!--- Coupon Functions --->
	<cffunction name="subCouponHandler" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="subs" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.data = { "success":false, "offercoupon":false, "couponapplied":false }>
		
		<cftry>
			<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname='subCoupon#arguments.rootSubscriberID#', defaultValue={})>
			<cfset local.arrSubs = deserializeJSON(arguments.subs)>

			<!--- coupon applied --->
			<cfif structCount(local.strCoupon) AND val(local.strCoupon.couponID)>
				<cfset local.encSubs = hash(serializeJSON(local.arrSubs), "SHA", "UTF-8")>

				<!--- re-apply coupon to subs --->
				<cfif local.encSubs NEQ local.strCoupon.encsubs>
					<cfset local.applyResult = validateCouponCode(mcproxy_siteID=arguments.mcproxy_siteID, couponCode=local.strCoupon.couponCode, memberID=arguments.memberID, 
													rootSubscriberID=arguments.rootSubscriberID, subs=arguments.subs)>
				
					<cfset local.data.couponapplied = local.applyResult.isvalidcoupon>
					<cfset local.strCoupon = application.mcCacheManager.sessionGetValue(keyname='subCoupon#arguments.rootSubscriberID#', defaultValue={})>
				<cfelse>
					<cfset local.data.couponapplied = true>
				</cfif>

				<cfif local.data.couponapplied>
					<cfset structInsert(local.data, "strsubprice", local.strCoupon.strsubprice)>
					<cfset structInsert(local.data, "redeemdetail", local.strCoupon.redeemdetail)>
				</cfif>
			
			<!--- offer coupon --->
			<cfelse>
				<cfset local.data.offercoupon = offerCoupon(siteID=arguments.mcproxy_siteID, memberID=arguments.memberID, arrSubs=local.arrSubs)>
			</cfif>

			<cfset local.data.success = true>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="offerCoupon" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="arrSubs" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.offercoupon = false>
		
		<cftry>
			<cfxml variable="local.cartItemsXML">
				<cfoutput>
					<cart>
						<cfloop array="#arguments.arrSubs#" index="local.thisSub">
							<item mid="#arguments.memberID#" rateid="#local.thisSub.rateID#" itemid="#local.thisSub.subscriptionID#" itemtype="subscription" />
						</cfloop>
					</cart>
				</cfoutput>
			</cfxml>

			<!--- remove the <xml> tag, specifically the encoding. --->
			<cfset local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','')>

			<cfstoredproc procedure="tr_hasValidCoupons" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="Subscriptions">
				<cfprocparam type="IN" cfsqltype="CF_SQL_LONGVARCHAR " value="#local.cartItemsXML#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_BIT" value="0">
				<cfprocparam type="OUT" cfsqltype="CF_SQL_BIT" variable="local.offerCoupon">
			</cfstoredproc>

		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.offercoupon = false>
		</cfcatch>
		</cftry>

		<cfreturn local.offercoupon>
	</cffunction>

	<cffunction name="validateCouponCode" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="couponCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="subs" type="string" required="true">

		<cfscript>
		var local = structNew();
		
		try {
			local.arrSubs = deserializeJSON(arguments.subs);
			
			local.returnStruct = { "success": false, "isvalidcoupon": false, "couponresponse": "Invalid Promo Code" };

			arguments.couponCode = trim(arguments.couponCode);

			// reset sub coupon
			application.mcCacheManager.sessionSetValue(keyname="subCoupon#arguments.rootSubscriberID#", value={});
			
			// if no length 
			if (len(arguments.couponCode) is 0) return local.returnStruct;

			var memberID = arguments.memberID;

			cfxml(variable="local.cartItemsXML") {
				writeOutput('<cart>');
				local.arrSubs.each(function(element, index) {
					writeOutput('<item mid="#memberID#" rateid="#arguments.element.rateid#" itemid="#arguments.element.subscriptionid#" parentitemid="#arguments.element.parentsubscriptionid#" itemtype="subscription" />');
				});
				writeOutput('</cart>');
			}

			local.cartItemsXML = replaceNoCase(toString(local.cartItemsXML),'<?xml version="1.0" encoding="UTF-8"?>','');

			var sqlParams = {
				siteID = { value=arguments.mcproxy_siteID, cfsqltype="CF_SQL_INTEGER" },
				applicationType = { value="Subscriptions", cfsqltype="CF_SQL_VARCHAR" },
				cartItemsXML = { value=local.cartItemsXML, cfsqltype="CF_SQL_LONGVARCHAR" },
				couponCode = { value=arguments.couponCode, cfsqltype="CF_SQL_VARCHAR" }
			};

			var qryValidCoupon = queryExecute("
				SET NOCOUNT ON;
				
				DECLARE @siteID int = :siteID, @applicationType varchar(20) = :applicationType, @cartItemsXML xml = :cartItemsXML,
					@couponCode varchar(15) = :couponCode, @couponID int, @couponMessage varchar(200), @qualifiedCartItemsXML xml;
				
				EXEC dbo.tr_isValidCouponCode @siteID=@siteID, @applicationType=@applicationType, @cartItemsXML=@cartItemsXML, 
					@couponCode=@couponCode, @couponID=@couponID OUTPUT, @couponMessage=@couponMessage OUTPUT, 
					@qualifiedCartItemsXML=@qualifiedCartItemsXML OUTPUT;
				
				SELECT @couponID AS couponID, @couponMessage AS couponMessage, @qualifiedCartItemsXML AS qualifiedCartItemsXML;
				", 
				sqlParams, { datasource="#application.dsn.membercentral.dsn#" }
			);

			local.couponID = val(qryValidCoupon.couponID);
			local.returnStruct.couponResponse = qryValidCoupon.couponMessage;
			local.qualifiedCartItemsXML = qryValidCoupon.qualifiedCartItemsXML;

			// valid coupon
			if (local.couponID) {
				local.returnStruct.isValidCoupon = applyCouponToSubs(siteID=arguments.mcproxy_siteID, arrSubs=local.arrSubs, couponID=local.couponID, rootSubscriberID=arguments.rootSubscriberID,
													qualifiedCartItemsXML=local.qualifiedCartItemsXML);
			}

			local.returnStruct.success = true;
		} catch(any e) {
			application.objError.sendError(cfcatch=e, objectToDump=local);
			local.returnStruct.success = false;
		}
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="applyCouponToSubs" access="private" output="false" returntype="boolean">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="arrSubs" type="array" required="true">
		<cfargument name="couponID" type="numeric" required="true">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfargument name="qualifiedCartItemsXML" type="xml" required="true">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.qryCoupon = CreateObject("component","model.admin.coupons.coupon").getCouponDetailByCouponID(siteID=arguments.siteID, couponID=arguments.couponID)>
		<cfset local.strCoupon = {}>

		<cfif local.qryCoupon.recordCount AND arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item"))>
			<cfset local.applyTo = XMLSearch(local.qryCoupon.subscriptionsXML,"string(/cs/applyto)")>

			<cfset local.strCoupon = { 
				"couponID": local.qryCoupon.couponID,
				"couponCode": local.qryCoupon.couponCode,
				"applyto": local.applyTo,
				"pctOff": local.qryCoupon.pctOff,
				"pctOffMaxOff": local.qryCoupon.pctOffMaxOff,
				"amtOff": local.qryCoupon.amtOff,
				"redeemDetail": local.qryCoupon.redeemDetail,
				"invoiceDetail": local.qryCoupon.invoiceDetail,
				"encsubs": hash(serializeJSON(arguments.arrSubs), "SHA", "UTF-8"),
				"qcixml": arguments.qualifiedCartItemsXML,
				"strsubprice": structNew("ordered"),
				"totalamt": 0,
				"discount": 0,
				"discountappliedtotal": 0
			}>

			<cfloop array="#arguments.arrSubs#" index="local.thisSub">
				<cfset local.strCoupon.totalamt = NumberFormat(precisionEvaluate(local.strCoupon.totalamt + local.thisSub.amt),"0.00")>
			</cfloop>

			<cfset local.maxDiscountAmt = val(local.qryCoupon.amtOff) GT 0 ? val(local.qryCoupon.amtOff) : val(local.qryCoupon.pctOffMaxOff)>
			<cfset local.maxDiscountAmtRemaining = local.maxDiscountAmt>

			<cfloop array="#arguments.arrSubs#" index="local.thisSub">
				<cfset local.discount = 0>
				
				<cfset local.thisCartItemQualified = arrayLen(XMLSearch(arguments.qualifiedCartItemsXML,"/cart/item[@itemid='#local.thisSub.subscriptionid#'][@rateid='#local.thisSub.rateid#']"))>
				<cfif local.thisCartItemQualified AND local.thisSub.amt>
					<!--- Percentage Off --->
					<cfif val(local.qryCoupon.pctOff) gt 0>
						<cfset local.discount = NumberFormat(precisionEvaluate(local.thisSub.amt * (local.qryCoupon.pctOff / 100)),"0.00")>
						
						<!--- Maximum Discount Amount --->
						<cfif val(local.qryCoupon.pctOffMaxOff)>
							<cfif local.applyTo EQ 'subtree'>
								<cfset local.discount = MIN(local.discount,local.maxDiscountAmtRemaining)>
								<cfset local.maxDiscountAmtRemaining = NumberFormat(precisionEvaluate(local.maxDiscountAmtRemaining - local.discount),"0.00")>
							<cfelseif local.applyTo EQ 'sub' AND MAX(local.qryCoupon.pctOffMaxOff,local.discount) EQ local.discount>
								<cfset local.discount = local.qryCoupon.pctOffMaxOff>
							</cfif>
						</cfif>

						<cfif val(local.qryCoupon.pctOffMaxOff) gt 0 and max(local.qryCoupon.pctOffMaxOff,local.discount) EQ local.discount>
							<cfset local.discount = local.qryCoupon.pctOffMaxOff>
						</cfif>

					<!--- Dollar Amount Off --->
					<cfelseif val(local.qryCoupon.amtOff) gt 0>
						<cfif local.applyTo EQ 'subtree'>
							<cfset local.discount = min(local.thisSub.amt,local.maxDiscountAmtRemaining)>
							<cfset local.maxDiscountAmtRemaining = NumberFormat(precisionEvaluate(local.maxDiscountAmtRemaining - local.discount),"0.00")>
						<cfelse>
							<cfset local.discount = min(local.thisSub.amt,local.qryCoupon.amtOff)>
						</cfif>
					</cfif>
				</cfif>

				<cfset local.strCoupon.strSubPrice[local.thisSub.subscriptionid] = { 
					"rateid": local.thisSub.rateid,
					"amt":local.thisSub.amt, 
					"discount":local.discount, 
					"amtafterdiscount": NumberFormat(precisionEvaluate(local.thisSub.amt - local.discount),"0.00"),
					"numinstallments": local.thisSub.numinstallments, 
					"discountperinstallment": local.discount GT 0 ? NumberFormat(precisionEvaluate(local.discount / local.thisSub.numinstallments),"0.00") : 0
				}>

				<cfset local.strCoupon.discount = NumberFormat(precisionEvaluate(local.strCoupon.discount + local.discount),"0.00")>

				<cfif local.applyTo EQ 'subtree' AND local.maxDiscountAmt GT 0 AND local.maxDiscountAmtRemaining EQ 0>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfset local.strCoupon.discountappliedtotal = NumberFormat(precisionEvaluate(local.strCoupon.totalamt - local.strCoupon.discount),"0.00")>
			<cfset local.success = true>
		</cfif>

		<cfset application.mcCacheManager.sessionSetValue(keyname="subCoupon#arguments.rootSubscriberID#", value=local.strCoupon)>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="removeAppliedCoupon" access="public" output="false" returntype="struct">
		<cfargument name="rootSubscriberID" type="numeric" required="true">
		<cfscript>
		var local = structNew();
		
		local.returnStruct = { "success" = false };
		
		// remove applied coupon
		application.mcCacheManager.sessionSetValue(keyname="subCoupon#arguments.rootSubscriberID#", value={});

		local.returnStruct.success = true;
		
		return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfswitch expression="#arguments.event.getValue('subM','')#">
					<cfcase value="1">The link you accessed is not valid. Contact your association for assistance.</cfcase>
					<cfcase value="2">The link you accessed is not valid. Contact your association for assistance.</cfcase>
					<cfcase value="3">The link you accessed is no longer valid. You have already accepted this subscription. Contact your association for assistance.</cfcase>
					<cfcase value="4">The link you accessed is no longer valid. The subscription offer has expired. Contact your association for assistance.</cfcase>
					<cfcase value="5">The link you accessed is not valid. We were unable to locate the subscription. Contact your association for assistance.</cfcase>
					<cfcase value="6">We could not locate that subscription based on the information provided. Try again.</cfcase>
					<cfdefaultcase>There was an error retrieving the requested subscription. Contact your association for assistance.</cfdefaultcase>
				</cfswitch>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>