<cfif structKeyExists(local.tmpMemSetRights,"ViewInvoices") and local.tmpMemSetRights.ViewInvoices neq 1>
	<cflocation url="#buildCurrentLink(arguments.event,"message")#&message=1" addtoken="no">
</cfif>

<cfsavecontent variable="local.gridJS">
	<cfoutput>
	<script language="javascript">
		var mcma_currtab = "transactions";

		let invoiceListTable, transactionListTable, failedPaymentListTable;
		var #ToScript(local.invoiceListLink,'invoiceListLink')#
		var #ToScript(local.transactionListLink,'transactionListLink')#
		var #ToScript(local.failedPaymentListLink,'failedPaymentListLink')#

		var #ToScript(buildCurrentLink(arguments.event,"edit"),"mcma_link_edit")#
		var #ToScript(local.addPaymentURL,"mca_link_addpmt")#
		var #ToScript(local.myRightsTransactionsAdmin.transAllocatePayment,"mcma_hasrights_allocpmt")#
		var #ToScript(local.allocatePaymentURL,"mca_link_allocpmt")#
		var #ToScript(arguments.event.getValue('mc_admintoolInfo.myRights.memberPayProfileManage'),"mcma_hasrights_payprofilemanage")#
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.memberPayProfileManage') is 1>
			var #ToScript(local.manageCardsURL,"mcma_link_managecards")#
		</cfif>
		var #ToScript(local.myRightsTransactionsAdmin.transRefundPayment,"mcma_hasrights_refundpmt")#
		<cfif local.myRightsTransactionsAdmin.transRefundPayment is 1>
			var #ToScript(local.refundPaymentURL,"mcma_link_refpayment")#
		</cfif>
		var #ToScript(local.myRightsTransactionsAdmin.transAddSale,"mcma_hasrights_transaddsale")#
		<cfif local.myRightsTransactionsAdmin.transAddSale is 1>
			var #ToScript(local.addSaleURL,"mcma_link_addsale")#
		</cfif>
		var #ToScript(local.viewTransactionInfoURL,"mcma_link_viewtransinfo")#
		var #ToScript(local.viewInvoiceInfoURL,"mcma_link_viewinvinfo")#
		var #ToScript(local.adjustTransactionURL,"mcma_link_adjtrans")#
		var #ToScript(local.editTransactionURL,"mcma_link_edittrans")#
		var #ToScript(local.editInvoiceURL,"mcma_link_editinv")#
		<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
			var #ToScript(local.allocPaymentEncString,"mca_link_allocpmtencstring")#
		</cfif>
		var #ToScript(local.importAuthorizeNetPaymentURL,"mcma_link_impauthorizepmt")#
		
		$(function() {
			showUnAllocAmt(#local.strMemberCredit.unallocatedamount#, #local.isDueInvoice#);
			showOutstandAmt(#local.strMemberBalance.outstandingamount#);
			showOutstandInvAmt(#local.strMemberInvBalance.outstandingamount#);
			mca_setupDatePickerRangeFields('tran_fDateFrom','tran_fDateTo');
			mca_setupCalendarIcons('frmInvFilter');
			mca_setupCalendarIcons('frmFilter');
			prepInvoicesFilterForm();
			initInvoicesTable();
			initTransactionsTable();
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.gridJS)#">

<!--- button bar --->
<div class="toolButtonBar">
	<cfoutput>
	<cfif local.myRightsInvoiceAdmin.invoiceCreate is 1>
		<div><a href="javascript:editInvoice('',#local.strMember.qryMember.memberid#);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create an invoice for this member."><i class="fa-regular fa-circle-plus"></i> Create Invoice</a></div>
	</cfif>
	
	<cfif local.myRightsTransactionsAdmin.transAddSale is 1>
		<div><a href="javascript:addSale();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add/Record a sale from this member."><i class="fa-regular fa-tag"></i> Add Sale</a></div>
	</cfif>

	<cfif local.myRightsTransactionsAdmin.transAddPayment is 1>
		<div><a href="javascript:addPayment('#local.addPaymentEncString#');" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Add/Record a payment from this member."><i class="fa-regular fa-circle-plus"></i> Add Payment</a></div>
	</cfif>

	<cfif arguments.event.getValue('mc_admintoolInfo.myRights.memberPayProfileManage') is 1>
		<cfif local.qryMPForCOF.cofMPCount gt 0>
			<div><a href="javascript:manageCards(#local.strMember.qryMember.memberid#);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Manage this member's stored methods of payment."><cfif local.qryMPForCOF.cofBadCount gt 0><i class="fa-regular fa-circle-exclamation"></i> <cfelse><i class="fa-regular fa-credit-card"></i> </cfif> <span<cfif local.qryMPForCOF.cofBadCount gt 0> class="text-danger"</cfif>>Pay Methods</span></a></div>
		<cfelse>
			<div><span class="dim" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Unable to store methods of payment."><i class="fa-regular fa-credit-card"></i> Pay Methods</span></div>
		</cfif>
	</cfif>

	<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
		<div id="allocDIV"></div>
	</cfif>
	<cfif local.myRightsTransactionsAdmin.transRefundPayment is 1>
		<div id="refDIV"></div>
	</cfif>

	<cfif local.myRightsTransactionsAdmin.transAddPayment is 1 and local.qryMPForImportAuthNet.mpCount gt 0>
		<div><a href="javascript:importAuthorizeNetPayment(#local.strMember.qryMember.memberid#);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Import an Authorize.Net payment for this member."><i class="fa-regular fa-minimize"></i> Import Authorize.Net Payment</a></div>
	</cfif>
	</cfoutput>
</div>

<!--- invoices grid --->
<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="d-flex card-header--title font-weight-bold font-size-lg justify-content-between">
					<span>Associated Invoices</span>
					<span id="outstandInvSPAN" class="d-flex font-size-md align-items-center"></span>
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="toolButtonBar">
					<div><a href="javascript:filterAssociatedInvoices();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter invoices."><i class="fa-regular fa-filter"></i> Filter Invoices</a></div>
				</div>
				<div id="divFilterForm" style="display:none">
					<div class="mb-4">
						<form name="frmInvFilter" id="frmInvFilter" onsubmit="filterMemInvoiceGrid();return false;">
							<div class="row">
								<div class="col-xl-6 col-lg-12">
									<div class="form-row">
										<div class="col">
											<div class="form-label-group mb-2">
												<select id="statusID" name="statusID" class="form-control form-control-sm" data-toggle="custom-select2" multiple="yes">
													<cfoutput query="local.qryStatus">
														<option value="#local.qryStatus.statusID#">#local.qryStatus.status#</option>
													</cfoutput>
												</select>
												<label for="statusID">Status</label>
											</div>
										</div>
									</div>
									<div class="form-row mb-2">
										<div class="col">
											<div class="input-group flex-nowrap">
												<div class="input-group-prepend">
													<span class="input-group-text"><cfoutput>#local.qryOrgData.invoiceNumPrefix#</cfoutput><cfif len(local.qryOrgData.invoiceNumPrefix)>-</cfif></span>
												</div>
												<div class="form-label-group flex-grow-1 mb-0">
													<cfoutput>
													<input type="text" name="invoiceNumber" id="invoiceNumber" value="" class="form-control" onblur="mca_sanitizeInvNumInput('#arguments.event.getValue('mc_siteinfo.orgCode')#',this)">
													</cfoutput>
													<label for="invoiceNumber">Invoice Number</label>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="col">
											<div class="form-label-group mb-2">
												<select id="cardOnFile" name="cardOnFile" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
													<option value="0">No Pay Method</option>
													<cfoutput query="local.qryPayProfiles">
														<option value="#local.qryPayProfiles.profileID#">#local.qryPayProfiles.profileName#</option>
													</cfoutput>
												</select>
												<label for="cardOnFile">Pay Method</label>
											</div>
										</div>
									</div>
									<div class="form-row mb-2">
										<div class="col">
											<div class="input-group flex-nowrap">
												<div class="input-group-prepend">
													<span class="input-group-text">$</span>
												</div>
												<div class="form-label-group flex-grow-1 mb-0">
													<input type="text" name="dueAmtStart" id="dueAmtStart" value="" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
													<label for="dueAmtStart">Amt Due From</label>
												</div>
											</div>
										</div>
										<div class="col">
											<div class="input-group flex-nowrap">
												<div class="input-group-prepend">
													<span class="input-group-text">$</span>
												</div>
												<div class="form-label-group flex-grow-1 mb-0">
													<input type="text" name="dueAmtEnd" id="dueAmtEnd" value="" onBlur="if (this.value.length>0) this.value=formatCurrency(this.value);" class="form-control amtBox">
													<label for="dueAmtEnd">Amt Due To</label>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xl-6 col-lg-12">
									<div class="form-row">
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="billeddateStart" id="billeddateStart" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateStart"><i class="fa-regular fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('billeddateStart');"><i class="fa-regular fa-circle-xmark"></i></a></span>
														</div>
														<label for="billeddateStart">Billed from</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="billeddateEnd" id="billeddateEnd" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="billeddateEnd"><i class="fa-regular fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('billeddateEnd');"><i class="fa-regular fa-circle-xmark"></i></a></span>
														</div>
														<label for="billeddateEnd">Billed to</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="col-sm-6 col-xs-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="duedateStart" id="duedateStart" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="duedateStart"><i class="fa-regular fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('duedateStart');"><i class="fa-regular fa-circle-xmark"></i></a></span>
														</div>
														<label for="duedateStart">Due from</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-sm-6 col-xs-12">
											<div class="form-group">
												<div class="form-label-group mb-2">
													<div class="input-group dateFieldHolder">
														<input type="text" name="duedateEnd" id="duedateEnd" value="" class="form-control dateControl">
														<div class="input-group-append">
															<span class="input-group-text cursor-pointer calendar-button" data-target="duedateEnd"><i class="fa-regular fa-calendar"></i></span>
															<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('duedateEnd');"><i class="fa-regular fa-circle-xmark"></i></a></span>
														</div>
														<label for="duedateEnd">Due to</label>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div class="form-row">
										<div class="col">
											<div class="form-label-group mb-2">
												<select id="invProfile" name="invProfile" multiple="yes" class="form-control form-control-sm" data-toggle="custom-select2">
													<cfoutput query="local.qryInvoiceProfiles">
														<option value="#local.qryInvoiceProfiles.profileID#">#local.qryInvoiceProfiles.profileName#</option>
													</cfoutput>
												</select>
												<label for="invProfile">Invoice Profile</label>
											</div>
										</div>
										<div class="col">
											<div class="form-label-group mb-2">
												<input type="text" name="trDetail" id="trDetail" value="" class="form-control">
												<label for="trDetail">Transaction Detail Contains...</label>
											</div>
										</div>
									</div>
									<div class="form-row mt-2">
										<div class="col text-right">
											<button type="button" name="clearInvBtn" id="clearInvBtn" onclick="clearFilterInvoices()" class="btn btn-sm btn-secondary">Clear Filters</button>
											<button type="submit" name="filterInvBtn" id="filterInvBtn" class="btn btn-sm btn-primary"><i class="fa-regular fa-filter"></i> Filter Invoices</button>
										</div>
									</div>
								</div>
							</div>
						</form>
					</div>
				</div>
				<table id="invoiceList" class="table table-sm table-striped table-bordered" style="width:100%">
					<thead>
						<tr>
							<th>Due</th>
							<th>Invoice</th>
							<th>Assigned To</th>
							<th class="text-right">Amount</th>
							<th class="text-right">Due</th>
							<th>Actions</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
</div>

<!--- transactions grid --->
<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="d-flex card-header--title font-weight-bold font-size-lg justify-content-between">
					<span>Individual Transactions</span>
					<span class="d-flex font-size-md align-items-center">
						<span id="outstandSPAN"></span>
						<span id="unallocSPAN" class="ml-2"></span>
					</span>
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="toolButtonBar">
					<div><a href="javascript:filterIndividualTransactions();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Click to filter Transactions."><i class="fa-regular fa-filter"></i> Filter Transactions</a></div>
				</div>
				<div id="tran_filterbox" class="mb-4" style="display:none">
					<form name="frmFilter" id="frmFilter" onsubmit="filterTransactions();return false;">
						<input type="hidden" name="tran_fAR" id="tran_fAR" value="0">
						<input type="hidden" name="tran_fCB" id="tran_fCB" value="0">
						<div class="row">
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<select name="tran_fType" id="tran_fType" class="form-control">
												<option value="">All Transactions</option>
												<option value="1">Sale</option>
												<option value="2">Payment</option>
												<option value="3">Adjustment</option>
												<option value="4">Refund</option>
												<option value="6">Write Off</option>
												<option value="7">Sales Tax</option>
												<option value="9">NSF</option>
											</select>
											<label for="tran_fType">Transactions</label>
										</div>
									</div>
								</div>
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<input type="text" class="form-control" name="tran_fDetail" id="tran_fDetail" value="">
											<label for="tran_fDetail">Detail Contains...</label>
										</div>
									</div>
								</div>
							</div>
							<div class="col-xl-6 col-lg-12">
								<div class="form-row">
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="tran_fDateFrom" id="tran_fDateFrom" value="" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="tran_fDateFrom"><i class="fa-regular fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('tran_fDateFrom');"><i class="fa-regular fa-circle-xmark"></i></a></span>
												</div>
												<label for="tran_fDateFrom">Date from</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="form-label-group mb-2">
											<div class="input-group dateFieldHolder">
												<input type="text" name="tran_fDateTo" id="tran_fDateTo" value="" class="form-control dateControl">
												<div class="input-group-append">
													<span class="input-group-text cursor-pointer calendar-button" data-target="tran_fDateTo"><i class="fa-regular fa-calendar"></i></span>
													<span class="input-group-text"><a href="javascript:mca_clearDateRangeField('tran_fDateTo');"><i class="fa-regular fa-circle-xmark"></i></a></span>
												</div>
												<label for="tran_fDateTo">Date To</label>
											</div>
										</div>
									</div>
								</div>
								<div class="form-row mb-2">
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="tran_fAmtFrom" id="tran_fAmtFrom" value="" onBlur="formatAmtFilter($(this));" class="form-control">
												<label for="tran_fAmtFrom">Low Amount</label>
											</div>
										</div>
									</div>
									<div class="col">
										<div class="input-group flex-nowrap">
											<div class="input-group-prepend">
												<span class="input-group-text">$</span>
											</div>
											<div class="form-label-group flex-grow-1 mb-0">
												<input type="text" name="tran_fAmtTo" id="tran_fAmtTo" value="" onBlur="formatAmtFilter($(this));" class="form-control">
												<label for="tran_fAmtTo">High Amount</label>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
						<div class="text-right">
							<button type="button" name="clearBtn" id="clearBtn" onclick="clearFilterTransactions()" class="btn btn-sm btn-secondary">Clear Filters</button>		
							<button type="submit" name="filterBtn" id="filterBtn" class="btn btn-sm btn-primary">
								<i class="fa-regular fa-filter"></i> Filter Transactions
							</button>
						</div>
					</form>
				</div>
				<table id="transactionList" class="table table-sm table-striped table-bordered" style="width:100%">
					<thead>
						<tr>
							<th>Date</th>
							<th>Type</th>
							<th>Detail</th>
							<th class="text-right">Debit</th>
							<th class="text-right">Credit</th>
							<th>Actions</th>
						</tr>
					</thead>
				</table>
			</div>
		</div>
	</div>
</div>

<!--- failed payments grid --->
<div class="row mb-3">
	<div class="col-xl-12">
		<div class="card card-box mb-1">
			<div class="card-header py-1 bg-light">
				<div class="d-flex card-header--title font-weight-bold font-size-lg justify-content-between">
					Failed Payments
				</div>
			</div>
			<div class="card-body pb-3">
				<div class="d-flex justify-content-between mb-2">
					<span>Unsuccessful payment attempts tied to this member may be helpful in troubleshooting payment issues.</span>
					<input type="button" id="btnShowFailedPayments" class="btm btn-sm btn-primary" onclick="loadFailedPayments()" value="Show Failed Payments">
				</div>
				<div id="failedPaymentListTable" class="d-none">
					<table id="failedPaymentList" class="table table-sm table-striped table-bordered" style="width:100%">
						<thead>
							<tr>
								<th>Date</th>
								<th>Type</th>
								<th>Detail</th>
								<th class="text-right">Amount</th>
							</tr>
						</thead>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>