use membercentral
GO

DECLARE @sites TABLE (siteID int PRIMARY KEY, siteCode varchar(10));

INSERT INTO @sites (siteID, siteCode)
select distinct s.siteID, s.siteCode
from dbo.siteFeatures as sf
inner join dbo.sites as s on s.siteID = sf.siteID
inner join dbo.cms_siteResources as sr on sr.siteResourceID = s.siteResourceID and sr.siteResourceStatusID = 1
inner join dbo.sub_types as t on t.siteID = s.siteID and t.status = 'A'
where sf.subscriptions = 1
and sf.recurringEvents = 0;

DECLARE @siteID int;
SELECT @siteID = min(siteID) FROM @sites;
WHILE @siteID IS NOT NULL BEGIN
	EXEC dbo.enableSiteFeature @siteID=@siteID, @toolTypeList='recurringEvents';
	SELECT @siteID = min(siteID) FROM @sites WHERE siteID > @siteID;
END

select siteCode
FROM @sites
order by siteCode
GO
