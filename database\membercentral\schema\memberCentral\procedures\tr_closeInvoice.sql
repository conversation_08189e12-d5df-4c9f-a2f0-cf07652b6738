ALTER PROC dbo.tr_closeInvoice
@orgID int,
@enteredByMemberID int,
@invoiceIDList varchar(max)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblInvoicesTmp') IS NOT NULL
		DROP TABLE #tblInvoicesTmp;
	IF OBJECT_ID('tempdb..#tblInvoicesPrep') IS NOT NULL
		DROP TABLE #tblInvoicesPrep;
	IF OBJECT_ID('tempdb..#tblInvoices') IS NOT NULL
		DROP TABLE #tblInvoices;
	IF OBJECT_ID('tempdb..#tblInvoicesIP') IS NOT NULL
		DROP TABLE #tblInvoicesIP;
	CREATE TABLE #tblInvoicesTmp (invoiceID int PRIMARY KEY, statusID int, numDaysDelinquent int, dateDue date);
	CREATE TABLE #tblInvoicesPrep (invoiceID int, amtDueNoPendingOnInvoice decimal(18,2), INDEX IX_invoicesPrep (invoiceID,amtDueNoPendingOnInvoice));
	CREATE TABLE #tblInvoices (invoiceID int PRIMARY KEY, statusID int, numDaysDelinquent int, dateDue date, 
		amtDueNoPendingOnInvoice decimal(18,2), flagged bit, delinquentDate date);
	CREATE TABLE #tblInvoicesIP (invoiceProfileID int PRIMARY KEY, numDaysDelinquent int);

	-- split invoiceIDs
	INSERT INTO #tblInvoicesTmp (invoiceID)
	select tmp.listitem
	from dbo.fn_intListToTable(@invoiceIDList,',') as tmp
	group by tmp.listitem;

	-- get org invoice profiles to reduce IO of query below
	INSERT INTO #tblInvoicesIP (invoiceProfileID, numDaysDelinquent)
	SELECT profileID, numDaysDelinquent
	FROM dbo.tr_invoiceProfiles
	WHERE orgID = @orgID;

	update tmp 
	set statusID = i.statusID, 
		numDaysDelinquent = inp.numDaysDelinquent, 
		dateDue = i.dateDue
	from #tblInvoicesTmp as tmp
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = tmp.invoiceID
	INNER JOIN #tblInvoicesIP as inp on inp.invoiceProfileID = i.invoiceProfileID;

	-- these two queries are so we don't have to do a left outer join on tr_invoiceTransactions which caused random high IO
	INSERT INTO #tblInvoicesPrep (invoiceID, amtDueNoPendingOnInvoice)
	SELECT invoiceID, 0
	FROM #tblInvoicesTmp;

	INSERT INTO #tblInvoicesPrep (invoiceID, amtDueNoPendingOnInvoice)
	select tmp.invoiceID, isnull(sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount),0)
	from #tblInvoicesTmp as tmp
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.invoiceID = tmp.invoiceID
	group by tmp.invoiceID;

	INSERT INTO #tblInvoices (invoiceID, statusID, numDaysDelinquent, dateDue, amtDueNoPendingOnInvoice, flagged)
	select tmpIP.invoiceID, tmpI.statusID, tmpI.numDaysDelinquent, tmpI.dateDue, sum(tmpIP.amtDueNoPendingOnInvoice), 0
	from #tblInvoicesPrep as tmpIP
	INNER JOIN #tblInvoicesTmp as tmpI on tmpI.invoiceID = tmpIP.invoiceID
	group by tmpIP.invoiceID, tmpI.statusID, tmpI.numDaysDelinquent, tmpI.dateDue;

	UPDATE #tblInvoices
	SET delinquentDate = DateAdd(DD,numDaysDelinquent*-1,getdate());

	BEGIN TRAN;
		-- if currently open/pending, set to closed.
		update #tblInvoices
		set flagged = 1
		where statusID in (1,2);
		
		IF @@ROWCOUNT > 0 BEGIN
			UPDATE i
			set i.statusID = 3
			from dbo.tr_invoices as i
			inner join #tblInvoices as tbl on tbl.invoiceID = i.invoiceID and tbl.flagged = 1
			where i.orgID = @orgID;

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select invoiceID, getdate(), 3, statusID, @enteredByMemberID
			from #tblInvoices
			where flagged = 1;

			update #tblInvoices
			set statusID = 3, flagged = 0
			where flagged = 1;
		END

		-- if currently not paid but is really fully paid with active payments, set to paid and clear the PayProfileID
		update #tblInvoices
		set flagged = 1
		where statusID in (3,5)
		and amtDueNoPendingOnInvoice = 0;

		IF @@ROWCOUNT > 0 BEGIN
			INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"INV",
				"ORGID":' + cast(s.orgID as varchar(10)) + ',
				"SITEID":' + cast(s.siteID as varchar(10)) + ',
				"ACTORMEMBERID":' + cast(@enteredByMemberID as varchar(20)) + ',
				"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
				"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars('Pay Profile ' + mpp.detail + ' removed from Invoice ' + i.fullInvoiceNumber),'"','\"') + '" } }'
			FROM dbo.tr_invoices as i
			INNER JOIN #tblInvoices as tmp on tmp.invoiceID = i.invoiceID and tmp.flagged = 1
			INNER JOIN dbo.organizations as o on o.orgID = i.orgID
			INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
			INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = i.MPProfileID
			INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
			WHERE i.orgID = @orgID;

			UPDATE i
			set i.statusID = 4, 
				i.payProfileID = null,
				i.MPProfileID = null
			from dbo.tr_invoices as i
			inner join #tblInvoices as tbl on tbl.invoiceID = i.invoiceID and tbl.flagged = 1
			where i.orgID = @orgID;

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select invoiceID, getdate(), 4, statusID, @enteredByMemberID
			from #tblInvoices
			where flagged = 1;

			update #tblInvoices
			set statusID = 4, flagged = 0
			where flagged = 1;
		END

		-- if currently paid but is not really fully paid with active payments, set to closed.
		update #tblInvoices
		set flagged = 1
		where statusID = 4
		and amtDueNoPendingOnInvoice > 0;

		IF @@ROWCOUNT > 0 BEGIN
			UPDATE i 
			set i.statusID = 3
			from #tblInvoices as tbl
			inner join dbo.tr_invoices as i on i.orgID = @orgID
				and i.statusID = 4
				and tbl.invoiceID = i.invoiceID 
			where tbl.flagged = 1;

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select invoiceID, getdate(), 3, 4, @enteredByMemberID
			from #tblInvoices
			where flagged = 1;

			update #tblInvoices
			set statusID = 3, flagged = 0
			where flagged = 1;
		END

		-- if currently closed and not fully paid but delinq based on inv prof, set to delinq.
		update #tblInvoices
		set flagged = 1
		where numDaysDelinquent is not null
		and statusID = 3
		and amtDueNoPendingOnInvoice > 0
		and dateDue <= delinquentDate;

		IF @@ROWCOUNT > 0 BEGIN
			UPDATE i 
			set i.statusID = 5
			from #tblInvoices as tbl 
			inner join dbo.tr_invoices as i on i.orgID = @orgID
				and i.statusID = 3
				and tbl.invoiceID = i.invoiceID 
			where tbl.flagged = 1;

			insert into dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select invoiceID, getdate(), 5, 3, @enteredByMemberID
			from #tblInvoices
			where flagged = 1;

			update #tblInvoices
			set statusID = 5, flagged = 0
			where flagged = 1;
		END
	COMMIT TRAN;

	IF OBJECT_ID('tempdb..#tblInvoicesTmp') IS NOT NULL
		DROP TABLE #tblInvoicesTmp;
	IF OBJECT_ID('tempdb..#tblInvoicesPrep') IS NOT NULL
		DROP TABLE #tblInvoicesPrep;
	IF OBJECT_ID('tempdb..#tblInvoices') IS NOT NULL
		DROP TABLE #tblInvoices;
	IF OBJECT_ID('tempdb..#tblInvoicesIP') IS NOT NULL
		DROP TABLE #tblInvoicesIP;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
