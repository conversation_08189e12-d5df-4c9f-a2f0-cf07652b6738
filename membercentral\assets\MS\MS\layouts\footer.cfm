<cfoutput>
	<cfset local.zone = "P"><!-- <PERSON>er Logo -->
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<div id="zonePObj" class="hide">
			#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
		</div>
	</cfif>
	<cfset local.zone = "Q"><!-- Footer About -->
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<div id="zoneQObj" class="hide">
			#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
		</div>
	</cfif>

	<cfset local.zone = "R"><!-- Footer Quick Links -->
	<cfset local.zoneRcontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zoneRcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
	</cfif>
	
	<cfset local.zone = "S"><!-- Footer Contact -->
	<cfset local.zoneScontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zoneScontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
	</cfif>
	
   <cfset local.zone = "T"><!-- Footer 	Copyright and Social media -->
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<div id="zoneTObj" class="hide">
			#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
		</div>
	</cfif>

	 <!--Footer Start-->
      <div class="footer">
         <div class="container">
            <div class="row d-flex-wrap">
               <div class="col1 footer-info">
                  <div class="foot-logo-wrap footerLogoWrap hideNone">
                     
                  </div>
                  <div class="visible-desktop footerZonePbtnWrap">

                     <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                        <a href="/?pg=login" class="MAJButton footerZonePbtn">MEMBER LOGIN</a>
                     <cfelse>
                           <a href="/?logout" class="MAJButton footerZonePbtn">MEMBER LOGOUT</a>
                     </cfif>
                  </div>
               </div>
               <div class="col2 footer-links">
                  
                  <div class="hidden-desktop  visible-mobile footer-btns footerZonePbtnWrap">
                    <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                        <a href="/?pg=login" class="MAJButton footerZonePbtn">MEMBER LOGIN</a>
                     <cfelse>
                           <a href="/?logout" class="MAJButton footerZonePbtn">MEMBER LOGOUT</a>
                     </cfif>
                  </div>
               </div>
              
               <div class="col3 footer-links zoneRWrap <cfif len(local.zoneRcontent) eq 0>hideNone</cfif>">
                  #local.zoneRcontent#
               </div>
               <div class="col3 contact-links <cfif len(local.zoneScontent) eq 0>hideNone</cfif>">
                  #local.zoneScontent#
               </div>
            </div>
            <div class="row d-flex-wrap copyright-wrapper zoneTWrap hideNone">
               <div class="col12">
                  <hr />
               </div>
               <div class="col4 copyright-txt">
                  <p class="zoneTleftColumn">
                  </p>
               </div>
               <div class="col5">
                  <ul class="social-list zoneTRightColumn">
                  
                  </ul>
               </div>
            </div>
         </div>
      </div>
      <!--Footer End-->
</cfoutput>