<cfcomponent extends="model.admin.admin" output="no">
	
	<cfset variables.defaultEvent = 'controller'>

	<cffunction name="controller" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
	
		<cfscript>
			var local = structNew();

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// Build Quick Links ------------------------------------------------------------------------ ::
			this.link.trialSmithBilling = buildCurrentLink(arguments.event,"trialSmithBilling");

			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="trialSmithBilling" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" required="yes">

		<cfset var local = structNew()>
		<cfset local.saveBillingInfoLink = buildCurrentLink(arguments.event,"saveBillingInfo")>
		<cfset local.savetsRoyaltiesLink = buildCurrentLink(arguments.event,"savetsRoyalties")>
		
		<cfset local.strDepoTLA = CreateObject("component","model.admin.website.website").getTrialSmithInfo(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'))>
		<cfstoredproc procedure="site_getTrialSmithBilling" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			<cfprocparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('mc_siteinfo.siteCode')#">
			<cfprocresult name="local.strDepoTLA.qryOrgHosting" resultset="1">
			<cfprocresult name="local.strDepoTLA.qryOrgFundraising" resultset="2">
			<cfprocresult name="local.strDepoTLA.qryOrgLRIS" resultset="3">
			<cfprocresult name="local.strDepoTLA.qryOrgPublications" resultset="4">
			<cfprocresult name="local.strDepoTLA.qryOrgSolicitations" resultset="5">
			<cfprocresult name="local.strDepoTLA.qryOrgAPIAccess" resultset="6">
			<cfprocresult name="local.strDepoTLA.qryOrgEmailBlast" resultset="7">
			<cfprocresult name="local.strDepoTLA.qryOrgDistrictMatching" resultset="8">
			<cfprocresult name="local.strDepoTLA.qryOrgAddressUpdate" resultset="9">
			<cfprocresult name="local.strDepoTLA.qryOrgDedicatedServiceMgr" resultset="10">
			<cfprocresult name="local.strDepoTLA.qryOrgEmailHosting" resultset="11">
			<cfprocresult name="local.strDepoTLA.qryOrgPrivateListServerDomain" resultset="12">
			<cfprocresult name="local.strDepoTLA.qryOrgPrivateEmailSendingDomain" resultset="13">
			<cfprocresult name="local.strDepoTLA.qryOrgEntPlatformSecurity" resultset="14">
			<cfprocresult name="local.strDepoTLA.qryOrgDiscretionary" resultset="15">
			<cfprocresult name="local.strDepoTLA.qryOrgDiscLists" resultset="16">
			<cfprocresult name="local.strDepoTLA.qryOrgMktLists" resultset="17">
			<cfprocresult name="local.strDepoTLA.qryOrgSWMonthly" resultset="18">
			<cfprocresult name="local.strDepoTLA.qryOrgSWLive" resultset="19">
			<cfprocresult name="local.strDepoTLA.qryOrgSWOnDemand" resultset="20">
			<cfprocresult name="local.strDepoTLA.qryOrgSWBundles" resultset="21">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_trialSmithBilling.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveBillingInfo" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.data = "";
			local.saveBillingInfo = saveBillingInfoDetails(arguments.event);
			application.objCommon.redirect(this.link.trialSmithBilling);
		</cfscript>
	</cffunction>

	<cffunction name="savetsRoyalties" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.data = "";
			local.savetsRoyalties = savetsRoyaltiesDetails(arguments.event);
			application.objCommon.redirect("#this.link.trialSmithBilling#&tab=tsroyalties");
		</cfscript>
	</cffunction>

	<cffunction name="saveBillingInfoDetails" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgCode')#" cfsqltype="CF_SQL_VARCHAR">;
				
				BEGIN TRAN;
					update dbo.memberCentralBilling
					set DepoMemberDataID = nullif(<cfqueryparam value="#val(arguments.event.getValue('billingDepoMemberDataID'))#" cfsqltype="CF_SQL_INTEGER">,0),
						BillingEmail = <cfqueryparam value="#trim(arguments.event.getValue('billingemailaddress'))#" cfsqltype="CF_SQL_VARCHAR">	
					where orgCode = @orgCode;
						
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="savetsRoyaltiesDetails" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgCode')#" cfsqltype="CF_SQL_VARCHAR">;
				
				BEGIN TRAN;
					update dbo.memberCentralBilling
					SET	tsDocSalePct = <cfqueryparam value="#val(arguments.event.getValue('tsDocSalePct'))#" cfsqltype="CF_SQL_INTEGER">
						, tsDocContribAmt = <cfqueryparam value="#val(rereplace(arguments.event.getValue('tsDocContribAmt'),'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">
						, tsSubSalePct = <cfqueryparam value="#val(arguments.event.getValue('tsSubSalePct'))#" cfsqltype="CF_SQL_INTEGER">
						, eclipsRoyalty = <cfqueryparam value="#val(rereplace(arguments.event.getValue('eclipsRoyalty'),'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">
					
				where orgCode = @orgCode;
						
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="saveAMSHostingInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="AMSHostingEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="AMSHostingEffectiveDateYear" type="numeric" required="no">
		<cfargument name="AMSHostingInContract" type="boolean" required="no">
		<cfargument name="AMSHostingAdminRate" type="numeric" required="no">
		<cfargument name="AMSHostingAdminRateInc" type="numeric" required="no">
		<cfargument name="AMSHostingAdminRateAddtl" type="numeric" required="no">
		<cfargument name="AMSHostingMonthlyMin" type="numeric" required="no">
		<cfargument name="AMSHostingMonthlyFeeAMS" type="numeric" required="no">
		<cfargument name="AMSHostingMonthlyFeeWeb" type="numeric" required="no">
		<cfargument name="AMSHostingNoFee" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveAMSHostingInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingHosting
					SET adminRate = <cfqueryparam value="#val(rereplace(arguments.AMSHostingAdminRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						adminRateInc = <cfqueryparam value="#val(arguments.AMSHostingAdminRateInc)#" cfsqltype="CF_SQL_INTEGER">,
						adminRateAddtl = <cfqueryparam value="#val(rereplace(arguments.AMSHostingAdminRateAddtl,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyMin = <cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyMin,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyFeeAMS = <cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyFeeAMS,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyFeeWeb = <cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyFeeWeb,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						noFee = <cfqueryparam value="#arguments.AMSHostingNoFee#" cfsqltype="CF_SQL_BIT"> ,
						inContract = <cfqueryparam value="#arguments.AMSHostingInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"AMSHostingEffectiveDateMonth") AND len(arguments.AMSHostingEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.AMSHostingEffectiveDateMonth#/1/#arguments.AMSHostingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.AMSHostingEffectiveDateMonth) AND (len(arguments.AMSHostingAdminRate) OR len(arguments.AMSHostingAdminRateInc) OR len(arguments.AMSHostingAdminRateAddtl) OR len(arguments.AMSHostingMonthlyMin) OR len(arguments.AMSHostingMonthlyFeeAMS) OR len(arguments.AMSHostingMonthlyFeeWeb))>
						INSERT INTO dbo.memberCentralBillingHosting (orgCode, effectiveDate, adminRate, adminRateInc, adminRateAddtl, monthlyMin, monthlyFeeAMS, monthlyFeeWeb, 
							noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.AMSHostingEffectiveDateMonth#/1/#arguments.AMSHostingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.AMSHostingAdminRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(arguments.AMSHostingAdminRateInc)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#val(rereplace(arguments.AMSHostingAdminRateAddtl,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyMin,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyFeeAMS,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(rereplace(arguments.AMSHostingMonthlyFeeWeb,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfif structKeyExists(arguments,"AMSHostingNoFee") AND val(arguments.AMSHostingNoFee)>1<cfelse>0</cfif>,
							<cfqueryparam value="#val(arguments.AMSHostingInContract)#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveFundraisingInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="FundraisingEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="FundraisingEffectiveDateYear" type="numeric" required="no">
		<cfargument name="FundraisingInContract" type="boolean" required="no">
		<cfargument name="FundraisingMonthlyFee" type="numeric" required="no">
		<cfargument name="FundraisingNoFee" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveFundraisingInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingFundraising
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.FundraisingMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						noFee = <cfqueryparam value="#arguments.FundraisingNoFee#" cfsqltype="CF_SQL_BIT"> ,
						inContract = <cfqueryparam value="#arguments.FundraisingInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"FundraisingEffectiveDateMonth") AND len(arguments.FundraisingEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.FundraisingEffectiveDateMonth#/1/#arguments.FundraisingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.FundraisingEffectiveDateMonth) AND len(arguments.FundraisingMonthlyFee)>
						INSERT INTO dbo.memberCentralBillingFundraising (orgCode, effectiveDate, monthlyFee, noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.FundraisingEffectiveDateMonth#/1/#arguments.FundraisingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.FundraisingMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfif structKeyExists(arguments,"FundraisingNoFee") AND val(arguments.FundraisingNoFee)>1<cfelse>0</cfif>,
							<cfqueryparam value="#val(arguments.FundraisingInContract)#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveLRISInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="LRISEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="LRISEffectiveDateYear" type="numeric" required="no">
		<cfargument name="LRISInContract" type="boolean" required="no">
		<cfargument name="LRISMonthlyFee" type="numeric" required="no">
		<cfargument name="LRISNoFee" type="boolean" required="no">
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		<cftry>
			<cfquery name="local.qrySaveLRISInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingLRIS
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.LRISMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						noFee = <cfqueryparam value="#arguments.LRISNoFee#" cfsqltype="CF_SQL_BIT">,
						inContract = <cfqueryparam value="#arguments.LRISInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"LRISEffectiveDateMonth") AND len(arguments.LRISEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.LRISEffectiveDateMonth#/1/#arguments.LRISEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.LRISEffectiveDateMonth) AND len(arguments.LRISMonthlyFee)>
						INSERT INTO dbo.memberCentralBillingLRIS (orgCode, effectiveDate, monthlyFee, noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.LRISEffectiveDateMonth#/1/#arguments.LRISEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.LRISMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfif structKeyExists(request,"LRISNoFee") AND arguments.LRISNoFee>1<cfelse>0</cfif>,
							<cfqueryparam value="#arguments.LRISInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="savePublicationsInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="PublicationsEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="PublicationsEffectiveDateYear" type="numeric" required="no">
		<cfargument name="PublicationsInContract" type="boolean" required="no">
		<cfargument name="PublicationsMonthlyFee" type="numeric" required="no">
		<cfargument name="PublicationsNoFee" type="boolean" required="no">
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySavePublicationsInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingPublications
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.PublicationsMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						noFee = <cfqueryparam value="#arguments.PublicationsNoFee#" cfsqltype="CF_SQL_BIT">,
						inContract = <cfqueryparam value="#arguments.PublicationsInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"PublicationsEffectiveDateMonth") AND len(arguments.PublicationsEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.PublicationsEffectiveDateMonth#/1/#arguments.PublicationsEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.PublicationsEffectiveDateMonth) AND len(arguments.PublicationsMonthlyFee)>
						INSERT INTO dbo.memberCentralBillingPublications (orgCode, effectiveDate, monthlyFee, noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.PublicationsEffectiveDateMonth#/1/#arguments.PublicationsEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.PublicationsMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfif structKeyExists(request,"PublicationsNoFee") AND arguments.PublicationsNoFee>1<cfelse>0</cfif>,
							<cfqueryparam value="#arguments.PublicationsInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSolicitationsInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="string" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="string" required="yes">
		<cfargument name="SolicitationsEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="SolicitationsEffectiveDateYear" type="numeric" required="no">
		<cfargument name="SolicitationsInContract" type="boolean" required="no">
		<cfargument name="SolicitationsMonthlyFee" type="numeric" required="no">
		<cfargument name="SolicitationsNoFee" type="boolean" required="no">
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveSolicitationsInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingSolicitations
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.SolicitationsMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						noFee = <cfqueryparam value="#arguments.SolicitationsNoFee#" cfsqltype="CF_SQL_BIT">,
						inContract = <cfqueryparam value="#arguments.SolicitationsInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"SolicitationsEffectiveDateMonth") AND len(arguments.SolicitationsEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.SolicitationsEffectiveDateMonth#/1/#arguments.SolicitationsEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.SolicitationsEffectiveDateMonth) AND len(arguments.SolicitationsMonthlyFee)>
						INSERT INTO dbo.memberCentralBillingSolicitations (orgCode, effectiveDate, monthlyFee, noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.SolicitationsEffectiveDateMonth#/1/#arguments.SolicitationsEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.SolicitationsMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfif structKeyExists(request,"SolicitationsNoFee") AND arguments.SolicitationsNoFee>1<cfelse>0</cfif>,
							<cfqueryparam value="#arguments.SolicitationsInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveAPIAccessInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="APIAccessEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="APIAccessEffectiveDateYear" type="numeric" required="no">
		<cfargument name="APIAccessInContract" type="boolean" required="no">
		<cfargument name="APIAccessMonthlyFee" type="numeric" required="no">
		<cfargument name="APIAccessNoofCallIncFee" type="numeric" required="no">
		<cfargument name="APIAccessOverageFee" type="numeric" required="no">
		<cfargument name="APIAccessNoofCallsInOverageFee" type="numeric" required="no">
		<cfargument name="APIAccessNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveAPIAccessInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
							UPDATE dbo.memberCentralBillingAPIAccess
							SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.APIAccessMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								noofCallIncFee = <cfqueryparam value="#val(arguments.APIAccessNoofCallIncFee)#" cfsqltype="CF_SQL_INTEGER">,
								overageFee = <cfqueryparam value="#val(rereplace(arguments.APIAccessOverageFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
								noofCallsInOverageFee = <cfqueryparam value="#val(arguments.APIAccessNoofCallsInOverageFee)#" cfsqltype="CF_SQL_INTEGER">,
								noFee = <cfqueryparam value="#arguments.APIAccessNoFee#" cfsqltype="CF_SQL_BIT"> ,
								inContract = <cfqueryparam value="#arguments.APIAccessInContract#" cfsqltype="CF_SQL_BIT">
								<cfif structKeyExists(arguments,"APIAccessEffectiveDate_Month") AND len(arguments.APIAccessEffectiveDate_Month)>
									 , effectiveDate = <cfqueryparam value="#arguments.APIAccessEffectiveDate_Month#/1/#arguments.APIAccessEffectiveDate_Year#" cfsqltype="CF_SQL_DATE">
								</cfif>
							WHERE orgCode = @orgCode
							AND scheduleID = @scheduleID
							AND effectiveDate > @nowDate;
						<cfelse>
							<cfif len(arguments.APIAccessEffectiveDateMonth) AND (len(arguments.APIAccessMonthlyFee) OR len(arguments.APIAccessnoofCallIncFee) OR len(arguments.APIAccessOverageFee) OR len(arguments.APIAccessNoofCallsInOverageFee))>
								INSERT INTO dbo.memberCentralBillingAPIAccess (orgCode, effectiveDate, monthlyFee, noFee, inContract, noofCallIncFee, overageFee, noofCallsInOverageFee)
								VALUES(
									@orgCode,
									<cfqueryparam value="#arguments.APIAccessEffectiveDateMonth#/1/#arguments.APIAccessEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
									<cfqueryparam value="#val(rereplace(arguments.APIAccessMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
									<cfif structKeyExists(arguments,"APIAccessNoFee") AND val(arguments.APIAccessNoFee)>1<cfelse>0</cfif>,
									<cfqueryparam value="#val(arguments.APIAccessInContract)#" cfsqltype="CF_SQL_BIT">,
									<cfqueryparam value="#val(arguments.APIAccessNoofCallIncFee)#" cfsqltype="CF_SQL_INTEGER">,
									<cfqueryparam value="#val(rereplace(arguments.APIAccessOverageFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
									<cfqueryparam value="#val(arguments.APIAccessNoofCallsInOverageFee)#" cfsqltype="CF_SQL_INTEGER">
								);
									SET @scheduleID = SCOPE_IDENTITY();
							</cfif>
						</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveEmailBlastInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="EmailBlastEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="EmailBlastEffectiveDateYear" type="numeric" required="no">
		<cfargument name="EmailBlastInContract" type="boolean" required="no">
		<cfargument name="EmailBlastMonthlyFee" type="numeric" required="no">
		<cfargument name="EmailBlastMonthlyComp" type="numeric" required="no">
		<cfargument name="EmailBlastMonthlyPerAdminComp" type="numeric" required="no">
		<cfargument name="EmailBlastBillingRate" type="numeric" required="no">
		<cfargument name="EmailBlastIncOtherApps" type="boolean" required="no">
		<cfargument name="EmailBlastNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveEmailBlastInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingEmailBlast
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.EmailBlastMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyComp = <cfqueryparam value="#val(arguments.EmailBlastMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
						monthlyPerAdminComp = <cfqueryparam value="#val(arguments.EmailBlastMonthlyPerAdminComp)#" cfsqltype="CF_SQL_INTEGER">,
						billingRate = <cfqueryparam value="#val(rereplace(arguments.EmailBlastBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
						noFee = <cfqueryparam value="#arguments.EmailBlastNoFee#" cfsqltype="CF_SQL_BIT">,
						inContract = <cfqueryparam value="#arguments.EmailBlastInContract#" cfsqltype="CF_SQL_BIT">,
						includeOtherApps = <cfqueryparam value="#arguments.EmailBlastIncOtherApps#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"EmailBlastEffectiveDateMonth") AND len(arguments.EmailBlastEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.EmailBlastEffectiveDateMonth#/1/#arguments.EmailBlastEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.EmailBlastEffectiveDateMonth) AND (len(arguments.EmailBlastMonthlyFee) OR len(arguments.EmailBlastMonthlyComp) OR len(arguments.EmailBlastMonthlyPerAdminComp) OR len(arguments.EmailBlastBillingRate))>
						INSERT INTO dbo.memberCentralBillingEmailBlast (orgCode, effectiveDate, monthlyFee, monthlyComp, monthlyPerAdminComp, billingRate, noFee, includeOtherApps, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.EmailBlastEffectiveDateMonth#/1/#arguments.EmailBlastEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.EmailBlastMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(arguments.EmailBlastMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#val(arguments.EmailBlastMonthlyPerAdminComp)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#val(rereplace(arguments.EmailBlastBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
							<cfqueryparam value="#arguments.EmailBlastNoFee#" cfsqltype="CF_SQL_BIT"> ,
							<cfqueryparam value="#arguments.EmailBlastIncOtherApps#" cfsqltype="CF_SQL_BIT">,
							<cfqueryparam value="#arguments.EmailBlastInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDistrictInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="DistrictEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="DistrictEffectiveDateYear" type="numeric" required="no">
		<cfargument name="DistrictMatchingInContract" type="boolean" required="no">
		<cfargument name="DistrictBillingRate" type="numeric" required="no">
		<cfargument name="DistrictNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveDistrictInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingDistrict
						SET billingRate = <cfqueryparam value="#val(rereplace(arguments.DistrictBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
							noFee = <cfqueryparam value="#arguments.DistrictNoFee#" cfsqltype="CF_SQL_BIT">,
							inContract = <cfqueryparam value="#arguments.DistrictMatchingInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"DistrictEffectiveDateMonth") AND len(arguments.DistrictEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.DistrictEffectiveDateMonth#/1/#arguments.DistrictEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.DistrictEffectiveDateMonth) AND len(arguments.DistrictBillingRate)>
							INSERT INTO dbo.memberCentralBillingDistrict (orgCode, effectiveDate, billingRate, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.DistrictEffectiveDateMonth#/1/#arguments.DistrictEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.DistrictBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
								<cfqueryparam value="#arguments.DistrictNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.DistrictMatchingInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveAddressUpdateInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="AddressUpdateEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="AddressUpdateEffectiveDateYear" type="numeric" required="no">
		<cfargument name="AddressUpdateInContract" type="boolean" required="no">
		<cfargument name="AddressUpdateMonthlyFee" type="numeric" required="no">
		<cfargument name="AddressUpdateBillingRate" type="numeric" required="no">
		<cfargument name="AddressUpdateNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveAddressUpdateInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingAddressUpdate
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.AddressUpdateMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						billingRate = <cfqueryparam value="#val(rereplace(arguments.AddressUpdateBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
						noFee = <cfqueryparam value="#arguments.AddressUpdateNoFee#" cfsqltype="CF_SQL_BIT">,
						inContract = <cfqueryparam value="#arguments.AddressUpdateInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"AddressUpdateEffectiveDateMonth") AND len(arguments.AddressUpdateEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.AddressUpdateEffectiveDateMonth#/1/#arguments.AddressUpdateEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.AddressUpdateEffectiveDateMonth) AND (len(arguments.AddressUpdateMonthlyFee) OR len(arguments.AddressUpdateBillingRate))>
						INSERT INTO dbo.memberCentralBillingAddressUpdate (orgCode, effectiveDate, monthlyFee, billingRate, noFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.AddressUpdateEffectiveDateMonth#/1/#arguments.AddressUpdateEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.AddressUpdateMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(rereplace(arguments.AddressUpdateBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
							<cfqueryparam value="#arguments.AddressUpdateNoFee#" cfsqltype="CF_SQL_BIT">,
							<cfqueryparam value="#arguments.AddressUpdateInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDedicatedServiceMgrInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="DedicatedServiceMgrEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="DedicatedServiceMgrEffectiveDateYear" type="numeric" required="no">
		<cfargument name="DedicatedServiceMgrInContract" type="boolean" required="no">
		<cfargument name="DedicatedServiceMgrMonthlyFee" type="numeric" required="no">
		<cfargument name="DedicatedServiceMgrNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveDedicatedServiceMgrInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingDedicatedServiceMgr
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.DedicatedServiceMgrMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							noFee = <cfqueryparam value="#arguments.DedicatedServiceMgrNoFee#" cfsqltype="CF_SQL_BIT">,
							inContract = <cfqueryparam value="#arguments.DedicatedServiceMgrInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"DedicatedServiceMgrEffectiveDateMonth") AND len(arguments.DedicatedServiceMgrEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.DedicatedServiceMgrEffectiveDateMonth#/1/#arguments.DedicatedServiceMgrEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.DedicatedServiceMgrEffectiveDateMonth) AND len(arguments.DedicatedServiceMgrMonthlyFee)>
							INSERT INTO dbo.memberCentralBillingDedicatedServiceMgr (orgCode, effectiveDate, monthlyFee, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.DedicatedServiceMgrEffectiveDateMonth#/1/#arguments.DedicatedServiceMgrEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.DedicatedServiceMgrMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
									<cfqueryparam value="#arguments.DedicatedServiceMgrNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.DedicatedServiceMgrInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveEmailHostingInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="EmailHostingEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="EmailHostingEffectiveDateYear" type="numeric" required="no">
		<cfargument name="EmailHostingInContract" type="boolean" required="no">
		<cfargument name="EmailHostingMonthlyFee" type="numeric" required="no">
		<cfargument name="EmailHostingNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		<cftry>
			<cfquery name="local.qrySaveEmailHostingInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingEmailHosting
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.EmailHostingMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							noFee = <cfqueryparam value="#arguments.EmailHostingNoFee#" cfsqltype="CF_SQL_BIT">,
							inContract = <cfqueryparam value="#arguments.EmailHostingInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"EmailHostingEffectiveDateMonth") AND len(arguments.EmailHostingEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.EmailHostingEffectiveDateMonth#/1/#arguments.EmailHostingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.EmailHostingEffectiveDateMonth) AND len(arguments.EmailHostingMonthlyFee)>
							INSERT INTO dbo.memberCentralBillingEmailHosting (orgCode, effectiveDate, monthlyFee, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.EmailHostingEffectiveDateMonth#/1/#arguments.EmailHostingEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.EmailHostingMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								<cfqueryparam value="#arguments.EmailHostingNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.EmailHostingInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="savePrivateListServerDomainInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="PrivateListServerDomainEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="PrivateListServerDomainEffectiveDateYear" type="numeric" required="no">
		<cfargument name="PrivateListServerDomainInContract" type="boolean" required="no">
		<cfargument name="PrivateListServerDomainMonthlyFee" type="numeric" required="no">
		<cfargument name="PrivateListServerDomainNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		<cftry>
			<cfquery name="local.qrySavePrivateListServerDomainInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingPrivateListServerDomain
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.PrivateListServerDomainMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							noFee = <cfqueryparam value="#arguments.PrivateListServerDomainNoFee#" cfsqltype="CF_SQL_BIT">,
							inContract = <cfqueryparam value="#arguments.PrivateListServerDomainInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"PrivateListServerDomainEffectiveDateMonth") AND len(arguments.PrivateListServerDomainEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.PrivateListServerDomainEffectiveDateMonth#/1/#arguments.PrivateListServerDomainEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.PrivateListServerDomainEffectiveDateMonth) AND len(arguments.PrivateListServerDomainMonthlyFee)>
							INSERT INTO dbo.memberCentralBillingPrivateListServerDomain (orgCode, effectiveDate, monthlyFee, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.PrivateListServerDomainEffectiveDateMonth#/1/#arguments.PrivateListServerDomainEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.PrivateListServerDomainMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								<cfqueryparam value="#arguments.PrivateListServerDomainNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.PrivateListServerDomainInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="savePrivateEmailSendingDomainInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="PrivateEmailSendingDomainEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="PrivateEmailSendingDomainEffectiveDateYear" type="numeric" required="no">
		<cfargument name="PrivateEmailSendingDomainInContract" type="boolean" required="no">
		<cfargument name="PrivateEmailSendingDomainMonthlyFee" type="numeric" required="no">
		<cfargument name="PrivateEmailSendingDomainNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySavePrivateEmailSendingDomainInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingPrivateEmailSendingDomain
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.PrivateEmailSendingDomainMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							noFee = <cfqueryparam value="#arguments.PrivateEmailSendingDomainNoFee#" cfsqltype="CF_SQL_BIT">,
							inContract = <cfqueryparam value="#arguments.PrivateEmailSendingDomainInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"PrivateEmailSendingDomainEffectiveDateMonth") AND len(arguments.PrivateEmailSendingDomainEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.PrivateEmailSendingDomainEffectiveDateMonth#/1/#arguments.PrivateEmailSendingDomainEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.PrivateEmailSendingDomainEffectiveDateMonth) AND len(arguments.PrivateEmailSendingDomainMonthlyFee)>
							INSERT INTO dbo.memberCentralBillingPrivateEmailSendingDomain (orgCode, effectiveDate, monthlyFee, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.PrivateEmailSendingDomainEffectiveDateMonth#/1/#arguments.PrivateEmailSendingDomainEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.PrivateEmailSendingDomainMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								<cfqueryparam value="#arguments.PrivateEmailSendingDomainNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.PrivateEmailSendingDomainInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveEntPlatformSecurityInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="EntPlatformSecurityEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="EntPlatformSecurityEffectiveDateYear" type="numeric" required="no">
		<cfargument name="EntPlatformSecurityInContract" type="boolean" required="no">
		<cfargument name="EntPlatformSecurityMonthlyFee" type="numeric" required="no">
		<cfargument name="EntPlatformSecurityNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveEntPlatformSecurityInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingEntPlatformSecurity
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.EntPlatformSecurityMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							noFee = <cfqueryparam value="#arguments.EntPlatformSecurityNoFee#" cfsqltype="CF_SQL_BIT"> ,
							inContract = <cfqueryparam value="#arguments.EntPlatformSecurityInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"EntPlatformSecurityEffectiveDateMonth") AND len(arguments.EntPlatformSecurityEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.EntPlatformSecurityEffectiveDateMonth#/1/#arguments.EntPlatformSecurityEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.EntPlatformSecurityEffectiveDateMonth) AND len(arguments.EntPlatformSecurityMonthlyFee)>
							INSERT INTO dbo.memberCentralBillingEntPlatformSecurity (orgCode, effectiveDate, monthlyFee, noFee, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.EntPlatformSecurityEffectiveDateMonth#/1/#arguments.EntPlatformSecurityEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#val(rereplace(arguments.EntPlatformSecurityMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								<cfqueryparam value="#arguments.EntPlatformSecurityNoFee#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#arguments.EntPlatformSecurityInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDiscretionaryInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="DiscretionaryEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="DiscretionaryEffectiveDateYear" type="numeric" required="no">
		<cfargument name="DiscretionaryInContract" type="boolean" required="no">
		<cfargument name="DiscretionaryMonthlyFee" type="numeric" required="no">
		<cfargument name="DiscretionaryNumMonths" type="numeric" required="no">
		<cfargument name="DiscretionaryFeeDesc" type="string" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		<cftry>
			<cfquery name="local.qrySaveDiscretionaryInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingDiscretionary
						SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.DiscretionaryMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							feeDesc = <cfqueryparam value="#trim(arguments.DiscretionaryFeeDesc)#" cfsqltype="CF_SQL_VARCHAR">,
							numMonths = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.DiscretionaryNumMonths)#">,0),
							inContract = <cfqueryparam value="#arguments.DiscretionaryInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"DiscretionaryEffectiveDateMonth") AND len(arguments.DiscretionaryEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.DiscretionaryEffectiveDateMonth#/1/#arguments.DiscretionaryEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.DiscretionaryEffectiveDateMonth) AND (len(arguments.DiscretionaryFeeDesc) OR len(arguments.DiscretionaryMonthlyFee))>
							INSERT INTO dbo.memberCentralBillingDiscretionary (orgCode, effectiveDate, feeDesc, monthlyFee, numMonths, inContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.DiscretionaryEffectiveDateMonth#/1/#arguments.DiscretionaryEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam value="#arguments.DiscretionaryFeeDesc#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#val(rereplace(arguments.DiscretionaryMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
								NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.DiscretionaryNumMonths)#">,0),
								<cfqueryparam value="#arguments.DiscretionaryInContract#" cfsqltype="CF_SQL_BIT">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveDiscListInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="DiscListEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="DiscListEffectiveDateYear" type="numeric" required="no">
		<cfargument name="DiscListMonthlyFee" type="numeric" required="no">
		<cfargument name="DiscListMonthlyComp" type="numeric" required="no">
		<cfargument name="DiscListBillingRate" type="numeric" required="no">
		<cfargument name="DiscListBillingRatePer" type="numeric" required="no">
		<cfargument name="DiscListNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveDiscListInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingDiscLists
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.DiscListMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyComp = <cfqueryparam value="#val(arguments.DiscListMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
						billingRate = <cfqueryparam value="#val(rereplace(arguments.DiscListBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
						billingRatePer = <cfqueryparam value="#val(arguments.DiscListBillingRatePer)#" cfsqltype="CF_SQL_INTEGER">,
						noFee = <cfqueryparam value="#arguments.DiscListNoFee#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"DiscListEffectiveDateMonth") AND len(arguments.DiscListEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.DiscListEffectiveDateMonth#/1/#arguments.DiscListEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.DiscListEffectiveDateMonth) AND (len(arguments.DiscListMonthlyFee) OR len(arguments.DiscListMonthlyComp) OR len(arguments.DiscListBillingRate) OR len(arguments.DiscListBillingRatePer))>
						INSERT INTO dbo.memberCentralBillingDiscLists (orgCode, effectiveDate, monthlyFee, monthlyComp, billingRate, billingRatePer, noFee)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.DiscListEffectiveDateMonth#/1/#arguments.DiscListEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.DiscListMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(arguments.DiscListMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#val(rereplace(arguments.DiscListBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
							<cfqueryparam value="#val(arguments.DiscListBillingRatePer)#" cfsqltype="CF_SQL_INTEGER">,
							<cfif structKeyExists(request,"DiscListNoFee") AND val(arguments.DiscListNoFee)>1<cfelse>0</cfif>
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveMktListInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="MktListEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="MktListEffectiveDateYear" type="numeric" required="no">
		<cfargument name="MktListMonthlyFee" type="numeric" required="no">
		<cfargument name="MktListMonthlyComp" type="numeric" required="no">
		<cfargument name="MktListBillingRate" type="numeric" required="no">
		<cfargument name="MktListBillingRatePer" type="numeric" required="no">
		<cfargument name="MktListNoFee" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveMktListInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingMktLists
					SET monthlyFee = <cfqueryparam value="#val(rereplace(arguments.MktListMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyComp = <cfqueryparam value="#val(arguments.MktListMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
						billingRate = <cfqueryparam value="#val(rereplace(arguments.MktListBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
						billingRatePer = <cfqueryparam value="#val(arguments.MktListBillingRatePer)#" cfsqltype="CF_SQL_INTEGER">,
						noFee = <cfqueryparam value="#arguments.MktListNoFee#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"MktListEffectiveDateMonth") AND len(arguments.MktListEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.MktListEffectiveDateMonth#/1/#arguments.MktListEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.MktListEffectiveDateMonth) AND (len(arguments.MktListMonthlyFee) OR len(arguments.MktListMonthlyComp) OR len(arguments.MktListBillingRate) OR len(arguments.MktListBillingRatePer))>
						INSERT INTO dbo.memberCentralBillingMktLists (orgCode, effectiveDate, monthlyFee, monthlyComp, billingRate, billingRatePer, noFee)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.MktListEffectiveDateMonth#/1/#arguments.MktListEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.MktListMonthlyFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(arguments.MktListMonthlyComp)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#val(rereplace(arguments.MktListBillingRate,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="6">,
							<cfqueryparam value="#val(arguments.MktListBillingRatePer)#" cfsqltype="CF_SQL_INTEGER">,
							<cfqueryparam value="#arguments.MktListNoFee#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWMonthlyInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="SWMonthlyEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="SWMonthlyEffectiveDateYear" type="numeric" required="no">
		<cfargument name="SWMonthlySupportFee" type="numeric" required="no">
		<cfargument name="SWMonthlyPortalHostFee" type="numeric" required="no">
		<cfargument name="SWMonthlyInContract" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveSWMonthlyInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingSWMonthly
					SET monthlySupportFee = <cfqueryparam value="#val(rereplace(arguments.SWMonthlySupportFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						monthlyPortalHostingFee = <cfqueryparam value="#val(rereplace(arguments.SWMonthlyPortalHostFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
						inContract = <cfqueryparam value="#arguments.SWMonthlyInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"SWMonthlyEffectiveDateMonth") AND len(arguments.SWMonthlyEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.SWMonthlyEffectiveDateMonth#/1/#arguments.SWMonthlyEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.SWMonthlyEffectiveDateMonth) AND (len(arguments.SWMonthlySupportFee) OR len(arguments.SWMonthlyPortalHostFee))>
						INSERT INTO dbo.memberCentralBillingSWMonthly (orgCode, effectiveDate, monthlySupportFee, monthlyPortalHostingFee, inContract)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.SWMonthlyEffectiveDateMonth#/1/#arguments.SWMonthlyEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam value="#val(rereplace(arguments.SWMonthlySupportFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#val(rereplace(arguments.SWMonthlyPortalHostFee,'[^0-9\.]','','ALL'))#" cfsqltype="CF_SQL_DECIMAL" scale="2">,
							<cfqueryparam value="#arguments.SWMonthlyInContract#" cfsqltype="CF_SQL_BIT">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWLiveInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="SWLiveEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="SWLiveEffectiveDateYear" type="numeric" required="no">
		<cfargument name="SWLNonSyndSetupFeeMinInc" type="numeric" required="no">
		<cfargument name="SWLNonSyndSetupFeeAddtl" type="numeric" required="no">
		<cfargument name="SWLSyndSetupFeeMinInc" type="numeric" required="no">
		<cfargument name="SWLSyndSetupFeeAddtl" type="numeric" required="no">
		<cfargument name="SWLSyndSetupFeeAddtlAfter" type="numeric" required="no">
		<cfargument name="SWLNonSyncRegFee" type="numeric" required="no">
		<cfargument name="SWLNonSynRegFeeMinInc" type="numeric" required="no">
		<cfargument name="SWLNonSyncRegFeeAddtl" type="numeric" required="no">
		<cfargument name="SWLNonSyncPctPub" type="numeric" required="no">
		<cfargument name="SWLNonSyncPctSW" type="numeric" required="no">
		<cfargument name="SWLNonSyncRegFeeSW" type="numeric" required="no">
		<cfargument name="SWLNonSyncPctCC" type="numeric" required="no">
		<cfargument name="SWLSynSemEnablesSynd" type="numeric" required="no">
		<cfargument name="SWLSynSemSyndPerSite" type="numeric" required="no">
		<cfargument name="SWLSynRegFee" type="numeric" required="no">
		<cfargument name="SWLSynRegFeeMinInc" type="numeric" required="no">
		<cfargument name="SWLSynRegFeeAddtl" type="numeric" required="no">
		<cfargument name="SWLSynPctPub" type="numeric" required="no">
		<cfargument name="SWLSynPctPart" type="numeric" required="no">
		<cfargument name="SWLSynPctSW" type="numeric" required="no">
		<cfargument name="SWLSynRegFeeSW" type="numeric" required="no">
		<cfargument name="SWLSynRegFeeSW" type="numeric" required="no">
		<cfargument name="SWLSynPctCC" type="numeric" required="no">
		<cfargument name="SWLNonSynInContract" type="boolean" required="no">
		<cfargument name="SWLSynInContract" type="boolean" required="no">
			
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveSWLiveInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
				<cfif arguments.scheduleID GT 0>
					UPDATE dbo.memberCentralBillingSWLive
					SET nonSynSetupFeeInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLNonSyndSetupFeeMinInc)#">,
						nonSynSetupFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyndSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
						nonSynSetupFeeAddtlAfter = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyndSetupFeeAddtlAfter,'[^0-9\.]','','ALL'))#">,
						synSetupFeeInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLSyndSetupFeeMinInc)#">,
						synSetupFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSyndSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
						synSetupFeeAddtlAfter = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSyndSetupFeeAddtlAfter,'[^0-9\.]','','ALL'))#">,
						nonSynRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFee,'[^0-9\.]','','ALL'))#">,
						nonSynRegFeeMinInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLNonSynRegFeeMinInc)#">,
						nonSynRegFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
						nonSynPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctPub)#">,
						nonSynPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctSW)#">,
						nonSynRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFeeSW,'[^0-9\.]','','ALL'))#">,
						nonSynPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctCC)#">,
						synSemEnableSynd = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynSemEnablesSynd,'[^0-9\.]','','ALL'))#">,
						synSemSyndPerSite = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
						synRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFee,'[^0-9\.]','','ALL'))#">,
						synRegFeeMinInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLSynRegFeeMinInc)#">,
						synRegFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
						synPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctPub)#">,
						synPctPart = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctPart)#">,
						synPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctSW)#">,
						synRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
						synPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctCC)#">,
						nonSynInContract = <cfqueryparam value="#arguments.SWLNonSynInContract#" cfsqltype="CF_SQL_BIT">,
						synInContract = <cfqueryparam value="#arguments.SWLSynInContract#" cfsqltype="CF_SQL_BIT">
						<cfif structKeyExists(arguments,"SWLiveEffectiveDateMonth") AND len(arguments.SWLiveEffectiveDateMonth)>
							, effectiveDate = <cfqueryparam value="#arguments.SWLiveEffectiveDateMonth#/1/#arguments.SWLiveEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
						</cfif>
					WHERE orgCode = @orgCode
					AND scheduleID = @scheduleID
					AND effectiveDate > @nowDate;
				<cfelse>
					<cfif len(arguments.SWLiveEffectiveDateMonth)>
						INSERT INTO dbo.memberCentralBillingSWLive (orgCode, effectiveDate, nonSynInContract, nonSynSetupFeeInc, 
							nonSynSetupFeeAddtl, nonSynSetupFeeAddtlAfter, synInContract, synSetupFeeInc, synSetupFeeAddtl, 
							synSetupFeeAddtlAfter, nonSynRegFee, nonSynRegFeeMinInc, nonSynRegFeeAddtl, nonSynPctPub, nonSynPctSW, 
							nonSynRegFeeSW, nonSynPctCC, synSemEnableSynd, synSemSyndPerSite, synRegFee, synRegFeeMinInc, synRegFeeAddtl, 
							synPctPub, synPctPart, synPctSW, synRegFeeSW, synPctCC)
						VALUES(
							@orgCode,
							<cfqueryparam value="#arguments.SWLiveEffectiveDateMonth#/1/#arguments.SWLiveEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.SWLNonSynInContract)#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLNonSyndSetupFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyndSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyndSetupFeeAddtlAfter,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#val(arguments.SWLSynInContract)#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLSyndSetupFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSyndSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSyndSetupFeeAddtlAfter,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLNonSynRegFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLNonSyncRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLNonSyncPctCC)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynSemEnablesSynd,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWLSynRegFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctPart)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWLSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWLSynPctCC)#">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWOnDemandInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="SWOnDemandEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="SWOnDemandEffectiveDateYear" type="numeric" required="no">
		<cfargument name="SWODSemSetUpFee" type="numeric" required="no">
		<cfargument name="SWODSemSetUpMinInC" type="numeric" required="no">
		<cfargument name="SWODSemSetUpIncEa" type="numeric" required="no">
		<cfargument name="SWODExamSetUpFee" type="numeric" required="no">	
		<cfargument name="SWODSemHostOvrFee" type="numeric" required="no">
		<cfargument name="SWODSemHostVideoFee" type="numeric" required="no">
		<cfargument name="SWODNonSynRegFee" type="numeric" required="no">
		<cfargument name="SWODNonSynPctPub" type="numeric" required="no">
		<cfargument name="SWODNonSynPctSW" type="numeric" required="no">	
		<cfargument name="SWODNonSynRegFeeSW" type="numeric" required="no">
		<cfargument name="SWODNonSynPctCC" type="numeric" required="no">	
		<cfargument name="SWODSynSemEnableSynd" type="numeric" required="no">
		<cfargument name="SWODSynSemSyndPerSite" type="numeric" required="no">
		<cfargument name="SWODSynRegFee" type="numeric" required="no">	
		<cfargument name="SWODSynPctPub" type="numeric" required="no">
		<cfargument name="SWODSynPctPart" type="numeric" required="no">	
		<cfargument name="SWODSynPctSW" type="numeric" required="no">	
		<cfargument name="SWODSynRegFeeSW" type="numeric" required="no">
		<cfargument name="SWODSynPctCC" type="numeric" required="no">
		<cfargument name="SWODSetupFeeAddtl" type="numeric" required="no">
		<cfargument name="SWODConSyncAddtl" type="numeric" required="no">
		<cfargument name="SWODConEditAddtl" type="numeric" required="no">											
		<cfargument name="SWODSemSetupInContract" type="boolean" required="no">
		<cfargument name="SWODSemHostInContract" type="boolean" required="no">	
		<cfargument name="SWODNonSynInContract" type="boolean" required="no">
		<cfargument name="SWODSynInContract" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		<cftry>
			<cfquery name="local.qrySaveSWOnDemandInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingSWOnDemand
						SET semSetupFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemSetUpFee,'[^0-9\.]','','ALL'))#">,
							semSetupInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWODSemSetUpMinInC)#">,
							semSetupIncEa = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemSetUpIncEa,'[^0-9\.]','','ALL'))#">,
							examSetupFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODExamSetUpFee,'[^0-9\.]','','ALL'))#">,
							semHostOvrFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemHostOvrFee,'[^0-9\.]','','ALL'))#">,
							semHostVideoFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemHostVideoFee,'[^0-9\.]','','ALL'))#">,
							nonSynRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODNonSynRegFee,'[^0-9\.]','','ALL'))#">,
							nonSynPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctPub)#">,
							nonSynPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctSW)#">,
							nonSynRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODNonSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
							nonSynPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctCC)#">,
							synSemEnableSynd = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynSemEnableSynd,'[^0-9\.]','','ALL'))#">,
							synSemSyndPerSite = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							synRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynRegFee,'[^0-9\.]','','ALL'))#">,
							synPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctPub)#">,
							synPctPart = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctPart)#">,
							synPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctSW)#">,
							synRegfeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
							synPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctCC)#">,
							setupFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
							conSyncAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODConSyncAddtl,'[^0-9\.]','','ALL'))#">,
							conEditAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODConEditAddtl,'[^0-9\.]','','ALL'))#">,
							semSetupInContract = <cfqueryparam value="#arguments.SWODSemSetupInContract#" cfsqltype="CF_SQL_BIT">,
							semHostInContract = <cfqueryparam value="#arguments.SWODSemHostInContract#" cfsqltype="CF_SQL_BIT">,
							nonSynInContract = <cfqueryparam value="#arguments.SWODNonSynInContract#" cfsqltype="CF_SQL_BIT">,
							synInContract = <cfqueryparam value="#arguments.SWODSynInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"SWOnDemandEffectiveDateMonth") AND len(arguments.SWOnDemandEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.SWOnDemandEffectiveDateMonth#/1/#arguments.SWOnDemandEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.SWOnDemandEffectiveDateMonth)>
							INSERT INTO dbo.memberCentralBillingSWOnDemand (orgCode, effectiveDate, semSetupFee, semSetupInc, semSetupIncEa, 
								examSetupFee, semHostOvrFee, semHostVideoFee, nonSynRegFee, nonSynPctPub, nonSynPctSW, nonSynRegFeeSW,
								nonSynPctCC, synSemEnableSynd, synSemSyndPerSite, synRegFee, synPctPub, synPctPart, synPctSW, synRegfeeSW,
								synPctCC, setupFeeAddtl, conSyncAddtl, conEditAddtl, semSetupInContract, semHostInContract,
								nonSynInContract, synInContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.SWOnDemandEffectiveDateMonth#/1/#arguments.SWOnDemandEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemSetUpFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWODSemSetUpMinInC)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemSetUpIncEa,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODExamSetUpFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemHostOvrFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSemHostVideoFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODNonSynRegFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctPub)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctSW)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODNonSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODNonSynPctCC)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynSemEnableSynd,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynRegFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctPub)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctPart)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctSW)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSynRegFeeSW,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWODSynPctCC)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODSetupFeeAddtl,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODConSyncAddtl,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWODConEditAddtl,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWODSemSetupInContract#">,
								<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWODSemHostInContract#">,
								<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWODNonSynInContract#">,
								<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWODSynInContract#">
							);
								SET @scheduleID = SCOPE_IDENTITY();
						</cfif>
					</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="saveSWBundlesInfo" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteCode" type="string" required="yes">	
		<cfargument name="mcproxy_orgCode" type="string" required="yes">	
		<cfargument name="scheduleID" type="numeric" required="yes">
		<cfargument name="SWBundlesEffectiveDateMonth" type="numeric" required="no">
		<cfargument name="SWBundlesEffectiveDateYear" type="numeric" required="no">
		<cfargument name="SWBBunSetUpFee" type="numeric" required="no">
		<cfargument name="SWBBunSetUpMinInC" type="numeric" required="no">
		<cfargument name="SWBBunSetUpIncEa" type="numeric" required="no">
		<cfargument name="SWBNonSynSWLRegFee" type="numeric" required="no">	
		<cfargument name="SWBNonSynSWLRegFeeMinInc" type="numeric" required="no">
		<cfargument name="SWBNonSynSWLRegFeeAddtl" type="numeric" required="no">
		<cfargument name="SWBNonSynSWLPctPub" type="numeric" required="no">
		<cfargument name="SWBNonSynSWLPctSW" type="numeric" required="no">
		<cfargument name="SWBNonSynSWLRegFeeSW" type="numeric" required="no">	
		<cfargument name="SWBNonSynSWLPctCC" type="numeric" required="no">
		<cfargument name="SWBSynSWLSemEnableSynd" type="numeric" required="no">	
		<cfargument name="SWBSynSWLSemSyndPerSite" type="numeric" required="no">
		<cfargument name="SWBSynSWLRegFee" type="numeric" required="no">
		<cfargument name="SWBSynSWLRegFeeMinInc" type="numeric" required="no">	
		<cfargument name="SWBSynSWLRegFeeAddtl" type="numeric" required="no">
		<cfargument name="SWBSynSWLPctPub" type="numeric" required="no">	
		<cfargument name="SWBSynSWLPctPart" type="numeric" required="no">	
		<cfargument name="SWBSynSWLPctSW" type="numeric" required="no">
		<cfargument name="SWBSynSWLRegFeeSW" type="numeric" required="no">
		<cfargument name="SWBSynSWLPctCC" type="numeric" required="no">
		<cfargument name="SWBNonSynSWODRegFee" type="numeric" required="no">
		<cfargument name="SWBNonSynSWODPctPub" type="numeric" required="no">		
		<cfargument name="SWBNonSynSWODPctSW" type="numeric" required="no">
		<cfargument name="SWBNonSynSWODRegFeeSW" type="numeric" required="no">
		<cfargument name="SWBNonSynSWODPctCC" type="numeric" required="no">
		<cfargument name="SWBSynSWODSemEnableSynd" type="numeric" required="no">
		<cfargument name="SWBSynSWODSemSyndPerSite" type="numeric" required="no">
		<cfargument name="SWBSynSWODRegFee" type="numeric" required="no">
		<cfargument name="SWBSynSWODPctPub" type="numeric" required="no">
		<cfargument name="SWBSynSWODPctPart" type="numeric" required="no">
		<cfargument name="SWBSynSWODPctSW" type="numeric" required="no">
		<cfargument name="SWBSynSWODRegFeeSW" type="numeric" required="no">		
		<cfargument name="SWBSynSWODPctCC" type="numeric" required="no">
		<cfargument name="SWBBunSetUpInContract" type="boolean" required="no">
		<cfargument name="SWBNonSynSWLInContract" type="boolean" required="no">	
		<cfargument name="SWBSynSWLInContract" type="boolean" required="no">
		<cfargument name="SWBNonSynSWODInContract" type="boolean" required="no">
		<cfargument name="SWBSynSWODInContract" type="boolean" required="no">

		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>

		<cftry>
			<cfquery name="local.qrySaveSWBundlesInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
				DECLARE @orgCode varchar(10) = <cfqueryparam value="#arguments.mcproxy_orgCode#" cfsqltype="CF_SQL_VARCHAR">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.scheduleID#">,
					@nowDate datetime = GETDATE();
					<cfif arguments.scheduleID GT 0>
						UPDATE dbo.memberCentralBillingSWBundles
						SET bunSetupFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBBunSetUpFee,'[^0-9\.]','','ALL'))#">,
							bunSetupInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBBunSetUpMinInC)#">,
							bunSetupIncEa = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBBunSetUpIncEa,'[^0-9\.]','','ALL'))#">,
							nonSynSWLRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFee,'[^0-9\.]','','ALL'))#">,
							nonSynSWLRegFeeMinInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBNonSynSWLRegFeeMinInc)#">,
							nonSynSWLRegFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							nonSynSWLPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctPub)#">,
							nonSynSWLPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctSW)#">,
							nonSynSWLRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFeeSW,'[^0-9\.]','','ALL'))#">,
							nonSynSWLPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctCC)#">,
							synSWLSemEnableSynd = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLSemEnableSynd,'[^0-9\.]','','ALL'))#">,
							synSWLSemSyndPerSite = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							synSWLRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFee,'[^0-9\.]','','ALL'))#">,
							synSWLRegFeeMinInc = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBSynSWLRegFeeMinInc)#">,
							synSWLRegFeeAddtl = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							synSWLPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctPub)#">,
							synSWLPctPart = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctPart)#">,
							synSWLPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctSW)#">,
							synSWLRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFeeSW,'[^0-9\.]','','ALL'))#">,
							synSWLPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctCC)#">,
							nonSynSWODRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWODRegFee,'[^0-9\.]','','ALL'))#">,
							nonSynSWODPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctPub)#">,
							nonSynSWODPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctSW)#">,
							nonSynSWODRegFeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWODRegFeeSW,'[^0-9\.]','','ALL'))#">,
							nonSynSWODPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctCC)#">,
							synSWODSemEnableSynd = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODSemEnableSynd,'[^0-9\.]','','ALL'))#">,
							synSWODSemSyndPerSite = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							synSWODRegFee = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODRegFee,'[^0-9\.]','','ALL'))#">,
							synSWODPctPub = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctPub)#">,
							synSWODPctPart = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctPart)#">,
							synSWODPctSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctSW)#">,
							synSWODRegfeeSW = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODRegFeeSW,'[^0-9\.]','','ALL'))#">,
							synSWODPctCC = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctCC)#">,
							bunSetupInContract = <cfqueryparam value="#arguments.SWBBunSetUpInContract#" cfsqltype="CF_SQL_BIT">,
							nonSynSWLInContract = <cfqueryparam value="#arguments.SWBNonSynSWLInContract#" cfsqltype="CF_SQL_BIT">,
							synSWLInContract = <cfqueryparam value="#arguments.SWBSynSWLInContract#" cfsqltype="CF_SQL_BIT">,
							nonSynSWODInContract = <cfqueryparam value="#arguments.SWBNonSynSWODInContract#" cfsqltype="CF_SQL_BIT">,
							synSWODInContract = <cfqueryparam value="#arguments.SWBSynSWODInContract#" cfsqltype="CF_SQL_BIT">
							<cfif structKeyExists(arguments,"SWBundlesEffectiveDateMonth") AND len(arguments.SWBundlesEffectiveDateMonth)>
								, effectiveDate = <cfqueryparam value="#arguments.SWBundlesEffectiveDateMonth#/1/#arguments.SWBundlesEffectiveDateYear#" cfsqltype="CF_SQL_DATE">
							</cfif>
						WHERE orgCode = @orgCode
						AND scheduleID = @scheduleID
						AND effectiveDate > @nowDate;
					<cfelse>
						<cfif len(arguments.SWBundlesEffectiveDateMonth)>
							INSERT INTO dbo.memberCentralBillingSWBundles (orgCode, effectiveDate, bunSetupFee, bunSetupInc, bunSetupIncEa,
									nonSynSWLRegFee, nonSynSWLRegFeeMinInc, nonSynSWLRegFeeAddtl, nonSynSWLPctPub, nonSynSWLPctSW, nonSynSWLRegFeeSW,
									nonSynSWLPctCC, synSWLSemEnableSynd, synSWLSemSyndPerSite, synSWLRegFee, synSWLRegFeeMinInc, synSWLRegFeeAddtl,
									synSWLPctPub, synSWLPctPart, synSWLPctSW, synSWLRegFeeSW, synSWLPctCC, nonSynSWODRegFee, nonSynSWODPctPub,
									nonSynSWODPctSW, nonSynSWODRegFeeSW, nonSynSWODPctCC, synSWODSemEnableSynd, synSWODSemSyndPerSite, synSWODRegFee,
									synSWODPctPub, synSWODPctPart, synSWODPctSW, synSWODRegfeeSW, synSWODPctCC, bunSetupInContract, nonSynSWLInContract, 
									synSWLInContract, nonSynSWODInContract, synSWODInContract)
							VALUES(
								@orgCode,
								<cfqueryparam value="#arguments.SWBundlesEffectiveDateMonth#/1/#arguments.SWBundlesEffectiveDateYear#" cfsqltype="CF_SQL_DATE">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBBunSetUpFee,'[^0-9\.]','','ALL'))#">,
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBBunSetUpMinInC)#">,
								<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBBunSetUpIncEa,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBNonSynSWLRegFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWLRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWLPctCC)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLSemEnableSynd,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.SWBSynSWLRegFeeMinInc)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFeeAddtl,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctPart)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWLRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWLPctCC)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWODRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBNonSynSWODRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBNonSynSWODPctCC)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODSemEnableSynd,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODSemSyndPerSite,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODRegFee,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctPub)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctPart)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctSW)#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(rereplace(arguments.SWBSynSWODRegFeeSW,'[^0-9\.]','','ALL'))#">,
							<cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#val(arguments.SWBSynSWODPctCC)#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWBBunSetUpInContract#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWBNonSynSWLInContract#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWBSynSWLInContract#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWBNonSynSWODInContract#">,
							<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.SWBSynSWODInContract#">
						);
							SET @scheduleID = SCOPE_IDENTITY();
					</cfif>
				</cfif>
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="deleteSchedule" access="public" output="false" returntype="struct" hint="remove page site resource">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="mcproxy_orgCode" type="string" required="yes">
		<cfargument name="scheduleID" type="numeric" required="true">
		<cfargument name="schedType" type="string" required="true">
		
		<cfset var local = structNew()>
		<cfset local.mcsiteID = application.objSiteInfo.getSiteIDFromSiteCode(siteCode='MC')>
		<cfset local.TrialSmithBillingSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TrialSmithBilling',siteID= local.mcsiteID)>
		<cfset local.trialSmithBillingRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.TrialSmithBillingSRID, memberID=session.cfcuser.memberdata.memberID, siteID= local.mcsiteID)>
		
		<cfif NOT local.trialSmithBillingRights.ManageBilling>
			<cfthrow message="invalid request">
		</cfif>
		
		<cftry>
			
			<cfquery name="local.qrySaveAMSHostingInfo" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @orgCode varchar(10) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(arguments.mcproxy_orgCode)#">,
				@scheduleID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.scheduleID)#">,
				@nowDate date = GETDATE();

			<cfswitch expression="#arguments.schedType#">
				<cfcase value="AMSHosting">
					DELETE FROM dbo.memberCentralBillingHosting
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="Fundraising">
					DELETE FROM dbo.memberCentralBillingFundraising
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="LRIS">
					DELETE FROM dbo.memberCentralBillingLRIS
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="Publications">
					DELETE FROM dbo.memberCentralBillingPublications
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="Solicitations">
					DELETE FROM dbo.memberCentralBillingSolicitations
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="APIAccess">
					DELETE FROM dbo.memberCentralBillingAPIAccess
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="EmailBlast">
					DELETE FROM dbo.memberCentralBillingEmailBlast
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="DistrictMatching">
					DELETE FROM dbo.memberCentralBillingDistrict
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="AddressUpdate">
					DELETE FROM dbo.memberCentralBillingAddressUpdate
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="DedicatedServiceMgr">
					DELETE FROM dbo.memberCentralBillingDedicatedServiceMgr
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="EmailHosting">
					DELETE FROM dbo.memberCentralBillingEmailHosting
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="PrivateListServerDomain">
					DELETE FROM dbo.memberCentralBillingPrivateListServerDomain
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="PrivateEmailSendingDomain">
					DELETE FROM dbo.memberCentralBillingPrivateEmailSendingDomain
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="EntPlatformSecurity">
					DELETE FROM dbo.memberCentralBillingEntPlatformSecurity
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="Discretionary">
					DELETE FROM dbo.memberCentralBillingDiscretionary
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="DiscList">
					DELETE FROM dbo.memberCentralBillingDiscLists
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="MktList">
					DELETE FROM dbo.memberCentralBillingMktLists
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="SWMonthly">
					DELETE FROM dbo.memberCentralBillingSWMonthly
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="SWLive">
					DELETE FROM dbo.memberCentralBillingSWLive
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="SWOnDemand">
					DELETE FROM dbo.memberCentralBillingSWOnDemand
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
				<cfcase value="SWBundles">
					DELETE FROM dbo.memberCentralBillingSWBundles
					WHERE scheduleID = @scheduleID
					AND orgCode = @orgCode
					AND effectiveDate > @nowDate;
				</cfcase>
			</cfswitch>
			</cfquery>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
			<cfset local.data.sectionName = "">
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>
</cfcomponent>