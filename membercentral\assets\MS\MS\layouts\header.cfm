<cfoutput>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<cfset local.isLoggedIn = 0>
	<cfif application.objUser.isLoggedIn(cfcuser=session.cfcuser)> 
		<cfset local.isLoggedIn = 'loggedIn'>
	</cfif>

	<cfset local.zone = "A"><!-- Site Logo -->
	<cfset local.zoneAcontent = ''>
	<cfif application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1)>
		<cfset local.zoneAcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
	</cfif>
	
	<cfset local.zone = "C"><!-- Top bar social media -->
	<cfset local.zoneCcontent = ''>
    <cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
        <cfset local.zoneCcontent = trim(REReplace(REReplace(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""),"<ul>",''),"</ul>",""))>
    </cfif>

	<cfset local.zone = "D"><!-- Menu Button -->
	<cfset local.zoneDcontent = ''>
    <cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
        <cfset local.zoneDcontent = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))>
    </cfif>

	<cfset local.menuContent = ''>
	<cfif structKeyExists(local.strMenus,"primaryNav")>
		<div id="menuHolder" class="hideNone">
			#trim(REReplace(REReplace(local.strMenus.primaryNav.menuHTML.rawcontent,"<p>",""),"</p>",""))#
		</div>	
	</cfif> 
	 <!--Header Start-->
      <header class="header outer-width">
         <div class="top-header">
            <div class="container zoneBLgWrap">
               <div class="top-strip">
                  <div class="ts-left-box ">
                     <ul class="toplist">  </ul>
                  </div>

                  <div class="social-links">
                     <ul>
                     
                        <li class="searchBtnFn xsHidden979">
                           <a href="javascript:void(0);">
                              <i class="fa-solid fa-magnifying-glass"></i>
                           </a>
                           <ul>
                              <li class="formframe">
                                 <div>
                                    <form name="searchbox" id="searchbox" action="/" method="get" onsubmit="return validateSearchForm()"> 
                                       <input name="pg" id="pg" type="hidden" value="search">
                                       <input name="s_a" id="s_a" type="hidden" value="doSearch"> 
                                       <input name="s_frm" id="s_frm" type="hidden" value="1"> 

                                       <input name="s_key_all" id="s_key_all" autocomplete="off" placeholder="What are you looking for?" value="" type="text">
                                          <a href="javascript:document.getElementById('searchbox').submit();" class="btnWhite">Search</a>
                                    </form>
                                    <a href="##" class="searchclose">
                                       <svg width="19" height="19" viewBox="0 0 19 19" fill="none"
                                          xmlns="http://www.w3.org/2000/svg">
                                          <path
                                             d="M11.4197 9.49948L18.6026 2.31603C19.1325 1.78616 19.1325 0.927246 18.6026 0.397403C18.0716 -0.132468 17.2127 -0.132468 16.6828 0.397403L9.5 7.58004L2.31715 0.397403C1.78621 -0.132468 0.928323 -0.132468 0.397414 0.397403C-0.132472 0.927273 -0.132472 1.78619 0.397414 2.31603L7.58027 9.49948L0.397414 16.6829C-0.132472 17.2128 -0.132472 18.0717 0.397414 18.6026C0.663402 18.8675 1.01033 19 1.35728 19C1.70528 19 2.0522 18.8675 2.31715 18.6026L9.5 11.42L16.6828 18.6026C16.9478 18.8675 17.2958 19 17.6427 19C17.9897 19 18.3366 18.8675 18.6026 18.6026C19.1325 18.0717 19.1325 17.2128 18.6026 16.6829L11.4197 9.49948Z"
                                             fill="##9A8D83" />
                                       </svg>
                                    </a>
                                 </div>
                              </li>
                           </ul>
                        </li>
                     </ul>
                  </div>
               </div>
            </div>
         </div>
            

         <div id="navbar-example" class="navbar">
            <div class="navbar-inner">
               <div class="container">
                  <div class="row flex-row">
                     <a class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                     </a>
                     #local.zoneAcontent#
                     <div class="mobile-contactus zoneBMobileWrap">
                        <div class="dropdown">
                           <a href="javascript:void(0);" class="dropdown-toggle hideNone" data-toggle="dropdown">
                              <i class="fa fa-ellipsis-v" aria-hidden="true"></i>
                           </a>
                           <div class="dropdown-menu">
                              
                              <div>
                                 <ul class="social-list">
                                    
                                 </ul>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="top-btns">
                        <div class="top-btn-wrap headerBtnWrap">
                           <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                              #local.zoneCcontent#
                              <a href="/?pg=login" class="MAJButton">MEMBER LOGIN</a>
                           <cfelse>
                               <a href="/?logout" class="MAJButton">MEMBER LOGOUT</a>
                           </cfif>
                        </div>
                     </div>
                  </div>
               </div>
               
               <div class="main-navbar">
                  <div class="container">
                     <div class="row-fluid">
                        <div class="nav-collapse collapse">
                           <ul class="nav">
                              <li class="searchBtnFn xs979 headerloginWrap">
                                 <ul>
                                    <li class="span9">
                                       <div>
                                          <form name="searchbox" id="searchbox11" action="/?pg=search" method="get" onsubmit="return validateSearchForm('##searchbox11 ##s_key_all1')"> 
                                             <input name="pg" id="pg" type="hidden" value="search">
                                             <input name="s_a" id="s_a" type="hidden" value="doSearch"> 
                                             <input name="s_frm" id="s_frm" type="hidden" value="1">

                                             <input name="s_key_all" id="s_key_all" autocomplete="off" placeholder="Search our site..." value="" type="text">
                                             <a href="javascript:document.getElementById('searchbox11').submit();">
                                                <img src="/images/white-srch.png" alt="">
                                             </a>
                                          </form>
                                       </div>
                                    </li>
                                 </ul>
                              </li>                              
                           </ul>

                           <div class="hidden-desktop">
                              <div class="top-btn-wrap headerBtnWrap">
                                 <cfif NOT application.objUser.isLoggedIn(cfcuser=session.cfcuser)>
                                    #local.zoneCcontent#
                                    <a href="/?pg=login" class="MAJButton">MEMBER LOGIN</a>
                                 <cfelse>
                                    <a href="/?logout" class="MAJButton">MEMBER LOGOUT</a>
                                 </cfif>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div>
         </div>
         <input type="hidden" name="isLoggedIn" id="isLoggedIn" value="#local.isLoggedIn#">
         <!-- /navbar- -->
      </header>
      <div class="headerSpace"></div>
      <cfset local.zone = "B">
      <cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
         <div id="zoneBObj" class="hide">
            #trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
         </div>
      </cfif>
      <!--Header End-->
</cfoutput>