component name="MCPayECheck" extends="cbmongodb.models.ActiveEntity" collection="MCPayECheck" database="membercentralExtAPI" accessors=false {

	property name="request" schema="true" validate="struct";
	property name="request.method" schema="true" validate="string";
	property name="request.endpoint" schema="true" validate="string";
	property name="request.bodycontent" schema="true";
	property name="response" schema="true" validate="struct";
	property name="response.statuscode" schema="true" validate="numeric";
	property name="response.bodycontent" schema="true";
	property name="response.headers" schema="true" validate="struct";
	property name="timestamp" schema="true" validate="date";

}