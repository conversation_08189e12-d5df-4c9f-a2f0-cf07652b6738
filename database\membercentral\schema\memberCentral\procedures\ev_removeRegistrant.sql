ALTER PROC dbo.ev_removeRegistrant
@registrantID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@AROption char(1),
@cancellationFee decimal(18,2),
@cancellationFeeGLAccountID int,
@deallocateFeeByAllocatedPayments bit

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpRegTrans') IS NOT NULL 
		DROP TABLE #tmpRegTrans;

	DECLARE @orgID int, @ApplicationTypeID int, @minTID int, @assignedToMemberID int, @invoiceProfileID int, @trashID int, 
		@invoiceID int, @GLAccountID int, @invoiceNumber varchar(19), @adjAmount decimal(18,2), @nowdate datetime, @detail varchar(500), 
		@minPaymentTID int, @unAllocAmount decimal(18,2), @allocAmount decimal(18,2), @cancellationFeeTransactionID int, @xmlSchedule xml, 
		@deferredGLAccountID int, @invoiceIDList varchar(max), @eventID int, @eventStartDate date, @defDate date;
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);
	DECLARE @tblOrigPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
	DECLARE @tblCurrentPaymentTransactions TABLE (paymentTransactionID int, allocatedAmountToReg decimal(18,2));
	DECLARE @tblPaymentForReallocationTransactions TABLE (paymentTransactionID int, amountAvailable decimal(18,2));
	DECLARE @tblRegistrants TABLE (registrantID int);
	DECLARE @tblInstances TABLE (instanceID int);
	DECLARE @tblSeats TABLE (seatID int);

	IF @AROption NOT IN ('A','B','C')
		GOTO on_done;

	select @nowdate = getdate();
	select @ApplicationTypeID = dbo.fn_getApplicationTypeIDFromName('Events');

	-- detail is 479 here because we prepend cancellation fee wording below which is 21 chars.
	select @eventID = e.eventID, @orgID = m.orgID, @assignedToMemberID = m.activeMemberID, @detail = left(eventcontent.contentTitle,479)
	from dbo.ev_registrants as r
	inner join dbo.ams_members as m on m.memberID = r.memberID
	inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID
	inner join dbo.ev_events as e on e.eventID = reg.eventID and reg.siteID = e.siteID
	cross apply dbo.fn_getContent(e.eventcontentID,1) as eventcontent
	where r.registrantID = @registrantID;

	-- get registrant and any linked subevent registrants
	INSERT INTO @tblRegistrants (registrantID) 
	select registrantID
	from dbo.ev_registrants
	where recordedOnSiteID = @recordedOnSiteID
	and [status] = 'A'
	and registrantID = @registrantID;

	INSERT INTO @tblRegistrants (registrantID) 
	select registrantID
	from dbo.ev_registrants
	where recordedOnSiteID = @recordedOnSiteID
	and [status] = 'A'
	and parentRegistrantID = @registrantID;

	-- get all reg transactions
	select rt.transactionID, rt.typeID
	into #tmpRegTrans
	from @tblRegistrants as r
	cross apply dbo.fn_ev_registrantTransactions(r.registrantID) as rt
	OPTION(RECOMPILE);

	-- put all open invoices used for registrants into table since they were already created and can be used for adjustments
	insert into @tblInvoices (invoiceID, invoiceProfileID)
	select distinct i.invoiceID, i.invoiceProfileID
	from #tmpRegTrans as rt
	inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = rt.transactionID
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
	inner join dbo.tr_invoiceStatuses as invs on invs.statusID = i.statusID
	where invs.status = 'Open';

	-- payment allocations
	INSERT INTO @tblOrigPaymentTransactions (paymentTransactionID, allocatedAmountToReg)
	select rt.transactionID, sum(atop.allocAmount)
	from #tmpRegTrans as rt
	cross apply dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,rt.transactionID) as atop
	inner join #tmpRegTrans as tmpRev on tmpRev.transactionID = atop.transactionID
	where rt.typeID = 2
	group by rt.transactionID;

	-- ticket instances
	INSERT INTO @tblInstances (instanceID)
	select rpi.instanceID
	from @tblRegistrants as tmp
	inner join dbo.ev_registrantPackageInstances as rpi on rpi.registrantID = tmp.registrantID
	where rpi.status <> 'D';

	INSERT INTO @tblSeats (seatID)
	select s.seatID
	from @tblInstances as tblI
	inner join dbo.ev_registrantPackageInstanceSeats as s on s.instanceID = tblI.instanceID
	and s.status <> 'D';
	
	-- get all registrant-related sales transactions we need to adjust
	IF @AROption = 'A' 
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select rt.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
		from #tmpRegTrans as rt
		inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
		where tsFull.cache_amountAfterAdjustment > 0
		and rt.typeID = 1
		OPTION(RECOMPILE);
	IF @AROption = 'B' 
		INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
		select rt.transactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount, t.creditGLAccountID
		from #tmpRegTrans as rt
		inner join dbo.tr_transactions as t on t.transactionID = rt.transactionID
		cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,t.transactionID) as tsFull
		where rt.typeID = 1
		and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
		OPTION(RECOMPILE);

	BEGIN TRAN;
		-- update registrant status
		UPDATE dbo.ev_registrants
		SET [status] = 'D'
		WHERE registrantID in (select registrantID from @tblRegistrants)
		AND [status] <> 'D';
		
		IF @AROption IN ('A','B') BEGIN
			UPDATE dbo.tr_applications
			SET [status] = 'D'
			WHERE itemID in (select registrantID from @tblRegistrants)
			AND itemType = 'Rate'
			AND applicationTypeID = @ApplicationTypeID
			AND [status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.dataID = tr.itemID and fd.itemType = 'EventRegCustom'
			INNER JOIN @tblRegistrants as tmp on tmp.registrantID = fd.itemID 
			WHERE tr.itemType = 'Custom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.dataID = tr.itemID and fd.itemType = 'ticketPackInstCustom'
			INNER JOIN @tblInstances as tmp on tmp.instanceID = fd.itemID 
			WHERE tr.itemType = 'ticketPackInstCustom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';

			UPDATE tr
			SET tr.[status] = 'D'
			FROM dbo.tr_applications as tr
			INNER JOIN dbo.cf_fieldData as fd ON fd.itemID = tr.itemID and fd.itemType = 'ticketPackSeatCustom'
			INNER JOIN @tblSeats as tmp on tmp.seatID = fd.itemID 
			WHERE tr.itemType = 'ticketPackInstCustom'
			AND tr.applicationTypeID = @ApplicationTypeID
			AND tr.[status] <> 'D';
		END

		-- remove registrant custom fields
		DELETE fd
		FROM dbo.cf_fieldData as fd
		INNER JOIN @tblRegistrants as tmp on tmp.registrantID = fd.itemID and fd.itemType = 'EventRegCustom';

		-- remove from registrant roles
		DELETE FROM dbo.ev_registrantCategories
		WHERE registrantID in (select registrantID from @tblRegistrants);

		-- remove registrant role details
		DELETE fd
		FROM dbo.cf_fieldData as fd
		INNER JOIN @tblRegistrants as tmp on tmp.registrantID = fd.itemID and fd.itemType = 'EventRole';

		-- remove ticket data
		DELETE fd
		FROM dbo.cf_fieldData as fd
		INNER JOIN @tblInstances as tmp on tmp.instanceID = fd.itemID and fd.itemType = 'ticketPackInstCustom';

		UPDATE dbo.ev_registrantPackageInstances
		SET [status] = 'D'
		WHERE instanceID in (select instanceID from @tblInstances)
		AND [status] <> 'D';
		
		DELETE fd
		FROM dbo.cf_fieldData as fd
		INNER JOIN @tblSeats as tmp on tmp.seatID = fd.itemID and fd.itemType = 'ticketPackSeatCustom';

		UPDATE dbo.ev_registrantPackageInstanceSeats
		SET [status] = 'D'
		WHERE seatID in (select seatID from @tblSeats)
		AND [status] <> 'D';

		DELETE FROM dbo.crd_requests
		WHERE registrantID = @registrantID;

		-- if there are adjustments to make
		IF EXISTS (select transactionID from @tblAdjust) BEGIN
			SELECT @minTID = min(transactionID) from @tblAdjust;
			WHILE @minTID IS NOT NULL BEGIN
				select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

				select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
				select @invoiceProfileID = invoiceProfileID from dbo.tr_glAccounts where orgID = @orgID and glAccountID = @GLAccountID;
				select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
				
				-- if necessary, create invoice assigned to registrant based on invoice profile
				IF @invoiceID is null BEGIN
					EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
						@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
						@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

					insert into @tblInvoices (invoiceID, invoiceProfileID)
					values (@invoiceID, @invoiceProfileID);
				END	

				EXEC dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
					@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdate,
					@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, @byPassAccrual=0,
					@xmlSchedule=null, @transactionID=@trashID OUTPUT;
				
				SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
			END
		END

		IF ISNULL(@cancellationFee,0) > 0 BEGIN
			select @invoiceProfileID=null, @invoiceID=null;

			set @detail = 'Cancellation Fee For ' + @detail;
			select @invoiceProfileID = invoiceProfileID from dbo.tr_GLAccounts where orgID = @orgID and glAccountID = @cancellationFeeGLAccountID;
			select @deferredGLAccountID = dbo.fn_tr_getDeferredGLAccountID(@cancellationFeeGLAccountID);

			EXEC dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
				@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
				@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

			-- handle deferred revenue. date should be LATEST of nowDate or event start date.
			IF @deferredGLAccountID is not null BEGIN
				select @eventStartDate = et.startTime
				FROM dbo.ev_events as e 
				inner join dbo.ev_times et on e.eventID = et.eventID   
				INNER JOIN dbo.sites s on s.siteID = @recordedOnSiteID AND s.defaultTimeZoneID = et.timeZoneID
				WHERE e.eventID = @eventID;
				
				IF @eventStartDate is null 
					SET @eventStartDate = @nowdate;

				IF @eventStartDate > @nowdate
					SET @defDate = @eventStartDate;
				ELSE 
					SET @defDate = @nowdate;

				set @xmlSchedule = '<rows><row amt="' + cast(@cancellationFee as varchar(16)) + '" dt="' + convert(varchar(10),@defDate,101) + '" /></rows>';
			END

			EXEC dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@recordedOnSiteID, @assignedToMemberID=@assignedToMemberID, 
				@recordedByMemberID=@recordedByMemberID, @statsSessionID=@statsSessionID, @status='Active', @detail=@detail, 
				@parentTransactionID=null, @amount=@cancellationFee, @transactionDate=@nowdate, 
				@creditGLAccountID=@cancellationFeeGLAccountID, @invoiceID=@invoiceID, @stateIDForTax=null, @zipForTax=null, @taxAmount=0, 
				@bypassTax=1, @bypassInvoiceMessage=0, @bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@cancellationFeeTransactionID OUTPUT;

			SET @invoiceIDList = cast(@invoiceID as varchar(10));

			EXEC dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@recordedByMemberID, @invoiceIDList=@invoiceIDList;

			IF @AROption = 'A' AND @deallocateFeeByAllocatedPayments = 1 BEGIN
				-- get current payment transactions after any changes to deallocations
				; WITH currentRegTrans AS (
					select rt.transactionID, rt.typeID
					from @tblRegistrants as r
					cross apply dbo.fn_ev_registrantTransactions(r.registrantID) as rt
				)
				insert into @tblCurrentPaymentTransactions (paymentTransactionID, allocatedAmountToReg)
				select tmpPay.transactionID, sum(atop.allocAmount) as allocatedAmount
				from currentRegTrans as tmpPay
				cross apply dbo.fn_tr_getAllocatedTransactionsofPayment(@orgID,tmpPay.transactionID) as atop
				inner join currentRegTrans as tmpRev on tmpRev.transactionID = atop.transactionID
				where tmpPay.typeID = 2
				group by tmpPay.transactionID;

				-- get amount of previous allocated payments now available for reallocation
				insert into @tblPaymentForReallocationTransactions (paymentTransactionID, amountAvailable)
				select o.paymentTransactionID, o.allocatedAmountToReg - isnull(c.allocatedAmountToReg,0)
				from @tblOrigPaymentTransactions as o
				left outer join @tblCurrentPaymentTransactions as c on c.paymentTransactionID = o.paymentTransactionID;

				select @minPaymentTID = min(paymentTransactionID) from @tblPaymentForReallocationTransactions where amountAvailable > 0;
				while @minPaymentTID is not null begin
					select @unAllocAmount = null, @allocAmount=null;
					
					select @unAllocAmount = amountAvailable 
					from @tblPaymentForReallocationTransactions
					where paymentTransactionID = @minPaymentTID;

					-- select minimum of cancellationFee and unallocated Payment Amount
					SELECT @allocAmount=MIN(x) FROM (VALUES (@cancellationFee),(@unAllocAmount)) AS value(x);

					EXEC dbo.tr_allocateToSale @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@allocAmount, @transactionDate=@nowDate, 
						@paymentTransactionID=@minPaymentTID, @saleTransactionID=@cancellationFeeTransactionID;

					SET @cancellationFee = @cancellationFee - @allocAmount;
					SET @unAllocAmount = @unAllocAmount - @allocAmount

					update @tblPaymentForReallocationTransactions
					set amountAvailable = @unAllocAmount
					where paymentTransactionID = @minPaymentTID;

					IF @cancellationFee = 0
						BREAK;

					select @minPaymentTID = min(paymentTransactionID) 
						from @tblPaymentForReallocationTransactions
						where paymentTransactionID > @minPaymentTID
						and amountAvailable > 0;
				end
			END
		END
	COMMIT TRAN;


	-- reprocess any applicable conditions
	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;
	CREATE TABLE #tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

	INSERT INTO #tblMCQRun (orgID, memberID, conditionID)
	SELECT distinct c.orgID, @assignedToMemberID, c.conditionID
	from dbo.ams_virtualGroupConditions as c
	where c.orgID = @orgID
	and c.fieldCode = 'ev_entry';

	EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

	IF OBJECT_ID('tempdb..#tblMCQRun') IS NOT NULL 
		DROP TABLE #tblMCQRun;

	on_done:

	IF OBJECT_ID('tempdb..#tmpRegTrans') IS NOT NULL 
		DROP TABLE #tmpRegTrans;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
