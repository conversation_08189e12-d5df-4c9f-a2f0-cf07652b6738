<cfoutput>
	<cfset local.strMenus = application.objCMS.getPageMenus(event=event)>
	<cfset local.logo = ""/>
	<cfif application.objCMS.getZoneItemCount(zone='A',event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['A'],1)>
			<cfset local.logo = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['A'][1].data,"<p>",""),"</p>",""))> 
		</cfif>
	</cfif>
	
	<cfset local.zoneH1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='H' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['H'],1)>
			<cfset local.zoneH1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['H'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<!--Print Header-->
	<div class="printHeader">
		#local.logo#
		<p>https://www.membercentral.com/</p>
	</div>
	<!--Print Header End-->
	<!--Header Start-->
	<header class="header outer-width">
		<div id="navbar-example" class="navbar">
			<div class="navbar-inner">
				<div class="container containerCustom w-1200">
					<div class="row-fluid">
						<a class="btn btn-navbar collapsed" data-toggle="collapse" data-target=".nav-collapse">
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
							<span class="icon-bar"></span>
						</a>
						<a href="/" class="navbar-brand">
							#local.logo#
						</a>
						<div class="nav-collapse collapse">
							<span class="menuWrapper hide">
								<cfif structKeyExists(local.strMenus,"primaryNav")>
									#trim(REReplace(local.strMenus.primaryNav.menuHTML.rawcontent, "<\/?p[^>]*>", "", "all"))#
								</cfif>
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- /navbar- -->
	</header>
	<div class="headerSpace"></div>
	<!--Header End-->
	
</cfoutput>