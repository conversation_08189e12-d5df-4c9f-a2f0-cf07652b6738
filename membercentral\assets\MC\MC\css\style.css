@charset "utf-8";

body { 
    margin: 0; padding: 0; background-color: #fff; font-weight: 400;
    font-family: 'Montserrat', sans-serif;
    font-style: normal;
    font-size: 17px;
    line-height: 1.8;
    color: #1F2225;
}
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
input { outline: none; }
img { max-width: 100%; }
a {color: #1F5FD5;text-decoration: none;}
a:hover, a:focus {color: #0E55A4;text-decoration: underline;}

a.btn,
a.btn:hover {
	transition: all 0.3s ease !important; 
	background-size: 100% 150% !important;
}



.TitleText {
    font-size: 48px;
    font-weight: 700;
    line-height: 1.2;
    font-family: 'Poppins', sans-serif;
    color: #1F2225;
    display: block;
}
.SectionHeader {
    font-size: 14px;
    font-weight: 700;
    color: #29C0E2;
    text-transform: uppercase;
    line-height: 1.2;
    margin: 0 0 10px;
}
.HeaderText {
    font-weight: 700;
    font-size: 40px;
    color: #1F2225;
    text-align: center;
    font-family: 'Poppins', sans-serif;
}
.HeaderTextSmall {
    font-weight: 700;
    font-size: 32px;
    line-height: 1.4;
    font-family: 'Poppins', sans-serif;
    color: #1F2225;
    margin: 0 0 15px;
    }
.SubHeading {
    font-size: 17px;
    color: #2D56A1;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
    display: block;
} 
.ColumnHeader {
    font-size: 22px;
    font-weight: 600;
    color: #2D56A1;
}
.title20 {
    font-size: 20px;
    text-align: center;
    font-weight: 700;
    color: #0E55A4;
    margin:0 0 15px;
    font-family: 'Poppins', sans-serif;
    text-transform: uppercase;
}
.HighlightHeading {
    font-size: 32px;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    color: #1F2225;
}

h1, h2, h3, h4, h5, h6 {
    color: #1F2225;
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    line-height: 1.3;
    }
p, .BodyText,  li {
    font-family: 'Montserrat', sans-serif;
    font-weight: 500;
    font-size: 17px;
    line-height: 1.4;
    color: #1F2225;
    }
p.BodyTextLarge, .BodyTextLarge { font-size: 19px; }
p.InfoText, .InfoText { font-size: 13px; }

.Poppins{font-family: 'Poppins', sans-serif;}
.Montserrat{font-family: 'Montserrat', sans-serif;}

.MCButton {
    border: 2px solid #2D56A1;
    border-style: solid;
    color: #2D56A1;
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 25px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.MCButton.btn-sm {
    padding: 12px 25px;
}
.MCButton:hover {
    background: #2D56A1;
    border-color: #2D56A1;
    color: #ffffff;
    text-decoration: none;
}

.FactButtonBlue {
    border: 2px solid #0E55A4;
    border-style: solid;
    color: #ffffff;
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 25px 10px 49px;
    text-align: center;
    background: #0E55A4;
    text-shadow: none;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    background-image: url('data:image/svg+xml,<svg width="19" height="12" viewBox="0 0 19 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.7148 5.20996C18.8398 5.45996 18.9336 5.70996 18.9336 5.95996C18.9336 6.24121 18.8398 6.49121 18.7148 6.70996C17.8086 8.24121 16.5898 9.45996 15.0586 10.335C13.4961 11.2725 11.7773 11.71 9.93359 11.71C8.05859 11.71 6.37109 11.2725 4.80859 10.335C3.24609 9.45996 2.02734 8.24121 1.15234 6.70996C0.996094 6.49121 0.933594 6.24121 0.933594 5.95996C0.933594 5.70996 0.996094 5.45996 1.15234 5.20996C2.02734 3.70996 3.24609 2.49121 4.80859 1.58496C6.37109 0.678711 8.05859 0.209961 9.93359 0.209961C11.7773 0.209961 13.4961 0.678711 15.0586 1.58496C16.5898 2.49121 17.8086 3.70996 18.7148 5.20996ZM9.93359 10.21C11.4961 10.21 12.9648 9.83496 14.2773 9.05371C15.5898 8.30371 16.6523 7.27246 17.4336 5.95996C16.9336 5.17871 16.3398 4.49121 15.6523 3.86621C14.9648 3.24121 14.1836 2.74121 13.3086 2.36621C13.8711 3.14746 14.1836 4.02246 14.1836 4.95996C14.1836 5.74121 13.9648 6.45996 13.5898 7.08496C13.2148 7.74121 12.6836 8.27246 12.0586 8.64746C11.4023 9.02246 10.6836 9.20996 9.93359 9.20996C9.15234 9.20996 8.43359 9.02246 7.80859 8.64746C7.15234 8.27246 6.62109 7.74121 6.24609 7.08496C5.87109 6.45996 5.68359 5.74121 5.68359 4.95996C5.68359 4.27246 5.83984 3.58496 6.18359 2.95996C6.18359 3.45996 6.33984 3.86621 6.68359 4.20996C7.02734 4.55371 7.43359 4.70996 7.93359 4.70996C8.40234 4.70996 8.80859 4.55371 9.15234 4.20996C9.49609 3.86621 9.68359 3.45996 9.68359 2.95996C9.68359 2.49121 9.49609 2.08496 9.18359 1.74121C7.74609 1.86621 6.46484 2.30371 5.27734 3.05371C4.08984 3.80371 3.12109 4.77246 2.43359 5.95996C3.18359 7.27246 4.24609 8.30371 5.55859 9.05371C6.87109 9.83496 8.33984 10.21 9.93359 10.21Z" fill="white"/></svg>');
    background-repeat: no-repeat;
    background-position: left 20px center;
}
.FactButtonBlue.btn-sm {
    padding: 12px 25px;
}
.FactButtonBlue:hover {
    background-image: url('data:image/svg+xml,<svg width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.7148 5.97058C18.8398 6.22058 18.9336 6.47058 18.9336 6.72058C18.9336 7.00183 18.8398 7.25183 18.7148 7.47058C17.8086 9.00183 16.5898 10.2206 15.0586 11.0956C13.4961 12.0331 11.7773 12.4706 9.93359 12.4706C8.05859 12.4706 6.37109 12.0331 4.80859 11.0956C3.24609 10.2206 2.02734 9.00183 1.15234 7.47058C0.996094 7.25183 0.933594 7.00183 0.933594 6.72058C0.933594 6.47058 0.996094 6.22058 1.15234 5.97058C2.02734 4.47058 3.24609 3.25183 4.80859 2.34558C6.37109 1.43933 8.05859 0.970581 9.93359 0.970581C11.7773 0.970581 13.4961 1.43933 15.0586 2.34558C16.5898 3.25183 17.8086 4.47058 18.7148 5.97058ZM9.93359 10.9706C11.4961 10.9706 12.9648 10.5956 14.2773 9.81433C15.5898 9.06433 16.6523 8.03308 17.4336 6.72058C16.9336 5.93933 16.3398 5.25183 15.6523 4.62683C14.9648 4.00183 14.1836 3.50183 13.3086 3.12683C13.8711 3.90808 14.1836 4.78308 14.1836 5.72058C14.1836 6.50183 13.9648 7.22058 13.5898 7.84558C13.2148 8.50183 12.6836 9.03308 12.0586 9.40808C11.4023 9.78308 10.6836 9.97058 9.93359 9.97058C9.15234 9.97058 8.43359 9.78308 7.80859 9.40808C7.15234 9.03308 6.62109 8.50183 6.24609 7.84558C5.87109 7.22058 5.68359 6.50183 5.68359 5.72058C5.68359 5.03308 5.83984 4.34558 6.18359 3.72058C6.18359 4.22058 6.33984 4.62683 6.68359 4.97058C7.02734 5.31433 7.43359 5.47058 7.93359 5.47058C8.40234 5.47058 8.80859 5.31433 9.15234 4.97058C9.49609 4.62683 9.68359 4.22058 9.68359 3.72058C9.68359 3.25183 9.49609 2.84558 9.18359 2.50183C7.74609 2.62683 6.46484 3.06433 5.27734 3.81433C4.08984 4.56433 3.12109 5.53308 2.43359 6.72058C3.18359 8.03308 4.24609 9.06433 5.55859 9.81433C6.87109 10.5956 8.33984 10.9706 9.93359 10.9706Z" fill="%230E55A4"/></svg>');
    border-color: #0E55A4;
    background-color: #ffffff;
    color: #0E55A4;
    text-decoration: none;
}

.FactButton {
    font-size: 16px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 25px 10px 49px;
    text-align: center;
    text-shadow: none;
    border-radius: 50px;
    font-family: 'Poppins', sans-serif;
    background-image: url('data:image/svg+xml,<svg width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.7148 5.97058C18.8398 6.22058 18.9336 6.47058 18.9336 6.72058C18.9336 7.00183 18.8398 7.25183 18.7148 7.47058C17.8086 9.00183 16.5898 10.2206 15.0586 11.0956C13.4961 12.0331 11.7773 12.4706 9.93359 12.4706C8.05859 12.4706 6.37109 12.0331 4.80859 11.0956C3.24609 10.2206 2.02734 9.00183 1.15234 7.47058C0.996094 7.25183 0.933594 7.00183 0.933594 6.72058C0.933594 6.47058 0.996094 6.22058 1.15234 5.97058C2.02734 4.47058 3.24609 3.25183 4.80859 2.34558C6.37109 1.43933 8.05859 0.970581 9.93359 0.970581C11.7773 0.970581 13.4961 1.43933 15.0586 2.34558C16.5898 3.25183 17.8086 4.47058 18.7148 5.97058ZM9.93359 10.9706C11.4961 10.9706 12.9648 10.5956 14.2773 9.81433C15.5898 9.06433 16.6523 8.03308 17.4336 6.72058C16.9336 5.93933 16.3398 5.25183 15.6523 4.62683C14.9648 4.00183 14.1836 3.50183 13.3086 3.12683C13.8711 3.90808 14.1836 4.78308 14.1836 5.72058C14.1836 6.50183 13.9648 7.22058 13.5898 7.84558C13.2148 8.50183 12.6836 9.03308 12.0586 9.40808C11.4023 9.78308 10.6836 9.97058 9.93359 9.97058C9.15234 9.97058 8.43359 9.78308 7.80859 9.40808C7.15234 9.03308 6.62109 8.50183 6.24609 7.84558C5.87109 7.22058 5.68359 6.50183 5.68359 5.72058C5.68359 5.03308 5.83984 4.34558 6.18359 3.72058C6.18359 4.22058 6.33984 4.62683 6.68359 4.97058C7.02734 5.31433 7.43359 5.47058 7.93359 5.47058C8.40234 5.47058 8.80859 5.31433 9.15234 4.97058C9.49609 4.62683 9.68359 4.22058 9.68359 3.72058C9.68359 3.25183 9.49609 2.84558 9.18359 2.50183C7.74609 2.62683 6.46484 3.06433 5.27734 3.81433C4.08984 4.56433 3.12109 5.53308 2.43359 6.72058C3.18359 8.03308 4.24609 9.06433 5.55859 9.81433C6.87109 10.5956 8.33984 10.9706 9.93359 10.9706Z" fill="%230E55A4"/></svg>');
    border: 1px solid #0E55A4;
    background-color: #ffffff;
    color: #0E55A4;
    background-repeat: no-repeat;
    background-position: left 20px center;
}
.FactButton.btn-sm {
    padding: 12px 25px;
}
.FactButton:hover {
    text-decoration: none;
    border: 1px solid #0E55A4;
    color: #ffffff;
    background: #0E55A4;
    background-image: url('data:image/svg+xml,<svg width="19" height="12" viewBox="0 0 19 12" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.7148 5.20996C18.8398 5.45996 18.9336 5.70996 18.9336 5.95996C18.9336 6.24121 18.8398 6.49121 18.7148 6.70996C17.8086 8.24121 16.5898 9.45996 15.0586 10.335C13.4961 11.2725 11.7773 11.71 9.93359 11.71C8.05859 11.71 6.37109 11.2725 4.80859 10.335C3.24609 9.45996 2.02734 8.24121 1.15234 6.70996C0.996094 6.49121 0.933594 6.24121 0.933594 5.95996C0.933594 5.70996 0.996094 5.45996 1.15234 5.20996C2.02734 3.70996 3.24609 2.49121 4.80859 1.58496C6.37109 0.678711 8.05859 0.209961 9.93359 0.209961C11.7773 0.209961 13.4961 0.678711 15.0586 1.58496C16.5898 2.49121 17.8086 3.70996 18.7148 5.20996ZM9.93359 10.21C11.4961 10.21 12.9648 9.83496 14.2773 9.05371C15.5898 8.30371 16.6523 7.27246 17.4336 5.95996C16.9336 5.17871 16.3398 4.49121 15.6523 3.86621C14.9648 3.24121 14.1836 2.74121 13.3086 2.36621C13.8711 3.14746 14.1836 4.02246 14.1836 4.95996C14.1836 5.74121 13.9648 6.45996 13.5898 7.08496C13.2148 7.74121 12.6836 8.27246 12.0586 8.64746C11.4023 9.02246 10.6836 9.20996 9.93359 9.20996C9.15234 9.20996 8.43359 9.02246 7.80859 8.64746C7.15234 8.27246 6.62109 7.74121 6.24609 7.08496C5.87109 6.45996 5.68359 5.74121 5.68359 4.95996C5.68359 4.27246 5.83984 3.58496 6.18359 2.95996C6.18359 3.45996 6.33984 3.86621 6.68359 4.20996C7.02734 4.55371 7.43359 4.70996 7.93359 4.70996C8.40234 4.70996 8.80859 4.55371 9.15234 4.20996C9.49609 3.86621 9.68359 3.45996 9.68359 2.95996C9.68359 2.49121 9.49609 2.08496 9.18359 1.74121C7.74609 1.86621 6.46484 2.30371 5.27734 3.05371C4.08984 3.80371 3.12109 4.77246 2.43359 5.95996C3.18359 7.27246 4.24609 8.30371 5.55859 9.05371C6.87109 9.83496 8.33984 10.21 9.93359 10.21Z" fill="white"/></svg>');
    background-position: left 20px center;
    background-repeat: no-repeat;
}

.WhiteBorder {
    border: 2px solid #FFFFFF;
    border-style: solid;
    color: #FFFFFF;
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 25px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.WhiteBorder.btn-sm {
    padding: 12px 25px;
}
.WhiteBorder:hover {
    background: #FFFFFF;
    border-color: #FFFFFF;
    color: #1F5FD5;
    text-decoration: none;
}


.TextButton {
    border: 2px solid transparent;
    border-style: solid;
    color: #2D56A1;
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 10px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.TextButton::before {
    content: "\f061";
    margin-right: 10px;
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
}
    
.TextButton:hover {
    color: #0E55A4;
    text-decoration: none;
}

.TextButtonWhite {
    border: 2px solid transparent;
    border-style: solid;
    color: #ffffff;
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 26px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.TextButtonWhite::before {
    content: "\f061";
    margin-right: 10px;
    font-family: "Font Awesome 6 Pro";
    font-weight: 900;
}
    
.TextButtonWhite:hover {
    color: #1F5FD5;
    text-decoration: none;
    background: #ffffff;
}
.MCWhiteBorder {
    border: 2px solid #ffffff;
    
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 15px 30px;
    text-align: center;
    background: #2D56A1;
    text-shadow: none;border-radius: 50px;
}
.MCWhiteBorder:hover {
    background: #29C0E2;
    border-color: #29C0E2;
    color: #ffffff;
    text-decoration: none;
}

.BlueButton {
    border: 2px solid #0E55A4;
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 13px 30px;
    text-align: center;
    background: #0E55A4;
    text-shadow: none;
    border-radius: 50px;
    text-shadow: none;
}
.BlueButton:hover {
    background: transparent;
    border-color: #2D56A1;
    color: #2D56A1;
    text-decoration: none;
}
.CTAWhiteBorder {
    border: 1px solid #ffffff;
    color: #ffffff;
    font-size: 22px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 8px 30px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.CTAWhiteBorder:hover {
    background: #2D56A1;
    border-color: #ffffff;
    color: #ffffff;
    text-decoration: none;
}
.LinkButton {font-size: 16px;color: #0655A3;font-weight: 700;text-decoration: none;}
.LinkButton:hover {color: #29C0E2;text-decoration: none;}

.blue {
    color: #2D56A1 !important;
}
.cyan {
    color: #29C0E2 !important;
}
.green {
    color: #6D9F40 !important;
}
.clr-1 {
    color: #79257B !important;
}
.bg-blue {
    background: #2D56A1 !important;
}

.pd_40 { padding: 40px 0px; }
.pd_50 { padding: 50px 0px; }
.pd_60 { padding: 60px 0; }
.pd_70 { padding: 70px 0; }
.pd_30 { padding: 30px 0; }

.mb-5 { margin-bottom: 5px !important; }
.mb-30 { margin-bottom: 30px !important; }
.mb-40 { margin-bottom: 40px !important; }
.mb-50 { margin-bottom: 50px !important; }

.gray-bg { background: #F1F1F1; }
.clearfix::before, .clearfix::after { content: ""; display: table; width: 100%; clear: both; }
.container.containerCustom { padding: 0 15px; }

*::-webkit-input-placeholder {   }
*::-moz-placeholder {   }
*:-ms-input-placeholder {   }
*:-moz-placeholder {   }



.form-design .controls *::-webkit-input-placeholder  { color: #1F2225;}
.form-design .controls *::-moz-placeholder  { color: #1F2225;}
.form-design .controls *:-ms-input-placeholder  { color: #1F2225;}
.form-design .controls *:-moz-placeholder  { color: #1F2225;}
.xs979 { display: none !important; }
.xs767, .xsVisible { display: none !important; }
.xsHidden979 { display: block !important; }
.xsHidden767, .xsHidden { display: block !important; }
.textUnderline { text-decoration: underline; }


/***Header***/
.printHeader, .printFooter { display: none; }
.header {background: #ffffff;position: fixed;width: 100%;top: 0;z-index: 999;}
.headerSpace {width: 100%;height: 72px;background-color: transparent;}
.header .navbar { margin-bottom: 0; }
.header .navbar-inner {border: none;-moz-border-radius: 0;-ms-border-radius: 0;-o-border-radius: 0;-webkit-border-radius: 0;border-radius: 0;-moz-box-shadow: none;-ms-box-shadow: none;-o-box-shadow: none;-webkit-box-shadow: none;box-shadow: none;padding: 0;min-height: inherit;background: #ffffff;}
.header .navbar-brand {
    margin-left: 0px;
    float: left;
    max-height: 100%;
    padding: 12px 40px 10px 0px;
    width: 310px;
    text-align: center;
}
.header .navbar .nav>li:not(.btn-link)>a {
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    line-height: 1.4;
    color: #30313C;
    text-shadow: none;
    position: relative;
    z-index: 1;
    padding: 25px 18px;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
}
header .navbar .nav li form .btn-flex {
    align-items: center;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree ul {
    margin: 0;
    display: block;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree ul li {
    display: block;
}

.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree ul li a {
    color: #ffffff;
}

.header .navbar .nav li.dropdown .megaMenuSection .header-drop-title h2 {
    color: #2D56A1;font-size: 36px;font-weight: 600;
}

.header .navbar .nav>li.dropdown>a:after {width: 6px;height: 6px;content: "";z-index: 1;position: relative;top: -3px;border: 1px solid #0E55A4;display: inline-block;border-style: none solid solid none;transform: rotate(45deg);margin-left: 8px;}

.header .navbar .nav>li:nth-last-child(5):focus>a, .header .navbar .nav>li:nth-last-child(5):visited>a, .header .navbar .nav>li:nth-last-child(5)>a:visited, .header .navbar .nav>li:nth-last-child(3):focus>a, .header .navbar .nav>li:nth-last-child(3):visited>a, .header .navbar .nav>li:nth-last-child(3)>a:visited { background-color: #f1b828; color: #fff; }

.header .navbar .nav li:nth-last-child(3) {}
.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.mainMenu li { display: block; width: 100%; margin-bottom: 10px; }
.header .navbar .nav li:nth-last-child(3) ul.memberSection ul.mainMenu li a { color: #fff; }
.header .navbar .nav li.dropdown .memberSection li, .header .navbar .nav li.dropdown .memberSection li p, .header .navbar .nav li.dropdown .memberSection li a { 
    color: #fff; display: inline-block; padding: 0; font-weight: 300; font-size: 16px;
}
.header .navbar .nav li.dropdown .memberSection li p {margin-bottom: 20px;font-size: 18px;}
.header .navbar .nav li.dropdown .memberSection li a { text-decoration: underline; }
.header .navbar .nav li.dropdown .memberSection li label { font-weight: 300; font-size: 16px;  }
.header .navbar .nav li.dropdown .memberSection li input {
    background-color: #fff;
    border: 0;
    height: 45px;
    border-radius: 0;
    width: 100%;
    margin-bottom: 15px;
    color: #2d2d2d;
    font-weight: 400;
    padding: 0 10px;
}
.header .navbar .nav li.dropdown .memberSection li input:focus {
    box-shadow: none;
}
.mainMenuMob-list li>a>img {margin: 0 auto 10px;height: 45px;display: inline-block;}
.header .navbar .nav li.dropdown .memberSection li form a.btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; display: inline-block; width: auto; text-decoration: none; }
.header .navbar .nav li.dropdown .memberSection li form a.btn:hover { background: #fff; color: #2d3e55; }
.header .navbar .nav li.dropdown .memberSection li form a { width: 50%; float: left; }
font .navbar .nav li.dropdown .memberSection li form a.WhiteBorder { border: 2px solid; font-size: 18px; font-weight: 700; height: 52px; min-width: 120px; text-transform: capitalize; border-radius: 0px;   display: inline-block; vertical-align: middle; line-height: 46px; margin: 0;  box-shadow: none; text-shadow: none; padding: 0 25px; text-align: center; width: auto; font-weight: 400; text-decoration: none; }
.header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:hover, .header .navbar .nav li.dropdown .memberSection li form a.WhiteBorder:focus { background: #BA0C2F; color: #ffffff; border-color: #BA0C2F; }
.header .navbar .nav li.dropdown .memberSection li form a:last-child {   font-size: 14px; text-align: left; padding: 0px; text-decoration: none; margin-left: 15px; margin-top: 5px; text-transform: inherit; }
.header .navbar .nav li.dropdown li a:hover, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:hover, .header .navbar .nav li.dropdown li a:focus, .header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a:focus, .header .navbar .nav li .dropdown-menu>li:hover a { background: transparent; }
.dropdown li { padding: 0 0px; margin-left: 0;line-height: 1.4;}
.header .navbar .nav li.btn-link {margin-left: 30px;}
.header .navbar .nav > li.searchBtnFn > a {padding-top: 25px;padding-bottom: 15px;}
.header .navbar .nav li.active a { color: #0BBA97; background-color: #ffffff; border-color: #eeeeee; box-shadow: none; }
.header .navbar .nav li.dropdown.megamenu {display: inline-block;padding: 0;position: static;}
.header .navbar .nav li.dropdown:not(.megamenu) {
    position: relative;
}
.header .navbar .nav li a:hover, .header .navbar .nav li a:focus, .header .navbar .nav li:hover>a {
     background: transparent;
     color: #2D56A1;
     box-shadow: none;
}
.header .nav-collapse.collapse { margin: 0; }
.header .nav-collapse .nav {
    margin: 0;
    float: right;
    width: auto;
    position: static;
    display: inline-flex;
    align-items: center;
    margin-top: 0;
    }
.header .navbar .nav>li>.dropdown-menu::after, .header .navbar .nav>li>.dropdown-menu::before { display: none; }
.dropdown-menu>li>a { color: #3b3b3c; }
.header .navbar .nav li .dropdown-menu>li>a { padding: 7px 10px; font-size: 11px; line-height: 16px; border-right: none; text-align: left; white-space: normal; }
.header .dropdown-menu { -moz-box-shadow: none; -ms-box-shadow: none; -o-box-shadow: none; -webkit-box-shadow: none; box-shadow: none; }
.header .navbar .nav li .dropdown-menu>li:last-child a { border-bottom: none; }
.dropdown-menu { width: 215px; }
.navbar .nav>li.btn-link>a.MCButton {
    border: 2px solid #2D56A1;
    border-style: solid;
    color: #2D56A1;
    font-size: 18px;
    line-height: 26px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 10px 25px;
    text-align: center;
    background: transparent;
    text-shadow: none;
    border-radius: 50px;
}
.navbar .nav>li.btn-link>a:hover {background: #2D56A1;border-color: #2D56A1;color: #ffffff;}
.dropdown-submenu>.dropdown-menu { border: none; padding: 0; margin: 0; }
.dropdown-submenu>a::after { /*display: none;*/ }
.header .navbar .nav li .dropdown-menu>li.dropdown-submenu ul li a { border: none; background: rgba(0, 0, 0, 0.1); }
.navbar .nav li.dropdown.open>.dropdown-toggle, .navbar .nav li.dropdown.active>.dropdown-toggle, .navbar .nav li.dropdown.open.active>.dropdown-toggle, .navbar .nav li.active>.dropdown-toggle 
{ color: #2d2d2d; background-color: #ffffff;text-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
font-weight: 600; border-color: #eeeeee; box-shadow: none; }
 [data-toggle="dropdown"] {
 display: none;
}
.header .navbar .nav li.dropdown .megaMenuSection li a.active {
    color: #eca950;
    font-weight: 700;
}
.dropdown-menu { border-radius: 0; background: #2D55A3; }
.header .navbar .nav li.dropdown li a {padding: 15px 20px;border: none;margin-bottom: 0px;color: #ffffff;line-height: 1.42857;font-size: 16px;font-weight: 400;}
.header .navbar .nav li.dropdown .megaMenuSection .heading { max-width: 215px; margin: 0; top: 50%; transform: translateY(-50%); position: absolute; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading { text-transform: uppercase; font-weight: 500; width: 100%; max-width: 308px; text-align: right; }
.header .navbar .nav li.dropdown .megaMenuSection .searchHeading p.TitleText {
    color: #fff;
    border: 0;
    text-shadow: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .heading .TitleText { line-height: normal; color: #fff; line-height: normal; font-size: 38px; font-weight: 500; margin: 0; text-transform: capitalize; border:0; box-shadow: none; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe {width: 100%;padding: 0;border-radius: 5px;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input {float: left;background: #DDD8D3 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAA0AAAAOCAYAAAD0f5bSAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFmSURBVHgBjVI7TsNAEJ3ZJYoRjTtERbjBigQBnXMDSjrgBk46qsAFgnMCUkLJCeIuIH7mBqYBSlcoKNkdZjc/yzESI+1vdt6bL8JMKipUUsorAlII4CNgQkS90Uu3DwVBu3n11glfVz5n0h89d8/yCumpsAZCxFMG6gHSuRbUEwYfCCFg1oO1rUOcfA7jhSev3r7m87SM0WuEAZAYEEC2rs1OlkSZ1QtegbPQ5rIY1+gpYnaMbY7fUgZzvQXVnEESpVAiRObNGRL5C5B1bS++Cv0ykEDctqdBzJY6Lq3zJERYBNgiMemRM9Q6WYaHepoLQqe62w5zgACkGLgQge7y4U/71Ghf8E9nkQeHbJPPv40wzfFj5LxJu00+hjH34p2viml4GsAjYiDCDQNSfiskPK5s7t9Ovu4zLOZR2QuVPTfGkM77whPT56B4aiDl1jRXQH9Jtd565aJZwlT8F/SjqckFSWyCv0wrhb9anqj3AAAAAElFTkSuQmCC');border: 0;color: #33383A;background-position: left 20px center;width: calc(100% - 140px);background-repeat: no-repeat;font-size: 15px;height: auto;display: inline-block;margin: 0;height: 50px;box-shadow: none;outline: none;padding: 0 15px 0 50px;font-weight: 300;font-style: italic;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-webkit-input-placeholder {color: #33383A; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input::-moz-placeholder {color: #33383A;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-ms-input-placeholder {color: #33383A; }
.header .navbar .nav li.dropdown .megaMenuSection .formframe input:-moz-placeholder {color: #33383A;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a {float: right;color: #fff;background: #ed3943;border: 2px solid #ed3943;font-size: 14px;font-weight: 900;height: 50px;min-width: auto;text-transform: uppercase;line-height: 46px;margin: 0;box-shadow: none;text-shadow: none;padding: 0 25px;display: inline-block;width: auto;border-radius: 50px;letter-spacing: 0.1em;}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a:hover {background: #eca94f;color: #fff;border: 2px solid #eca94f;text-decoration: none;}
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn { color: #fff; background: transparent; border: 2px solid #fff; font-size: 13px; font-weight: 500; height: 50px; min-width: auto; text-transform: uppercase; border-radius: 0px;   line-height: 46px; padding: 0; margin: 0; box-shadow: none; text-shadow: none; padding: 0 25px; margin-top: 20px; display: inline-block; width: auto; }
.header .navbar .nav li.dropdown .megaMenuSection .heading .btn:hover { background: #fff; color: #2d3e55; border-color: #fff; }
.header .navbar .nav li.dropdown .megaMenuSection li a {
    color: #ffffff;
    text-decoration: none;
    text-align: left;
    font-weight: 400;
    font-size: 16px;
    display: block;
    padding: 0px 0;
    }
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree p.HeaderText {
    font-size: 20px;
    margin: 0 0 10px;
    text-transform: none;
    font-weight: 600;
    border-bottom: 1px solid #ffffff;
    display: block;
    padding-bottom: 10px;
    margin-bottom: 15px;
}
.header .nav-collapse .nav .dropdown.headerlogin>ul.memberSection {
    padding: 80px 40px 80px 500px;
}
.header .navbar .nav li.dropdown .megaMenuSection li a:hover,
.header .navbar .nav li.dropdown .megaMenuSection li a:focus {text-decoration: none;}
.header .navbar .nav li.dropdown .megaMenuSection li .subMenu { padding-left: 20px; }


header .navbar .nav li form p {
    font-size: 14px;
    color: #ffffff;
    text-transform: uppercase;
    width: 100%;
    font-weight: 700;
    margin: 0 0 2px;
}
header .navbar .nav li form .pwd-input,
header .navbar .nav li form .login-input {
    border: 1px solid #aeaeae;
    height: 33px;
    flex: 0 0 calc(50% - 50px);
    max-width: calc(50% - 50px);
    margin: 0;
    background-color: transparent;
    border-radius: 0;
    border-color: #ffffff;
    color: #ffffff;
    box-shadow: none;
}
.searchBtnFn .default { display: block; }
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection {
    display: inherit;
    width: 370px;
    margin: 0 !important;
    padding: 0;
}
.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.formDiv {
    display: inherit;
    width: 100%;
    max-width: 100%;
    margin: 0;
    padding: 32px 80px 25px 20px;
    position: relative;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxone {
    width: 400px;
    background: #ffffff;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    text-align: center;
    display: flex !important;
    align-items: center;
    justify-content: center;
    padding: 20px;
    margin: 0 !important;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxtwo {
    display: inherit;
    width: 60%;
    margin-left: 0 !important;
}
.header .nav-collapse .nav .dropdown .dropdown-menu.memberSection li.megaMenuSection.member-boxthree {
    display: inherit;
    width: 40%;
    margin-left: 0 !important;
    padding-left: 6%;
}
.header .navbar .nav li.dropdown .memberSection li.member-boxtwo a.SANButton {
  margin: 20px 15px 0 15px;
}
.header .navbar .nav li .memberSection a.toggle-form {
    position: absolute;
    top: 30px;
    right: 30px;
    z-index: 1;
    color: #ffffff;
    padding: 0;
    text-transform: uppercase;
    font-weight: 400;
    font-size: 20px;
}
.social-mobile, .mobile-links { display: none; }
.header .navbar .nav li form a {
    padding: 0;
    color: #ffffff;
}
header .navbar .nav li form a.MAJButton:hover {
    background: #ffffff;
    border-color: #ffffff;
}

header .navbar .nav li form {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin: 0;
}

header .navbar .nav li form a.MAJButton {
    background: #472103;
    padding: 9px 10px;
    color: #ffffff;
    height: auto;
    width: 80px;
    text-align: center;
    font-size: 14px;
}

header .navbar .nav li.headerlogin {
    background: #ed3943;
    width: 205px;
    max-width: 215px;
    min-height: 116px;
    padding: 0;
}

header .navbar .nav li form input.login-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAAVCAYAAABLy77vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEbSURBVHgBpZQBEcIwDEVTDgGTUAdMAhImAQk4AAdIYA44FAwHDAUFBeCgJPDLhW7rVvh3vdyW9PWTlBlKyHtfcCjx2BpjHpQjBlheje9qD/hkyB0bJZ4j6HkSjIscNtR6Aw4Iue0YZIlCl3AbnH65mkW1S8RjH4ibfeXQ8hKITYGCUtMJueE+sd0VrDcDeauangQVamKbntwhXAMak3L1ciYT4rVTBzhxRlMEmPNdNaMQjH6vizlWcLSWvHrXoNbGkE108ipx4CGqrUJCT0JOL0ecW7ivseceEuvJk+hCHfaWciEt3l8oXy2iFdACD1fK102Dij9An7/LnN4/SRos0zhRnqqPCf/9nflFtZBMQPv3hbOUJ/mOvxr+BDf719dvV9PeAAAAAElFTkSuQmCC');
    background-repeat: no-repeat;
    background-position: center left 8px;
    padding-left: 35px;
}

header .navbar .nav li form input.pwd-input {
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBnZIBEYMwDEXTKaiESaiDgYM5KA42B+BgErgpQMIkgIPhgDnIwvi79VivDfy7f21peE3aECnEzFZci5/800NchXFGAXIydGIrvosHzOfvXjyKS2PMmAMdkU0/Z5faJ0VWLYJt5kBelxwLzActcd18h6kAB5ijPKwSTwfKayKdbAo2Yiwpr5N4SMEuGF+JmM8DyFDQ0jbRgFpz+WjmHi9+VINkfcZFO/gKyGaQ539N6MM8CKf7ANSgrAJ78UaOZYQyvmpII01GpNX6B5nfNme0glXB2u8CBbCWl2evd4MCGO8uLdAb5j6QM8wR6hAAAAAASUVORK5CYII=');
    background-repeat: no-repeat;
    background-position: center left 8px;
    padding-left: 35px;
}
.header .navbar .nav li form a:last-child {
    font-weight: 400;
}
.header-drop-title {
    display: table-cell;
    width: 30%;
    vertical-align: top;
    padding: 40px;
background: #f0f0f0;}
.top-strip-wrap {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    width: 100%;
}
header .top-strip {
    display: flex;
    justify-content: end;
    padding: 0;
    z-index: 1;
    position: absolute;
    top: 0;
    right: 0;
    padding-right: 340px;
}
.top-strip-wrap .container.containerCustom {
    position: relative;
}
header .top-strip ul {
    list-style: none;
    padding: 0px;
    margin: 0px;
    display: flex;
}
header .top-strip ul li a {
    display: inline-block;
    padding: 5px 10px;
    color: #ed3943;
    font-size: 12px;
}
.mainMenuMob {
    display: table-cell;
    width: 70%;
    vertical-align: top;
}
.mainMenuMob .mainMenuMob-col {
    display: inline-block;
    float: left;
    width: 33.33%;
    padding: 40px;
}
.mainMenuOnclickBtn { cursor: pointer; }

a {}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a {
    position: relative;
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a>.nav_icon {
    position: absolute;
    left: 15px;
    top: 15px;
    width: 45px;
    height: 45px;
    border-radius: 4px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu>a>.nav_icon img {
    filter: grayscale(100) brightness(100);
    -webkit-filter: grayscale(100) brightness(100);
    -moz-filter: grayscale(100) brightness(100);
    -ms-filter: grayscale(100) brightness(100);
    -o-filter: grayscale(100) brightness(100);
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a>h4 {
    color: #333;
    margin: 0 0 5px;
    font-size: 15px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection>a>p {
    color: #1F2225;
    font-size: 15px;
    font-weight: 500;
    font-family: "Montserrat", sans-serif;
    margin: 0;
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu>a>h4, .header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection.show-menu>a>p {
    color: #fdfdfd;
}

.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul {
    list-style: none;
    padding: 0;
    margin: 0 0 0 -30px;
}

.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li {
}
.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a {
    padding: 20px 30px;
    position: relative;
    display: flex;
    gap: 10px;
    align-items: center;
}

.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a:hover {
    background: #0000000D;
}

.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a:hover .link-icon {
    background: #F5A800;
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection:hover>a {
    background: #00000008;
}

.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection:hover>a>.nav_icon {
    background: #F5A800;
}
.header .nav-collapse .nav .dropdown.megamenu .dropdown-menu li.megaMenuSection:hover>a>.nav_icon img {
    filter: grayscale(100) brightness(100);
    -webkit-filter: grayscale(100) brightness(100);
    -moz-filter: grayscale(100) brightness(100);
    -ms-filter: grayscale(100) brightness(100);
    -o-filter: grayscale(100) brightness(100);
}
.header .navbar .nav li.dropdown .megaMenuSection li .menubox ul li a .link-icon {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #F5A800;
    background: transparent;
    border-radius: 5px;
    margin-top: 0;
}
/*-------Slider-----***/
.slider {position: relative;background: #ffffff;z-index: 0;margin-bottom: 60px;padding-top: 95px;}
.slider .item {background-repeat: no-repeat;background-size: cover;background-position: center;min-height: 400px;position: relative;padding: 60px 0;z-index: 2;padding-bottom: 256px;}

.slider .item>img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    object-fit: contain;
    object-position: top right;top: 0;
    left: 0;
}
.slider .item:before {content: "";position: absolute;width: 100%;height: 100%;display: block;background: #ffffff;left: 0;background: radial-gradient(67.91% 78.89% at 2.43% 100%, #8EE3F6 0%, #FFFFFF 100%) /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */;top: 0;z-index: -1;transform: scaleX(-1);}
.slider .owl-dots {position: relative;margin: -20px auto 0 !important;bottom: auto;width: auto !important;display: inline-block !important;left: 0;transform: translateY(0%);display: flex !important;flex-direction: row;top: -45px;max-width: 1170px;justify-content: flex-end;}
.owl-theme .owl-dots .owl-dot {outline: none;background: transparent;margin: 5px;}
.owl-theme .owl-dots .owl-dot span {
    background: #ed3943;
    margin: 0;
}
.slider .row.d-flex-wrap .span6 {flex: 0 0 50%;margin: 0;padding: 0 15px;}
.owl-theme .owl-dots .owl-dot.active span, .owl-theme .owl-dots .owl-dot:hover span {
opacity: 1;background: #ffffff;}
.carousel-caption {background: transparent;max-width: 1180px;margin: 0 auto;position: relative;top: 0;left: 0;padding: 0;}
.captionFrame {}
.captionFrame ul li {
    font-size: 17px;
    color: #1F2225;
    font-weight: 500;
    line-height: 1.45;
    margin-bottom: 15px;
    border: 0;
    text-shadow: none;
    text-align: left;
}
.captionFrame ul li:nth-child(1) {
    margin-bottom: 12px;
    font-weight: 700;
    color: #2D56A1;
    margin: 0 0 10px;
    text-transform: uppercase;
}
.captionBtnBox { position: absolute; right: 0; width: 100%; max-width: 1920px; height: 100%; top: 0; left: 0; margin: 0 auto; }
.captionBtnBox .captionBtnFrame ul {
    margin: 0px;
}
.captionBtnFrame {
    background-color: rgb(12 33 52 / 65%);
    position: absolute;
    right: 0;
    width: 100%;
    max-width: 423px;
    height: 100%;
    padding: 42px 34px;
    top: 0;
    z-index: 1;
}
.captionBtnBox ul li { width: 100%; overflow: hidden; position: relative; margin-bottom: 30px; }
.captionBtnBox ul li:last-child { margin-bottom: 0px; }
.captionBtnBox ul li a {
    padding: 15px 22px 15px 22px;
    display: flex;
    align-items: center;
    width: 100%;
    background: rgba(255, 255, 255, 0.0);
    border: 1px solid #ffffff;
    min-height: 98px;
}
.captionBtnBox ul li a:hover { background: rgba(255, 255, 255, 1.0); }
.captionBtnBox ul li a .iconBox { width: 50px; float: left; margin: 0px 0px; text-align: center; }
.captionBtnBox ul li a .iconBox img {margin: 0 auto;padding-top: 2px;filter: contrast(0)brightness(100);width: 43px;height: 43px;object-fit: contain;}
.captionBtnBox ul li a .iconBox svg path {
    fill: #ffffff;
}

.captionBtnBox ul li a .iconBox img.default { display: block; }
.captionBtnBox ul li a .textBox {
    position: absolute;
    left: 100px;
    top: 50%;
    transform: translateY(-50%);
    max-width: 200px;
    overflow: hidden; 
}
.captionBtnBox ul li a .textBox h2 { 
    margin: 0; padding: 0; color: #fff; font-size: 20px; font-weight: 500; line-height: 1.3; 
}
.captionBtnBox ul li a .arrow { 
    float: right;
    padding: 19px 0;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
    color: #ffffff;
    font-size: 20px;
 }
 .captionBtnBox ul li a:hover .textBox h2,
 .captionBtnBox ul li a:hover .arrow  {
    color: #2d2d2d;
} 
.captionBtnBox ul li a:hover .iconBox svg path {
    fill: #2d2d2d;;
}
.captionBtnBox ul li a:hover .iconBox img {
    filter: none;
}
.captionBtnBox.captionBtnBox-mb { display: none; }

/*-------FriendsLogoBox Css----------*/
.section-HeaderText {
    color: #08173A;
    font-size: 30px;
    font-weight: 700;
    margin-bottom: 60px;
}
.BlackLine, .WhiteLine, .GreenLine { position: relative; }
.BlackLine:before, .WhiteLine:before {
    content: '';
    position: absolute;
    bottom: -15px;
    left:0;
    right:0;
    margin:0 auto;
    width: 200px;
    height: 3px;
    background: #0C1F4F;
}
.WhiteLine:before { background: #ffffff; }
.friendsSliderBox .HeaderText {
    background: #fff;
    padding: 8px 14px;
    display: inline-block;
    font-size: 14px;
    text-transform: uppercase;
    color: #6C6C6C;
    margin-bottom: 40px;
}
.friendsSliderBox .owl-carousel .owl-item img {
    width: auto;
    height: 50px;
    margin: 0 auto;
    object-fit: contain;
    }
.friendsSliderBox .owl-carousel ul li {
    display: inline-block;
    vertical-align: middle;
    width: 16.66%;
    
    padding: 15px 10px 0;
text-align: center;}
.friendsSliderBox .owl-carousel ul {display: flex;align-items: center;margin: 0 auto;justify-content: center;}
.friendsSliderBox .owl-carousel .item ul {margin: 0 auto;}
.friendsSliderBox .owl-carousel .item {text-align: center;}
.friendsSliderBox {margin-bottom: 0;position: relative;padding: 0 50px;}
.friendsSliderBox .owl-carousel ul li a {display: inline-block;margin: 0 auto;}
.owl-carousel .owl-nav button.owl-prev span, .owl-carousel .owl-nav button.owl-next span {color: #B3B3B3;font-size: 40px;padding: 0;width: 40px;display: inline-block;line-height: 32px;height: 40px;margin-top: 0;border-radius: 5px;}
.owl-carousel .owl-nav button.owl-prev, .owl-carousel .owl-nav button.owl-prev:hover {
    position: absolute;
    top:50%;
    margin: -12px 0 0 0;
    left: -40px;
    height: auto;
    display: inline-block;
    background: transparent;
}
.owl-carousel.owl-theme .owl-nav [class*=owl-]:hover span {
    color: #ffffff;
    background: #2D56A1;
}
.owl-carousel .owl-nav button.owl-next, .owl-carousel .owl-nav button.owl-next:hover {
    right: -40px;
    position: absolute;
    top: 50%;
    margin: -12px 0 0 0;
    height: auto;
    display: inline-block;
}
.friendsSliderBox.friendsSliderBox-mobile {
    display: none;
}

.friendsLogoBox .tab-content .tab-pane {
    display: block;
}
.friendsLogoBox .tab-content {
    position: relative;
}
.friendsLogoBox .tab-content .tab-pane:not(.active) {
    opacity: 0;
    position: absolute;
    z-index: -999;
    width: 100%;
    height: 100%;
    overflow: hidden;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
}

/*--------Become A Member---------***/
.member-boxleft {
    display: inline-block;
    vertical-align: middle;
    width: 67%;
}
.member-boxright {
    display: inline-block;
    vertical-align: top;
    padding-left: 18px;
    margin-top: 70px;
}
.member-right {
    border-left: 1px solid #0C1F4F;
    padding-left: 50px;
}
.member-boxleft h3, .member-boxright h3 {
    margin: 0 0 15px 0;
}

/**--------Footer---------***/
.footer-info p {
    font-size: 16px;
    line-height: 25px;
    margin: 25px 0;
    

}
ul.follow-us li { display: inline-block; vertical-align: middle; font-size: 19px; font-weight: 500;

text-transform: uppercase; margin:0 4px; }
ul.follow-us li:first-child { margin-right: 20px; }
ul.follow-us li a {
    display: block;width: 40px; height: 40px; border-radius: 50%;
    border:2px solid transparent; line-height: 40px; color: #0BBA97; font-size: 20px; text-align: center;
}
ul.follow-us li a:hover, ul.follow-us li a:focus {
    border-color: #BA0C2F; color: #BA0C2F;
}
.footer-links.contact-links ul li {margin-bottom: 18px;color: #4d4d4d; position: relative; }
.contact-links ul li i {
    color: #BA0C2F;
    font-size: 20px;
    width: 30px;
}
.contact-links ul li span {
    display: inline-block; vertical-align: top; width: calc(100% - 50px);
}
.contact-links ul li>img {
    position: absolute;
    left: 0;
    top: 0;
    height: 25px;
    width: 25px;
    object-fit: contain;
}
.copyright {
    /* text-align: center; */
}
.copyright p, 
.copyright p a {
    font-size: 15px;
    margin: 0;
    opacity: 1;
    color: #717A80;
    font-weight: 500;
    font-family: 'Montserrat', sans-serif;
    }
.copyright p a { color: #619EED; }
.copyright p a:first-child { margin-left: 15px; }
.copyright p>span {
    margin: 0 10px;
}
img.footlogo {}
.col1.footer-info {
    position: relative;
}
.footer-links h3, .contact-links h3 {
    font-size: 19px;
    font-weight: 600;
    color: #1F2225;
    margin: 0 0 10px 0;
    line-height: 1.3;
    font-family: 'Poppins', sans-serif;
}
.footer .footer-links .MCButton.btn-sm {padding: 8px 25px;}   
.footer-links ul li {
    position: relative;
    padding-left: 0;
    margin-bottom: 10px;
    color: #4d4d4d;
    font-size: 15px;
    font-family: 'Poppins', sans-serif;
    font-weight: 500;
    }
.footer-links ul li a, .contact-links ul li a, .contact-links ul li span {
    font-size: 15px;
    color: #4d4d4d;
    font-weight: 500;
    line-height: 1.3;
    background: transparent;
    text-shadow: none;
    font-family: 'Poppins', sans-serif;
}
.footer-links ul li a:hover, .footer-links ul li a:focus,
.contact-links ul li a:hover, .contact-links ul li a:focus {
    text-decoration: underline;
    color: #0E55A4;
}
.d-flex-wrap {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}
.footer {
    background: #ffffff;
    background-repeat: no-repeat !important;
    background-size: cover !important;
    padding: 60px 0 0;
    position: relative;
    z-index: 1;
    border-top-left-radius: 200px;
    }
.row.d-flex-wrap:before,
.row.d-flex-wrap:after {
    display: none;
}
.row.d-flex-wrap {
    margin-left: -15px;
    margin-right: -15px;
}
.footer .row.d-flex-wrap>div {
    padding-left: 15px;
    padding-right: 15px;
}
.footer .row.d-flex-wrap>.foot-logo-wrap{
    -webkit-flex: 0 0 170px;
    flex: 0 0 170px;
    max-width: 170px;
    position: relative;
    z-index: 1;
}
.footer .row.d-flex-wrap>.rightfoot {
    -webkit-flex: 0 0 calc(100% - 170px);
    flex: 0 0 calc(100% - 170px);
    max-width: calc(100% - 170px);
}

.footer .row.d-flex-wrap .col1,
.footer .row.d-flex-wrap .col2,
.footer .row.d-flex-wrap .col3,
.footer .row.d-flex-wrap .col4,
.footer .row.d-flex-wrap .col5 {
    -webkit-flex: 0 0 25%;
    flex: 0 0 25%;
    max-width: 25%;
    padding-left: 15px;
    padding-right: 15px;
}
.footer .row.d-flex-wrap>div.footer-info h3 {
    font-size: 25px;
    color:#DDDDDD;
    margin: 0 0 15px;
}
.footer .footer-links,
.footer .footer-info {
    padding: 20px 0;
}
.footer-links ul.social-list {
    display: flex;
    flex-wrap: wrap;
}
.footer-links ul.social-list li {
    margin-right: 20px;
}
.footer-links ul.social-list a {
    font-size: 20px;
    color: #ffffff;
}
.copyright-block {
    padding: 15px 0;
}
.footer-links ul li p {
    text-transform: uppercase;
    color: #ffffff;
    font-weight: 600;
    line-height: 1.3;
    font-size: 15px;
}
.align-items-center {
    align-items: center;
}
.justify-content-between {
    justify-content: space-between;
}
/*-----------------------------Inner Page CSS----------------------------***/


/*--- Banner Inner --**/
.bannerInner {position: relative;background: #2b5f71;min-height: 240px;}
.bannerInner .fixed-bg {
    width: 100%;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    opacity: 0.3;
}

/********************/
.quicklink-mobile { display: none; }
.events {margin-top: 40px;display: block;}
.sponsors-box {
    background: #CFD2D9;
    text-align: center;
    padding: 23px 20px;
}
.sponsors-box span {
    background: #fff;
    padding: 4px 12px;
    color: #6C6C6C;
    font-size: 14px;
    font-weight: 600;
    text-transform: uppercase;
    position: relative;
    margin-top: 28px;
    display: inline-block;
}
.sponsors-box span small {
    font-size: 24px;
    color: #000;
    font-weight: 500;
    position: absolute;
    bottom: -22px;
    left: 0;
    right: 0;
    margin: 0 auto;
}
.sponsors-boxtwo {
    display: block;
    text-align: center;
    margin: 50px 0 30px 0;
    padding: 0 25px;
}
.sponsors-boxthree {
    background: #E9E9E9;
    display: block;
    text-align: center;
    padding: 38px 20px;
    margin:0 25px;
}
.sponsors-link ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
}
.sponsors-link ul a {
    display: inline-block;
    padding: 15px 40px;
    background: #f6f7fb;
    border: 1px solid #ddddde;
    margin-left: -1px;
    font-size: 20px;
    font-weight: 900;
    color: #41464b;
    text-decoration: none;
    position: relative;
}
.sponsors-link ul a:before {
    contain: "";
    width: 10px;
    height: 10px;
    background-color: #eca94f;
    -webkit-transform: translate(-50%, -50%)rotate(45deg);
    transform: translate(-50%, -50%)rotate(45deg);
    position: absolute;
    top: 100%;
    left: 50%;
    content: "";
    opacity: 0;
}
.sponsors-link ul li.active a {
    background: #eca94f;
    border-color: #eca94f;
}       
.sponsors-link ul li.active a:before {
    opacity: 1;
}
.sponsors-link {
    margin-bottom: 40px;
}
.events .friendsLogoBox {
    display: none;
}
.inner-content-area > p {
    margin: 15px 0 20px 0;
}



.BulletList ul {margin-bottom: 50px;margin-left: 0;list-style: none;}
.BulletList ul li {position: relative;padding-left: 30px;margin-bottom: 5px;font-size: 16px;line-height: 2;position: relative;background-position: left center;background-size: contain;background-repeat: no-repeat;color: #33383A;background-size: 16px;background-position: top 2px left;}
.BulletList ul li::before {
    content: '';
    position: absolute;
    top: 13px;
    left: 15px;
    width: 4px;
    height: 4px;
    
    border-radius: 50%;
    background: #4d4d75;
    
}
.BulletList ul ul {
    margin-top: 12px;
    margin-bottom: 5px;
}

/*---Main Content Div----***/

/* sbm Clases */
.sponsors-sec h2 {
    font-size: 22px;
    color: #33383A;
    font-weight: 700;
    margin-top: 0;
} 
.sponsors-img-list ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    padding: 0;
    margin: 0;
}
.sponsors-img-list ul li {
    padding: 15px;
}
.infoicon-sec {width: 100%;background: radial-gradient(67.91% 78.89% at 2.43% 100%, #8EE3F6 0%, #FFFFFF 100%) /* warning: gradient uses a rotation that is not supported by CSS and may not behave as expected */;padding: 30px 0 350px !important;margin-bottom: -330px;}
.flex-row {
    display: flex;
    flex-wrap: wrap;
margin-left: -15px;margin-right: -15px;width: auto;}
.flex-row:before,.flex-row:after {
    display: none;
    margin: 0;
}
.infoicon-sec .flex-row .col-4 {
    flex: 0 0 33.33%;
    -webkit-flex: 0 0 33.33%;
    max-width: 33.33%;
    padding: 0 12px;
    margin-bottom: 40px;
}
.info-iconbox-wrapper {
	display: flex;
	height: 100%;	
}
.info-iconbox {
    text-align: center;
    position: relative;
    z-index: 2;
    padding: 20px 30px;
    background: #FFFFFF;
    border-radius: 24px;
    box-shadow: 6px 6px 52px 0px #7CADB826;
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.info-iconbox img.bgimg {
    position: absolute;
    width: 100%;
    height: 100%;
    object-fit: cover !important;
    top: 0;
    left: 0;
    z-index: -2;
    object-position: center !important;
}
.info-iconbox a:hover {text-decoration: none;}   
.info-iconbox span {
    display: inline-flex;
    border-radius: 50%;
    position: relative;
    width: 70px;
    height: 70px;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 20px;
    font-weight: 700;
    font-family: 'Poppins', sans-serif;
    }
.info-iconbox img {
    display: inline-block;
    margin: 0 auto;
    width: 72px;
    height: 72px;
    object-fit: contain;
}
.info-iconbox h2 {
    font-size: 22px;
    font-weight: 600;
    line-height: 1.2;
    margin: 12px 0;
}
.info-iconbox.text-clr-1 h2 {color: #29C0E2;}
.info-iconbox.text-clr-2 h2 {color: #8D93D9;}
.info-iconbox.text-clr-3 h2 {color: #6D9F40;}
.info-iconbox.text-clr-4 h2 {color: #79257B;}
.info-iconbox.text-clr-5 h2 {color: #0655A3;}
.info-iconbox.text-clr-6 h2 {color: #F79400;}
.info-iconbox h2 b {
    font-weight: 900;
}
.info-iconbox p {
    color: #1F2225;
}
.info-iconbox .iconlink {
    position: absolute;
    width: 70px;
    height: 70px;
    display: inline-flex;
    right: 0;
    bottom: 0;
    z-index: 1;
    background: #ee3a43;
    color: #ffffff;
    align-items: center;
    justify-content: center;
    font-size: 40px;
    text-decoration: none;
}
.info-iconbox:hover img.bgimg,
.info-iconbox:hover:before {
    
}
.pt-0 {
    padding-top: 0px !important;
}
.pb-0 {
    padding-bottom: 0px !important;
}
.upcoming-event-sec .flex-row {
    margin-left: -15px;
    margin-right: -15px;
}
.upcoming-event-sec .flex-row .btn-wrap {
    margin-top: 50px;
    text-align: center;
}

.img-card .img-holder {
    position: relative;
}
.img-card .img-holder span {
    font-size: 35px;
    color: #ffffff;
    display: inline-block;
    background: #eca94f;
    padding: 5px;
    position: absolute;
    width: 50px;
    bottom: 0;
    right: 0;
    line-height: 1;
    text-align: center;
    height: 50px;
}
.img-card h2 {
    font-size: 22px;
    line-height: 1.4;
    margin: 5px 0 5px;
    color: #000000;
    text-decoration: none;
}
.img-card span {
    font-size: 12px;
    display: block;
}
.img-card:hover {
    box-shadow: 0 0 15px rgb(0 0 0 / 18%);
}
.reg-here-link {
    font-size: 12px;
    font-weight: 700;
    color: #2d2d2d;
    text-transform: uppercase;
    position: relative;
    background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAARCAYAAADUryzEAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAFiSURBVHgBnVNBTgJBEOzuWRKP+4T9gYsKiTf4gb5AORnCQXgCL9ALIZ7AF8gPxJMJiswT9gfu0QjT7cxGCNmdXYx12GTSXdU1tT0IHqwmcbj5gpgAIndmgKTR1XNfL+aJvKFbYezbY5jrTRFh9m3M8Lyrk4LAYhTHFKgn+J1agcQOuWz23vRO4HUcRzVSK8/UMqRrNnXnhNzJkp/zZBSYusYSgbCm1CTrW45ProVw4puguBZSwAXxLWxWbRKFV94J1pWhdWrv2y51gtInEIjBj+iQCCEcE1QHF7m7usTFyDBfFFvH94fTz3IRSdbMbvo26CjXkJL9kfqfZGdBk7X24qMfJFuwyCMFR3wPxYDSQOiiiuwcNrsfU6p3tCN3ctUQFd5B5VrjwH2zTTy7Wc7sUgzhj2CmjuNkMvuF7E2gXVGElpcpMGdjBs2e3gWPvr7FqBEjmhYSRhmPJRFR8+0L3McPboCcJBIS7s8AAAAASUVORK5CYII=');
    background-position: left center;
    background-size: contain;
    background-repeat: no-repeat;
    padding: 1px 0 1px 22px;
}
.mt-40 {
    margin-top: 40px !important;
}
.magazine-block {
    text-align: center;
}
.magazine-block h2 {
    font-size: 30px;
    
    font-weight: 700;
    margin: 50px 0 10px;
    text-transform: uppercase;
}
.newscard {
    display: block;
    position: relative;
    min-height: 225px;
    background: #ffffff;
    border: 1px solid #BCBCBC;
    padding-left: 95px;
}
.newscard .newstag {
    position: absolute;
    top: 0;
    left: 0;
    width: 95px;
    height: 100%;
    background-color: #2d2d2d;
    color: #ffffff;
}
.newscard .newstag>span {
    -webkit-transform: rotate(-90deg) translate(-50%, -50%);
    transform: rotate(-90deg) translate(-50%, -50%);
    -webkit-transform-origin: top left;
    transform-origin: top left;
    position: absolute;
    top: 50%;
    left: 50%;
    display: block;
    text-align: center;
    width: auto;
    font-weight: 700;
}
.newscard .news-inner-wrap {
    position: relative;
    left: 0;
    padding: 30px 30px 30px 190px;
    min-height: 200px;
}
.newscard .news-inner-wrap img {
    position: absolute;
    left: 70px;
    top: 65px;
    z-index: 1;
}

.newscard .news-inner-wrap h2 {
    font-size: 30px;
    font-weight: 700;
    
    text-transform: uppercase;
}

.newscard:not(:first-child) {
    margin-top: 70px;
}
.gray-bg2 {
    background: #F7F5F4;
}

.newscard .news-inner-wrap p {
    color: #33383A;
}
body .header .navbar .nav>li.searchBtnFn:hover .dropdown-menu, body .header .navbar .nav>li.headerlogin:not(.show-form):hover .dropdown-menu {
    display: none !important;
}
.header .navbar .nav>li.searchBtnFn:hover .dropdown-toggle:after {
    display: none !important;    
}

body .header .navbar .nav>li.searchBtnFn.dropdown.show-search-bar>ul.dropdown-menu {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    z-index: 999;
}

body .header .navbar .nav>li.searchBtnFn.dropdown>ul.dropdown-menu {
    margin: 0;
    background: #ffffff;
    left: auto;
    right: 205px;
    width: calc(100% - 605px);
    padding: 0;
    height: 116px;
    top: 0;
}

.header .navbar .nav li a.nav-member-center {
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    height: 116px;
    margin-left: auto;
    margin-right: 0;
    padding: 10px;
    position: relative;
    z-index: 1;
}
.searchnav-logo {
    padding: 24px 40px 14px 40px;
}
.nav-member-center p {
    color: #ffffff;
    margin: 10px 0 0;
}
.nav-member-center img {
    width: 35px;
}

.header .nav-collapse .nav .searchBtnFn.dropdown .dropdown-menu li.megaMenuSection.member-center-wrap {
    width: 215px;
} 
.header .navbar .nav li.dropdown .megaMenuSection .formframe a.searchclose {
    background: transparent !important;
    color: #9A8D83;
    padding: 0;
    border: none;
    display: inline-flex;
    align-items: center;
    position: absolute;
    right: 0;
    top: 50%;
    right: 30px;
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    z-index: 1;
    text-transform: none;
}
.header .navbar .nav li.dropdown .megaMenuSection .formframe a.searchclose svg {
    margin-left: 8px;
}
header .top-strip ul li:not(:last-child):after {
    content: "|";
    display: inline-block;
    color: #ed3943;
}
header .top-strip ul li {
    font-size: 12px;
    line-height: 1.2;
}
.breadcrumd-list ul {
    display: flex;
    flex-wrap: wrap;
    padding: 0px;
    justify-content: center;
    margin-top: 20px;
}

.breadcrumd-list ul li {
    list-style: none;
    color: #CC9933;
}
.breadcrumd-list ul li a {
    padding:  10px 0;
    font-size: 14px;
}
.breadcrumd-list ul li a {
    color: #FFFFFF;
}
.breadcrumd-list ul li:not(:last-child):after {
    display: inline-block;
    content: '|';
    margin: 0 15px;
    color: #ffffff;
}
.inner-page-content {
    position: relative;
}
.inner-page-content .inner-content-area {
    padding: 50px 150px 0 50px;
    margin-bottom: 100px;
    width: 100%;
    flex: 0 0 calc(100% - 450px);
    max-width: calc(100% - 450px);
}
.inner-page-content .sidebar {
    flex: 0 0 450px;
    width: 450px;
    background: #DBD5CD;
    padding: 20px;
    left: 0;
    top: 0;
    margin: 0;
    bottom: 0;
}
.inner-page-content>.row-fluid {
    display: flex;
    flex-flow: row-reverse;
    flex-wrap: wrap;
}

.inner-page-content>.row-fluid {
    flex-flow: row-reverse;
}
.quicklink-desktop h3, .events h3 {
    text-transform: uppercase;
    color: #fff;
    font-size: 22px;
    letter-spacing: 0.1em;
    margin: 0 0 25px 0;
}
.sidebar .eventbox-row {
    flex-direction: column;
}
.sidebar .eventbox-col {
    width: 100%;
    margin: 0 0 30px 0;
}
.events { margin-top: 40px; }
.sidebar-iconbox {
    height: 70px;
    border: 1px solid #fff;
    display: flex;
    margin-bottom: 10px;
    padding: 10px 20px;
    align-items: center;
    position: relative;
}
.sidebar-iconbox:hover {
    background: #F1B828;
    border-color: #F1B828;
}
.sidebar-iconbox .iconBox {
    margin-right: 20px;
}
.sidebar-iconbox .textBox h2 {
    font-size: 20px;
    color: #fff;
    font-weight: normal;
    line-height: 100%;
}
.sidebar-iconbox .arrow {
    position: absolute;
    right: 20px;
}
.sidebar .eventbox-img span img {
    width: 100%;
    height: auto;
    max-width: inherit;
}

/*Left content*/
.content-info p {
    margin: 20px 0;
}


.membership-headlinebox h5 {
    text-decoration-line: underline;
    color: #BA0C2F;
    font-size: 22px;
    font-weight: 600;
}
.membership-headlinebox p {
    margin:20px 0;
}
.forgot-mb {
    display: none;
}
.primary-btnmb { display: none; }
.headtitle { display: none; }

/*-------eventbox Css----------*/
.eventbox {
    padding: 40px 0;
    background: #DBD5CD;
}
.eventbox-row {
    display: flex;
}
.eventbox-col {
    margin-right: 30px;
    width: 33.33%;
    box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.25);
}
.eventbox-col:last-child {
    margin-right: 0px;
}
.eventone {
    background: #5b6165;
}
.eventtwo {
    background: #2b596d;
}
.eventthree {
    background: #ed3943;
}
.eventbox-img {
    height: 180px;
    overflow: hidden;
    position: relative;
}
.eventbox-img span img {
    width: auto;
    height: 100%;
    max-width: inherit;
}
.event-head {
    position: absolute;
    z-index: 9;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 100%;
    top: 0;
    left: 0;
    height: 100%;
}
.event-head img {
    width: auto;
}
.event-head h4 {
    font-size: 34px;
    font-weight: 400;
    color: #ffffff;
    text-transform: uppercase;
}
.event-head h4 b {
    font-weight: 900;
}
.event-head h4::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 3px;
    background: #fff;
    bottom: -20px;
    left: 0;
    right: 0;
    margin: 0 auto;
}
.eventbox-info {
    padding: 15px;
}
.eventbox-item {
    text-align: center;
    margin: 10px 0 0 0;
    border-bottom: 1px solid #fff;
}
.eventbox-item:hover .eventbox-item-in { background: #fff; }
.eventbox-item-in {
    padding: 15px 10px;
    margin-bottom: 10px;
}
.eventbox-item ul {
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 0;
    opacity: 0.8;
}
.eventbox-item ul li {
    font-size: 14px;
    letter-spacing: 0.04em;
    color: #FFFFFF;
    position: relative;
    padding: 0 20px;
    text-transform: uppercase;
}
.eventbox-item ul li i { margin-right: 5px; }
.eventbox-item ul li:before {
    content: '|';
    position: absolute;
    height: 25px;
    right:0;
    top:0;
}
.eventbox-item ul li:last-child:before {
    display: none;
}
.eventbox-item ul li img { margin-right: 5px; }
.eventbox-item ul li img.hover-img { display: none; }
.eventbox-item:hover ul li img.active-img { display: none; }
.eventbox-item:hover ul li img.hover-img { display: inline-block; }
.eventbox-item p {
    color: #fff;
    font-size: 20px;
    margin: 10px 0 0 0;
}
.eventbox-item:hover ul li { color: #1B365D; }
.eventbox-item:hover p { color: #1B365D; }
.eventbox-item.eventbox-item-link {
    border: 0;
    margin: 15px 0;
}
.event-link {
    color: #fff;
    margin: 0 15px;
    letter-spacing: 0.1em;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: bold;
}
.event-link:hover,
.event-link:focus {
    color:#fff;
    text-decoration: underline;
}

.mb-20 {
    margin-bottom: 20px;
}
.side-title-center {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid rgba(87, 96, 102, 0.30);
}
.owl-theme .owl-dots .owl-dot span {
    background-color: #ffffff;
    width: 14px;
    height: 14px;
opacity: 0.5;}
.info-iconbox h2>a {
    color: inherit;
    text-decoration: none;
}

.info-iconbox:hover span img {
    
    
}
.d-inline-block {
    display: inline-block   ;
}
.fs22 {
    font-size: 22px;
}
.BulletList-row {
    display: flex;
    flex-wrap: wrap;
}
.BulletList-row .BulletList {
    -webkit-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}
.mr-10 {
    margin-right: 10px;
}


.my-10 {
    margin-top: 10px;
    margin-bottom: 10px;
}
.textLine-sec p {
    text-align: center;
    color: #33383A;
}
.img-card {
    margin-bottom: 26px;
    background: #F2F2F2;
border-radius: 8px;overflow: hidden;}
.img-card .img-card-content {
    padding: 25px 40px;
}
.img-card .img-card-content a:hover {
    text-decoration: none;
}
.img-card .img-card-content span.datebx {
    color: #ee3a43;
    font-size: 16px;
}
.img-card .img-card-content p {
    font-size: 16px;
color: #4D4D4D;}
.anouncebanner {
    display: none;
    background: #17432F;
    padding: 50px 20px;
}
.anouncebanner p {
    color: #ffffff;
    text-align: center;
    margin-bottom: 25px;
    font-size: 22px;
    font-weight: 400;
}
.anouncebanner .button-wrap {
    display: flex;
    justify-content: space-between;
}
.anouncebanner .button-wrap a {
    display: inline-block;
    padding: 10px 10px;
    width: 48%;
    color: #ffffff;
    border: 1px solid #ffffff;
    border-radius: 50px;
    text-align: center;
}
.header .navbar .nav li.open-droupdown a {
    font-weight: 700;
    color: #083372;
}
.upcoming-event-sec {
    position: relative;
    z-index: 3;
}
.upcoming-event-sec .flex-row>.col-4 {
    margin: 0 !important;
    flex: 0 0 33.33%;
    -webkit-flex: 0 0 33.33%;
    width: 33.33%;
    padding: 0 15px;
}
.upcoming-event-sec .flex-row>.col-6 {
    margin: 0 !important;
    flex: 0 0 50%;
    -webkit-flex: 0 0 50%;
    width: 50%;
    padding: 0 15px;
}

.upcoming-event-sec .flex-row>.col-12 {
    flex: 0 0 100%;
    -webkit-flex: 0 0 100%;
    width: 100%;
    padding: 0 15px;
}
.footer img.bg-img {
    position: absolute;
    width: 100%;
    height: 100%;
    z-index: -1;
    left: 0;
    top: 0;
}

.footer .for-mobile {
    position: relative;
    z-index: 2;
    padding: 30px 15px 20px;
    margin-top: 55px;
}

.footer .for-mobile h2 {
    color: #ffffff;
    font-size: 22px;
    margin: 0 0 15px;
    font-weight: 400;
    line-height: 1.3;
}

.footer .for-mobile  .row-flex {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.footer .for-mobile .row-flex .col12 h2 {
    text-align: center;
}

.footer .for-mobile .row-flex .col6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}

.footer .for-mobile .row-flex .col12 {
    flex: 0 0 100%;
    max-width: 100%;
    margin-top: 30px;
}

.footer .for-mobile .row-flex .sbmrow {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    width: 100%;
    padding: 0 15px;
}

.footer .for-mobile .row-flex .sbmrow ul {
    flex: 0 0 calc(50% - 15px);
    max-width: calc(50% - 15px);
}

.footer .for-mobile ul li ,
 .footer .for-mobile ul li a {
    color: #ffffff;
    font-size: 15px;
    font-weight: 700;
}

.footer .for-mobile ul li {
    border: 1px solid rgb(255 255 255 / 30%);
    border-style: solid none;
    margin: -1px 0 0;
    padding: 4px 0;
    display: flex;
    align-items: center;
    min-height: 40px;
}
.footer .for-mobile ul li a:hover {
    color: #E1C783;
    text-decoration: none;
}

.footer .for-mobile ul li:before {
    content: "\f101";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin-right: 10px;
    color: #A2D5ED;
}
.footer .for-mobile .copyright a {
    color: #ffffff;
    text-decoration: underline;
}
.footer .for-mobile .copyright p {
    font-weight: 900;
    padding: 0 015px;
}
.footer .for-mobile .copyright p>span {
    margin: 0 5px;
}
.footsocial-list {
    background: #0B2239;
    padding: 8px 15px;
    width: 100%;
    position: absolute;
    top: -55px;
    z-index: 1;
}
.footsocial-list ul {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
}
.footsocial-list ul li:first-child {
    color: #ffffff;
    flex: 1 1 auto;
    font-size: 13px;
    font-weight: 900;
    
}
.footer .for-mobile .footsocial-list>ul li a {
    display: inline-block;
    font-size: 18px;
    color: #ffffff;
    text-decoration: none;
    text-align: center;
}
.footer .for-mobile .footsocial-list>ul li:before {
    display: none;
}
.footer .for-mobile .footsocial-list li {
    border-style: none;
    padding: 0;
    text-transform: uppercase;
}
.footer .for-mobile .footsocial-list li:not(:first-child) {
    margin-left: 25px;
}
.footer .for-mobile .row-flex .col6:first-child ul>li:before {
    display: none;
}
.footer .for-mobile ul li:hover:before {
    color: #E1C783;
}

.sponsors-img-list ul li {
    min-width: auto;
}

.whats-new-sec .flex-row {
    margin-left: -15px;
    margin-right: -15px;
}

.whats-new-sec .flex-row>div {
    margin: 0;
    padding: 0 15px;
}
.whats-new-sec .flex-row>div.sbm8 {
    padding-left: 60px;
}
.whats-new-sec .flex-row>div.span4 {
    flex: 0 0 35%;
    max-width: 35%;
}

.whats-new-sec .flex-row>div.span8 {
    flex: 0 0 65%;
    max-width: 65%;
}
.foot-logo:after {
    width: 126px;
}
.loggedinBox {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    margin: 0 auto;
    max-width: 250px;
    padding: 5px 0;
}
.header .navbar .nav li .loggedinBox a.MAJButton {
    padding: 12px 23px;
    min-height: auto;
    color: #ffffff;
    background: #472103;
    border-color: #472103;
    font-size: 18px;
}

.header .navbar .nav li .loggedinBox>span p {
    color: #ffffff;
    margin: 8px 0 0;
    font-weight: 500;
    font-size: 20px;
}

.header .navbar .nav li .loggedinBox>span {
    display: block;
    text-align: center;
}

.header .navbar .nav>li.show-form.dropdown>.dropdown-menu {
    display: block !important;
    visibility: visible !important;
    z-index: 999 !important;
    opacity: 1 !important;
    top: 0;
    left: 0;
    margin: 0;
    width: 100%;
}

.header .navbar .nav li a.nav-member-center:after {
    display: none;
}


textarea, 
input[type="text"], 
input[type="password"], 
input[type="datetime"], 
input[type="datetime-local"], 
input[type="date"], 
input[type="month"], 
input[type="time"], 
input[type="week"], 
input[type="number"], 
input[type="email"], 
input[type="url"], 
input[type="search"], 
input[type="tel"], 
input[type="color"],
.uneditable-input {
    min-height: 30px;
}

.footer .for-mobile ul,
.captionFrame ul,
.friendsSliderBox .item ul,
.footer .footer-links ul, 
.footer .footer-info ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
.event-mobile, .news-mobile {
    display: none;
}
.d-none {
    display: none !important;
}

ul.social-list {
    display: flex;
}

ul.social-list li a {
    width: 30px;
    height: 30px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: #eba94f;
    border-radius: 50%;
    color: #41464b;
}

ul.social-list li {
    margin-right: 20px;
}
.foot-logo-wrap {

}
.my-30 {
    margin-top: 30px;
    margin-bottom: 30px;
}
.titlewrap h1, .titlewrap h2, .titlewrap h3, .titlewrap h4, .titlewrap h5, .titlewrap h6 {
    font-weight: 400;
    color: #ee3a43;
    text-transform: uppercase;   
}
.titlewrap h1 b, .titlewrap h2 b, .titlewrap h3 b, .titlewrap h4 b, .titlewrap h5 b, .titlewrap h6 b {
    font-weight: 900;
}
.row.row-flex {
    margin: 0 -15px;
}
.row.row-flex>.span4 {
    flex: 0 0 25%;
    -webkit-flex: 0 0 25%;
    max-width: 25%;
    width: 25%;
    padding: 0 15px;
}
.row.row-flex>.span8 {
    flex: 0 0 75%;
    -webkit-flex: 0 0 75%;
    max-width: 75%;
    width: 75%;
    margin-left: 0;
    padding: 0 15px;
}
.event-list .sbm-event .sbm-e-head span:first-child:after {
    display: inline-block;
    content: "|";
    color: #33383A;
    opacity: 0.3;
    position: absolute;
    right: 0;
    top: 0;
}
.event-list .sbm-event .sbm-e-head {
    display: flex;
    justify-content: space-between;
    color: #A8462B;
    font-weight: 400;
    text-align: center;
}
.event-list .sbm-event .sbm-e-head span {
    min-width: 45%;
    text-align: center;
    position: relative;
}


.DiamondBullets ul li {
    position: relative;
    padding-left: 0;
    margin-bottom: 22px;
}
.DiamondBullets ul li a {
    font-size: 19px;
    color: #2d2d2d;
    display: block;
    padding: 24px 20px;
    border: 1px solid #2d2d2d;
}
.DiamondBullets ul li a:after {
    content: "\f105";
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    width: auto;
    height: auto;
    background-position: 0 0;
    position: absolute;
    top: 50%;
    right: 15px;
    line-height: 1;
    transform: translateY(-50%);
    font-size: 30px;
}
.DiamondBullets ul li a:hover {
    text-decoration: underline;
}
.DiamondBullets ul li a:hover:before {
    background-position: 0 -18px;
}
.btn-flex {
    display: flex;
}
.header .navbar .nav>li.dropdown>a {
    z-index: 99;
    position: relative;
}
.droptitle {
font-size: 22px;
font-weight: 700;
margin-bottom: 5px;
display: block;

color: #2D56A1;line-height: 1.3;}

    
    .header .navbar .nav li.dropdown .megaMenuSection li a:hover:before {
    font-size: 100%;
    opacity: 1;
    margin-right: 5px;
    }



.trust-member-sec {
    position: relative;
    z-index: 1;
    margin-top: -256px;
}
.trust-member-sec:after {
    content: "";
    position: absolute;
    width: 100%;
    background: #ffffff;
    height: 100%;
    border-top-right-radius: 200px;
    right: 0;
    top: 0;
    transform-origin: top right;
    z-index: -1;
}



.memberCentral-today-sec {
    background: #29C0E2;
text-align: center;}
.reduces-costs-sec .row.d-flex-wrap>.col-6 {
    flex: 0 0 50%;
    -webkit-flex: 0 0 50%;
    max-width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}
.reduces-costs-sec .boxcontent .HeaderTextSmall {
    text-align: left;
    color: #000000;
margin-bottom: 30px;}
.reduces-costs-sec .boxcontent p, .reduces-costs-sec .boxcontent li {
    color: #1F2225;
    }
.reduces-costs-sec .boxcontent {
    padding-top: 20px;
    padding-bottom: 20px;
position: relative;z-index: 2;}

.reduces-costs-sec .boxcontent .img-holder {
    margin-bottom: 30px;
}
/* .reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap  {margin-right: -180px;margin-top: -30%;} */

.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
    margin-right: -250px;
    margin-top: -34.5%;
}
.inner-pg2.reduces-costs-sec .boxcontent.flex-sm-reverse .img-holder.img-with-shap {
    margin-top: -38.5%;
}

.align-self-center {
    align-self: center;
}
.memberCentral-today-sec h2 {font-size: 48px;color: #ffffff;margin: 0 0 20px;font-weight: 600;}
.testimonials-sec {
    position: relative;
    z-index: 2;
    background: #F3FCFE;
    overflow: hidden;
    }
.testimonials-sec .top-white-img {
    position: relative;
    top: -1px;
width: 100%;z-index: 2;}
.testimonials-sec .below-blue-img {
    position: absolute;
    width: 100%;
    height: 100%;
    
    object-fit: cover;
    object-position: bottom right;
    top: 0;
    left: 0;
z-index: 1;}

.testimonials-sec .left-wrap {
    flex: 0 0 61%;
    -webkit-flex: 0 0 63%;
    max-width: 63%;
    padding-left: 15px;
    padding-right: 35px;
    }
.testimonials-sec .right-wrap {
    flex: 0 0 37%;
    -webkit-flex: 0 0 37%;
    max-width: 37%;
    padding-left: 15px;
    padding-right: 15px;
    padding-top: 40px;
    }
.testimonials-sec .container.containerCustom {
    position: relative;
    z-index: 2;
}
.testimonial-block  {
    display: flex;
    padding: 55px 45px;
}
.testimonial-block  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    flex: 0 0 calc(100% - 210px);
    max-width: calc(100% - 210px);
}
.testimonial-block p {
    color: #1F2225;
    font-size: 21px;
    line-height: 1.45;
    font-weight: 500;
    }
    .testimonial-block p b {
        font-weight: 700;
    }
.testimonials-sec .owl-theme .owl-dots {text-align: left;margin-top: 35px !important;}
.testimonials-sec .owl-theme .owl-dots button:first-child {margin-left: 0;}
.testimonial-block ul li:nth-child(2) {margin-top: 25px;margin-bottom: 7px;}
.testimonial-block ul li:nth-child(2)  {font-size: 16px;font-weight: 700;color: #0E55A4;font-family: 'Montserrat', sans-serif;}
.testimonial-block ul li:nth-child(3) {font-size: 16px;font-weight: 900;color: #292929;padding-left: 120px;}

.testimonial-block ul li:last-child img {width: 100px;}

.testimonials-sec .right-wrap img {position: relative;right: 0;top: 0;}
.testimonials-sec .right-wrap .img-holder {margin-right: -180px;}

.inner-banner-sec.slider .item:before {background: #132D50;}
.inner-banner-sec.slider .TitleText {color: #ffffff;}
.inner-banner-sec.slider .captionFrame ul li:nth-child(2) {color: #ffffff;}

.inner-banner-sec .carousel-caption.centered *,
.inner-banner-sec .carousel-caption.centered {
    text-align: center;
}
.hero-banner .row.d-flex-wrap {min-height: 230px;align-items: center;}
.captionFrame ul li:nth-child(2) {margin-bottom: 15px;}
.hero-banner {
    padding: 80px 0;
    position: relative;
    z-index: 2;
    background: #F2F2F2;
}
.hero-banner:before {
    content: "";
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    background: #FFFFFF;
    border-bottom-right-radius: 477px;
    z-index: -1;
top: 0;left: 0;}
.iconbox {
    display: flex;
    flex-wrap: wrap;
}
.iconbox .left-icon {
    flex: 0 0 60px;
    -webkit-flex: 0 0 60px;
    max-width: 60px;
}
.iconbox .right-icon {
    flex: 0 0 calc(100% - 60px);
    -webkit-flex: 0 0 calc(100% - 60px);
    max-width: calc(100% - 60px);
}
.start-box {
    display: inline-flex;
    align-items: center;
    max-width: 225px;
    text-align: left;position: relative;padding: 7px 10px;
    box-shadow: 0 7px 8px rgba(0, 0, 0, 0.2);
    border-radius: 6px;
    margin-right: 75px;
}
.start-box p {color: #000000;opacity: 1;font-size: 12px;}
.start-box span.num {position: absolute;right: -7px;top: -7px;background: #b34039;line-height: 20px;width: 20px;display: inline-block;height: 20px;color: #ffffff;border-radius: 50%;font-size: 12px;text-align: center;}
.start-box .right-icon {
    position: absolute;
    right: -65px;
    top: 0;
box-shadow: 0 2px 5px rgb(0 0 0 / 27%);border-radius: 6px;}
.start-box .close {
    position: absolute;
    left: -26px;
    top: 0;
color: #000000;opacity: 1;font-weight: 300;background: #aeaeae;color: #ffffff;width: 18px;text-align: center;line-height: 18px;font-size: 10px;border-radius: 50px;}

.bg-left-img {
    position: absolute;
    width: 748px;
    left: -577px;
    top: -297px;
z-index: -1;pointer-events: none;}
.bg-right-img {
    position: absolute;
    width: 748px;
    right: -211px;
    top: -166px;
z-index: -1;width: 450px;}
.wrapper {
}
.op-50 {
    opacity: 0.5 !important;
}
.o-hidden {
    overflow: hidden !important;
}
.iconbox-sec.bg-blue .HeaderTextSmall, .iconbox-sec.bg-blue p, .iconbox-sec.bg-blue * {
    color: #ffffff;
}
.iconbox {display: block;position: relative;padding-left: 130px;}
.iconbox .left-icon {position: absolute;left: 0;top: 0;z-index: 1;background: #ffffff;height: 100px;display: inline-flex;justify-content: center;align-items: center;border-radius: 50%;padding: 10px;min-width: 100px;max-width: 100px;}
.iconbox .left-icon img {width: 60px;height: 60px;object-fit: contain;}
.iconbox h4 {font-size: 22px;}
.iconbox:not(:last-child) {margin-bottom: 30px;}
.iconbox-sec .row.flex-row {justify-content: center;}
.iconbox-sec .row.flex-row .span8 {margin: 0 auto;padding: 0 15px;width: 100%;max-width: 800px;}

.divider1 {
    width: 100%;
    height: 8px;
}
.bg-1 {
    background: #29C0E2;
}
.bg-2 {
    background: #79257B;
}
.bg-3 {
    background: #6D9F40;
}
.announcement-banner-sec {
    text-align: center;
}
.announcement-banner-sec .btn-wrap {
    text-align: center;
}
.pd_60.bg-blue .HeaderText,
.bg-1 .boxcontent .HeaderTextSmall, .bg-1 .boxcontent p, .bg-1 .boxcontent li,
.bg-2 .boxcontent .HeaderTextSmall, .bg-2 .boxcontent p, .bg-2 .boxcontent li,
.bg-3 .boxcontent .HeaderTextSmall, .bg-3 .boxcontent p, .bg-3 .boxcontent li,
.bg-blue .boxcontent .HeaderTextSmall, .bg-blue .boxcontent p, .bg-blue .boxcontent li {
    color: #ffffff;}

.bg-1 .BulletList ul li:before,
.bg-2 .BulletList ul li:before,
.bg-3 .BulletList ul li:before,
.bg-blue .BulletList ul li:before {
    background: #ffffff;
}
.announcement-banner-sec .btn-wrap>a {
    margin: 0 10px 15px;
min-width: 190px;}

.inner-banner-sec.hero-banner .carousel-caption {padding-left: 15px;padding-right: 15px;}    
blockquote, blockquote, blockquote.pull-right {
    font-size: 22px;
    font-weight: 500;
    color: #0655A3;
border-style: none;padding: 0;}
blockquote p {
    font-size: inherit;
color: inherit;font-weight: 500;margin-bottom: 15px;line-height: 1.45;}
blockquote small {
    font-size: 16px;
color: inherit;}
blockquote small b {
    font-weight: 700;
    text-transform: uppercase;
display: inline-block;margin-top: 5px;}
.p-lh-2x>p {
    line-height: 2;
}
.num-block {
    text-align: center;
padding: 0 20px;}
.num-block .TitleText {
    text-align: center;
    color: #000000;
    margin: 0 0 15px;
}
.num-block .HeaderTextSmall {
    margin: 0;
}
.num-sec .flex-row .col-4 {
    flex: 0 0 33.33%;
    -webkit-flex: 0 0 33.33%;
    max-width: 33.33%;
    padding: 0 15px;}
.num-sec .flex-row .col-4:not(:last-child) {border-right: 4px solid #ffffff;}
.inner-banner3.hero-banner:before {display: none;}

.header .navbar .nav li.dropdown .megaMenuSection li a.BlueButton {
    border: 2px solid #2D55A3;
    color: #ffffff;
    font-size: 15px;
    font-weight: 600;
    text-decoration: auto;
    display: inline-block;
    padding: 12px 25px;
    text-align: center;
    background: #2D55A3;
    text-shadow: none;
    border-radius: 50px;
    margin-top: 15px;
}
.header .navbar .nav li.dropdown .megaMenuSection li a.BlueButton:hover {
    background: transparent;
    border-color: #2D56A1;
    color: #2D56A1;
    text-decoration: none;
}
.header .navbar .nav li.dropdown:not(.megamenu) li a i {
    margin-left: 5px;
}
.captionFrame ul li:last-child {
    margin-top: 20px;
}
.mb-10 {margin-bottom: 10px !important;}
.trust-member-sec .divider-top-bottom {
    /* position: relative; */
    position: absolute;
    right: 0;
    left: auto;
    bottom: 0;
    width: 100%;
    z-index: -1;
    height: 100%;
    object-fit: contain;
    object-position: right bottom;
}
.left-icon-box {
    display: flex;
    margin-bottom: 15px;
}
.left-icon-box .lib-icon {
    flex: 0 0 80px;
    -webkit-flex: 0 0 80px;
    max-width: 80px;
    text-align: center;
    align-self: flex-start;
    border-radius: 12px;
    padding: 10px;
    height: 80px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}
.left-icon-box .lib-content {
    flex: 0 0 calc(100% - 80px);
    -webkit-flex: 0 0 calc(100% - 80px);
    max-width: calc(100% - 80px);
    padding-left: 30px;
}

.left-icon-box .lib-icon.bg-blue {
    background: #0E55A4;
}
.left-icon-box .fs22 {
        margin-top: 0;
}
.check-list {
    margin: 0;
    padding: 0;
    list-style: none;
}

.check-list li {
    position: relative;
    padding-left: 30px;
    margin-bottom: 15px;
    font-size: 17px;
    line-height: 1.5;
    font-weight: 500;
    color: #33383A;
}

.check-list li::before {
    content: '\f00c'; /* Unicode for Font Awesome checkmark */
    font-family: "Font Awesome 6 Pro"; /* Ensure Font Awesome is used */
    font-weight: 900; /* Font Awesome solid style */
    position: absolute;
    left: 0;
    top: 0;
    color: #6D9F40; /* Green color for the checkmark */
    font-size: 18px;
    font-weight: bold;
}
.list-doubleline.check-list {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-top: 30px;
}
.list-doubleline.check-list li {
    flex: 0 0 calc(50% - 8px);
    max-width: calc(50% - 8px);
}
.border-radius-1 {
    border-top-left-radius: 2px;
    border-top-right-radius: 90px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 90px;
}
.list-doubleline.check-list li b {
    font-weight: 700;
}
.testimonial-slider .owl-stage-outer {
    box-shadow: 6px 6px 52px 0px #7CADB826;
    background: #ffffff;
    border-radius: 20px;
    overflow: visible;
    margin-top: 40px;
}
.testimonial-block img.left-logo {
    position: absolute;
    left: 0;
    top: 50%;
}

.testimonial-user {
    position: relative;
    z-index: 1;
    padding-left: 75px;
    align-self: flex-start;
}
.testimonial-user .left-logo {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    padding: 16px;
    background: #013473;
    border-radius: 24px;
    z-index: 1;
}
.testimonial-block ul li:nth-child(2) span {
    font-size: 15px;
    color: #222525;
    font-weight: 500;
}
.testimonials-sec .right-wrap h2 {
    font-size: 20px;
    margin: 0 0 5px;
}

.testimonials-sec .right-wrap p {
    font-size: 18px;
}
.testimonial-links a {
    display: block;
    opacity: 0.5;
    padding-left: 0;
    border-left: 0px solid #6D9D43;
    margin-bottom: 20px;
}
.testimonial-links a:not(.active):hover,
.testimonial-links a:not(.active):focus {
    opacity: 0.6;
    text-decoration: none;
}
.testimonial-links a.active {
    opacity: 1;
    padding-left: 20px;
    border-left: 3px solid #6D9D43;
    text-decoration: none;
}




.banner-card {
    --card-padding: 0 0 0 109px;
    --icon-size: 40px;
    --font-size: 14px;
    --badge-radius: 12px;
    --stat-font-size: 1.25rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    padding: var(--card-padding);
    border-radius: 1rem;
    max-width: 700px;
    margin: auto;
    position: relative;
  }

  .banner-card .image-section {
    position: relative;
    border-radius: 1rem;
    flex: 1 1 100%;
    padding-bottom: 53px;
    width: 100%;
  }

  .banner-card .image-section img.w-100 {
    width: 100%;
    height: auto;
    display: block;
  }

  .banner-card .badge {
    position: absolute;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: var(--badge-radius);
    font-size: 14px;
    color: #fff;
    white-space: normal;
  }

  .banner-card .benefit {
    top: -15%;
    left: -120px;
    width: 145px;
    line-height: 1.4;
    z-index: -1;
    padding: 20px;
  }

  .banner-card .edit-icon {
    top: -30px;
    left: 53%;
    transform: translateX(-50%);
    width: 65px;
    height: 65px;
    font-size: 32px;
    font-weight: 300;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .banner-card .statistic {
    bottom: 0;
    right: 90px;
    left: auto;
    flex-direction: row;
    font-size: 14px;
    padding: 20px 15px;
    width: 282px;
    gap: 15px;
  }

  .banner-card .statistic span {
    font-size: var(--stat-font-size);
    line-height: 1.4;
    font-size: 14px;
            font-weight: 600;
  }

  .banner-card .membership {
    bottom: 10px;
    right: 16px;
    width: 65px;
    height: 65px;
    font-size: 32px;
    font-weight: 300;
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  .banner-card .rating {
    position: absolute;
    bottom: 95px;
    right: 60px;
    background: white;
    padding: 8px 8px;
    border-radius: 8px;
    font-size: var(--font-size);
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    width: auto;
  }

  .banner-card .rating img {
    width: 16px !important;
  }
  .banner-card .benefit span {
    min-height: 130px;
    display: block;
}
.banner-card .statistic span:first-child {
    font-size: 32px;
    font-weight: 300;
}

  .accordion-design .accordion-heading {
      border-radius: 5px;
      margin-bottom: 0px;
      box-shadow: none;
      border-style: none;
    }
    
.accordion-design .accordion-group {
    border-style: none;
    margin: 0 0 20px;
    box-shadow: none;
}
 .accordion-design .accordion-toggle {
    color: #2D56A1;
    font-weight: bold;
    text-decoration: none;
    display: block;
    background: #29C0E21A;
    border-style: none !important;
    box-shadow: none;
    background-image: url(data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2210%22%20viewBox%3D%220%200%2016%2010%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M8.4375%200.775635L15.293%207.59595C15.6094%207.94751%2015.6094%208.47485%2015.293%208.79126L14.4844%209.59985C14.168%209.91626%2013.6406%209.91626%2013.2891%209.59985L7.875%204.18579L2.42578%209.59985C2.07422%209.91626%201.54688%209.91626%201.23047%209.59985L0.421875%208.79126C0.105469%208.47485%200.105469%207.94751%200.421875%207.59595L7.27734%200.775635C7.59375%200.459229%208.12109%200.459229%208.4375%200.775635Z%22%20fill%3D%22%232D56A1%22%2F%3E%3C%2Fsvg%3E);
    background-repeat: no-repeat;
    background-position: center right 15px;
    font-size: 17px;
    font-weight: 700;
    border-radius: 10px;
    text-transform: uppercase;
    transition: all 0.3s ease;
}

.accordion-design .accordion-toggle.collapsed {
    background-image: url(data:image/svg+xml,%3Csvg%20width%3D%2216%22%20height%3D%2210%22%20viewBox%3D%220%200%2016%2010%22%20fill%3D%22none%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%3E%3Cpath%20d%3D%22M7.27734%209.59985L0.421875%202.77954C0.105469%202.42798%200.105469%201.90063%200.421875%201.58423L1.23047%200.775635C1.54688%200.459229%202.07422%200.459229%202.42578%200.775635L7.875%206.1897L13.2891%200.775635C13.6406%200.459229%2014.168%200.459229%2014.4844%200.775635L15.293%201.58423C15.6094%201.90063%2015.6094%202.42798%2015.293%202.77954L8.4375%209.59985C8.12109%209.91626%207.59375%209.91626%207.27734%209.59985Z%22%20fill%3D%22%232D56A1%22%2F%3E%3C%2Fsvg%3E);
     background-repeat: no-repeat;
     background-position: center right 15px;
 }


 .accordion-design .accordion-toggle:hover {
    text-decoration: underline;
 }

 .accordion-design .accordion-body {
    background-color: #ffffff;
    border-radius: 5px;
    opacity: 0;
 }

 .accordion-design .accordion-inner {
    font-size: 14px;
    color: #333;
    border-style: none;
    background: #ffffff;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
 }

 .accordion-design .explore-link {
    color: #00bfff;
    font-weight: bold;
    text-decoration: none;
 }

 .accordion-design .explore-link:hover {
    text-decoration: underline;
 }
 .accordion-design .accordion-body {
    opacity: 1;
    overflow: visible;
    position: initial;
}
.accordion-design .accordion-toggle:hover {
    text-decoration: none;
}
.accordion-design .TextButton {
    font-size: 16px;
    padding: 0;
}
.accordion-design .accordion-toggle:not(.collapsed) {
    background-color: transparent;
}
.accordion-img-design .accordionImg {
    display: none;
}
.accordion-img-design .accordionImg.show {
    display: block;
}
.accordion-design .accordion-body .accordion-left-content {
    position: absolute;
    left: 15px;
    top: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1;
    width: 45%;
}

.accordion-design .accordion-body.in .accordion-inner {
    transition-delay: 0.1s;
    opacity: 1;
    visibility: visible;
}

.accordion-design .accordion-body.in .accordion-left-content {
    visibility: visible;
    opacity: 1;
}

.bring-toggather-sec .row.flex-row {
    position: relative;
}


.sec-bring-toggather {
    padding: 120px 0;
}
.banner-card.banner-card-2 {
    --card-padding: 0 0 0 95px;
}
.banner-card.banner-card-2 {

}

.bring-toggather-sec .banner-card {
    margin: 215px 0 0;
}
.bring-toggather-sec .btn-wrap {
    margin: 50px 0 0;
}
.banner-card .benefit span {
    min-height: 109px;
}

.banner-card.banner-card-2 .badge {
    /* bottom: 10%; */
    /* left: 55%; */
}
.banner-card.banner-card-2 .badge img {
    width: auto;
    align-self: flex-start;
}
.banner-card.banner-card-2 .benefit {
    top: -135px;
    left: -95px;
}
.bring-toggather-sec .left-image-placeholder {
    min-height: 710px;
}
.bring-toggather-sec .left-image-placeholder2 {
    min-height: 530px;
}
.banner-card.banner-card-2 .edit-icon {top: 40%;left: auto;right: -30px;transform: none;box-shadow: 0px 2.28px 22.77px 0px #2D56A133;}
.banner-card.banner-card-2 .statistic {top: -94px;left: 70px;bottom: auto;}
.banner-card.banner-card-2 .statistic span {}
.banner-card.banner-card-2 .membership {bottom: 10%;left: 55%;}
.banner-card.banner-card-2 .rating {bottom: unset;top: -134px;left: 70px;right: auto;box-shadow: 0px 1.67px 16.68px 0px #2D56A133;}
.banner-card.banner-card-2 .rating img {}
.banner-card.banner-card-2 .benefit span {}
.banner-card .image-section img.w-100 {
    width: 100%;
    height: auto;
    display: block;
    border-top-left-radius: 4px;
border-top-right-radius: 90px;
border-bottom-right-radius: 4px;
border-bottom-left-radius: 90px;
}
.bring-toggather-sec .row.flex-row .span6 {
    flex: 0 0 50%;
    max-width: 50%;
    margin: 0;
    width: 50%;
    padding-left: 15px;
    padding-right: 15px;
}

.bring-toggather-sec .row.flex-row .span6:nth-child(2) {
    padding-left: 50px;
}
.bring-toggather-sec .row.flex-row .span6:nth-child(1) {
    padding-right: 50px;
}

.notch-box, .Highlight {
    background: linear-gradient(90deg, #0655A3 0%, #0B6FD5 100%);
    padding: 50px;
    border-radius: 20px;
    position: relative;
}
.Highlight * {
    color: #ffffff;
}

.notch-box h3 {
    color: #ffffff;
}
.notch-box p {
    color: #ffffff;
}

.sec-topnotch {
    position: relative;
    z-index: 1;
}
.sec-topnotch .span6 {
    margin: 0;
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
}
.sec-topnotch .img-holder img {
    border-top-left-radius: 2px;
    border-top-right-radius: 90px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 90px;
}
.sec-topnotch .topnotch-bg {
    position: absolute;
    left: 0px;
    bottom: 0px;
    width: 410px;
    height: 100%;
    z-index: 1;
}
.notch-box .row.d-flex-wrap {
    position: relative;
    z-index: 2;
}

.footer .divider-top-left {
    position: absolute;
    right: 0;
    left: 0;
    bottom: auto;
    top: 0;
    width: 100%;
    z-index: 0;
    height: 60%;
    object-fit: contain;
    object-position: left bottom;
    z-index: -1;
}

.img-style-1 {
    position: relative;
    padding-right: 42px;
    width: 100%;
    max-width: 560px;
}
.img-style-1 .mainImg {
    border-top-right-radius: 80px;
    border-top-left-radius: 2px;
    border-bottom-left-radius: 80px;
    border-bottom-right-radius: 2px;
    width: 100%;
}
.img-style-1 .is1-icon-1 {
    border-radius: 14px;
    width: 84px;
    height: 78px;
    position: absolute;
    left: auto;
    right: 0;
    top: 34px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
}
.img-style-1 .is1-icon-1 {
    background: #6D9D43;
}
.bg-blue {
    background: #0E55A4 !important;
}


.explore-links-sec {
    border-top-right-radius: 125px;
    margin-top: -170px;
    background: #ffffff;
    position: relative;
    z-index: 1;
}
.explore-links-sec ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 66px;
}
.explore-links-sec ul li:first-child {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 17px;
    line-height: 100%;
    text-transform: uppercase;
    align-self: center;
}
.explore-links-sec ul li:first-child:before {
    content: "\f103";
    font-family: "Font Awesome 6 Pro";
    color: #6D9D43;
    margin-right: 12px;
}
.explore-links-sec ul li a {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 15px;
    line-height: 100%;
    color: #1F2225;
    text-transform: none;
}
.explore-links-sec ul li a:hover,
.explore-links-sec ul li a.active {
    color: #6D9D43;
    text-decoration: none;
}
#stickyHeader.sticky {
    position: sticky;
    top: 60px;
    padding-top: 20px;
    padding-bottom: 10px;
    z-index: 9;
    box-shadow: 6px 6px 52px 0px #7CADB826;
    background: #ffffff;
    border-radius: 0;
}
.left-icon-box.d2 {

}

.left-icon-box.d2 .lib-icon {
    flex: 0 0 70px;
    -webkit-flex: 0 0 70px;
    max-width: 70px;
    height: 70px;
}

.left-icon-box.d2 .lib-content {
    flex: 0 0 calc(100% - 70px);
    -webkit-flex: 0 0 calc(100% - 70px);
    max-width: calc(100% - 70px);
    padding-left: 25px;
}
.left-icon-box.d2 h2 {
    font-size: 19px;
    font-weight: 700;
    margin-top: 0;
}

.left-icon-box.d2:hover {
    text-decoration: none;
}
.left-icon-box.d2:hover .lib-icon{
    background-color: #F5A800 !important;
}

.form-design {margin: 0;}
.form-design .controls {

}
.form-design .controls .input-large {
    width: 100%;
    padding: 10px;
    border-radius: 4px;
    border: 1px solid #29C0E20D;
    font-size: 16px;
    background: #29C0E20D;
    padding: 15px;
    height: auto;
    font-family: Montserrat;
    font-weight: 500;
    font-size: 15px;
    line-height: 20px;
    outline: none;
    box-shadow: none;
    border: none;
    color: #1F2225;
}
.form-design .controls .input-large:focus {
    box-shadow: none;
}

.schedule-box {
    box-shadow: 6px 6px 52px 0px #7CADB826;
    padding: 30px 60px;
    border-radius: 20px;
}
.schedule-box .span6 {
    flex: 0 0 50%;
    max-width: 50%;
    width: 50%;
    margin: 0;
    padding: 0 40px;
}
.schedule-box  .row.d-flex-wrap {
    margin: 0 -40px;
}
.schedule-box .span6:first-child {
    padding-right: 60px;
}
.card-img-right-box {
    display: flex;
    flex-wrap: wrap;
    box-shadow: 6px 6px 52px 0px #7CADB826;
    border-bottom-right-radius: 90px;
    margin-bottom: 30px;
}
.card-img-right-box .img-holder {
    flex: 0 0 60%;
    max-width: 60%;
    position: relative;
}
.card-img-right-box .cirb-content {
    flex: 0 0 40%;
    max-width: 40%;
    align-self: center;
    padding: 20px 110px 20px 30px;
}
.card-img-right-box .img-holder>img {
    width: 100%;
    height: 100%;
    border-top-left-radius: 90px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 90px;
    border-bottom-left-radius: 2px;
    object-fit: cover;
}
.info-block-sec {
    
}
.info-block-sec .span12 {
    flex: 0 0 100%;
    max-width: 100%;
    width: 100%;
    margin: 0px;
    padding: 0 15px;
}
.card-img-right-box .img-holder .icons-left-centred  {
    position: absolute;
    left: -50px;
    top: 50%;
    z-index: 1;
    width: 100px;
    height: 100px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    transform: translateY(-50%);
    border-radius: 12px;
}

.check-list.list-col-4 {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
	justify-content: center;
}
.check-list.list-col-4 li {
    flex: 0 0 calc(25% - 12px);
    max-width: calc(25% - 12px);
    font-size: 17px;
}
.info-block-sec .check-list.list-col-4 {
    margin-top: 20px;
}
.info-block-sec .btn-wrap {
    margin-top: 30px;
}
.card-img-right-box .cirb-content .HeaderTextSmall {
    margin-right: -20px;
}
.numberBox h2 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 60px;
    color: #1F2225;
    margin: 0px;
}
.info-iconbox.text-left {
    text-align: left;
}
.numberBoxWrapper{
	display: flex;
	height: 100%;
}
.numberBox {
    display: block;
    position: relative;
    z-index: 2;
    background: #FFFFFF;
    border-radius: 24px;
    box-shadow: 6px 6px 52px 0px #7CADB826;
    padding: 20px 30px;
	flex-grow: 1;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
}
.number-box-sec .row.d-flex-wrap .span3 {
    flex: 0 0 25%;
    max-width: 25%;
    margin: 0;
    padding: 0 15px;
}
.infoicon-sec.number-box-sec {
    padding-top: 60px;
}
.infoicon-sec.number-box-sec {
    
}
.banner-icon-1 {
    display: inline-flex;
    width: 40px;
    height: 40px;
    align-items: center;
    justify-content: center;
    border-radius: 5px;
    padding: 7px;
}
.home3-banner {
    
}
.home3-banner .captionFrame ul li {
    text-align: center;
}
.home3-banner .row.d-flex-wrap .span12 {
    margin: 0;
    width: 100%;
    padding: 0 15px;
}
.home3-banner.slider .item {
    padding-bottom: 140px;
    padding-top: 0;
    min-height: auto;
}

.slider.home3-banner .SubHeading {
    align-items: center;
    display: inline-flex;
    gap: 20px;
}

.card-icon-top-right .img-holder {
    position: relative;
    z-index: 1;
}

.card-icon-top-right .img-holder img {
    border-top-left-radius: 90px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 90px;
    border-bottom-left-radius: 2px;
    width: 100%;
}

.card-icon-top-right .icons-top-right {
    width: 97px;
    height: 97px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 57px;
    top: -40px;
    z-index: 9;
    border-radius: 12px;
}

.card-icon-top-right {
    position: relative;
    z-index: 1;
}
.sec-row-flex .span6 {
    flex: 0 0 50%;
    max-width: 50%;
    width: 50%;
    margin: 0;
    padding: 0 15px;
}
.details-1 p {
    font-size: 21px;
    margin-bottom: 16px;
}
.details-1 .check-list li {
    font-size: 17px;
}

.shadowbox-1 {
    background-color: #FFFFFF;    
    box-shadow: 6px 6px 52px 0px #7CADB826;
    padding: 15px;
    display: flex;
    border-radius: 20px;
}
.shadowbox-1 .sb1-icon {
    flex: 0 0 42px;
    max-width: 42px;
}
.shadowbox-1 .sb1-details {
    flex: 0 0 calc(100% - 42px);
    max-width: calc(100% - 42px);
    padding-left: 15px;
}
.shadowbox-1 .sb1-details .SubHeading {
    text-transform: none;
    color: #1F2225;
}
.shadowbox-1 .sb1-details p {
    font-size: 15px;
}
.sec-shadowbox .row.d-flex-wrap {
    row-gap: 30px;
}
.sec-shadowbox .span3 {
    flex: 0 0 25%;
    max-width: 25%;
    width: 25%;
    margin: 0;
    padding: 0 15px;   
}
.sec-shadowbox .span4 {
    flex: 0 0 33.33%;
    max-width: 33.33%;
    width: 33.33%;
    margin: 0;
    padding: 0 15px;   
}

.sec-shadowbox .shadowbox-1 {
    height: 100%;
}
.check-list.shadow-added li {
    box-shadow: 6px 6px 52px 0px #7CADB826;
    padding: 16px 16px 16px 45px;
    border-radius: 20px;
    margin-bottom: 10px;
    flex: 0 0 calc(25% - 27px);
    max-width: calc(25% - 24px);
}
.check-list.shadow-added li::before {
    left: 15px;
    top: 15px;
}
.check-list.list-col-4.shadow-added {
    align-items: start;
}
.notch-box .btn-wrap {
    display: inline-flex;
    flex-wrap: wrap;
    gap: 10px;
}
.feature-list-sec .btn-wrap {
    text-align: center;
    margin-top: 30px;
}
.feature-list-sec {

}

.banner-card.banner-card-3 {margin: 100px 0 0;padding-left: 30px;width: 100%;}

.banner-card.banner-card-3 .edit-icon {
    transform: translateY(-50%);
    top: 0;
    right: 30px;
    left: auto;
    width: 75px;
    height: 75px;
}

.banner-card.banner-card-3 .image-section img.w-100 {
    border-top-left-radius: 90px;
    border-top-right-radius: 2px;
    border-bottom-right-radius: 90px;
    border-bottom-left-radius: 2px;
}
.banner-card.banner-card-3 .statistic {
    padding-top: 80px;
    left: -30px;
    z-index: -1;
    width: 370px;
    padding-left: 20px;
    top: 100%;
    bottom: auto;
    margin-top: -120px;
}
.banner-card.banner-card-3 .statistic span {
    font-size: 20px;
    font-weight: 600;
    font-family: 'Poppins', sans-serif;
}
.banner-card.banner-card-3 .benefit {
    background: transparent;
    padding: 0;
    left: -30px;
    top: -70px;
    width: 240px;
}

.explore-links-sec.no-top-banner {
    margin: 0;
}
.explore-links-wrapper {
    box-shadow: 0px 6px 52px 0px #7CADB826;
}
.explore-links-sec .explore-links-wrapper ul li a {
    padding: 15px 0;
    display: inline-block;
}
.pl-15 {
    padding-left: 15px !important;
}
.header .nav-collapse.collapse {
    display: flex;
    align-items: center;
    justify-content: end;
    gap: 40px;
    position: initial;
}

.testimonial-block:before {
    content: "";
    position: absolute;
    width: 75px;
    height: 75px;
    background: url(./../images/Quotes.png);
    top: -30px;
    left: 40px;
    z-index: 1;
}

.testimonial-slider .owl-carousel {
    overflow: hidden;
}
.Herobanner-2.slider {
    padding-top: 80px;
}
.Herobanner-2.slider {
    padding-top: 80px;
}
.Herobanner-2.slider .item {
    padding-top: 0px;
    min-height: auto;
    padding-bottom: 150px;
}
.card-img-right-box .TextButton {
    margin-top: 40px;
}
.slider.home3-banner {
    padding-top: 72px;
    margin-bottom: 0;
}

.slider.home3-banner + .explore-links-sec {
    margin-top: -137px;
}
.home3-banner .captionFrame ul li:last-child {
    margin: 0;
}
.bg-gradient-1 {
    background: radial-gradient(67.91% 78.89% at 2.43% 100%, #8EE3F6 0%, #FFFFFF 100%) !important;
}
.bring-toggather-sec.bg-gradient-1:after {
    content: "";
    position: absolute;
    width: 100%;
    height: 210px;
    background: #ffffff;
    bottom: 0;
    top: auto;
    left: 0;
    border-top-left-radius: 200px;
    z-index: 1;
}

.bring-toggather-sec.bg-gradient-1 {
    position: relative;
}
.bring-toggather-sec.bg-gradient-1 .container.containerCustom {
    position: relative;
    z-index: 2;
}
.infoicon-sec .row-fluid.flex-row .span3 {
    flex: 0 0 25%;
    max-width: 25%;
    margin: 0;
    padding-left: 15px;
    padding-right: 15px;
}
.banner-card .image-section>img {
    border-top-left-radius: 2px;
    border-top-right-radius: 90px;
    border-bottom-right-radius: 2px;
    border-bottom-left-radius: 90px;
    min-height: 200px;
    object-fit: cover;
}
.fs21, 
.captionFrame ul li.fs21 {
    font-size: 21px;
}

.explore-links-sec.active-class ul li:not(:first-child) {
    display: block;
    width: 100%;
}
.explore-links-sec.active-class ul {
    gap: 0;
}
.Herobanner-2.slider .row.d-flex-wrap {
    justify-content: center;
}
.contentArea {
    background: #ffffff;
    position: relative;
    z-index: 1;
}
#stickyHeader.sticky .explore-links-wrapper {box-shadow: none;padding: 0;}
.sec-shadowbox .row{
	justify-content: center;
}
.cyanBlue{background-color: #66C8D8;}
.royalBlue{background-color: #2c57a0;}
.oliveGreen{background-color: #6D9D43;}
.cobaltBlue{background-color: #0E55A4;}
.orange{background-color: #f3a81c;}
.white{background-color: #ffffff;}
.topleft{border-top-left-radius: 90px!important;}
.topright{border-top-right-radius: 90px!important;}
.bottomright{border-bottom-right-radius: 90px!important;}
.bottomleft{border-bottom-left-radius: 90px!important;}