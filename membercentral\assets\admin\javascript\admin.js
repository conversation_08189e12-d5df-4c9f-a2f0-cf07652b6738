// Base64 Object - https://gist.github.com/ncerminara/11257943
var Base64={_keyStr:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",encode:function(e){var t="";var n,r,i,s,o,u,a;var f=0;e=Base64._utf8_encode(e);while(f<e.length){n=e.charCodeAt(f++);r=e.charCodeAt(f++);i=e.charCodeAt(f++);s=n>>2;o=(n&3)<<4|r>>4;u=(r&15)<<2|i>>6;a=i&63;if(isNaN(r)){u=a=64}else if(isNaN(i)){a=64}t=t+this._keyStr.charAt(s)+this._keyStr.charAt(o)+this._keyStr.charAt(u)+this._keyStr.charAt(a)}return t},decode:function(e){var t="";var n,r,i;var s,o,u,a;var f=0;e=e.replace(/[^A-Za-z0-9\+\/\=]/g,"");while(f<e.length){s=this._keyStr.indexOf(e.charAt(f++));o=this._keyStr.indexOf(e.charAt(f++));u=this._keyStr.indexOf(e.charAt(f++));a=this._keyStr.indexOf(e.charAt(f++));n=s<<2|o>>4;r=(o&15)<<4|u>>2;i=(u&3)<<6|a;t=t+String.fromCharCode(n);if(u!=64){t=t+String.fromCharCode(r)}if(a!=64){t=t+String.fromCharCode(i)}}t=Base64._utf8_decode(t);return t},_utf8_encode:function(e){e=e.replace(/\r\n/g,"\n");var t="";for(var n=0;n<e.length;n++){var r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r)}else if(r>127&&r<2048){t+=String.fromCharCode(r>>6|192);t+=String.fromCharCode(r&63|128)}else{t+=String.fromCharCode(r>>12|224);t+=String.fromCharCode(r>>6&63|128);t+=String.fromCharCode(r&63|128)}}return t},_utf8_decode:function(e){var t="";var n=0;var r=c1=c2=0;while(n<e.length){r=e.charCodeAt(n);if(r<128){t+=String.fromCharCode(r);n++}else if(r>191&&r<224){c2=e.charCodeAt(n+1);t+=String.fromCharCode((r&31)<<6|c2&63);n+=2}else{c2=e.charCodeAt(n+1);c3=e.charCodeAt(n+2);t+=String.fromCharCode((r&15)<<12|(c2&63)<<6|c3&63);n+=3}}return t}};

function mcShowLoginError(ae) {
	if ($('#username').val().trim() == '') $('#username').addClass('is-invalid');
	else $('#username').removeClass('is-invalid');
	if ($('#password').val().trim() == '') $('#password').addClass('is-invalid');
	else $('#password').removeClass('is-invalid');
}
function mcHideLoginError() {
	$('#username').removeClass('is-invalid');
	$('#password').removeClass('is-invalid');
}
function selectMemberToView(fldID) {
	var memnum = $('input#cp_vwbtn_memnum').val();
	if (memnum.length > 0) {
		var fnMemNumLookupResult = function(r) {
			if (r.success && r.memberid > 0) gotoMember('',r.memberid,r.membernumber,r.firstname);
			else {
				MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'Go to Member',
					iframe: true,
					contenturl: mca_msgtl_link+'&fldName='+fldID,
					strmodalfooter: {
						classlist: 'd-none'
					}
				});
				$('button#btnMCTopSearchMember').removeClass('d-none');
				$('div#divMCTopSearchMemberLoading').addClass('d-none');
			}
		};
		$('button#btnMCTopSearchMember').addClass('d-none');
		$('div#divMCTopSearchMemberLoading').removeClass('d-none');
		var objParams = { memberNumber:memnum };
		TS_AJX('MEMBER','getMemberDataByMemberNumber',objParams,fnMemNumLookupResult,fnMemNumLookupResult,1000000,fnMemNumLookupResult);
	} else {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Go to Member',
			iframe: true,
			contenturl: mca_msgtl_link+'&fldName='+fldID,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
		$('button#btnMCTopSearchMember').removeClass('d-none');
		$('div#divMCTopSearchMemberLoading').addClass('d-none');
	}
}

function gotoMember(fldID,mID,mNum,mName) {
	self.location.href = mca_mgtl_link + '&memberID=' + mID;
}

function removeWatermark() {
	$('#divMCMainContainer').removeClass('mc_beta_watermark mc_development_watermark');
	$('#remWatermarkLink, #divRecentMCUpdates, #divSuperKey').addClass('d-none');
	try { TS_AJX('AJAXUTILS','clearCPWatermark'); } catch (e) { };
}
function mca_getAdminAlert(params,successCallback,failCallback) {
	$.ajax({
		type: 'GET',
		url: '?event=proxy.ts_json&c=ADMALRT&m=getCPAlerts',
		data: params,
	}).done(successCallback).fail(failCallback);
}
function mca_showAdminAlerts() {
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'xl',
		title: 'Control Panel Alerts',
		iframe: false,
		strmodalbody: { 
			content: $('#divAdminAlertMsg').html()
		},
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true
		}
	});
	$('.alertTabNav').first().trigger('click');
}
function mca_alertcheckR(r) {
	if (r.success == true && r.arralerts.length > 0) { 
		var selectdiv = 1;
		var [tabmsg,source,template,tabLinks,tabContent] = ['','','','','',''];
		var divmsg = '<ul id="ControlPanelAlertTabs" class="nav nav-pills nav-pills-dotted">{{tablinks}}</ul>';
		var totalIssueCount = 0;
		r.arralerts.forEach(function(thisAlert) {
			var renderedTemplate;
			source = $('#'+thisAlert.template).html();
			template = Handlebars.compile(source);
			tabmsg = tabmsg + '&nbsp; <i class="' + thisAlert.icon + '"></i> ' + thisAlert.count;
			tabtitle = '<i class=\'' + thisAlert.icon + '\'></i> ' + thisAlert.title + '<span class="badge badge-danger ml-1"><b>' + thisAlert.count + '</b></span>';
			totalIssueCount = totalIssueCount + thisAlert.count;

			tabLinks = tabLinks + '<li class="nav-item"><a href="javascript:void(0);" data-tabname="tab-' + thisAlert.uid + '" role="tab" class="nav-link alertTabNav' + ((selectdiv == 1)? ' active' : '' )+'">'+ tabtitle +'</a></li>';

			try {
				renderedTemplate = template(thisAlert);
			} catch(e) {
				renderedTemplate = "There was an error displaying this information, please contact support.";
			}

			tabContent = tabContent + '<div id="tab-' + thisAlert.uid + '" role="tabpanel" class="tab-pane tab-' + thisAlert.uid+'Content ' + ((selectdiv == 1)? ' active' : '' )+' "><div class="alert alert-danger">' + thisAlert.msg + '</div>' + renderedTemplate + '</div>';
			selectdiv = 0;
		});
		divmsg = divmsg.replace('{{tablinks}}',tabLinks) + '<div class=" tab-content mc_tabcontent p-3 pb-0 tabContentWrap alertTabContentWrapper">'+ tabContent +'</div>';
		$('div#divAdminAlertMsg').html(divmsg);
		$('div#MCAlertTab').html('<a href="" onclick="mca_showAdminAlerts(); return false;" class="d-inline-block"><span class="badge badge-pill badge-danger">Issues</span><span class="text-danger d-md-none">' + totalIssueCount + '</span><span class="text-danger d-none d-md-inline-block">' + tabmsg + '</span></a>');

	} else {
		$('div#MCAlertTab').html('');
	}
}
function mca_alertcheck() {
	mca_getAdminAlert({alertType:'standard'},mca_alertcheckR,mca_alertcheckR);
}
function deleteTransAlert(id) {
	if (confirm('Are you sure you want to delete this message?')) {
		var objParams = { alertID:id };
		$.getJSON('/?event=proxy.ts_json&c=ADMALRT&m=deleteTransactionAlert', objParams).done(removeTransAlertRow).fail(removeTransAlertRow);
	}
}
function removeTransAlertRow(r) {
	if (r.success == true && r.alertid > 0) {
		$('#trTransAlertRow'+r.alertid).remove();
	} else {
		alert('An error occured while removing the message. Try again.');
	}
}
function mca_announcecheckR(r) {
	if (r.success == true && r.arralerts.length && r.arralerts[0].arrannouncements.length > 0) { 
		var source = $('#mca_announcements_template').html();
		var template = Handlebars.compile(source);
		$('#divMCAnnouncementsArea').html(template(r.arralerts[0]));
		$('#divMCAnnouncements').removeClass('d-none');
	}
}
function mca_announcecheck() {
	mca_getAdminAlert({alertType:'getAnnouncements'},mca_announcecheckR,mca_announcecheckR);
}
function mca_updatecheckR(r) {
	if (r.success == true && r.arralerts.length && r.arralerts[0].data && r.arralerts[0].data.length > 0) { 
		var source = $('#mca_update_template').html();
		var template = Handlebars.compile(source);
		$('#divRecentMCUpdatesArea').html(template(r.arralerts[0]));
		$('#divRecentMCUpdates').removeClass('d-none');
	}
}
function mca_updatecheck(articleCount) {
	mca_getAdminAlert({alertType:'getUpdates', articleCount:articleCount},mca_updatecheckR,mca_updatecheckR);
}
function mca_getSiteToolLink(s,tt,ta,mtd,p) {
	let strData = { "s":s, "tt":tt, "ta":ta, "mtd":mtd, "p":p };
	return mca_st_link + '&mctinfo=' + Base64.encode(JSON.stringify(strData));
}
function mca_manageGroup(gid,selectedTab) {
	let hideModalFooter = selectedTab && selectedTab != 'groupSettings';
	
	if (top.MCModalUtils.isShown()) {
		top.MCModalUtils.showLoading('Manage Group');
		if (!top.$('#MCModal .modal-dialog-slideout').hasClass('modal-xl'))
			top.$('#MCModal .modal-dialog-slideout').removeClass('modal-lg modal-sm').addClass('modal-xl');
		top.MCModalUtils.buildFooter({ classlist: hideModalFooter ? 'd-none' : '' });
		self.location.href = top.mca_grp_link+'&groupID=' + gid + (selectedTab && selectedTab.length ? '&tab='+selectedTab : '');
	} else {
		MCModalUtils.showModal({
			isslideout: true,
			size: 'xl',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: 'Manage Group',
			iframe: true,
			contenturl: mca_grp_link+'&groupID=' + gid + (selectedTab && selectedTab.length ? '&tab='+selectedTab : ''),
			strmodalfooter : {
				classlist: hideModalFooter ? ' d-none' : '',
				showclose: false
			}
		});
	}
}
function mcActivateTooltip(scope) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	scope.find('[data-toggle="tooltip"]').tooltip({trigger : 'hover'});
}

/* designed to be called directly as a DataTable.js createdRow event handler */
function mca_activateDataTableRowTooltip(row, data, index) {
	$($('[title]:not([data-toggle])',row).find('i')).attr('data-toggle','tooltip');
	$('[title]:not([data-toggle])',row).attr('data-toggle','tooltip');
	$('[title]:not([data-toggle])',row).addClass('toolTipWrap'+row);
	$('[data-toggle="tooltip"]',row).tooltip({trigger : 'hover',template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner" style="width:500px"></div></div>'})
	.on('click',this,function(){
		$(this).tooltip('hide');
	});
	$('[data-toggle="tooltip"] i',row).tooltip({trigger : 'hover',template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner" style="width:500px"></div></div>'})
	.on('click',this,function(){
		$(this).tooltip('hide');
	});
	
}

function mca_setupSelect2(scope,dropdownParent) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	scope.find('[data-toggle="custom-select2"]').each((function () {
		var multipleAttr = $(this).prop('multiple');
		var clearAttr = $(this).data('allowclear');
		var isMultiple = (typeof multipleAttr !== typeof undefined && multipleAttr !== false) ? true : false;
		var allowClear = (typeof clearAttr !== typeof undefined) ? clearAttr : true;
		var strConfig = {
			theme: 'bootstrap4',
			width: '100%',
			multiple: isMultiple,
			placeholder: $(this).attr('placeholder') || '',
			allowClear: allowClear,
			closeOnSelect: isMultiple ? false : true
		};
		if (typeof dropdownParent != 'undefined') { strConfig.dropdownParent = dropdownParent; };
		$(this).select2(strConfig);
	}));
}
function mca_setupSelect2ByID(id) {
	var thisElement = $('#'+id);

	if (thisElement.hasClass("select2-hidden-accessible"))
		thisElement.select2('destroy');

	var multipleAttr = thisElement.prop('multiple');
	var clearAttr = thisElement.data('allowclear');
	var isMultiple = (typeof multipleAttr !== typeof undefined && multipleAttr !== false) ? true : false;
	var allowClear = (typeof clearAttr !== typeof undefined) ? clearAttr : true;
	thisElement.select2({
		theme: 'bootstrap4',
		width: '100%',
		multiple: isMultiple,
		placeholder: thisElement.attr('placeholder') || '',
		allowClear: allowClear,
		closeOnSelect: isMultiple ? false : true
	});
}
function mca_setupCustomFileControls(formName) {
	$('form#'+ formName +' div.custom-file input.custom-file-input').on('change',function(){
		var fileName = $(this).val();
		$(this).next('.custom-file-label').html(fileName);
	});
}
function mcActivateBSToggle(scope) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	scope.find('[data-toggle="toggle"]').bootstrapToggle('destroy').bootstrapToggle();
}
function mca_initNavPills(navID, selectedTab, lockTab, onTabChangeHandler){
	if (onTabChangeHandler && (typeof onTabChangeHandler == "function")){
		$('#'+ navID).find('a[data-toggle="pill"],a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
			onTabChangeHandler(e.target);
		})
	}

	var tabSelector = '#'+ navID +' li:first-child a';
	if(selectedTab && selectedTab.length) {
		var selectedTabID = $('#' + navID).find(`[data-tabname='${selectedTab}']`).attr('id');
		if(selectedTabID) tabSelector = '#'+ navID +' a#' + selectedTabID;
	}
	$(tabSelector).tab('show');

	if(lockTab && lockTab.length){
		var lockedTabID = $('#' + navID).find(`[data-tabname='${lockTab}']`).attr('id');
		if(lockedTabID) $('#'+ navID +' a:not(#'+ selectedTabID+')').addClass('disabled');
	}
}
function mca_initConfirmButton(actionBtn, onConfirmHandler, ovActionBtnHTML, ovConfirmBtnHTML, onConfirmLoadingText){
	if (actionBtn.attr('data-confirm') == 1) {
		actionBtn.attr('data-confirm',0).addClass('disabled').html(onConfirmLoadingText ? onConfirmLoadingText : 'Deleting...');
		onConfirmHandler();
	} else {
		actionBtn.attr('data-confirm',1).html(ovConfirmBtnHTML ? ovConfirmBtnHTML : '<i class="fa-solid fa-circle-info"></i> Confirm');
		setTimeout(function(actionBtn){ 
			if (actionBtn.attr('data-confirm') == 1) {
				actionBtn.attr('data-confirm',0).removeClass('disabled').html(ovActionBtnHTML ? ovActionBtnHTML : '<i class="fa-solid fa-trash-can"></i>');
			}
		},2500,actionBtn);
	}
}
function mca_setupTagsInput(el, alertEl, strRegex, label, animateToTop, confirmKeys, delimiter, maxTags){
	var selector = (typeof el == 'object' && Array.isArray(el)) ? $('#'+ el.join(', #')) : $('#'+ el);

	selector.tagsinput({
		confirmKeys: confirmKeys ? confirmKeys : [13,32,44,59], /*enter, space, comma, and semi-colon*/
		trimValue: true,
		delimiter: delimiter ? delimiter : ';',
		maxTags: maxTags
	});
	selector.on('beforeItemAdd', function(event) {
		mca_hideAlert(alertEl);
		var thisRegEx = new RegExp(strRegex,"i");
		if (event.item.length && !(thisRegEx.test(event.item))) {
			mca_showAlert(alertEl, event.item + ' is an invalid ' + label, animateToTop);
			event.cancel = true;
		}
	});
}
function mca_callChainedSelect(element1, element2, resourceUrl, jsonlib, method, varName, elemIdDefault, isMultiselect, removeEmptyOption, arrOptionAttr){
	var strSelected = $("#" + element1).val();
	mca_chainedSelect(
		element2,      /* select box id  */
		elemIdDefault, /* select box default value */
		strSelected,   /* value of the select */
		resourceUrl,   /* resourceurl for ajax */
		jsonlib,       /* json library */
		method,        /* method to be run */
		varName,       /* parameter variable*/ 
		isMultiselect, /* refresh multiselect */
		removeEmptyOption || false, /* remove option with value 0 */
		arrOptionAttr || []
	);
}
function mca_chainedSelect(elemIdName, elemIdDefault, selected, resourceUrl, jsonlib, method, varName, isMultiselect, removeEmptyOption, arrOptionAttr){
	var strURL = resourceUrl+'&mode=stream&pg=admin&mca_jsonlib='+jsonlib+'&mca_jsonfunc='+method+'&'+varName+'='+selected;
	var txtColumnID = 1;
	$.ajax({
		url: strURL,
		dataType: 'json',
		success: function(response){
			$('#' + elemIdName).empty();
			var selectOption = false;
			var elemIdDefaultArr = new Array();
			var tempString = elemIdDefault.toString();
			elemIdDefaultArr = tempString.split(',');
			for (var i = 0; i < response.DATA.length; i++) {
				var o = new Option(response.DATA[i][txtColumnID], response.DATA[i][0]);
				/* jquerify the DOM object 'o' so we can use the html method */
				$(o).html(response.DATA[i][txtColumnID]);
				/* add extra attributes to option, if defined */
				if(arrOptionAttr.length && response.COLUMNS && response.COLUMNS.length){
					$.each(arrOptionAttr, function(index,obj){
						var valIndex = response.COLUMNS.indexOf(obj.datafield.toUpperCase());
						if(valIndex != -1){
							o.setAttribute(obj.name,response.DATA[i][valIndex]);
						}
					});
				}
				$('#' + elemIdName).append(o);
				
				for (var subStr in elemIdDefaultArr ) {
					elemIdDefaultArr[subStr] = parseInt(elemIdDefaultArr[subStr], 10);
					if (elemIdDefaultArr[subStr] >= 0 && response.DATA[i][0] == elemIdDefaultArr[subStr]) {
						selectOption = true;
						break;
					}
				}
			}
			if (selectOption){
				for (var subStr in elemIdDefaultArr ) {
					elemIdDefaultArr[subStr] = parseInt(elemIdDefaultArr[subStr], 10);
					$('#' + elemIdName + " option[value='" + elemIdDefaultArr[subStr] + "']").prop('selected',true);
				}
			}
		
			if(removeEmptyOption)
				$("#" + elemIdName + " option[value='0']").remove();

			if (isMultiselect) {
				var isSelect2Control = ($('#' + elemIdName).data('toggle') || '') == 'custom-select2';
				if(isSelect2Control) {
					if (!selectOption) { $('#' + elemIdName).val(null).trigger('change'); }
					else { $('#' + elemIdName).trigger('change'); }
				}
				else {
					$('#' + elemIdName).multiselect("refresh");
					if (!selectOption) { $('#' + elemIdName).multiselect("uncheckAll"); }
				}
			}
		},
		error: function(ErrorMsg){ }
	})
}
function mca_getLoadingHTML(msg,noMargin) {
	var loadingText = msg ? msg : 'Please wait...';
	return '<div class="text-center '+ (noMargin ? '' : 'mt-4') +'"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div><div class="font-weight-bold mt-2">'+ loadingText +'</div></div>';
}
function mca_getOverlayHTML() {
	return '<div class="mc_overlay"><div class="w-100 h-100 d-flex justify-content-center align-items-center"><div class="spinner-border m-2 text-primary" role="status"><span class="sr-only">Loading...</span></div></div></div>';
}
function mca_setupJumpTo(scope) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	scope.find('.mc_jumpto').on('click', function(event) {
		event.preventDefault();
		var jumpTo = $('#' + $(this).data('jumpto'));
		if(jumpTo.length && jumpTo.is(':visible')) {
			$('html, body').animate({
				scrollTop: jumpTo.offset().top - 175
			}, 750);
		}
	});
}
function mca_setupScrollable(scope) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	scope.find('.scrollbar-container').each((function() {
		const ps = new PerfectScrollbar($(this)[0], {
			wheelSpeed: 2,
			wheelPropagation: false,
			minScrollbarLength: 20
		});
	}));
}
function mca_resetGridRowMove(thisGrid, rowID, dir, moveUpColNum, moveDownColNum, moveUpTitle, moveDownTitle, moveUpIcon, moveDownIcon, destRowFromAttr, preOpHandler, postOpHandler) {
	var destRowID = mca_getGridDestinationRowID(thisGrid, rowID, dir, destRowFromAttr);
	
	var destRowMoveUpCell = thisGrid.cellById(destRowID, moveUpColNum);
	var destRowMoveDownCell = thisGrid.cellById(destRowID, moveDownColNum);
	var currRowMoveUpCell = thisGrid.cellById(rowID, moveUpColNum);
	var currRowMoveDownCell = thisGrid.cellById(rowID, moveDownColNum);

	var preOpResult = {};
	if(preOpHandler && typeof preOpHandler === "function") preOpResult = preOpHandler(thisGrid, rowID, destRowID);

	var spacerImgStr = '/assets/common/images/spacer.gif^^';
	
	if(dir == 'up'){
		/*curr row moving to first row and was staying as last one!*/
		if(currRowMoveDownCell.getValue().split('^').length < 4 && destRowMoveUpCell.getValue().split('^').length < 4) {
			currRowMoveDownCell.setValue(currRowMoveUpCell.getValue().replace(moveUpTitle, moveDownTitle).replace(moveUpIcon, moveDownIcon).replace('"up"', '"down"'));
			currRowMoveUpCell.setValue(spacerImgStr);
			destRowMoveUpCell.setValue(destRowMoveDownCell.getValue().replace(moveDownTitle, moveUpTitle).replace(moveDownIcon, moveUpIcon).replace('"down"', '"up"'));
			destRowMoveDownCell.setValue(spacerImgStr);
		}
		/*curr row moving to first row and was not staying as last one!*/
		else if(destRowMoveUpCell.getValue().split('^').length < 4 && currRowMoveUpCell.getValue().split('^').length >= 4) {
			destRowMoveUpCell.setValue(destRowMoveDownCell.getValue().replace(moveDownTitle, moveUpTitle).replace(moveDownIcon, moveUpIcon).replace('"down"', '"up"'));
			currRowMoveUpCell.setValue(spacerImgStr);
		}
		/*curr row not moving to first row and was staying as last one!*/
		else if(currRowMoveDownCell.getValue().split('^').length < 4 && destRowMoveUpCell.getValue().split('^').length >= 4) {
			currRowMoveDownCell.setValue(currRowMoveUpCell.getValue().replace(moveUpTitle, moveDownTitle).replace(moveUpIcon, moveDownIcon).replace('"up"', '"down"'));
			destRowMoveDownCell.setValue(spacerImgStr);
		}
	}
	else if(dir == 'down'){
		/*curr row moving to last row and was staying as first one!*/
		if(currRowMoveUpCell.getValue().split('^').length < 4 && destRowMoveDownCell.getValue().split('^').length < 4) {
			currRowMoveUpCell.setValue(currRowMoveDownCell.getValue().replace(moveDownTitle, moveUpTitle).replace(moveDownIcon, moveUpIcon).replace('"down"', '"up"'));
			currRowMoveDownCell.setValue(spacerImgStr);
			destRowMoveDownCell.setValue(destRowMoveUpCell.getValue().replace(moveUpTitle, moveDownTitle).replace(moveUpIcon, moveDownIcon).replace('"up"', '"down"'));
			destRowMoveUpCell.setValue(spacerImgStr);
		}
		/*curr row moving to last row and was not staying as first one!*/
		else if(destRowMoveDownCell.getValue().split('^').length < 4 && currRowMoveUpCell.getValue().split('^').length >= 4) {
			destRowMoveDownCell.setValue(destRowMoveUpCell.getValue().replace(moveUpTitle, moveDownTitle).replace(moveUpIcon, moveDownIcon).replace('"up"', '"down"'));
			currRowMoveDownCell.setValue(spacerImgStr);
		}
		/*curr row not moving to last row and was staying as first one!*/
		else if(currRowMoveUpCell.getValue().split('^').length < 4 && destRowMoveDownCell.getValue().split('^').length >= 4) {
			currRowMoveUpCell.setValue(currRowMoveDownCell.getValue().replace(moveDownTitle, moveUpTitle).replace(moveDownIcon, moveUpIcon).replace('"down"', '"up"'));
			destRowMoveUpCell.setValue(spacerImgStr);
		}
	}

	if(postOpHandler && typeof postOpHandler === "function") postOpHandler(thisGrid, rowID, destRowID, preOpResult);
	
	thisGrid.moveRow(rowID, dir);
	thisGrid._fixAlterCss();
}
function mca_getGridDestinationRowID(thisGrid, rowID, dir, destRowFromAttr){
	var destRowID;

	if(destRowFromAttr && destRowFromAttr.length){
		var currRowIndex = thisGrid.getRowIndex(rowID);
		var destRowID = '';
		
		var i = (dir == 'up' ? currRowIndex-1 : currRowIndex+1);
		var notReachedLimit = (dir == 'up' ? (i>=0)  : (i<thisGrid.getRowsNum()) )
		while(notReachedLimit) {
			var currRowID = thisGrid.getRowId(i);
			if(thisGrid.getRowAttribute(currRowID,destRowFromAttr) == 1) {
				destRowID = currRowID;
				break;
			}
			i = (dir == 'up' ? i-1 :  i+1);
			notReachedLimit = (dir == 'up' ? (i>=0)  : (i<thisGrid.getRowsNum()) )
		}
	}
	else destRowID = thisGrid.getRowId(thisGrid.getRowIndex(rowID) + (dir == 'up' ? -1 : 1));

	return destRowID;
}

function mca_hideAlert(el) {
	var selector = (typeof el == 'object' && Array.isArray(el)) ? $('#'+ el.join(', #')) : $('#'+ el);
	selector.html('').addClass('d-none');
};
function mca_showAlert(el, msg, animateToTop) { 
	$('#' + el).html(msg).removeClass('d-none');
	if(animateToTop) $("html, body").animate({ scrollTop: 0 }, "slow");
};
function mca_showAlertAutoClose(el, msg, cls, animateToTop) {
	$('#' + el).html('<div class="alert d-flex align-items-center pl-2 align-content-center alert-'+cls+' alert-dismissible fade show" role="alert">	<span class="font-size-lg d-block d-40 mr-2 text-center">'+((cls=='success')? '<i class="fa-solid fa-circle-check"></i>':'') + '</span><span>'+msg+'</span>	<button type="button" class="close" data-dismiss="alert" aria-label="Close">	<span aria-hidden="true">&times;</span>		</button>	</div>').removeClass('d-none');

	$('#' + el + ' .alert').delay(15000).fadeTo(200, 0).slideUp(200, function() {
		$(this).alert('close');
	});
	if(animateToTop) $("html, body").animate({ scrollTop: 0 }, "slow");
};
function mca_validateInteger(val) {
	var n = ~~Number(val);
	return String(n) === val;
}
function mca_formatInteger(num, nonZero) {
	return (!mca_validateInteger(num) || (nonZero && num == 0)) ? (nonZero ? '' : 0) : num;
}
function mca_stripCurrency(str){
	str += '';
	var rgx = /^\d|\.|-$/;
	var out = '';
	for( var i = 0; i < str.length; i++ ){ if( rgx.test( str.charAt(i) ) ){ if( !( ( str.charAt(i) == '.' && out.indexOf( '.' ) != -1 ) || ( str.charAt(i) == '-' && out.length != 0 ) ) ){ out += str.charAt(i); } } }
	return parseFloat(out);
}
function mca_isAlphaNumeric(val){
	var regex = /^[0-9A-Za-z\s]+$/;
	if(regex.test(val)) return true;
	else return false;
}

function copyEventLinkToClipboard(u){
	if (!Clipboard.isSupported()) {
		Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
			var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">Your browser does not support this feature.</div></div>';
			new Noty({ 
				type:'error',
				layout:'bottomLeft',
				theme:'bootstrap-v4',
				text:mc_noty_msg,
				closeWith:['button','click'],
				timeout:3000 
			}).show();
		});
	} else {
		$("body").append('<div id="evlinkcopydiv" class="d-none" data-clipboard-text="'+Base64.decode(u)+'"></div>');
		var evlinkClip = new Clipboard('div#evlinkcopydiv');
		evlinkClip.on('success', function(e) {
			Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
				var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">We\'ve copied the event link to your clipboard.</div></div>';
				new Noty({ 
					type:'success',
					layout:'bottomLeft', 
					theme:'bootstrap-v4', 
					text:mc_noty_msg,
					closeWith:['button','click'],
					timeout:3000 
				}).show();
			})
		});
		$('div#evlinkcopydiv').click(); // to trigger the clipboard
		$('div#evlinkcopydiv').remove(); // to remove the div
		evlinkClip.destroy(); // so subsequent clicks dont show the past noty messages.
	}
}
function mca_setUpCustomRangeBubble(scope) {
	if (typeof scope == 'undefined') var scope = $('#divMCMainContainer');
	
	var setBubble = function(range, bubble) {
		const val = range.val();
		const min = range.attr('min') ? range.attr('min') : 0;
		const max = range.attr('max') ? range.attr('max') : 100;
		const newVal = Number(((val - min) * 100) / (max - min));
		bubble.html(val);
		bubble.css('left',`calc(${newVal}% + (${8 - newVal * 0.15}px))`);
	};

	scope.find('.custom-range-wrap').each(function() {
		const range = $(this).find('.custom-range');
		const bubble = $(this).find('.custom-range-bubble');
		range.off('input');
		range.on('input',function() {
			setBubble(range, bubble);
		});
		setBubble(range, bubble);
	});
}
function mca_moveDataTableRow(rowID,dir,moveUpLinkClass,moveDownLinkClass) {
	let tRow = $('#'+rowID);
	if (dir == 'up') {
		let tRowPrev = tRow.prev();
		tRow.remove().insertBefore(tRowPrev);
	} else {
		let tRowNext = tRow.next();
		tRow.remove().insertAfter(tRowNext);
	}

	let tRowParent = $('#'+rowID).parent();
	if (tRowParent.find('tr').length > 1) {
		tRowParent.find('a.'+moveUpLinkClass+',a.'+moveDownLinkClass).removeClass('invisible');
		tRowParent.find('a.'+moveUpLinkClass+':first').addClass('invisible');
		tRowParent.find('a.'+moveDownLinkClass+':last').addClass('invisible');
	} else {
		tRowParent.find('a.'+moveUpLinkClass+',a.'+moveDownLinkClass).addClass('invisible');
	}
}
function mca_scrollTo(id,scrollDuration) {
	let MCScrollToElement = $('#'+id);
	if(MCScrollToElement.length && MCScrollToElement.is(':visible')) {
		$('html, body').animate({
			scrollTop: MCScrollToElement.offset().top - 150
		}, typeof scrollDuration != "undefined" ? scrollDuration : 400);
	}
	return false;
}
function changeCPUserPW() {
	$('.show.popover').popover('hide') ;
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'md',
		title: 'Change Password',
		iframe: true,
		contenturl: changePasswordLink,
		strmodalfooter: {
			classlist: 'd-flex',
			showclose: true,
			showextrabutton: true,
			extrabuttonclass: 'btn-primary ml-auto',
			extrabuttononclickhandler: 'changeCPUserPWValidate',
			extrabuttonlabel: 'Change Password'
		}
	});
}
function changeCPUserPWValidate(){
	$('#MCModalBodyIframe')[0].contentWindow._validateCPUserPWForm();				
}
function setDefaultNavMenu(obj) {
	$('.show.popover').popover('hide') ;
	MCModalUtils.showModal({
		isslideout: true,
		modaloptions: {
			backdrop: 'static',
			keyboard: false
		},
		size: 'lg',
		title: 'Set Default Control Panel Screen',
		iframe: true,
		contenturl: mca_df_navmenu + '&new_mca_a=' + obj.data.mca_a
	});
}

// acct
function mca_addPayment(po,pmtLink) {
	if (top.MCModalUtils.isShown()) {
		let addpmt_link = typeof pmtLink != "undefined" ? pmtLink : top.mca_link_addpmt;
		top.MCModalUtils.showLoading('Add Payment');
		top.$('#MCModalFooter').attr('class','modal-footer d-none');
		top.window.frames['MCModalBodyIframe'].location = addpmt_link+'&pa=pay&po='+escape(po);
	} else {
		let addpmt_link = typeof pmtLink != "undefined" ? pmtLink : mca_link_addpmt;

		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Add Payment',
			iframe: true,
			contenturl: addpmt_link+'&pa=pay&po='+escape(po)
		});
	}
}
function mca_allocIndivPayment(po,allocPmtLink) {
	let allocpmt_link = typeof allocPmtLink != "undefined" ? allocPmtLink : mca_link_allocpmt;
	top.MCModalUtils.showLoading('Allocate Payment');
	top.$('#MCModalFooter').attr('class','modal-footer d-none');
	window.frames['MCModalBodyIframe'].location = allocpmt_link+'&po='+escape(po);
}

// vgc
function mca_editVGC(cid,ctid,rid,rvid,csid,gnum,ac,rbm,errmsg) {
	let formOperation = cid > 0 ? 'Edit' : 'Create';
	let conditionWord, conditionTypeName, condTitle;

	switch(ctid) {
		case 1: 
			conditionWord = 'Condition';
			conditionTypeName = 'Group Assignment Condition';
			condTitle = "Add Condition to Rule";
			break;
		case 2: 
			conditionWord = 'Filter';
			conditionTypeName = 'Report Filter';
			condTitle = "Add Report Filter";
			break;
		case 3: 
			conditionWord = 'Condition';
			conditionTypeName = 'Email Blast Condition';
			condTitle = "Add Email Blast Condition";
			break;
	};

	let title = cid == 0 && rid && rid > 0 ? condTitle : formOperation + ' ' + conditionTypeName;
	if (rid > 0 && typeof window['mca_vgc_rule'+rid+'_title'] != "undefined")
		title = (cid > 0 ? 'Edit ' : 'Add ') + window['mca_vgc_rule'+rid+'_title'];

	// in flyout
	if (top.MCModalUtils.isShown()) {
		localStorage.setItem('mcrb_'+rid,JSON.stringify({rtnlink:location.href, title:top.$('#MCModalLabel').html()}));
		top.MCModalUtils.showLoading(title);
		top.$('#MCModalFooter').attr('class','modal-footer d-none');
		self.location.href = top.mca_getEditVGCLink(cid,ctid,rid,rvid,csid,gnum,ac,rbm,errmsg);
	} else {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: mca_getEditVGCLink(cid,ctid,rid,rvid,csid,gnum,ac,rbm,errmsg),
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
}
function mca_getEditVGCLink(cid,ctid,rid,rvid,csid,gnum,ac,rbm,errmsg) {
	let editVGCLink = mca_vgc_link + '&conditionTypeID=' + ctid + '&conditionID=' + cid + '&mcrb_gridnum=' + ((typeof gnum != "undefined" && gnum != '') ? gnum : 1);
	if (rid && rid > 0) editVGCLink += '&ruleID=' + rid;
	if (rvid && rvid > 0) editVGCLink += '&ruleVersionID=' + rvid;
	if (csid && csid.length) editVGCLink += '&csid=' + csid;
	if (ac && ac.length) editVGCLink += '&areaCode=' + ac;
	if (rbm && rbm.length) editVGCLink += '&rbmode=' + rbm;
	if (errmsg && errmsg.length) editVGCLink += '&err=' + errmsg;
	return editVGCLink;
}
function mca_showPermissions(rID,rName,onCloseFn,prName,rType,linkOverride,titleOverride,skipGrid,scriptCall,scriptCallParams,duplicateID) {
	let title, subTitle, contenturl;
	if(linkOverride) contenturl = linkOverride;
	else if (skipGrid && typeof variable !== 'mca_permsadd_link') contenturl = mca_permsadd_link;
	else if (typeof variable !== 'mca_perms_link') contenturl = mca_perms_link;
	else return false;
	contenturl += '&cmsRID=' + rID + (rName ? '&resourceName=' + rName : '') + (prName ? '&pResourceName=' + prName : '') + (scriptCall ? '&scriptCall=' + scriptCall : '') + (scriptCallParams ? '&scriptCallParamsString=' + scriptCallParams : '') + (duplicateID ? '&duplicateID=' + duplicateID : '');

	if(titleOverride) title = titleOverride;
	else {
		title = rName.length ? 'Security for ' + decodeURI(rName) : 'Security';
		subTitle = (prName || '') + ' ' + (rType || '');
		subTitle = subTitle.trim();
		title += (subTitle.length ? ' ('+ subTitle +')' : '');
	}
	if (top.MCModalUtils.isShown()) {
		let rtnlink;
		if (typeof variable !== 'mca_content_link'){
			rtnlink = mca_content_link+'&selectedTab=content';
		}else{
			rtnlink=location.href;
		}
		localStorage.setItem('mcrb_'+rID,JSON.stringify({rtnlink:rtnlink, title:top.$('#MCModalLabel').html()}));
		top.MCModalUtils.showLoading(title);
		
		top.MCModalUtils.buildFooter({
			classlist: 'text-right',
			showclose: false,
			buttons: [
				{
					class: "btn-primary btn-saveperms d-none",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSelectPermission :submit").click',
					label: 'Save Permission', 
					name: 'btnSavePerms',
					id: 'btnSavePerms'
				},
				{
					class: "btn-secondary py-1",
					clickhandler: '$("#MCModalBodyIframe")[0].contentWindow.backToPageContentList',
					label: 'Cancel', 
					name: 'btnCancelListPerms',
					id: 'btnCancelListPerms'
				}
			]
		});
		self.location.href = contenturl;
	} else {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: contenturl,
			strmodalfooter: {
				classlist: 'text-right',
				showclose: true,
				closebuttonlabel: 'Cancel',
				showextrabutton: true,
				extrabuttonclass: 'btn-primary btn-saveperms d-none',
				extrabuttononclickhandler: '$("#MCModalBodyIframe")[0].contentWindow.$("#frmSelectPermission :submit").click',
				extrabuttonlabel: 'Save Permission'
			}
		});
		if(onCloseFn) $('#MCModal').on('hidden.bs.modal', onCloseFn);
	}	
}
function mca_cancelMergeInstr() {
	$('#mca_mergeCodes').html('').hide();
	$('#adminwrapper').show();
}
function mca_showMergeInstr(contenturl){
	if (top.MCModalUtils.isShown()) {
		$('#adminwrapper').hide();
		if ($('#mca_mergeCodes').length == 0) $('body').append('<div id="mca_mergeCodes" class="p-3"><a href="" class="ml-auto" onclick="mca_cancelMergeInstr();"><i class="fa-solid fa-chevrons-left"></i> Back</a><div id="mergeCodes"></div></div>');

		$('#mergeCodes').html(mca_getLoadingHTML()).load(contenturl.replace('mode=direct','mode=stream'));

	} else {
		let title;
		title = '<span id="topModalTitle">Merge codes are supported in this content.</span>';
		
		MCModalUtils.showModal({
			isslideout: true,
			size: 'lg',
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			title: title,
			iframe: true,
			contenturl: contenturl
			
		});
	}
}
function mca_copyLinkToClipBoard(txt){
	if (!Clipboard.isSupported()) {
		Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
			var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg\"></i><div class=\"mc-noty\">Your browser does not support this feature.</div></div>';
			new Noty({ 
				type:'error',
				layout:'bottomLeft',
				theme:'bootstrap-v4',
				text:mc_noty_msg,
				closeWith:['button','click'],
				timeout:3000 
			}).show();
		});
	} else {
		$("body").append('<div id="mca_cpyclipboardtext" class="d-none" data-clipboard-text="'+Base64.decode(txt)+'"></div>');
		var mca_linkClip = new Clipboard('div#mca_cpyclipboardtext');
		mca_linkClip.on('success', function(e) {
			Promise.all([MCLoader.loadJS('/assets/common/javascript/noty/3.1.4/noty.min.js'),MCLoader.loadCSS('/assets/common/javascript/noty/3.1.4/noty.css')]).then(function(){
				var mc_noty_msg = '<div class=\"mc-noty-item\"><i class=\"fa-regular fa-thumbs-up fa-lg mt-2\"></i><div class=\"mc-noty\">We\'ve copied the following to your clipboard.</div><br/>' + e.text.replace(/\n/gi,'<br\/>') + '</div>';
				new Noty({ 
					type:'success',
					layout:'bottomLeft', 
					theme:'bootstrap-v4', 
					text:mc_noty_msg,
					closeWith:['button','click'],
					timeout:3000 
				}).show();
			});
		});
		$('div#mca_cpyclipboardtext').click();
		$('div#mca_cpyclipboardtext').remove(); 
		mca_linkClip.destroy();
	}
}
function mca_delayFunc(ms) {
	return new Promise(resolve => setTimeout(resolve, ms));
}

// create EmailUtils if it doesn't already exist
var EmailUtils = EmailUtils || {};
EmailUtils.resizePreviewMessageBody = function(iFrame) {
	var iFrameID = document.getElementById(iFrame);
	if(iFrameID) {
		var iframeheight = 0;
		var index = 0;	
		iframeheight = (iFrameID.contentWindow.document.body.scrollHeight || iFrameID.contentDocument.documentElement.scrollHeight) + 50;
		iFrameID.style.height = iframeheight + "px";
		if(iframeheight < 60){
			var iframeinterval = setInterval( function(){
				index= index + 1 ;
				iframeheight = (iFrameID.contentWindow.document.body.scrollHeight || iFrameID.contentDocument.documentElement.scrollHeight) + 50;
				if(iframeheight > 60) {
					iFrameID.style.height = iframeheight + "px";
					clearInterval(iframeinterval);
				}
				if(index > 10){
					iFrameID.style.height = 'auto';
					clearInterval(iframeinterval);
				}
				
			}, 1000);
		}
				
	}
}

// create MCModalUtils if it doesn't already exist
var MCModalUtils = MCModalUtils || {};

MCModalUtils.showModal = function (objModal) {
	let modalOptions = { show: true	};
	if (objModal.modaloptions) {
		if (typeof objModal.modaloptions.backdrop != "undefined") modalOptions.backdrop = objModal.modaloptions.backdrop;
		if (typeof objModal.modaloptions.keyboard != "undefined") modalOptions.keyboard = objModal.modaloptions.keyboard;
	}

	let displayMode;
	if (typeof objModal.isslideout != "undefined" && objModal.isslideout) displayMode = 'slideout';
	else if (typeof objModal.verticallycentered != "undefined" && objModal.verticallycentered) displayMode = 'verticallycentered';

	if (typeof objModal.strmodalbody != "object") objModal.strmodalbody = {};
	if (typeof objModal.strmodalfooter != "object") objModal.strmodalfooter = {};

	let strModalParams = {
		displaymode: displayMode,
		size: objModal.size,
		title: objModal.title,
		iframe: typeof objModal.iframe != "undefined" ? objModal.iframe : false,
		contenturl: typeof objModal.contenturl != "undefined" ? objModal.contenturl : '',
		contentobjparams: typeof objModal.contentobjparams != "undefined" ? objModal.contentobjparams : {},
		strmodalbody: {
			classlist: typeof objModal.strmodalbody.classlist != "undefined" ? objModal.strmodalbody.classlist : '',
			content: typeof objModal.strmodalbody.content != "undefined" ? objModal.strmodalbody.content : mca_getLoadingHTML()
		},
		strmodalfooter : {
			classlist: typeof objModal.strmodalfooter.classlist != "undefined" ? objModal.strmodalfooter.classlist : '',
			showclose: typeof objModal.strmodalfooter.showclose != "undefined" ? objModal.strmodalfooter.showclose : true,
			closebuttonlabel: typeof objModal.strmodalfooter.closebuttonlabel != "undefined" ? objModal.strmodalfooter.closebuttonlabel : 'Close',
			showextrabutton: typeof objModal.strmodalfooter.showextrabutton != "undefined" ? objModal.strmodalfooter.showextrabutton : false,
			extrabuttonclass: typeof objModal.strmodalfooter.extrabuttonclass != "undefined" ? objModal.strmodalfooter.extrabuttonclass : '',
			hasextrabuttononclickhandler: typeof objModal.strmodalfooter.extrabuttononclickhandler != "undefined" ? true : false,
			extrabuttononclickhandler: typeof objModal.strmodalfooter.extrabuttononclickhandler != "undefined" ? objModal.strmodalfooter.extrabuttononclickhandler : '',
			extrabuttonlabel: typeof objModal.strmodalfooter.extrabuttonlabel != "undefined" ? objModal.strmodalfooter.extrabuttonlabel : '',
			extrabuttoniconclass: typeof objModal.strmodalfooter.extrabuttoniconclass != "undefined" ? objModal.strmodalfooter.extrabuttoniconclass : '',
			disableextrabutton: ((typeof objModal.iframe != "undefined" && objModal.iframe) || (typeof objModal.contenturl != "undefined" && objModal.contenturl.length)) ? true : false,
			buttons: typeof objModal.strmodalfooter.buttons != "undefined" && Array.isArray(objModal.strmodalfooter.buttons) ? objModal.strmodalfooter.buttons : []
		}
	};

	let MCModalSource = $('#mca_modal_template').html();
	let MCModalTemplate = Handlebars.compile(MCModalSource);
	$('#MCModalContainer').html(MCModalTemplate(strModalParams)).removeClass('d-none');
	
	if (!strModalParams.iframe && strModalParams.contenturl.length) {
		$('#MCModalBody').load(strModalParams.contenturl, strModalParams.contentobjparams, function() { MCModalUtils.onLoadCompleteModal(); });
	}
	
	$('#MCModal').modal(modalOptions);
	$('#MCModal').on('hidden.bs.modal', MCModalUtils.onModalHidden);
}

MCModalUtils.showLoading = function(hdr,bdy) {
	let loadingHTML = '<div class="mc-skeleton mc-skeleton-text"></div>';
	let hdrLoadingHTML = '<div class="d-flex p-3">'+('<div class="col">'+('<div class="d-flex"><div class="col-auto px-1"><div class="mc-skeleton mc-skeleton-text" style="width:25px;height:25px;"></div></div><div class="col px-1">'+loadingHTML+'</div></div>').repeat(2)+'</div>').repeat(2)+'</div>';
	let bdyLoadingHTML = '<div class="p-3 my-5">'+('<div class="d-flex">'+('<div class="col">'+loadingHTML.repeat(2)+'</div>').repeat(2)+'</div>').repeat(2)+'</div>';
	$('#MCModalHeader').find('.MCModalSubTitle').remove();
	$('#MCModalLabel').html(hdr && hdr.length ? hdr : '<div class="mc-skeleton mc-skeleton-text w-50"></div>');
	$('#MCModalBodyIframe').contents().find('body').html(bdy && bdy.length ? bdy : (hdrLoadingHTML+bdyLoadingHTML).repeat(2));
}

MCModalUtils.renderInlinePage = function(newtitle,inlinecontenturl,oldtitle,strOldFtr,fnCallBack,scrollTo) {
	const finalUrl = inlinecontenturl.replace('mode=direct', 'mode=stream');
	sessionStorage.setItem('mca_pageInfo',JSON.stringify({title: oldtitle, strFtr: strOldFtr, scrollTo: scrollTo}));
	MCModalUtils.setTitle(newtitle);

	let MCModalIframe = $("#MCModalBodyIframe")[0].contentWindow;
	MCModalIframe.$('#adminwrapper').hide();
	if (MCModalIframe.$('#mca_inlinePage').length == 0) MCModalIframe.$('body').append('<div id="mca_inlinePage" class="p-3"></div>');
	MCModalIframe.$('#mca_inlinePage').html(mca_getLoadingHTML()).show();
	if (typeof fnCallBack === 'function') {
		MCModalIframe.$('#mca_inlinePage').load(finalUrl, fnCallBack);
	} else {
		MCModalIframe.$('#mca_inlinePage').load(finalUrl);
	}
}

MCModalUtils.renderMainPage = function() {
	let MCModalIframe = $("#MCModalBodyIframe")[0].contentWindow;
	let pageInfo = JSON.parse(sessionStorage.getItem('mca_pageInfo'));
	MCModalUtils.setTitle(pageInfo.title);
	MCModalUtils.buildFooter(pageInfo.strFtr);
	MCModalIframe.$('#mca_inlinePage').html('').hide();
	MCModalIframe.$('#adminwrapper').show();
	if (pageInfo.scrollTo.length) mca_delayFunc(400).then(() => MCModalIframe.mca_scrollTo(pageInfo.scrollTo));
	sessionStorage.removeItem('mca_pageInfo');
}

MCModalUtils.setTitle = function(t,isHTML) {
	$('#MCModalHeader').find('.MCModalSubTitle').remove();
	if (isHTML) $('#MCModalLabel').html(t);
	else $('#MCModalLabel').html(t.length > 60 ? (t.substring(0,59) + '...') : t);
}

MCModalUtils.buildFooter = function(objFtr) {
	let strModalFooterParams = {
		classlist: typeof objFtr.classlist != "undefined" ? objFtr.classlist : '',
		showclose: typeof objFtr.showclose != "undefined" ? objFtr.showclose : true,
		closebuttonlabel: typeof objFtr.closebuttonlabel != "undefined" ? objFtr.closebuttonlabel : 'Close',
		showextrabutton: typeof objFtr.showextrabutton != "undefined" ? objFtr.showextrabutton : false,
		extrabuttonclass: typeof objFtr.extrabuttonclass != "undefined" ? objFtr.extrabuttonclass : '',
		hasextrabuttononclickhandler: typeof objFtr.extrabuttononclickhandler != "undefined" ? true : false,
		extrabuttononclickhandler: typeof objFtr.extrabuttononclickhandler != "undefined" ? objFtr.extrabuttononclickhandler : '',
		extrabuttonlabel: typeof objFtr.extrabuttonlabel != "undefined" ? objFtr.extrabuttonlabel : '',
		extrabuttoniconclass: typeof objFtr.extrabuttoniconclass != "undefined" ? objFtr.extrabuttoniconclass : '',
		buttons: typeof objFtr.buttons != "undefined" && Array.isArray(objFtr.buttons) ? objFtr.buttons : []
	};
	let MCModalFooterSource = $('#mca_modal_footer_template').html();
	let MCModalFooterTemplate = Handlebars.compile(MCModalFooterSource);
	$('#MCModalFooter').html(MCModalFooterTemplate(strModalFooterParams)).attr('class','modal-footer');
	if (strModalFooterParams.classlist.length) $('#MCModalFooter').addClass(strModalFooterParams.classlist);
}

MCModalUtils.isShown = () => $('#MCModal').data('bs.modal')?._isShown;

MCModalUtils.onLoadCompleteModal = function (modalElem) {
	let modalScope = modalElem ? modalElem : $('#MCModalFooter');
	modalScope.find('button').prop('disabled',false);
}

MCModalUtils.hideModal = function (modalElem) {
	let modalScope = modalElem ? modalElem : $('#MCModal');
	modalScope.modal('hide');
}

MCModalUtils.onModalHidden = function (e) {
	MCModalUtils.disposeOnClose(e);
	$('#MCModalContainer').html('').addClass('d-none');
}

MCModalUtils.disposeOnClose = function (e) {
	$(e.target).modal('dispose');
}

MCModalUtils.hideZendeskWebWidgetWhenModalOpens = function (e) {
	/* hide zendesk webwidget if it exists */
	if (typeof zE == 'function') zE.hide()
};

MCModalUtils.showZendeskWebWidgetWhenModalCloses = function (e) {
	/* show zendesk webwidget if it exists */
	if (typeof zE == 'function') zE.show()
};

$().ready(function() {
	$('body').on('shown.bs.modal', MCModalUtils.hideZendeskWebWidgetWhenModalOpens);
	$('body').on('hidden.bs.modal', MCModalUtils.showZendeskWebWidgetWhenModalCloses);

	$(document).on('click','.alertTabNav',function(){
		$('.alertTabNav').removeClass('active');
		$(this).addClass('active');
		tabName = $(this).attr('data-tabname');
		$('.alertTabContentWrapper.tabContentWrap > .tab-pane').removeClass('active');
		$('.alertTabContentWrapper.tabContentWrap .'+tabName+'Content').addClass('active');
	});

	if ($.fn.dataTable) {
		$.extend(true, $.fn.dataTable.defaults, {
			"language": {
				"processing": '<div class="w-100 h-100 d-flex justify-content-center align-items-center"><i class="spinner-border m-1 text-primary" role="status"></i> <span>&nbsp;Loading...</span></div>'
			}
		});
		$(document).on('preDraw.dt', function (ev, settings) {
			$(settings.nTableWrapper).find('table.dataTable').addClass('mc_dt_blur');
		});
		$(document).on('draw.dt', function (ev, settings) {
			$(settings.nTableWrapper).find('table.dataTable').removeClass('mc_dt_blur');
		});
	}
});