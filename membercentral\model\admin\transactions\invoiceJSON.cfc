<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			// SET RIGHTS INTO EVENT -------------------------------------------------------------------- ::
			this.siteResourceID = arguments.event.getTrimValue('srID',0);
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getInvoiceList" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>
		<cfset local.objInvoiceAdmin = CreateObject('component','model.admin.transactions.invoiceAdmin')>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfscript>
		arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
		arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
		arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
		arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
		arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

		arguments.event.paramValue('statusID','');
		arguments.event.paramValue('ip','');
		arguments.event.setValue('vn',reReplace(arguments.event.getTrimValue('vn',''),'[^0-9]','','ALL'));
		arguments.event.paramValue('ds','');
		arguments.event.paramValue('de','');
		arguments.event.paramValue('bs','');
		arguments.event.paramValue('be','');
		arguments.event.paramValue('cof','');
		arguments.event.paramValue('am','');
		arguments.event.paramValue('ag','');
		arguments.event.paramValue('lr','');
		arguments.event.paramValue('ar','');
		arguments.event.paramValue('at','');
		arguments.event.paramValue('as','');
		arguments.event.paramValue('ae','');
		arguments.event.paramValue('ias','');
		arguments.event.paramValue('iae','');			
		if (len(arguments.event.getValue('as')))
			arguments.event.setValue('as',val(ReReplace(arguments.event.getValue('as'),'[^0-9\.]','','ALL')));
		if (len(arguments.event.getValue('ae')))
			arguments.event.setValue('ae',val(ReReplace(arguments.event.getValue('ae'),'[^0-9\.]','','ALL')));
		if (len(arguments.event.getValue('ias')))
			arguments.event.setValue('ias',val(ReReplace(arguments.event.getValue('ias'),'[^0-9\.]','','ALL')));
		if (len(arguments.event.getValue('iae')))
			arguments.event.setValue('iae',val(ReReplace(arguments.event.getValue('iae'),'[^0-9\.]','','ALL')));

		local.chkAllState = arguments.event.getValue('chkAll',0);
		</cfscript>
		
		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.event.getTrimValue('cof')>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"tmp2.dateDue #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"tmp2.dateDue #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"tmp2.invoiceNumber #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"tmp2.memberName #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"sum(it.cache_invoiceAmountAfterAdjustment) #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<!--- if associated with and Association rules ("consider linked records" option)) are not empty --->
		<cfset local.runGetAssociated = false>
		<cfif len(trim(arguments.event.getValue('at'))) and len(trim(arguments.event.getValue('ar')))>
			<cfset local.runGetAssociated = true>
		</cfif>

		<!--- get invoices --->
		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#" result="local.qryResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpinnerTbl') is not null
					DROP TABLE ##tmpinnerTbl;
				IF OBJECT_ID('tempdb..##tmpinvoices') is not null
					DROP TABLE ##tmpinvoices;
				CREATE TABLE ##tmpinnerTbl (invoiceID int PRIMARY KEY, invoiceNumber varchar(19), dateCreated datetime, dateBilled datetime, dateDue datetime, 
					assignedToMemberID int, memberName varchar(300), company varchar(200), invoiceStatus varchar(10), invoiceProfile varchar(50), hasCard bit);
				CREATE TABLE ##tmpinvoices (invoiceID int PRIMARY KEY, invoiceNumber varchar(19), dateCreated datetime, dateBilled datetime, dateDue datetime, 
					invoiceStatus varchar(10), invoiceProfile varchar(50), assignedToMemberID int, memberName varchar(300), company varchar(200), hasCard bit, 
					invAmt decimal(14,2), invDue decimal(14,2), inPaymentQueue bit, row int);

				DECLARE @orgID int, @totalCount int, @posStart int, @posStartAndCount int;
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

				<cfif local.runGetAssociated>
					#local.objInvoiceAdmin.getAssociatedWithQuery(associatedMemberID=val(arguments.event.getValue('am')), associatedGroupID=val(arguments.event.getValue('ag')),
						linkedRecords=arguments.event.getValue('lr'), linkedRecordOptions=arguments.event.getValue('ar'), assocType=arguments.event.getValue('at'))#
				</cfif>
				
				INSERT INTO ##tmpinnerTbl (invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, assignedToMemberID, memberName, company, 
					invoiceStatus, invoiceProfile, hasCard)
				select i.invoiceID, i.fullInvoiceNumber as invoiceNumber, 
					i.dateCreated, i.dateBilled, i.dateDue, m2.memberID as assignedToMemberID, 
					m2.lastname 
					+ case when o.hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
					+ ', ' + m2.firstname 
					+ case when o.hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
					+ ' (' + m2.membernumber + ')' as memberName, m2.company, 
					istat.status as invoiceStatus, ip.profileName as invoiceProfile,
					case when i.payProfileID is null then 0 else 1 end as hasCard
				from dbo.tr_invoices as i
				inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
				inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
				inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
				inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
				inner join dbo.organizations as o on o.orgID = m.orgID
				<cfif local.runGetAssociated>
					inner join ##tmpAWQInvoices as assocInv on assocInv.invoiceID = i.invoiceID
				</cfif>			
				where i.orgID = @orgID
				<cfif len(arguments.event.getTrimValue('statusID'))>
					and i.statusID in (<cfqueryparam value="#arguments.event.getValue('statusID')#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
				<cfif len(arguments.event.getTrimValue('ip'))>
					and i.invoiceProfileID in (<cfqueryparam value="#arguments.event.getValue('ip')#" cfsqltype="CF_SQL_INTEGER" list="yes">)
				</cfif>
				<cfif val(arguments.event.getValue('vn')) gt 0 and isValid("integer",val(arguments.event.getValue('vn')))>
					and i.invoiceNumber = <cfqueryparam value="#val(arguments.event.getValue('vn'))#" cfsqltype="CF_SQL_INTEGER">
				<cfelseif val(arguments.event.getValue('vn')) gt 0>
					and i.invoiceNumber = 0
				</cfif>
				<cfif len(arguments.event.getValue('ds'))>
					and i.dateDue >= <cfqueryparam value="#arguments.event.getValue('ds')#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getValue('de'))>
					and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('de'))#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getValue('bs'))>
					and i.dateBilled >= <cfqueryparam value="#arguments.event.getValue('bs')#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getValue('be'))>
					and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('be'))#" cfsqltype="CF_SQL_DATE">
				</cfif>
				<cfif len(arguments.event.getValue('cof'))>
					and (
						<cfif local.cof0>
							i.payProfileID is null
						</cfif>
						<cfif local.cof0 and listLen(local.cofList)>
							or 
						</cfif>
						<cfif listLen(local.cofList)>
							i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
						</cfif>
						)
				</cfif>;

				INSERT INTO ##tmpinvoices (invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, invoiceStatus, invoiceProfile, assignedToMemberID, 
					memberName, Company, hasCard, invAmt, invDue, inPaymentQueue, row)
				select tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateCreated, tmp2.dateBilled, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
					tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, tmp2.hasCard,
					sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
					sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
					inPaymentQueue = cast(case when api.invoiceID is not null then 1 else 0 end as bit),
					ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row 
				from ##tmpinnerTbl as tmp2
				left outer join dbo.tr_invoiceTransactions as it 
				<cfif len(arguments.event.getTrimValue('trd',''))>
					inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
				</cfif>
				on it.orgID = @orgID and it.invoiceID = tmp2.invoiceID
				left outer join (
					select qpid.invoiceID
					from platformQueue.dbo.queue_payInvoicesDetail as qpid
					inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
					inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
					where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
					) api on api.invoiceID = tmp2.invoiceID
				<cfif len(arguments.event.getTrimValue('trd',''))>
					where t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('trd')#%">
				</cfif>
				group by tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateCreated, tmp2.dateBilled, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
					tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, tmp2.hasCard, api.invoiceID
				having tmp2.invoiceID = tmp2.invoiceid
				<cfif len(arguments.event.getValue('as'))>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#arguments.event.getValue('as')#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(arguments.event.getValue('ae'))>
					and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#arguments.event.getValue('ae')#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(arguments.event.getValue('ias'))>
					and sum(it.cache_invoiceAmountAfterAdjustment) >= <cfqueryparam value="#arguments.event.getValue('ias')#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>
				<cfif len(arguments.event.getValue('iae'))>
					and sum(it.cache_invoiceAmountAfterAdjustment) <= <cfqueryparam value="#arguments.event.getValue('iae')#" cfsqltype="CF_SQL_DECIMAL" scale="2">
				</cfif>;

				SET @totalCount = @@ROWCOUNT;

				SELECT invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, invoiceStatus, invoiceProfile, 
					assignedToMemberID, memberName, Company, hasCard, invAmt, invDue, inPaymentQueue, row,
					@totalCount AS totalCount, 'invoiceRow_' + CAST(invoiceID AS varchar(10)) AS DT_RowId
				FROM ##tmpinvoices
				WHERE row > @posStart 
				AND row <= @posStartAndCount
				ORDER BY row;
				
				IF OBJECT_ID('tempdb..##tmpinnerTbl') is not null
					DROP TABLE ##tmpinnerTbl;
				IF OBJECT_ID('tempdb..##tmpinvoices') is not null
					DROP TABLE ##tmpinvoices;
				<cfif local.runGetAssociated>
					IF OBJECT_ID('tempdb..##tmpAWQInvoices') is not null
						DROP TABLE ##tmpAWQInvoices;
				</cfif>

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryInvoices">
			<cfif (listFindNoCase("Closed,Delinquent",local.qryInvoices.invoiceStatus) 
					AND local.qryInvoices.InvDue gt 0 
					AND local.qryInvoices.inPaymentQueue is 0 
					AND local.myRightsTransactionsAdmin.transAllocatePayment is 1)
				OR (local.qryInvoices.InvDue gt 0 
					AND local.qryInvoices.inPaymentQueue is 0 
					AND arguments.event.getValue('mc_admintoolInfo.myRights.invoiceClose') is 1 
					AND local.myRightsTransactionsAdmin.transAllocatePayment is 1)>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=local.qryInvoices.assignedTomemberid, t="Invoice #local.qryInvoices.invoiceNumber#", ta=local.qryInvoices.InvDue, tmid=local.qryInvoices.assignedTomemberid, ad="v|#local.qryInvoices.invoiceid#")>
			<cfelse>
				<cfset local.addPaymentEncString = "">
			</cfif>

			<cfset local.tmpStr = {
				"invoiceID": local.qryInvoices.invoiceID,
				"invoiceNumber": local.qryInvoices.invoiceNumber,
				"dateDue": dateformat(local.qryInvoices.dateDue,"m/d/yyyy"),
				"dateBilled": dateformat(local.qryInvoices.dateBilled,"m/d/yyyy"),
				"dateCreated": dateformat(local.qryInvoices.dateCreated,"m/d/yyyy"),
				"invoiceStatus": local.qryInvoices.invoiceStatus,
				"inPaymentQueue": local.qryInvoices.inPaymentQueue,
				"invoiceProfile": local.qryInvoices.invoiceProfile,
				"membername": local.qryInvoices.membername,
				"company": local.qryInvoices.company,
				"assignedTomemberid": local.qryInvoices.assignedTomemberid,
				"InvAmt": dollarFormat(local.qryInvoices.InvAmt),
				"InvDue": dollarFormat(local.qryInvoices.InvDue),
				"hasCard": local.qryInvoices.hasCard,
				"addPaymentEncString": local.addPaymentEncString,
				"DT_RowId": "invoiceRow_#local.qryInvoices.invoiceID#",
				"DT_RowData": {
					"invoiceNumber": local.qryInvoices.invoiceNumber,
					"inPaymentQueue": local.qryInvoices.inPaymentQueue
				}
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryInvoices.totalCount),
			"recordsFiltered": val(local.qryInvoices.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>