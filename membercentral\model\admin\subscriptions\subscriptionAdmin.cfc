<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = 'controller'>
	<cfset this.siteResourceID = 0>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// build quick links ------------------------------------------------------------------------ ::
			this.link.subscribe = buildCurrentLink(arguments.event,"subscribe");
			this.link.manageSubscription = buildCurrentLink(arguments.event,"manageSubscription");

			// Subscriptions
			this.link.list = buildCurrentLink(arguments.event,"list");
			this.link.addSubscription = buildCurrentLink(arguments.event,"addSubscription");
			this.link.editSubscription = buildCurrentLink(arguments.event,"editSubscription");
			this.link.insertSubscription = buildCurrentLink(arguments.event,"insertSubscription") & "&mode=stream";
			this.link.saveSubscription = buildCurrentLink(arguments.event,"saveSubscription") & "&mode=stream";
			this.link.manageSubsForSet = buildCurrentLink(arguments.event,"manageSubsForSet") & "&mode=direct";
			this.link.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
			this.link.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');
			this.link.copySubscription = buildCurrentLink(arguments.event,"copySubscription") & "&mode=direct";
			this.link.saveCopySubscription = buildCurrentLink(arguments.event,"saveCopySubscription") & "&mode=stream";

			// Types
			this.link.addType = buildCurrentLink(arguments.event,"addType");
			this.link.editType = buildCurrentLink(arguments.event,"editType");
			this.link.insertType = buildCurrentLink(arguments.event,"insertType") & "&mode=stream";
			this.link.saveType = buildCurrentLink(arguments.event,"saveType") & "&mode=stream";
			
			// Sets
			this.link.addSubSet = buildCurrentLink(arguments.event,"addSubSet") & "&mode=direct";
			this.link.editSubSet = buildCurrentLink(arguments.event,"editSubSet");
			this.link.saveSet = buildCurrentLink(arguments.event,"saveSet") & "&mode=stream";
			this.link.removeSet = buildCurrentLink(arguments.event,"removeSet") & "&mode=stream";

			// Addons
			this.link.addAddon = buildCurrentLink(arguments.event,"addAddon");
			this.link.editAddon = buildCurrentLink(arguments.event,"editAddon")& "&mode=direct";
			this.link.insertAddon = buildCurrentLink(arguments.event,"insertAddon") & "&mode=stream";
			this.link.saveAddon = buildCurrentLink(arguments.event,"saveAddon") & "&mode=stream";

			// Schedules and rates
			this.link.addSchedule = buildCurrentLink(arguments.event,"addSchedule") & "&mode=direct";
			this.link.editSchedule = buildCurrentLink(arguments.event,"editSchedule");
			this.link.insertSchedule = buildCurrentLink(arguments.event,"insertSchedule") & "&mode=stream";
			this.link.saveSchedule = buildCurrentLink(arguments.event,"saveSchedule") & "&mode=stream";
			this.link.addRate = buildCurrentLink(arguments.event,"addRate") & "&mode=direct";
			this.link.editRate = buildCurrentLink(arguments.event,"editRate") & "&mode=direct";		
			this.link.saveRate = buildCurrentLink(arguments.event,"saveRate") & "&mode=stream";
			this.link.copyRate = buildCurrentLink(arguments.event,"copyRate") & "&mode=direct";
			this.link.saveCopyRate = buildCurrentLink(arguments.event,"saveCopyRate") & "&mode=stream";
		
			// Frequencies
			this.link.editFrequency = buildCurrentLink(arguments.event,"editFrequency") & "&mode=direct";
			this.link.saveFrequency = buildCurrentLink(arguments.event,"saveFrequency") & "&mode=stream";
			
			// Member Dates
			this.link.editMemberDate = buildCurrentLink(arguments.event,"editMemberDate")& "&mode=direct"
			this.link.saveMemberDate = buildCurrentLink(arguments.event,"saveMemberDate") & "&mode=stream";
			
			// Reports
			this.link.listReports = buildCurrentLink(arguments.event,"listReports");
			this.link.listRenewals = buildCurrentLink(arguments.event,"listRenewals");
			
			// Offer Emails and PDFs
			this.link.startGenerateOffers = buildCurrentLink(arguments.event, "startGenerateOffers") & "&mode=direct";
			this.link.processGenerateOffers = buildCurrentLink(arguments.event, "processGenerateOffers") & "&mode=direct";
			this.link.processTestEmailTemplate = buildCurrentLink(arguments.event, "processTestEmailTemplate") & "&mode=direct";
			this.link.startMarkBilled = buildCurrentLink(arguments.event, "startMarkBilled") & "&mode=direct";
			this.link.processMarkBilled = buildCurrentLink(arguments.event, "processMarkBilled") & "&mode=direct";
			this.link.startMarkAccepted = buildCurrentLink(arguments.event, "startMarkAccepted") & "&mode=direct";
			this.link.processMarkAccepted = buildCurrentLink(arguments.event, "processMarkAccepted") & "&mode=direct";
			this.link.startDeleteRenewals = buildCurrentLink(arguments.event, "startDeleteRenewals") & "&mode=direct";
			this.link.processDeleteRenewals = buildCurrentLink(arguments.event, "processDeleteRenewals") & "&mode=direct";
			this.link.processMarkAcceptLink = buildCurrentLink(arguments.event, "processAcceptSubscription") & "&mode=direct";
			
			// Mark Inactive
			this.link.startMarkInactive = buildCurrentLink(arguments.event, "startMarkInactive") & "&mode=direct";
			this.link.processMarkInactive = buildCurrentLink(arguments.event, "processMarkInactive") & "&mode=direct";

			// Mark Active
			this.link.startMarkActive = buildCurrentLink(arguments.event, "startMarkActive") & "&mode=direct";
			this.link.processMarkActive = buildCurrentLink(arguments.event, "processMarkActive") & "&mode=direct";
			
			// Mark Expired
			this.link.startMarkExpired = buildCurrentLink(arguments.event, "startMarkExpired") & "&mode=direct";
			this.link.processMarkExpired = buildCurrentLink(arguments.event, "processMarkExpired") & "&mode=direct";
			
			// Renewals
			this.link.confirmGenerateRenewals = buildCurrentLink(arguments.event, "confirmGenerateRenewals") & "&mode=direct";
			this.link.startGenerateRenewals = buildCurrentLink(arguments.event, "startGenerateRenewals") & "&mode=direct";
			this.link.processGenerateRenewals = buildCurrentLink(arguments.event, "processGenerateRenewals") & "&mode=direct";

			// Force Add Sub
			this.link.startForceAddSub = buildCurrentLink(arguments.event, "startForceAddSub") & "&mode=direct";
			this.link.processForceAddSub = buildCurrentLink(arguments.event, "processForceAddSub") & "&mode=direct";
			
			// Generate Add Sub
			this.link.startGenerateSub = buildCurrentLink(arguments.event, "startGenerateSub") & "&mode=direct";
			this.link.processGenerateSub = buildCurrentLink(arguments.event, "processGenerateSub") & "&mode=direct";
			
			// Sales
			this.link.linkCCToSubscriber = buildCurrentLink(arguments.event,"linkCCToSubscriber");
			this.link.saveCCToSubscriber = buildCurrentLink(arguments.event,"saveCCToSubscriber") & "&mode=stream";
			
			// Exports
			this.link.startExportSubscriptions = buildCurrentLink(arguments.event, "startExportSubscriptions") & "&mode=direct";
			this.link.showPaperStatementsForm = buildCurrentLink(arguments.event, "showPaperStatementsForm") & "&mode=direct";
			this.link.enqueuePaperStatements = buildCurrentLink(arguments.event, "enqueuePaperStatements") & "&mode=direct";
			this.link.downloadPaperStatements = buildCurrentLink(arguments.event, "downloadPaperStatements") & "&mode=direct";
			this.link.startExportSubscriptionsAccounting = buildCurrentLink(arguments.event, "startExportSubscriptionsAccounting") & "&mode=direct";
			this.link.startExportSubscriptionsChangeLog = buildCurrentLink(arguments.event, "startExportSubscriptionsChangeLog") & "&mode=direct";
			this.link.startExportSubscriptionsSchedule = buildCurrentLink(arguments.event, "startExportSubscriptionsSchedule") & "&mode=direct";
			this.link.exportSubscriptions	= buildCurrentLink(arguments.event,"exportSubscriptions") & "&mode=stream";
			this.link.exportSubscriptionsAccounting	= buildCurrentLink(arguments.event,"exportSubscriptionsAccounting") & "&mode=stream";
			this.link.exportSubscriptionsChangeLog	= buildCurrentLink(arguments.event,"exportSubscriptionsChangeLog") & "&mode=stream";
			this.link.exportSubscriptionsSchedule	= buildCurrentLink(arguments.event,"exportSubscriptionsSchedule") & "&mode=stream";
			this.link.exportSubStructureCSV	= buildCurrentLink(arguments.event,"exportSubStructureCSV") & "&mode=stream";

			this.link.removeMemberSubscription = buildCurrentLink(arguments.event,"removeMemberSubscription") & "&mode=direct";
			this.link.expireMemberSubscription = buildCurrentLink(arguments.event,"expireMemberSubscription") & "&mode=direct";
			this.link.cleanupInvoicesSubscription = buildCurrentLink(arguments.event,"cleanupInvoicesSubscription") & "&mode=direct";
			this.link.exportSubStructureZIP	= buildCurrentLink(arguments.event,"exportSubStructureZIP") & "&mode=stream";
			this.link.prepareSubStructureImport = buildCurrentLink(arguments.event,"prepareSubStructureImport");
			this.link.doImportSubscriptionStructure	= buildCurrentLink(arguments.event,"doImportSubscriptionStructure") & "&mode=stream";
			this.link.showSubTree = buildCurrentLink(arguments.event,"showSubTree") & "&mode=direct";

			// Subscriber Import
			this.link.sampleSubscriberImportTemplate = buildCurrentLink(arguments.event,"sampleSubscriberImportTemplate");		
			
			// Associate Cards on File Import
			this.link.sampleAssociateCOFImportTemplate = buildCurrentLink(arguments.event,"sampleAssociateCOFImportTemplate");
			
			// Remove Payment Method
			this.link.removePaymentMethod = buildCurrentLink(arguments.event,"removePaymentMethod") & "&mode=direct";
			this.link.processRemovePaymentMethod = buildCurrentLink(arguments.event,"processRemovePaymentMethod") & "&mode=direct";
			this.link.dspRemovePaymentMethod = buildCurrentLink(arguments.event,"dspRemovePaymentMethod") & "&mode=direct";
			
			// Remove Addons
			this.link.startRemoveAddons = buildCurrentLink(arguments.event, "startRemoveAddons") & "&mode=direct";
			this.link.processRemoveAddons = buildCurrentLink(arguments.event, "processRemoveAddons") & "&mode=direct";
			
			// Renewal Redirect
			this.link.saveSubscriptionSettings = buildCurrentLink(arguments.event,"saveSubscriptionSettings") & "&mode=stream";
			
			//Mass Update Offer Expiration Date
			this.link.startUpdateOfferExpirationDate = buildCurrentLink(arguments.event,"startUpdateOfferExpirationDate")& "&mode=direct";
			this.link.processUpdateOfferExpirationDate = buildCurrentLink(arguments.event,"processUpdateOfferExpirationDate")& "&mode=direct";

			//Mass Update Grace End Date
			this.link.startMassUpdateGraceEndDate = buildCurrentLink(arguments.event,"startMassUpdateGraceEndDate")& "&mode=direct";
			this.link.processMassUpdateGraceEndDate = buildCurrentLink(arguments.event,"processMassUpdateGraceEndDate")& "&mode=direct";

			// Error
			this.link.message = buildCurrentLink(arguments.event,"message");

			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;			

			// method to run ---------------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="subscribe" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.subscriberID = arguments.event.getValue('sid', 0)>

		<cfset local.subscribeLink = arguments.event.getValue('subsV2',0) EQ 1 ? this.link.manageSubscription : this.link.subscribe>
		<cfset arguments.event.setValue('mainsuburl', local.subscribeLink & "&mid=#arguments.event.getValue('mid',0)#&sid=#arguments.event.getValue('sid',0)#&mode=direct")>

		<cfif local.subscriberID eq 0>
			<!--- Add --->
			<cfset local.data = CreateObject("component","subscriptionReg").doSubscribe(event=arguments.event)>
		<cfelse>
			<!--- Edit --->
			<cfset local.data = CreateObject("component","subscriptionReg").doSubscribeEdit(event=arguments.event)>
		</cfif>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSubReg = CreateObject("component","subscriptionReg")>
		<cfset local.subAction = arguments.event.getValue('subAction','showManageSubs')>
		<cfset local.mainSubURL = this.link.manageSubscription & "&mid=#arguments.event.getValue('mid',0)#&subid=#arguments.event.getValue('subid',0)#">

		<cfset arguments.event.setValue('manageSubURL', local.mainSubURL)>

		<cfswitch expression="#local.subAction#">
			<cfcase value="showAddOns">
				<cfset local.data = local.objSubReg.manageSubscription_showAddOns(event=arguments.event)>
			</cfcase>
			<cfcase value="showPaymentSchedule">
				<cfset local.data = local.objSubReg.manageSubscription_renderPaymentScheduleForm(event=arguments.event)>
			</cfcase>
			<cfcase value="doConfirm">
				<cfset local.strResult = local.objSubReg.manageSubscription_doConfirm(event=arguments.event)>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						<cfif not local.strResult.success>
							alert('An error occured while trying to save the subscription');
						</cfif>
						top.reloadMemberSubs();
						top.MCModalUtils.hideModal();
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfset arguments.event.setValue('formLink', "#local.mainSubURL#&subAction=doConfirm&mode=direct")>
				<cfset local.data = local.objSubReg.manageSubscription(event=arguments.event)>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editMemberExpireSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qrySubscriber" datasource="#application.dsn.membercentral.dsn#">
			select st.statusCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_statuses as st on st.statusID = s.statusID
			where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
		</cfquery>
		<cfif listFindNoCase("R,O,E",local.qrySubscriber.statusCode)>
			<cfset local.showAROptions = false>
		<cfelse>
			<cfset local.showAROptions = true>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_expireSubscription.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="expireMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.memberID = arguments.event.getValue('mID', 0)>
		<cfset local.subscriberID = arguments.event.getValue('sID', 0)>
		<cfset local.AROption = arguments.event.getValue('rdoAR','C')>

		<cfset local.success = CreateObject("component","model.admin.subscriptions.subscriptions").expireMemberSubscription(actorMemberID=session.cfcuser.memberdata.memberID, actorStatsSessionID=session.cfcuser.statsSessionID, memberID=local.memberID, subscriberID=local.subscriberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'), AROption=local.AROption)>
		<cfset local.strMemCredit = CreateObject("component","model.admin.members.members").getMemberCreditAndOutstandingAmount(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadMemberSubs();
					<cfif local.AROption eq "A" and local.strMemCredit.success and local.strMemCredit.unallocatedamount gt 0>
						top.showRefundPaymentOnSubRemoveSuccess();
					<cfelse>
						top.MCModalUtils.hideModal();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editMemberCleanupInvoicesSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qrySubscriber" datasource="#application.dsn.membercentral.dsn#">
			select st.statusCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_statuses as st on st.statusID = s.statusID
			where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
		</cfquery>
		<cfif listFindNoCase("E,D",local.qrySubscriber.statusCode)>
			<cfset local.showAROptions = true>
		<cfelse>
			<cfset local.showAROptions = false>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfinclude template="dsp_cleanupInvoiceSubscriber.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cleanupInvoicesSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.memberID = arguments.event.getValue('mID', 0)>
		<cfset local.subscriberID = arguments.event.getValue('sID', 0)>
		<cfset local.AROption = arguments.event.getValue('rdoAR','C')>
		
		<cfset local.success = CreateObject("component","model.admin.subscriptions.subscriptions").cleanupInvoicesSubscription(actorMemberID=session.cfcuser.memberdata.memberID, actorStatsSessionID=session.cfcuser.statsSessionID, memberID=local.memberID, subscriberID=local.subscriberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'), AROption=local.AROption)>
		<cfset local.strMemCredit = CreateObject("component","model.admin.members.members").getMemberCreditAndOutstandingAmount(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadMemberSubs();
					<cfif local.AROption eq "A" and local.strMemCredit.success and local.strMemCredit.unallocatedamount gt 0>
						top.showRefundPaymentOnSubRemoveSuccess();
					<cfelse>
						top.MCModalUtils.hideModal();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editMemberRemoveSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qrySubscriber" datasource="#application.dsn.membercentral.dsn#">
			select st.statusCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_statuses as st on st.statusID = s.statusID
			where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
		</cfquery>
		<cfif listFindNoCase("R,O,E",local.qrySubscriber.statusCode)>
			<cfset local.showAROptions = false>
		<cfelse>
			<cfset local.showAROptions = true>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_removeSubscription.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeMemberSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.memberID = arguments.event.getValue('mID', 0)>
		<cfset local.subscriberID = arguments.event.getValue('sID', 0)>
		<cfset local.AROption = arguments.event.getValue('rdoAR','C')>

		<cfset local.success = CreateObject("component","model.admin.subscriptions.subscriptions").removeMemberSubscription(actorMemberID=session.cfcuser.memberdata.memberID, actorStatsSessionID=session.cfcuser.statsSessionID, memberID=local.memberID, subscriberID=local.subscriberID, siteID=arguments.event.getValue('mc_siteinfo.siteid'), AROption=local.AROption)>
		<cfset local.strMemCredit = CreateObject("component","model.admin.members.members").getMemberCreditAndOutstandingAmount(mcproxy_orgID=arguments.event.getValue('mc_siteInfo.orgID'), mcproxy_siteID=arguments.event.getValue('mc_siteInfo.siteID'), memberID=local.memberID)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadMemberSubs();
					<cfif local.AROption eq "A" and local.strMemCredit.success and local.strMemCredit.unallocatedamount gt 0>
						top.showRefundPaymentOnSubRemoveSuccess();
					<cfelse>
						top.MCModalUtils.hideModal();
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Subscriptions --->
	<cffunction name="list" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="impExData" type="string" required="false">
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);
			
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");			
			local.siteID = arguments.event.getValue('mc_siteinfo.siteid');

			// build XML LINKS --------------------------------------------------------------------------
			local.typesListXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=subscriptionTypesAllXML&mode=stream';
			local.subscriptionListXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=subscriptionsAllXML&mode=stream';
			local.subSetListJSON = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscriptionSets&mode=stream";
			local.schedListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAllSchedules&mode=stream';
			local.rateFreqListLink = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getRateFrequencies&mode=stream';
			local.subGridXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=subGridXML&mode=stream';
			local.memberDatesJSON = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=memberDatesGridJSON&mode=stream";

			local.strETData = {
				siteID=local.siteID,
				treeCode='ETSUBS',
				title="Subscription Email Templates ",
				intro="Here you manage email templates used by subscriptions.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);
		</cfscript>

		<cfset local.instanceSettings = getInstanceSettings(this.appInstanceID)>
		<cfset local.qrySubscriptionTypes = local.objSubs.getSubscriptionTypes(siteID=local.siteID)>
		<cfset local.qrySubscriptions = local.objSubs.getSubscriptions(siteID=local.siteID)>
		<cfset local.qrySubStatuses = local.objSubs.getSubStatuses()>
		<cfset local.subscriptionIssuesEmail = local.objSubs.getSubscriptionIssuesEmail(siteID=local.siteID)>
		<cfset local.subsAppSettingsXML = local.objSubs.getSubscriptionAppSettingsXML(siteID=local.siteID)>
		<cfset local.freeRateDisplay = xmlSearch(local.subsAppSettingsXML,'string(/settings/setting[@name="freeRateDisplay"]/@value)')>
		<cfset local.showPhotosInSubsRenew = val(xmlSearch(local.subsAppSettingsXML,'string(/settings/setting[@name="showPhotosInSubsRenew"]/@value)'))>
		<cfset local.qrySubRenewalsFieldSet = createObject("component","model.admin.memberFieldSets.memberFieldSets").getSettingsFieldsetID(siteResourceID=this.siteResourceID, area='SubRenewals', module="Subscriptions")>
		<cfset local.subRenewalsFieldsetID = val(local.qrySubRenewalsFieldSet.fieldsetID)>
		<cfset local.strSubRenewalsFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=local.siteID, selectorID="subRenewalsFE", selectedValue=local.subRenewalsFieldsetID)>

		<cftry>
			<cfquery name="local.qryGetIssues" datasource="#application.dsn.membercentral.dsn#" timeout="5">
				SET NOCOUNT ON;

				DECLARE @finalMsg varchar(max);
				EXEC dbo.sub_reportIssues @siteID=#local.siteID#, @sendemail=0, @finalMsg=@finalMsg OUTPUT;

				SELECT @finalMsg as finalMsg;
			</cfquery>

			<cfset local.hasSetupIssues = len(local.qryGetIssues.finalMsg) gt 0>
		<cfcatch type="Any">
			<cfset local.hasSetupIssues = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_subscriptions.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objSubs = CreateObject("component","subscriptions");
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.typeID = arguments.event.getValue('typeID',0);
			local.subID = 0;
			local.schedID = 0;
			local.paymentOrder = 1;
			local.subActivationCode = 'N';
			local.subAlternateActivationCode = 'N';
			local.formLink = this.link.insertSubscription;

			// Build breadCrumb Trail
			appendBreadCrumbs(arguments.event,{ link='', text='Add Subscription' });
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@languageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#">;

			SELECT subs.subscriptionID, subs.typeID, subs.subscriptionName, subs.reportCode, subs.scheduleID, subs.autoExpire, subs.status, subs.soldSeparately,
				subs.rateTermDateFlag, subs.GLAccountID, subs.paymentOrder, subs.allowRateGLAccountOverride,
				subs.frontEndContentID, frontEndContent.contentTitle as feContentTitle, frontEndContent.contentDesc as feContentDesc,
				frontEndContent.rawContent as feContentRawContent, frontEndContent.isHTML as feContentIsHTML, 
				subs.frontEndCompletedContentID, frontEndCompletedContent.contentTitle as feCompletedContentTitle, frontEndCompletedContent.contentDesc as feCompletedContentDesc,
				frontEndCompletedContent.rawContent as feCompletedContentRawContent, frontEndCompletedContent.isHTML as feCompletedContentIsHTML,
				subs.frontEndParentSubContentID, frontEndParentSubContent.rawContent as frontEndParentSubRawContent, 
				subs.emailTemplateID, et.templateName as emailTemplateName, subs.renewEmailTemplateID, ret.templateName as renewEmailTemplateName,
				o.subActivationCode, o2.subActivationCode as subAlternateActivationCOde,
				rgl.thePathExpanded as GLAccountPath
			from dbo.sub_subscriptions subs
			INNER JOIN dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = subs.GLAccountID
			INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
			INNER JOIN dbo.sub_activationOptions o on o.subActivationID = subs.subActivationID
			INNER JOIN dbo.sub_activationOptions o2 on o2.subActivationID = subs.subAlternateActivationID
			CROSS APPLY dbo.fn_getContent(subs.frontEndContentID,@languageID) as frontEndContent
			CROSS APPLY dbo.fn_getContent(subs.frontEndCompletedContentID,@languageID) as frontEndCompletedContent
			CROSS APPLY dbo.fn_getContent(subs.frontEndParentSubContentID,@languageID) as frontEndParentSubContent
			LEFT OUTER JOIN dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID
			LEFT OUTER JOIN dbo.et_emailTemplates ret on ret.templateID = subs.renewEmailTemplateID
			WHERE subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset queryAddRow(local.subData)>
		<cfset querySetCell(local.subData,'subscriptionID', 0) />
		<cfset querySetCell(local.subData,'paymentOrder', local.paymentOrder) />
		<cfset querySetCell(local.subData,'subActivationCode', local.subActivationCode) />
		<cfset querySetCell(local.subData,'subAlternateActivationCode', local.subAlternateActivationCode) />
		<cfset querySetCell(local.subData,'typeID', local.typeID) />
		<cfset querySetCell(local.subData,'scheduleID', local.schedID) />
		<cfset querySetCell(local.subData,'GLAccountPath', '') />
		<cfset querySetCell(local.subData,'frontEndContentID', 0) />
		<cfset querySetCell(local.subData,'feContentRawContent', '') />
		<cfset querySetCell(local.subData,'frontEndCompletedContentID', 0) />
		<cfset querySetCell(local.subData,'feCompletedContentRawContent', '') />
		<cfset querySetCell(local.subData,'frontEndParentSubContentID', 0)>
		<cfset querySetCell(local.subData,'frontEndParentSubRawContent', '')>

		<cfset querySetCell(local.subData,'emailTemplateID', 0) />
		<cfset querySetCell(local.subData,'emailTemplateName', '') />
		<cfset querySetCell(local.subData,'renewEmailTemplateID', 0) />
		<cfset querySetCell(local.subData,'renewEmailTemplateName', '') />
		<cfset querySetCell(local.subData,'allowRateGLAccountOverride', 0)>

		<cfset local.qrySubTypes = local.objSubs.getSubscriptionTypes(siteID = local.siteID)>
		<cfset local.qryRateSched = local.objSubs.getSubscriptionRateSchedules(siteID = local.siteID)>
		<cfset local.qryActivationOptions = local.objSubs.getSubscriptionActivationOptions()>
		<cfscript>
			// Prepare GL Account widget data for Subscription Revenue Account (required field)
			local.strSubscriptionGLAcctWidgetData = {
				label="Revenue GL Account *",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="SubscriptionGLAccountID",
				idFldValue=0,
				pathFldValue="",
				pathNoneTxt="(no account selected)"
			};
			local.strSubscriptionGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strSubscriptionGLAcctWidgetData);
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.typeID eq 0>
				<cflocation url="#this.link.list#" addtoken="no">
			<cfelse>
				<cfinclude template="frm_subscription.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objSubs = CreateObject("component","subscriptions");
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.subID = arguments.event.getValue('subID',0);

			local.formLink = this.link.saveSubscription;
			
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');

			local.memberListXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=membersForSubXML&subID=#local.subID#&mode=stream';
			local.addonListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAddonsForSub&subID=#local.subID#&mode=stream";
			local.setsForSubListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSetsForSub&subID=#local.subID#&mode=stream";
			appendBreadCrumbs(arguments.event,{ link='', text='Edit Subscription' });
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.subData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgID#">,
				@languageID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#">;

			SELECT subs.subscriptionID, subs.typeID, subs.subscriptionName, subs.reportCode, subs.uid, subs.scheduleID, subs.autoExpire, subs.status, subs.soldSeparately,  subs.rateTermDateFlag,
				subs.GLAccountID, subs.paymentOrder, subs.allowRateGLAccountOverride, subs.frontEndContentID,
				frontEndContent.contentTitle as feContentTitle, frontEndContent.contentDesc as feContentDesc,
				frontEndContent.rawContent as feContentRawContent, frontEndContent.isHTML as feContentIsHTML,
				subs.frontEndCompletedContentID, frontEndCompletedContent.contentTitle as feCompletedContentTitle, frontEndCompletedContent.contentDesc as feCompletedContentDesc,
				frontEndCompletedContent.rawContent as feCompletedContentRawContent, frontEndCompletedContent.isHTML as feCompletedContentIsHTML,
				subs.frontEndParentSubContentID, frontEndParentSubContent.rawContent as frontEndParentSubRawContent, 
				subs.emailTemplateID, et.templateName as emailTemplateName, subs.renewEmailTemplateID, ret.templateName as renewEmailTemplateName,
				o.subActivationCode, o2.subActivationCode as subAlternateActivationCode, rgl.thePathExpanded as GLAccountPath
			FROM dbo.sub_subscriptions subs
			INNER JOIN dbo.sub_types t on t.typeID = subs.typeID and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteID#">
			INNER JOIN dbo.tr_GLAccounts as gl on gl.orgID = @orgID and gl.GLAccountID = subs.GLAccountID
			INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
			INNER JOIN dbo.sub_activationOptions o on o.subActivationID = subs.subActivationID
			INNER JOIN dbo.sub_activationOptions o2 on o2.subActivationID = subs.subAlternateActivationID
			CROSS APPLY dbo.fn_getContent(subs.frontEndContentID,@languageID) as frontEndContent
			CROSS APPLY dbo.fn_getContent(subs.frontEndCompletedContentID,@languageID) as frontEndCompletedContent
			CROSS APPLY dbo.fn_getContent(subs.frontEndParentSubContentID,@languageID) as frontEndParentSubContent
			LEFT OUTER JOIN dbo.et_emailTemplates et on et.templateID = subs.emailTemplateID
			LEFT OUTER JOIN dbo.et_emailTemplates ret on ret.templateID = subs.renewEmailTemplateID
			WHERE subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.qrySubTypes = local.objSubs.getSubscriptionTypes(siteID=local.siteID)>
		<cfset local.qryRateSched = local.objSubs.getSubscriptionRateSchedules(siteID=local.siteID)>
		<cfset local.qryActivationOptions = local.objSubs.getSubscriptionActivationOptions()>
		
		<cfif local.subID eq 0>
			<cflocation url="#this.link.list#" addtoken="no">
		<cfelse>
			<cfscript>
				// Prepare GL Account widget data for Subscription Revenue Account (required field)
				local.strSubscriptionGLAcctWidgetData = {
					label="Revenue GL Account *",
					btnTxt="Choose GL Account",
					glatid=3,
					widgetMode='GLSelector',
					idFldName="SubscriptionGLAccountID",
					idFldValue=val(local.subData.GLAccountID),
					pathFldValue=local.subData.GLAccountPath,
					pathNoneTxt="(no account selected)"
				};
				local.strSubscriptionGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strSubscriptionGLAcctWidgetData);
			</cfscript>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<cfinclude template="frm_subscription.cfm">
				</cfoutput>
			</cfsavecontent>

			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
	</cffunction>
	
	<cffunction name="insertSubscription" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			local.subName = arguments.event.getValue('subName', '');
			local.reportCode = arguments.event.getValue('reportCode', '');
			local.subType = arguments.event.getValue('selType', 0);
			local.scheduleID = arguments.event.getValue('selSched', 0);
			local.rateTermDateFlag = arguments.event.getValue('selTermDates', 0);
			local.autoExpire = arguments.event.getValue('selAutoExp', 0);
			local.soldSep = arguments.event.getValue('selSoldSep', 0);
			local.paymentOrder = arguments.event.getValue('payOrder', 1);
			local.status = arguments.event.getValue('selStatus', 'A');
			local.accountID = arguments.event.getValue('SubscriptionGLAccountID', 0);
			local.feContentID = arguments.event.getValue('frontEndContentID', 0);
			local.feContent = arguments.event.getValue('frontEndContent', '');
			local.feCompletedContentID = arguments.event.getValue('frontEndCompletedContentID', 0);
			local.feCompletedContent = arguments.event.getValue('frontEndCompletedContent', '');
			local.frontEndParentSubContent = arguments.event.getValue('frontEndParentSubContent', '');
			local.emailTemplateID = arguments.event.getValue('SubscriptionEmailTemplateID', 0);
			local.renewEmailTemplateID = arguments.event.getValue('SubscriptionRenewEmailTemplateID', 0);
			local.allowRateGLOverride = arguments.event.getValue('selRateGLOverride', 0);
			local.activationOptionCode = arguments.event.getValue('selActOpt', '');
			local.alternateActivationOptionCode = arguments.event.getValue('selAltActOpt', '');
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
		</cfscript>

		<cfif (local.subName eq '') OR (local.subType eq 0) OR (local.scheduleID eq 0)>
			<cflocation url="#this.link.message#&message=3" addtoken="no">
		</cfif>
		
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>
		<cfset local.subID = local.objSubs.addSubscription(orgID=arguments.event.getValue('mc_siteinfo.orgID'), 
															siteID=arguments.event.getValue('mc_siteinfo.siteID'), 
															typeID=local.subType,
															subName=local.subName,
															reportCode=local.reportCode,
															scheduleID=local.scheduleID,
															rateTermDateFlag=local.rateTermDateFlag,
															autoExpire=local.autoExpire,
															status=local.status,
															soldSep=local.soldSep,
															paymentOrder=local.paymentOrder,
															accountID=local.accountID,
															allowRateGLOverride=local.allowRateGLOverride,
															activationOptionCode=local.activationOptionCode,
															alternateActivationOptionCode=local.alternateActivationOptionCode,
															frontEndContent=local.feContent,
															frontEndCompletedContent=local.feCompletedContent,
															frontEndParentSubContent=local.frontEndParentSubContent,
															emailTemplateID=val(local.emailTemplateID),
															renewEmailTemplateID=val(local.renewEmailTemplateID))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<!--- if no try errors, and we had something to save and it saved --->
				<cfif local.subID neq 0>
					<cflocation url="#this.link.editSubscription#&subID=#local.subID#&msg=1" addtoken="no">
				<cfelse>
					<cflocation url="#this.link.message#&message=3" addtoken="no">
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="saveSubscription" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			local.subID = arguments.event.getValue('subID', 0);
			local.subName = arguments.event.getValue('subName', '');
			local.reportCode = arguments.event.getValue('reportCode', '');
			local.subUID = arguments.event.getValue('subUID', '');
			local.subType = arguments.event.getValue('selType', 0);
			local.scheduleID = arguments.event.getValue('selSched', 0);
			local.rateTermDateFlag = 	arguments.event.getValue('selTermDates', 0);
			local.autoExpire = arguments.event.getValue('selAutoExp', 0);
			local.soldSep = arguments.event.getValue('selSoldSep', 0);
			local.status = arguments.event.getValue('selStatus', 'A');
			local.paymentOrder = arguments.event.getValue('payOrder', 1);
			local.accountCode = arguments.event.getValue('SubscriptionGLAccountID', 0);
			local.feContentID = arguments.event.getValue('frontEndContentID', 0);
			local.feContent = arguments.event.getValue('frontEndContent', '');
			local.feCompletedContentID = arguments.event.getValue('frontEndCompletedContentID', 0);
			local.feCompletedContent = arguments.event.getValue('frontEndCompletedContent', '');
			local.frontEndParentSubContent = arguments.event.getValue('frontEndParentSubContent', '');
			local.emailTemplateID = arguments.event.getValue('SubscriptionEmailTemplateID', 0);
			local.renewEmailTemplateID = arguments.event.getValue('SubscriptionRenewEmailTemplateID', 0);
			local.allowRateGLOverride = arguments.event.getValue('selRateGLOverride', 0);
			local.activationOptionCode = arguments.event.getValue('selActOpt', '');
			local.alternateActivationOptionCode = arguments.event.getValue('selAltActOpt', '');
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			local.updated = false;
			
			if ((local.subID neq 0) OR (local.subName eq '') OR (local.subType eq 0) OR (local.scheduleID eq 0))
			{
				local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
				
				local.updated = local.objSubs.updateSubscription(subID=local.subID, 
																subName=local.subName, 
																reportCode=local.reportCode, 
																subUID=local.subUID,
																typeID=local.subType,
																scheduleID=local.scheduleID,
																rateTermDateFlag=local.rateTermDateFlag,
																autoExpire=local.autoExpire,
																soldSep=local.soldSep,
																accountCode=local.accountCode,
																allowRateGLOverride=local.allowRateGLOverride,
																status=local.status,
																paymentOrder=local.paymentOrder,
																activationOptionCode=local.activationOptionCode,
																alternateActivationOptionCode=local.alternateActivationOptionCode,
																frontEndContent=local.feContent,
																frontEndCompletedContent=local.feCompletedContent,
																frontEndParentSubContent=local.frontEndParentSubContent,
																emailTemplateID=val(local.emailTemplateID),
																renewEmailTemplateID=val(local.renewEmailTemplateID),
																siteID=local.siteID);
																													
			}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<!--- if no try errors, and we had something to save and it saved --->
				<cfif local.updated>
					<cflocation url="#this.link.editSubscription#&subID=#local.subID#&msg=2" addtoken="no">
				<cfelse>
					<cflocation url="#this.link.message#&message=3" addtoken="no">
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageSubsForSet" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);			

			local.setID = arguments.event.getValue('setID',0);
			local.formLink = '';
			local.manageSetSubscriptionsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getManageSetSubscriptions&setID=#local.setID#&mode=stream";
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySetData">
            select setID, setName
            from dbo.sub_sets
            where setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.setID#">
            and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
        </cfquery>

        <cfset local.setName = local.qrySetData.setName>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.setID eq 0>
				<script>top.MCModalUtils.hideModal();</script>
			<cfelse>
				<cfinclude template="dsp_selectSubsForSets.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
		
	<!--- Types --->
	<cffunction name="addType" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);
			
			local.typeName = '';
			local.feEmailNotification = '';
			local.typeID = 0;
			local.feContentID = 0;
			local.feContentRawContent = '';
			local.feCompletedContentID = 0;
			local.feCompletedContentRawContent = '';
			
			local.formLink = this.link.insertType;

			appendBreadCrumbs(arguments.event,{ link='', text='Add Subscription Type' });
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriptionType.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editType" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.typeID = arguments.event.getValue('typeID',0);
			
			local.formLink = this.link.saveType;

			local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryTypeData">
			select t.typeID, t.typeName, t.feEmailNotification, t.frontEndContentID, t.frontEndCompletedContentID, t.uid, t.typeCode, t.siteResourceID,
				frontEndContent.contentTitle as feContentTitle, frontEndContent.contentDesc as feContentDesc, 
				frontEndContent.rawContent as feContentRawContent, frontEndContent.isHTML as feContentIsHTML,
				frontEndCompletedContent.contentTitle as feCompletedContentTitle, frontEndCompletedContent.contentDesc as feCompletedContentDesc, 
				frontEndCompletedContent.rawContent as feCompletedContentRawContent, frontEndCompletedContent.isHTML as feCompletedContentIsHTML
			from dbo.sub_types t
			cross apply dbo.fn_getContent(t.frontEndContentID,#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#) as frontEndContent
			cross apply dbo.fn_getContent(t.frontEndCompletedContentID,#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#) as frontEndCompletedContent
			where t.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.typeID#">
			and t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
		</cfquery>

		<cfset local.typeName = local.qryTypeData.typeName>
		<cfset local.feEmailNotification = local.qryTypeData.feEmailNotification>
		<cfset local.typeCode = local.qryTypeData.typeCode>
		<cfset local.typeUID = local.qryTypeData.uid>
		<cfset local.feContentID = local.qryTypeData.frontEndContentID>
		<cfset local.feContentRawContent = local.qryTypeData.feContentRawContent>
		<cfset local.feCompletedContentID = local.qryTypeData.frontEndCompletedContentID>
		<cfset local.feCompletedContentRawContent = local.qryTypeData.feCompletedContentRawContent>
		
		<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.typeName })>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.typeID eq 0>
				<script>top.closeBox();</script>
			<cfelse>
				<cfinclude template="frm_subscriptionType.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="insertType" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = "";
			
			local.typeName = arguments.event.getValue('typeName', '');
			local.feEmailNotification = application.objcommon.getValidatedEmailAddresses(emailAddressList=arguments.event.getValue('feEmailNotification', ''), delimiter=';');
			local.feContentID = arguments.event.getValue('frontEndContentID', 0);
			local.feContent = arguments.event.getValue('frontEndContent', '');
			local.feCompletedContentID = arguments.event.getValue('frontEndCompletedContentID', 0);
			local.feCompletedContent = arguments.event.getValue('frontEndCompletedContent', '');
			
			local.strAddTypeResult = CreateObject("component","subscriptions").addType(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), typeName=local.typeName, frontEndContent=local.feContent,
										frontEndCompletedContent=local.feCompletedContent, feEmailNotification=local.feEmailNotification);
		</cfscript>

		<cfif local.strAddTypeResult.success>
			<cflocation url="#this.link.editType#&typeID=#local.strAddTypeResult.typeID#&msg=1" addtoken="no">
		<cfelse>
			<cfif local.strAddTypeResult.errmsg.len()>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<div class="alert alert-danger">#local.strAddTypeResult.errmsg#</div>
					</cfoutput>
				</cfsavecontent>
			<cfelse>	
				<cflocation url="#this.link.message#&message=6" addtoken="no">
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
		
	<cffunction name="saveType" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.data = "";

			local.typeID = arguments.event.getValue('typeID', 0);
			local.typeName = arguments.event.getValue('typeName', '');
			local.typeCode = arguments.event.getTrimValue('typeCode', '');
			local.typeUID = arguments.event.getValue('typeUID', '');
			local.feEmailNotification = application.objcommon.getValidatedEmailAddresses(emailAddressList=arguments.event.getValue('feEmailNotification', ''), delimiter=';');
			local.feContent = arguments.event.getValue('frontEndContent', '');
			local.feCompletedContent = arguments.event.getValue('frontEndCompletedContent', '');
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			local.success = true;
			
			local.strUpdateTypeResult = CreateObject("component","subscriptions").updateType(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), typeID=local.typeID, typeName=local.typeName, 
											typeCode=local.typeCode, typeUID=local.typeUID, frontEndContent=local.feContent, frontEndCompletedContent=local.feCompletedContent, siteID=local.siteID, 
											feEmailNotification=local.feEmailNotification);
		</cfscript>

		<cfif local.strUpdateTypeResult.success>
			<cflocation url="#this.link.editType#&typeID=#local.typeID#&msg=2" addtoken="no">
		<cfelse>
			<cfif local.strUpdateTypeResult.errmsg.len()>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<div class="alert alert-danger">#local.strUpdateTypeResult.errmsg#</div>
					</cfoutput>
				</cfsavecontent>
			<cfelse>	
				<cflocation url="#this.link.message#&message=6" addtoken="no">
			</cfif>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<!--- Sets --->
	<cffunction name="editSubSet" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.setID = arguments.event.getValue('setID',0);
			local.formLink = this.link.saveSet;
			local.subSetListLink = this.link.list & '&tab=sets';
			local.subsListJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscriptionsForSet&setID=#local.setID#&mode=stream";
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySetData">
			select setID, setName, uid
			from dbo.sub_sets
			where setID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.setID#">
			and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
		</cfquery>

		<cfset local.setName = local.qrySetData.setName>
		<cfset local.setUID = local.qrySetData.uid>

		<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.setName })>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.setID eq 0>
				<cflocation url="#this.link.list#&tab=sets" addtoken="no">
			<cfelse>
				<cfinclude template="frm_subscriptionSet.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertSet" access="public" output="false" returntype="struct">
		<cfargument name="setName" type="string" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
	
		<cfscript>
			var local = structNew();
			
			if ((arguments.setName neq '')) {
				local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
				local.setID = local.objSubs.addSet(setName=arguments.setName, siteID=arguments.mcproxy_siteID);
			}
		</cfscript>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.setid = local.setID>
		<cfreturn local.returnStruct>
	</cffunction>
		
	<cffunction name="saveSet" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			local.setID = arguments.event.getValue('setID',0);
			local.setName = arguments.event.getValue('setName', '');
			local.setUID = arguments.event.getValue('setUID', '');
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			
			if ((local.setName neq '')) {
				local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
				local.objSubs.updateSet(setID=local.setID, setName=local.setName, setUID=local.setUID, siteID=local.siteID);
			}
		</cfscript>

		<cflocation url="#this.link.editSubSet#&setID=#local.setID#" addtoken="no">
	</cffunction>
		
	<!--- Addons --->
	<cffunction name="editAddon" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.addonID = arguments.event.getValue('aoID', 0);
			local.subID = arguments.event.getValue('subID', 0);
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionInfo">
			select subscriptionName
			from dbo.sub_subscriptions
			where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.addonData">
			select ao.addonID, ao.subscriptionID, IsNull(ao.childSetID, 0) as childSetID, 
				ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet,
				IsNull(ao.PCnum, 0) as PCnum, IsNull(PCPctOffEach, 0) as PCPctOffEach, 
				ao.frontEndAllowSelect, ao.frontEndAddAdditional, ao.frontEndAllowChangePrice, ao.frontEndContentID,
				frontEndContent.contentTitle as feContentTitle, frontEndContent.contentDesc as feContentDesc, 
				frontEndContent.rawContent as feContentRawContent, frontEndContent.isHTML as feContentIsHTML, 
				sets.setID, sets.setName
			from dbo.sub_addons ao
			inner join dbo.sub_sets sets on sets.setID = ao.childSetID and sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			cross apply dbo.fn_getContent(ao.frontEndContentID,#arguments.event.getValue('mc_pageDefinition.pageLanguageID')#) as frontEndContent
			where ao.addonID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.addonID#">
		</cfquery>
		
		
		<cfif local.addonID eq 0>
			<cfset local.formLink = this.link.insertAddon>
			
			<cfset queryAddRow(local.addonData)>
			<cfset querySetCell(local.addonData,'addonID', 0) />
			<cfset querySetCell(local.addonData,'subscriptionID', local.subID) />
			<cfset querySetCell(local.addonData,'minAllowed', '') />
			<cfset querySetCell(local.addonData,'maxAllowed', '') />
			<cfset querySetCell(local.addonData,'useAcctCodeInSet', 1) />
			<cfset querySetCell(local.addonData,'useTermEndDateInSet', 1) />
			<cfset querySetCell(local.addonData,'PCnum', 0) />
			<cfset querySetCell(local.addonData,'PCPctOffEach', 0) />
			<cfset querySetCell(local.addonData,'frontEndAllowSelect', 0) />
			<cfset querySetCell(local.addonData,'frontEndAddAdditional', 0) />
			<cfset querySetCell(local.addonData,'frontEndAllowChangePrice', 0) />
			<cfset querySetCell(local.addonData,'frontEndContentID', 0) />
			<cfset querySetCell(local.addonData,'feContentRawContent', '') />
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySets">
				select sets.setID, sets.setName
				from dbo.sub_sets sets
				where sets.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				and sets.status = 'A'
				and sets.setID not in (select childSetID from dbo.sub_addons where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subID#">)
				order by sets.setName
			</cfquery>
		<cfelse>
			<cfset local.formLink = this.link.saveAddon>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_subscriptionAddon.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="insertAddon" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			local.subID = arguments.event.getValue('subID', 0);
			local.childSetID = arguments.event.getValue('selSet', 0);
			
			local.minSelect = arguments.event.getValue('minSelect', 1);
			local.maxSelect = arguments.event.getValue('maxSelect', 1);
			
			if (minSelect eq 1)	{
				local.minAllowed = arguments.event.getValue('minAllowed', 0);
			}
			else {
				local.minAllowed = '';
			}
			
			if (maxSelect eq 1) {
				local.maxAllowed = arguments.event.getValue('maxAllowed', 0);
			}
			else {
				local.maxAllowed = '';
			}
			
			local.feChangeSelect = arguments.event.getValue('feChangeSelect', 0);
			local.feAddAdditional = arguments.event.getValue('feAddAdditional', 0);
			local.feChangePriceSelect = arguments.event.getValue('feChangePriceSelect', 0);

			local.PCnum = arguments.event.getValue('PCnum', 0);
			local.PCPctOffEach = arguments.event.getValue('PCPctOffEach', 0);

			local.useAcctCode = arguments.event.getValue('selAcctCode', 1);
			local.useTermEndDate = arguments.event.getValue('selTermSet', 1);

			local.feContentID = arguments.event.getValue('frontEndContentID', 0);
			local.feContent = arguments.event.getValue('frontEndContent', '');
			
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			
			if ((local.subID neq 0) AND (local.childSetID neq 0))
			{
				local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
				
				local.addonID = local.objSubs.addAddonToSet(subscriptionID=local.subID, 
															childSetID=local.childSetID, 
															minAllowed=local.minAllowed, 
															maxAllowed=local.maxAllowed,
															useAcctCodeInSet=local.useAcctCode, 
															useTermEndDateInSet=local.useTermEndDate, 
															PCnum=local.PCnum,
															PCPctOffEach=local.PCPctOffEach,
															feChangeSelect=local.feChangeSelect,
															feAddAdditional=local.feAddAdditional,
															feChangePriceSelect=local.feChangePriceSelect,
															frontEndContent=local.feContent,
															siteID=local.siteID);
			}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadAddons();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
		
	<cffunction name="saveAddon" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			
			local.addonID = arguments.event.getValue('addonID', 0);
			local.subID = arguments.event.getValue('subID', 0);
			local.childSetID = arguments.event.getValue('selSet', 0);
			
			local.minSelect = arguments.event.getValue('minSelect', 1);
			local.maxSelect = arguments.event.getValue('maxSelect', 1);
			
			if (minSelect eq 1) {
				local.minAllowed = arguments.event.getValue('minAllowed', 0);
			}
			else {
				local.minAllowed = '';
			}
			
			if (maxSelect eq 1) {
				local.maxAllowed = arguments.event.getValue('maxAllowed', 0);
			}
			else {
				local.maxAllowed = '';
			}
			
			local.feChangeSelect = arguments.event.getValue('feChangeSelect', 0);
			local.feAddAdditional = arguments.event.getValue('feAddAdditional', 0);
			local.feChangePriceSelect = arguments.event.getValue('feChangePriceSelect', 0);

			local.PCnum = arguments.event.getValue('PCnum', 0);
			local.PCPctOffEach = arguments.event.getValue('PCPctOffEach', 0);

			local.useAcctCode = arguments.event.getValue('selAcctCode', 1);
			local.useTermEndDate = arguments.event.getValue('selTermSet', 1);

			local.feContentID = arguments.event.getValue('frontEndContentID', 0);
			local.feContent = arguments.event.getValue('frontEndContent', '');
			
			local.siteID = arguments.event.getValue('mc_siteinfo.siteID');
			local.success = true;
			
			if ((local.addonID neq 0) AND (local.subID neq 0) AND (local.childSetID neq 0)) {
				local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");
				
				local.objSubs.updateAddonToSet(addonID=local.addonID,
												subscriptionID=local.subID,
												childSetID=local.childSetID,
												minAllowed=local.minAllowed,
												maxAllowed=local.maxAllowed,
												useAcctCodeInSet=local.useAcctCode,
												useTermEndDateInSet=local.useTermEndDate,
												PCnum=local.PCnum,
												PCPctOffEach=local.PCPctOffEach,
												feChangeSelect=local.feChangeSelect,
												feAddAdditional=local.feAddAdditional,
												feChangePriceSelect=local.feChangePriceSelect,
												frontEndContent=local.feContent,
												siteID=local.siteID);
			}
			else {
				local.success = false;
			}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadAddons();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<!--- Schedules and rates --->
	<cffunction name="addSchedule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);
			
			local.scheduleName = '';
			local.scheduleID = 0;
			local.formLink = this.link.insertSchedule;
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_schedule.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="editSchedule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.scheduleID = arguments.event.getValue('schedID',0);
			local.formLink = this.link.saveSchedule;

			local.rateListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getRatesForSchedule&schedID=#local.scheduleID#&mode=stream";
		</cfscript>

		<cfset local.permsGotoLink = CreateObject('component', 'model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct'>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryScheduleData">
			select scheduleID, scheduleName, uid
			from dbo.sub_rateSchedules
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and status = 'A'
		</cfquery>

		<cfset local.qryFrequencies = getFrequencies(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfset local.scheduleName = local.qryScheduleData.scheduleName>
		<cfset local.scheduleUID = local.qryScheduleData.uid>
		
		<cfset appendBreadCrumbs(arguments.event,{ link='', text=local.scheduleName })>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<cfif local.scheduleID eq 0>
				<cflocation url="#this.link.list#" addtoken="no">
			<cfelse>
				<cfinclude template="frm_schedule.cfm">
			</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="insertSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.strAddRateSchedule = CreateObject("component","subscriptions").addSchedule(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), scheduleName=arguments.event.getTrimValue('schedName', ''));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					<cfif local.strAddRateSchedule.success>
						top.MCModalUtils.hideModal();
						top.location.href = '#this.link.editSchedule#&schedID=#local.strAddRateSchedule.scheduleID#&msg=1';
					<cfelse>
						<cfif local.strAddRateSchedule.errmsg.len()>
							alert('#jsStringFormat(local.strAddRateSchedule.errmsg)#');
							top.MCModalUtils.hideModal();
						<cfelse>
							self.location.href = '#this.link.message#&message=4&mode=direct';
						</cfif>
					</cfif>
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<cffunction name="saveSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.scheduleID = arguments.event.getValue('schedID', 0);
			local.scheduleUID = arguments.event.getTrimValue('schedUID', '');
			local.updateSuccess = false;
	
			if (len(local.scheduleUID) AND NOT application.objUser.isSuperUser(cfcuser=session.cfcuser))
				local.scheduleUID = '';

			local.strUpdateRateSchedule = CreateObject("component","subscriptions").updateSchedule(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), scheduleID=local.scheduleID, 
				scheduleName=arguments.event.getTrimValue('schedName', ''), scheduleUID=local.scheduleUID);
		</cfscript>

		<cfif local.strUpdateRateSchedule.success>
			<cflocation url="#this.link.editSchedule#&schedID=#local.scheduleID#&msg=2" addtoken="false">
		<cfelse>
			<cfset local.errCode = findNoCase("schedulename already exists",local.strUpdateRateSchedule.errMsg) ? 'RATESCHNAMEEXISTS' : 'RATESCHSAVEERR'>
			<cflocation url="#this.link.message#&message=4&errCode=#local.errCode#" addtoken="false">
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>top.closeTypeBox();</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.scheduleID = arguments.event.getValue('schedID', 0);
			local.rateID = 0;
			local.rateStatus = 'A';
			local.isRenewalRate = 0;
			local.forceUpfront = 0;
			local.GLAccountID = 0;
			local.GLAccountPath = '';

			local.frontEndAllowChangePrice = 0;
			local.linkedNonRenewalRateID = 0;
			local.fallbackRenewalRateID = 0;
			local.keepChangedPriceOnRenewal = 0;
			local.frontEndChangePriceMin = 0;
			local.frontEndChangePriceMax = 0;

			local.objPaymentMethodSelector = createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector");
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRate">
			select rateID, rateName, reportCode, scheduleID, [status] as rateStatus, 
				rateStartDate, rateEndDate, rateStartDateAFID, rateEndDateAFID, rateAFStartDate, rateAFEndDate, 
				termStartDate, termEndDate, termStartDateAFID, termEndDateAFID, termAFStartDate, termAFEndDate, 
				graceEndDate, graceAFID, 
				recogStartDate, recogEndDate, recogStartDateAFID, recogEndDateAFID, recogAFStartDate, recogAFEndDate, 
				rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, frontEndAllowChangePrice, linkedNonRenewalRateID, 
				fallbackRenewalRateID, keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOtherRenewalRates">
			select rateID, rateName
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			and status <> 'D'
			and isRenewalRate = 1
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOtherJoinRates">
			select rateID, rateName
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			and status <> 'D'
			and isRenewalRate = 0
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFreq">
			select rf.rfid, rf.frequencyID, rf.rateAmt, f.frequencyName, f.frequency
			from dbo.sub_rateFrequencies rf
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
				and f.siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
				and f.status <> 'D'
			where rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateFreqs">
			select f.frequencyID, f.frequencyName, f.frequencyShortName, f.frequency, f.rateRequired, f.hasInstallments, f.monthlyInterval, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd
			from dbo.sub_frequencies f
			left outer join dbo.sub_rateFrequencies rf on rf.frequencyID = f.frequencyID 
				and rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			where f.siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
			and f.status <> 'D'
			order by f.frequencyName
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllAFs">
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
			ORDER BY afName
		</cfquery>
		
		<cfscript>
			local.rateStartDate = local.qryRate.rateStartDate;
			local.rateEndDate = local.qryRate.rateEndDate;
			local.termStartDate = local.qryRate.termStartDate;
			local.termEndDate = local.qryRate.termEndDate;
			local.graceEndDate = local.qryRate.graceEndDate;
			local.recogStartDate = local.qryRate.recogStartDate;
			local.recogEndDate = local.qryRate.recogEndDate;

			local.frmRateStartDate = "";
			local.frmRateEndDate = "";
			local.frmTermStartDate = "";
			local.frmTermEndDate = "";
			local.frmGraceEndDate = "";
			local.frmRecogStartDate = "";
			local.frmRecogEndDate = "";

			if (len(local.rateStartDate) gt 0) 
				local.frmRateStartDate = dateFormat(local.rateStartDate,'m/d/yyyy');
			if (len(local.rateEndDate) gt 0)
				local.frmRateEndDate = dateFormat(local.rateEndDate,'m/d/yyyy');
			if (len(local.termStartDate) gt 0)
				local.frmTermStartDate = dateFormat(local.termStartDate,'m/d/yyyy');
			if (len(local.termEndDate) gt 0)
				local.frmTermEndDate = dateFormat(local.termEndDate,'m/d/yyyy');
			if (len(local.graceEndDate) gt 0)
				local.frmGraceEndDate = dateFormat(local.graceEndDate,'m/d/yyyy');
			if (len(local.recogStartDate) gt 0)
				local.frmRecogStartDate = dateFormat(local.recogStartDate,'m/d/yyyy');
			if (len(local.recogEndDate) gt 0)
				local.frmRecogEndDate = dateFormat(local.recogEndDate,'m/d/yyyy');

			local.formlink = this.link.saveRate & "&schedID=" & local.scheduleID;
			
			// Prepare GL Account widget data for Rate Revenue GL Override (optional field with clear button)
			local.strRateGLAcctWidgetData = {
				label="Revenue GL Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=local.GLAccountID,
				pathFldValue=local.GLAccountPath,
				pathNoneTxt="(no account selected - use GL defined by subscription)",
				clearBtnTxt="Clear GL Account"
			};
			local.strRateGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRateGLAcctWidgetData);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			if(arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1)
				location(url="#this.link.message#&message=1", addtoken=false);

			local.scheduleID = arguments.event.getValue('schedID', 0);
			local.rateID = arguments.event.getValue('rateID', 0);

			local.objPaymentMethodSelector = createObject("component","model.admin.common.modules.paymentProfileSelector.paymentProfileSelector");
		</cfscript>

		<cfif (local.scheduleID eq 0) OR (local.rateID eq 0)>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRate">
			select rateID, rateName, reportCode, scheduleID, [status] as rateStatus, uid,
				rateStartDate, rateEndDate, isNull(rateStartDateAFID, 0) as rateStartDateAFID, isNull(rateEndDateAFID, 0) as rateEndDateAFID, rateAFStartDate, rateAFEndDate,
				termStartDate, termEndDate, isNull(termStartDateAFID,0) as termStartDateAFID, isNull(termEndDateAFID,0) as termEndDateAFID, termAFStartDate, termAFEndDate,
				graceEndDate, isNull(graceAFID, 0) as graceAFID,
				recogStartDate, recogEndDate, isNull(recogStartDateAFID,0) as recogStartDateAFID, isNull(recogEndDateAFID,0) as recogEndDateAFID, recogAFStartDate, recogAFEndDate,
				rateAdvanceOnTermEnd, isRenewalRate, forceUpfront, GLAccountID, frontEndAllowChangePrice, linkedNonRenewalRateID, fallbackRenewalRateID,
				keepChangedPriceOnRenewal, frontEndChangePriceMin, frontEndChangePriceMax
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOtherRenewalRates">
			select rateID, rateName
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			and status <> 'D'
			and isRenewalRate = 1
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOtherJoinRates">
			select rateID, rateName
			from dbo.sub_rates
			where scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.scheduleID#">
			and rateID <> <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			and status <> 'D'
			and isRenewalRate = 0
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGLAccount">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;

			select rgl.thePathExpanded as GLAccountPath
			from dbo.tr_GLAccounts as gl 
			INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgl on rgl.GLAccountID = gl.GLAccountID
			where gl.orgID = @orgID
			and gl.GLAccountID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRate.GLAccountID)#">;
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFreq">
			select rf.rfid, rf.frequencyID, rf.rateAmt, f.frequencyName, f.frequency
			from dbo.sub_rateFrequencies rf
			inner join dbo.sub_frequencies f on f.frequencyID = rf.frequencyID
				and f.siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
				and f.status <> 'D'
			where rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateFreqs">
			select f.frequencyID, f.frequencyName, f.frequencyShortName, f.frequency, f.rateRequired, f.hasInstallments, f.monthlyInterval, rf.rateAmt, rf.numInstallments, rf.allowFrontEnd
			from dbo.sub_frequencies f
			left outer join dbo.sub_rateFrequencies rf on rf.frequencyID = f.frequencyID 
				and rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
				and rf.status <> 'D'
			where f.siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
			and f.status <> 'D'
			order by f.frequencyName
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryAllAFs">
			SELECT AFID, afName
			FROM dbo.af_advanceFormulas
			WHERE siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
			ORDER BY afName
		</cfquery>
		
		<cfscript>
			local.rateStatus = local.qryRate.rateStatus;
			local.rateUID = local.qryRate.uid;
			local.isRenewalRate = local.qryRate.isRenewalRate;
			local.forceUpfront = local.qryRate.forceUpfront;
			local.GLAccountID = val(local.qryRate.GLAccountID);
			local.GLAccountPath = local.qryGLAccount.GLAccountPath;
			local.rateStartDate = local.qryRate.rateAFStartDate;
			local.rateEndDate = local.qryRate.rateAFEndDate;
			local.termStartDate = local.qryRate.termAFStartDate;
			local.termEndDate = local.qryRate.termAFEndDate;
			local.graceEndDate = local.qryRate.graceEndDate;
			local.recogStartDate = local.qryRate.recogAFStartDate;
			local.recogEndDate = local.qryRate.recogAFEndDate;

			local.frontEndAllowChangePrice = local.qryRate.frontEndAllowChangePrice;
			local.linkedNonRenewalRateID = local.qryRate.linkedNonRenewalRateID;
			local.fallbackRenewalRateID = local.qryRate.fallbackRenewalRateID;
			local.keepChangedPriceOnRenewal = local.qryRate.keepChangedPriceOnRenewal;
			local.frontEndChangePriceMin = local.qryRate.frontEndChangePriceMin;
			local.frontEndChangePriceMax = local.qryRate.frontEndChangePriceMax;
			
			local.frmRateStartDate = "";
			local.frmRateEndDate = "";
			local.frmTermStartDate = "";
			local.frmTermEndDate = "";
			local.frmGraceEndDate = "";
			local.frmRecogStartDate = "";
			local.frmRecogEndDate = "";

			if (len(local.rateStartDate) gt 0)
				local.frmRateStartDate = dateFormat(local.rateStartDate,'m/d/yyyy');
			if (len(local.rateEndDate) gt 0)
				local.frmRateEndDate = dateFormat(local.rateEndDate,'m/d/yyyy');
			if (len(local.termStartDate) gt 0)
				local.frmTermStartDate = dateFormat(local.termStartDate,'m/d/yyyy');
			if (len(local.termEndDate) gt 0)
				local.frmTermEndDate = dateFormat(local.termEndDate,'m/d/yyyy');
			if (len(local.graceEndDate) gt 0)
				local.frmGraceEndDate = dateFormat(local.graceEndDate,'m/d/yyyy');
			if (len(local.recogStartDate) gt 0)
				local.frmRecogStartDate = dateFormat(local.recogStartDate,'m/d/yyyy');
			if (len(local.recogEndDate) gt 0)
				local.frmRecogEndDate = dateFormat(local.recogEndDate,'m/d/yyyy');

			local.formlink = this.link.saveRate & "&schedID=" & local.scheduleID;
			// Prepare GL Account widget data for Rate Revenue GL Override (optional field with clear button)
			local.strRateGLAcctWidgetData = {
				label="Revenue GL Override",
				btnTxt="Choose GL Account",
				glatid=3,
				widgetMode='GLSelector',
				idFldName="GLAccountID",
				idFldValue=local.GLAccountID,
				pathFldValue=local.GLAccountPath,
				pathNoneTxt="(no account selected - use GL defined by subscription)",
				clearBtnTxt="Clear GL Account"
			};
			local.strRateGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRateGLAcctWidgetData);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfset var local = structNew()>

		<cfset local.rateID = arguments.event.getValue('rateid',0)>
		<cfset local.scheduleID = arguments.event.getValue('schedID',0)>
		<cfset local.accountID = arguments.event.getValue('GLAccountID', 0)>
		
		<cfscript>
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");

			local.rateName = arguments.event.getTrimValue('rateName','');
			local.reportCode = arguments.event.getTrimValue('reportCode','');
			local.rateUID = arguments.event.getTrimValue('rateUID','');
			local.rateStartDateTime = ParseDateTime(arguments.event.getValue('rateStartDate'));
			local.rateEndDateTime = ParseDateTime(arguments.event.getValue('rateEndDate'));
			local.termStartDateTime = ParseDateTime(arguments.event.getValue('termStartDate'));
			local.termEndDateTime = ParseDateTime(arguments.event.getValue('termEndDate'));
			
			if (len(arguments.event.getValue('graceEndDate',''))) {
				local.graceEndDateTime = ParseDateTime(arguments.event.getValue('graceEndDate'));
			} else {
				local.graceEndDateTime = '';
			}
			
			if (arguments.event.getValue('mc_siteInfo.useAccrualAcct')) {
				local.recogStartDateTime = ParseDateTime(arguments.event.getValue('recogStartDate'));
				local.recogEndDateTime = ParseDateTime(arguments.event.getValue('recogEndDate'));
			} else {
				local.recogStartDateTime = local.termStartDateTime;
				local.recogEndDateTime = local.termEndDateTime;
			}

			local.rateStatus = arguments.event.getValue('selStatus','');
			
			local.rateAdvanceOnTermEnd = arguments.event.getValue('selAFAdvanceOnTermEnd',0);
			local.isRenewalRate = arguments.event.getValue('selRenewalRate',0);
			local.forceUpfront = arguments.event.getValue('selForceUpfront',0);
			local.rateStartAFID = arguments.event.getValue('selAFRateStart',0);
			local.rateEndAFID = arguments.event.getValue('selAFRateEnd',0);
			local.termStartAFID = arguments.event.getValue('selAFTermStart',0);
			local.termEndAFID = arguments.event.getValue('selAFTermEnd',0);
			local.graceEndAFID = arguments.event.getValue('selAFGraceEnd',0);

			if (arguments.event.getValue('mc_siteInfo.useAccrualAcct')) {
				local.recogStartAFID = arguments.event.getValue('selAFRecogStart',0);
				local.recogEndAFID = arguments.event.getValue('selAFRecogEnd',0);
			} else {
				local.recogStartAFID = local.termStartAFID;
				local.recogEndAFID = local.termEndAFID;
			}

			// convert start time and end times...all start times start at midnight and all end time end at 11:59:59.997pm
			local.rateStartDateTime = DATEADD("d",DATEDIFF("d",0,local.rateStartDateTime), 0);
			local.rateEndDateTime = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.rateEndDateTime), 0)));
			local.termStartDateTime = DATEADD("d",DATEDIFF("d",0,local.termStartDateTime), 0);
			local.termEndDateTime = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.termEndDateTime), 0)));
			local.recogStartDateTime = DATEADD("d",DATEDIFF("d",0,local.recogStartDateTime), 0);
			local.recogEndDateTime = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.recogEndDateTime), 0)));
			if (len(local.graceEndDateTime))
				local.graceEndDateTime = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.graceEndDateTime), 0)));

			local.frontEndAllowChangePrice = val(arguments.event.getValue('selFrontEndAllowChangePrice',0));
			local.linkedNonRenewalRateID = val(arguments.event.getValue('selLinkedNonRenewalRateID',0));
			local.fallbackRenewalRateID = val(arguments.event.getValue('selFallbackRenewalRateID',0));

			if (local.frontEndAllowChangePrice) {
				local.keepChangedPriceOnRenewal = val(arguments.event.getValue('selKeepChangedPriceOnRenewal',0));
				local.frontEndChangePriceMin = val(arguments.event.getValue('selFrontEndChangePriceMin',0));
				local.frontEndChangePriceMax = val(arguments.event.getValue('selFrontEndChangePriceMax',0));
			} else {
			
				local.keepChangedPriceOnRenewal = 0;
				local.frontEndChangePriceMin = 0;
				local.frontEndChangePriceMax = 0;
			}

			// insert or update
			if (local.rateID is 0) {
				local.rateInfo = local.objSubs.addRate(scheduleID=local.scheduleID, rateName=local.rateName, reportCode=local.reportCode, rateStatus=local.rateStatus, 
									rateStartDateTime=local.rateStartDateTime, rateEndDateTime=local.rateEndDateTime, rateStartAFID=local.rateStartAFID, rateEndAFID=local.rateEndAFID,
									termStartDateTime=local.termStartDateTime, termEndDateTime=local.termEndDateTime, termStartAFID=local.termStartAFID, termEndAFID=local.termEndAFID, 
									graceEndDateTime=local.graceEndDateTime, graceEndAFID=local.graceEndAFID, 
									recogStartDateTime=local.recogStartDateTime, recogEndDateTime=local.recogEndDateTime, recogStartAFID=local.recogStartAFID, recogEndAFID=local.recogEndAFID, 
									rateAdvanceOnTermEnd=local.rateAdvanceOnTermEnd, isRenewalRate=local.isRenewalRate, forceUpfront=local.forceUpfront,
									accountID=local.accountID, frontEndAllowChangePrice=local.frontEndAllowChangePrice, linkedNonRenewalRateID=local.linkedNonRenewalRateID,
									fallbackRenewalRateID=local.fallbackRenewalRateID, keepChangedPriceOnRenewal=local.keepChangedPriceOnRenewal, 
									frontEndChangePriceMin=local.frontEndChangePriceMin, frontEndChangePriceMax=local.frontEndChangePriceMax);
				local.rateID = local.rateInfo.rateID;

			} else {
				local.objSubs.updateRate(rateID=local.rateID, rateName=local.rateName, reportCode=local.reportCode, rateUID=local.rateUID, rateStatus=local.rateStatus, 
									rateStartDateTime=local.rateStartDateTime, rateEndDateTime=local.rateEndDateTime, rateStartAFID=local.rateStartAFID, rateEndAFID=local.rateEndAFID,
									termStartDateTime=local.termStartDateTime, termEndDateTime=local.termEndDateTime, termStartAFID=local.termStartAFID, termEndAFID=local.termEndAFID, 
									graceEndDateTime=local.graceEndDateTime, graceEndAFID=local.graceEndAFID, 
									recogStartDateTime=local.recogStartDateTime, recogEndDateTime=local.recogEndDateTime, recogStartAFID=local.recogStartAFID, recogEndAFID=local.recogEndAFID, 
									rateAdvanceOnTermEnd=local.rateAdvanceOnTermEnd, isRenewalRate=local.isRenewalRate, forceUpfront=local.forceUpfront,
									accountID=local.accountID, frontEndAllowChangePrice=local.frontEndAllowChangePrice, linkedNonRenewalRateID=local.linkedNonRenewalRateID,
									fallbackRenewalRateID=local.fallbackRenewalRateID, keepChangedPriceOnRenewal=local.keepChangedPriceOnRenewal, 
									frontEndChangePriceMin=local.frontEndChangePriceMin, frontEndChangePriceMax=local.frontEndChangePriceMax);
			}
		</cfscript>
		
		<!--- use rateID to up update the freqAmts --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFreqs">
			select f.frequencyID, f.frequencyName
			from dbo.sub_frequencies f
			where f.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and f.status <> 'D'
			order by f.frequencyName
		</cfquery>
		
		<cfloop query="local.qryFreqs">

			<cfset local.allowFE = 0>
			<cfif arguments.event.getValue('freqFront_#local.qryFreqs.frequencyID#_allow',0) eq 1>
				<cfset local.allowFE = 1>
			</cfif>

			<cfif arguments.event.getValue('freqAmt_#local.qryFreqs.frequencyID#_blank',0) eq 1>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeleteFreq">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;
						CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Status] varchar(max), [Allow Front End] varchar(max));

						DECLARE @rfid int, @msg varchar(max), @crlf varchar(2) = char(13) + char(10);
						DECLARE @rateID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">,
							@frequencyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFreqs.frequencyID#">,
							@allowFrontEnd bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.allowFE#">;
					
						select @rfid=rfid
						from dbo.sub_rateFrequencies
						where rateID = @rateID
						and frequencyID = @frequencyID;

						IF @rfid is not null BEGIN
							INSERT INTO ##tmpAuditLogData ([rowCode], [Status], [Allow Front End])
							SELECT 'DATATYPECODE', 'STRING', 'BIT'
								UNION
							SELECT 'OLDVAL', CASE WHEN [status] = 'A' THEN 'Active' ELSE 'Deleted' END, CAST(allowFrontEnd AS varchar(1))
							FROM dbo.sub_rateFrequencies
							WHERE rfid = @rfid
								UNION
							SELECT 'NEWVAL', 'Deleted', CAST(@allowFrontEnd AS varchar(1));

							EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

							BEGIN TRAN;
								UPDATE dbo.sub_rateFrequenciesMerchantProfiles
								set [status] = 'D'
								where rfid = @rfid;
						
								UPDATE dbo.sub_rateFrequencies
								set [status] = 'D',
									allowFrontEnd = @allowFrontEnd
								where rateID = @rateID
								and frequencyID = @frequencyID;

								-- audit log
								IF ISNULL(@msg,'') <> '' BEGIN
									DECLARE @subKeyMapJSON varchar(100) = '{ "RFID":' + CAST(@rfid AS varchar(10)) + ', "RATEID":' + CAST(@rateID AS varchar(10)) + ', "FREQUENCYID":' + CAST(@frequencyID AS varchar(10)) + ' }';
									
									SELECT @msg = STRING_ESCAPE('[' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryFreqs.frequencyName#"> + '] Rate Frequency under Rate / Rate Schedule ' +
										QUOTENAME(r.rateName) + ' / ' + QUOTENAME(rs.scheduleName) + ' updated.','json') + @crlf + @msg
									FROM dbo.sub_rates AS r
									INNER JOIN dbo.sub_rateSchedules AS rs ON rs.scheduleID = r.scheduleID
									WHERE r.rateID = @rateID;
									
									EXEC dbo.sub_insertAuditLog 
										@orgID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">, 
										@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">, 
										@areaCode='SUBRATE', @msgjson=@msg, @subKeyMapJSON=@subKeyMapJSON, 
										@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
								END
							COMMIT TRAN;
						END

						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;
						
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfelse>
				<cfset local.currRateUpdate = val(Rereplace(arguments.event.getValue('freqAmt_#local.qryFreqs.frequencyID#',0),'[^0-9\.]','','ALL'))>
				<cfset local.addProfileID = arguments.event.getValue("addRFMP_#local.qryFreqs.frequencyID#","")>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFreqUpdate">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						IF OBJECT_ID('tempdb..##tblMCQSubCond') IS NOT NULL 
							DROP TABLE ##tblMCQSubCond;
						CREATE TABLE ##tblMCQSubCond (conditionID int);

						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;
						CREATE TABLE ##tmpAuditLogData ([rowCode] varchar(20) PRIMARY KEY, [Status] varchar(max), [Amount] varchar(max), [Allow Front End] varchar(max), [Payment Methods] varchar(max));

						declare @rfid int, @rfmpid int, @numInstallments int;
						DECLARE @rateID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">,
							@frequencyID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryFreqs.frequencyID#">,
							@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
							@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
							@allowFrontEnd bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.allowFE#">,
							@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
							@msg varchar(max), @crlf varchar(2) = char(13) + char(10);
					
						select @numInstallments = frequency
						from dbo.sub_frequencies
						where frequencyID = @frequencyID;

						select @rfID = rfID 
						from dbo.sub_rateFrequencies
						where rateID = @rateID
						and frequencyID = @frequencyID;

						INSERT INTO ##tmpAuditLogData ([rowCode], [Status], [Amount], [Allow Front End], [Payment Methods])
						VALUES ('DATATYPECODE', 'STRING', 'DECIMAL2', 'BIT', 'STRING');

						IF @rfid is NULL BEGIN
							-- repopulate sub split cache for rate freq
							INSERT INTO ##tblMCQSubCond (conditionID)
							SELECT DISTINCT c.conditionID
							FROM dbo.ams_virtualGroupConditions AS c
							INNER JOIN dbo.cache_members_conditions_subRates AS cr ON cr.orgID = @orgID 
								AND cr.conditionID = c.conditionID
								AND cr.rateID = @rateID
							WHERE c.orgID = @orgID
							AND c.fieldCode ='sub_entry';

							INSERT INTO ##tmpAuditLogData ([rowCode], [Amount], [Status], [Allow Front End], [Payment Methods])
							VALUES ('OLDVAL', 0, 'Active', 0, '');
						END
						ELSE BEGIN
							INSERT INTO ##tmpAuditLogData ([rowCode], [Amount], [Status], [Allow Front End], [Payment Methods])
							SELECT 'OLDVAL', rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, rf.allowFrontEnd, STRING_AGG(mp.profileName,', ')
							FROM dbo.sub_rateFrequencies AS rf
							LEFT OUTER JOIN dbo.sub_rateFrequenciesMerchantProfiles AS rfmp 
								INNER JOIN dbo.mp_profiles AS mp ON mp.siteID = @siteID
									AND mp.profileID = rfmp.profileID
								ON rfmp.rfid = rf.rfid
								AND rfmp.[status] = 'A'
							WHERE rf.rfid = @rfid
							AND rf.[status] = 'A'
							GROUP BY rf.rateAmt, rf.[status], rf.allowFrontEnd;
						END
					
						BEGIN TRAN;
							IF @rfid is NULL BEGIN
								INSERT INTO dbo.sub_rateFrequencies(rateID, frequencyID, rateAmt, numInstallments, allowFrontEnd)
								values(@rateID, @frequencyID, <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.currRateUpdate#">, @numInstallments, @allowFrontEnd);

								select @rfID = SCOPE_IDENTITY();
							END ELSE
								UPDATE dbo.sub_rateFrequencies
								set rateAmt = <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.currRateUpdate#">,
									numInstallments = @numInstallments,
									allowFrontEnd = @allowFrontEnd,
									status = 'A'
								where rfid=@rfID;
						
							
							-- sub rate freq merchant profiles
							MERGE INTO dbo.sub_rateFrequenciesMerchantProfiles AS target
							USING (
								SELECT @rfID AS rfID, listItem AS profileID
								FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.addProfileID#">,',')
							) AS source
							ON target.rfID = source.rfID AND target.profileID = source.profileID

							-- When matched: set status = 'A'
							WHEN MATCHED THEN 
								UPDATE SET target.[status] = 'A'

							-- When not matched: insert new record with status = 'A'
							WHEN NOT MATCHED BY TARGET THEN 
								INSERT (rfID, profileID, [status])
								VALUES (source.rfID, source.profileID, 'A')

							-- When not matched by source: set status to 'D'
							WHEN NOT MATCHED BY SOURCE AND Target.rfID = @rfID THEN
								UPDATE SET target.[status] = 'D';

							-- audit log
							INSERT INTO ##tmpAuditLogData ([rowCode], [Amount], [Status], [Allow Front End], [Payment Methods])
							SELECT 'NEWVAL', rf.rateAmt, CASE WHEN rf.[status] = 'A' THEN 'Active' ELSE 'Deleted' END, rf.allowFrontEnd, STRING_AGG(mp.profileName,', ')
							FROM dbo.sub_rateFrequencies AS rf
							LEFT OUTER JOIN dbo.sub_rateFrequenciesMerchantProfiles AS rfmp 
								INNER JOIN dbo.mp_profiles AS mp ON mp.siteID = @siteID
									AND mp.profileID = rfmp.profileID
								ON rfmp.rfid = rf.rfid
								AND rfmp.[status] = 'A'
							WHERE rf.rfid = @rfid
							AND rf.[status] = 'A'
							GROUP BY rf.rateAmt, rf.[status], rf.allowFrontEnd;

							EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpAuditLogData', @msg=@msg OUTPUT;

							IF ISNULL(@msg,'') <> '' BEGIN
								DECLARE @subKeyMapJSON varchar(100) = '{ "RFID":' + CAST(@rfid AS varchar(10)) + ', "RATEID":' + CAST(@rateID AS varchar(10)) + ', "FREQUENCYID":' + CAST(@frequencyID AS varchar(10)) + ' }';
									
								SELECT @msg = STRING_ESCAPE('[' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryFreqs.frequencyName#"> + '] Rate Frequency under Rate Schedule / Rate ' +
									QUOTENAME(rs.scheduleName) + ' / ' + QUOTENAME(r.rateName) + ' updated.','json') + @crlf + @msg
								FROM dbo.sub_rates AS r
								INNER JOIN dbo.sub_rateSchedules AS rs ON rs.scheduleID = r.scheduleID
								WHERE r.rateID = @rateID;

								EXEC dbo.sub_insertAuditLog @orgID=@orgID, @siteID=@siteID, @areaCode='SUBRATE', @msgjson=@msg,
									@subKeyMapJSON=@subKeyMapJSON, @enteredByMemberID=@recordedByMemberID;
							END
						COMMIT TRAN;

						IF EXISTS (SELECT 1 FROM ##tblMCQSubCond)
							EXEC dbo.cache_members_populateSubConditionsSplitData @orgID=@orgID, @limitToConditionID=NULL;

						IF OBJECT_ID('tempdb..##tblMCQSubCond') IS NOT NULL 
							DROP TABLE ##tblMCQSubCond;
						IF OBJECT_ID('tempdb..##tmpAuditLogData') IS NOT NULL
							DROP TABLE ##tmpAuditLogData;
						
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>
		</cfloop>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript"> 
					top.MCModalUtils.hideModal();
					top.$('.modal-backdrop').remove();
					top.reloadRates();
					top.editRateWindow(#local.rateID#);
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copyRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.ViewSubscriptionSetup',0) neq 1>
			<cflocation url="#this.link.message#&message=1" addtoken="false">
		</cfif>

		<cfset local.rateID = int(val(arguments.event.getValue('rateID',0)))>
	
		<cfif local.rateID eq 0>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRate">
			select rateName, scheduleID
			from dbo.sub_rates
			where rateID = #local.rateID#
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateScheds">
			select scheduleID, scheduleName
			from dbo.sub_rateSchedules
			where siteID = #arguments.event.getValue('mc_siteinfo.siteID')#
			and status <> 'D'
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_ratesCopy.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCopyRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rateID = int(val(arguments.event.getValue('rateID',0)))>
		<cfset local.rateName = arguments.event.getTrimValue('ratename','')>
		<cfset local.rateScheduleID = int(val(arguments.event.getValue('rateSchedule',0)))>
		<cfset local.copyPerms = int(val(arguments.event.getValue('selCopyPerms',0)))>

		<cfif local.rateID eq 0>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfstoredproc procedure="sub_copyRate" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.rateName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.rateScheduleID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.copyPerms#">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadRates();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Reports --->
	<cffunction name="listReports" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = structNew();

			// build XML LINKS --------------------------------------------------------------------------
			local.subscribersXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=allSubscribersRootOnlyXML&mode=stream';
			local.subscribersAccountingXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=allSubscribersAccountingRootOnlyXML&mode=stream';
			local.allSubscribersScheduleLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAllSubscribersScheduleList&mode=stream";
			local.subscriberChangeLogsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscriberChangeLogList&mode=stream";
			
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.subRenewalLink = buildCurrentLink(arguments.event,"showSubRenewalLink") & "&mode=direct";

			local.checkAll = arguments.event.getValue('chkAll',0);
			
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");

			local.SubReportFilter = local.objSubs.getSubReportFilter();

			if (len(local.SubReportFilter.listFilter.fTermStartFrom))
				local.SubReportFilter.listFilter.fTermStartFrom = dateFormat(local.SubReportFilter.listFilter.fTermStartFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.listFilter.fTermStartTo))
				local.SubReportFilter.listFilter.fTermStartTo = dateFormat(local.SubReportFilter.listFilter.fTermStartTo,'m/d/yyyy');
			
			if (len(local.SubReportFilter.listFilter.fTermEndFrom))
				local.SubReportFilter.listFilter.fTermEndFrom = dateFormat(local.SubReportFilter.listFilter.fTermEndFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.listFilter.fTermEndTo))
				local.SubReportFilter.listFilter.fTermEndTo = dateFormat(local.SubReportFilter.listFilter.fTermEndTo,'m/d/yyyy');
				
			if (len(local.SubReportFilter.listFilter.fOffrExpFrom))
				local.SubReportFilter.listFilter.fOffrExpFrom = dateFormat(local.SubReportFilter.listFilter.fOffrExpFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.listFilter.fOffrExpTo))
				local.SubReportFilter.listFilter.fOffrExpTo= dateFormat(local.SubReportFilter.listFilter.fOffrExpTo,'m/d/yyyy');
				
			if (len(local.SubReportFilter.accountingFilter.fTermStartFrom))
				local.SubReportFilter.accountingFilter.fTermStartFrom = dateFormat(local.SubReportFilter.accountingFilter.fTermStartFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.accountingFilter.fTermStartTo))
				local.SubReportFilter.accountingFilter.fTermStartTo = dateFormat(local.SubReportFilter.accountingFilter.fTermStartTo,'m/d/yyyy');
			
			if (len(local.SubReportFilter.accountingFilter.fTermEndFrom))
				local.SubReportFilter.accountingFilter.fTermEndFrom = dateFormat(local.SubReportFilter.accountingFilter.fTermEndFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.accountingFilter.fTermEndTo))
				local.SubReportFilter.accountingFilter.fTermEndTo = dateFormat(local.SubReportFilter.accountingFilter.fTermEndTo,'m/d/yyyy');
			
			if (len(local.SubReportFilter.scheduleFilter.fTermStartFrom))
				local.SubReportFilter.scheduleFilter.fTermStartFrom = dateFormat(local.SubReportFilter.scheduleFilter.fTermStartFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.scheduleFilter.fTermStartTo))
				local.SubReportFilter.scheduleFilter.fTermStartTo = dateFormat(local.SubReportFilter.scheduleFilter.fTermStartTo,'m/d/yyyy');
			
			if (len(local.SubReportFilter.scheduleFilter.fTermEndFrom))
				local.SubReportFilter.scheduleFilter.fTermEndFrom = dateFormat(local.SubReportFilter.scheduleFilter.fTermEndFrom,'m/d/yyyy');
			if (len(local.SubReportFilter.scheduleFilter.fTermEndTo))
				local.SubReportFilter.scheduleFilter.fTermEndTo = dateFormat(local.SubReportFilter.scheduleFilter.fTermEndTo,'m/d/yyyy');
		</cfscript>

		<cfif local.SubReportFilter.listFilter.fSubStatus eq "R">
			<cfset local.historyObj = CreateObject('component', 'model.system.platform.history')>
			<cfset local.auditTrailFlag = arguments.event.getValue('at','')>
			<cfif local.auditTrailFlag eq 'all'>
				<cfset local.subOfferAuditTrail = local.historyObj.getSubOfferUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
			<cfelse>
				<cfset local.subOfferAuditTrail = local.historyObj.getSubOfferUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'), limit=10)>
			</cfif>
		</cfif>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubTypes">
			select st.typeID, st.typeName
			from dbo.sub_Types st
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and st.status = 'A'
			order by st.typeName
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryStatuses">
			select statusCode, statusName
			from dbo.sub_statuses
			order by statusName
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPaymentStatuses">
			select statusCode, statusName
			from dbo.sub_paymentStatuses
			order by statusName
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFrequencies">
			select frequencyID, frequencyName
			from dbo.sub_frequencies
			where siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			and status <> 'D'
			order by frequencyName
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDeferredGLAccounts">
			select GLAccountID, AccountName, AccountCode, deferredGLAccountID
			from dbo.tr_GLAccounts 
			where orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			and deferredGLAccountID is not null
			order by AccountName;
		</cfquery>

		<cfset local.hasRightsImportSubscribers = false>
		<cfset local.hasRightsImportSubscribersCOF = false>
		<cfset local.hasRightsImportCustom = false>
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.importSubscribers',0) is 1>
			<cfset local.hasRightsImportSubscribers = true>
		</cfif>
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.importSubscribersCOF',0) is 1>
			<cfset local.hasRightsImportSubscribersCOF = true>
		</cfif>
		<cfif arguments.event.getValue('mc_admintoolInfo.myRights.importCustom',0) is 1>
			<cfset local.hasRightsImportCustom = true>
		</cfif>

		<cfset local.showImpTemplate = true>
		<cfif arguments.event.getValue('tab','') eq 'import' and local.hasRightsImportSubscribers and arguments.event.getValue('importFileName1','') neq ''>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processSubscriberImport(arguments.event)>
		<cfelseif arguments.event.getValue('tab','') eq 'import' and local.hasRightsImportSubscribersCOF and arguments.event.getValue('importFileName2','') neq ''>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processSubCardsOnFileImport(arguments.event)>
		<cfelseif arguments.event.getValue('impmode','') eq 'custom' and local.hasRightsImportCustom and FileExists(ExpandPath("model/admin/subscriptions/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfset local.objCustomImport = CreateObject("component","model.admin.subscriptions.custom.#arguments.event.getValue('mc_siteinfo.orgcode')#")>
			<cfif isDefined("local.objCustomImport.processImport")>
				<cfset local.showImpTemplate = false>
				<cfset local.impData = local.objCustomImport.processImport(event=arguments.event, doAgainURL="#this.link.listReports#&tab=import")>
			</cfif>
		</cfif>

		<cfif local.showImpTemplate and local.hasRightsImportCustom and FileExists(ExpandPath("model/admin/subscriptions/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfinvoke component="custom.#arguments.event.getValue('mc_siteinfo.orgcode')#" method="list" returnvariable="local.customData" subscriptionAdmin="#this#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_subscriptionReports.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = structNew();

			// build XML LINKS --------------------------------------------------------------------------
			local.subscribersListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubscribersForGenerateRenewals&mode=stream";
			
			local.checkAll = arguments.event.getValue('chkAll',0);
			if (local.checkAll eq 0) {
				local.chkButtonTitle = "Check All";
			}
			else {
				local.chkButtonTitle = "Uncheck All";
			}
			
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");

			local.SubRenewalsFilter = local.objSubs.getSubRenewalsFilter();
			
			if (len(local.SubRenewalsFilter.listFilter.fTermStartFrom))
				local.SubRenewalsFilter.listFilter.fTermStartFrom = dateFormat(local.SubRenewalsFilter.listFilter.fTermStartFrom,'m/d/yyyy');
			if (len(local.SubRenewalsFilter.listFilter.fTermStartTo))
				local.SubRenewalsFilter.listFilter.fTermStartTo = dateFormat(local.SubRenewalsFilter.listFilter.fTermStartTo,'m/d/yyyy');

			if (len(local.SubRenewalsFilter.listFilter.fTermEndFrom))
				local.SubRenewalsFilter.listFilter.fTermEndFrom = dateFormat(local.SubRenewalsFilter.listFilter.fTermEndFrom,'m/d/yyyy');
			if (len(local.SubRenewalsFilter.listFilter.fTermEndTo))
				local.SubRenewalsFilter.listFilter.fTermEndTo = dateFormat(local.SubRenewalsFilter.listFilter.fTermEndTo,'m/d/yyyy');
			
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			
			local.renewIDs = arguments.event.getValue('fSubscribers','');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubTypes" result="local.qrySubTypesResult">
			select typeID, typeName,
				dbo.fn_cache_perms_getResourceRightsXML(siteResourceID,<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcuser.memberdata.memberid#">,<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">) as subTypePerms
			from dbo.sub_types
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and status <> 'D'
			order by typeName
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryPaymentStatuses">
			select statusCode, statusName
			from dbo.sub_paymentStatuses
			order by statusName
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFrequencies">
			select frequencyID, frequencyName
			from dbo.sub_frequencies
			where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			and status <> 'D'
			order by frequencyName
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubStatuses">
			select statusCode, statusName
			from dbo.sub_statuses
			where canRenew = 1
			order by statusName
		</cfquery>
		
		<cfset local.historyObj = CreateObject('component', 'model.system.platform.history')>
		<cfset local.auditTrailFlag = arguments.event.getValue('at','')>
		<cfif local.auditTrailFlag eq 'all'>
			<cfset local.subRenewalAuditTrail = local.historyObj.getSubRenewalUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
		<cfelse>
			<cfset local.subRenewalAuditTrail = local.historyObj.getSubRenewalUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgid'), limit=10)>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_subscriptionRenewals.cfm">				
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>

	<!--- Offer Emails --->
	<cffunction name="startGenerateOffers" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		
		<cfset local.returnToMemberID = arguments.event.getValue('mid','0')>
		<cfset local.limitToSubscriberID = arguments.event.getValue('subscriberOnly','0')>
		<cfset local.emailTemplateID = arguments.event.getValue('fEmailTemplateID','0')>
		<cfset local.templateOnly = arguments.event.getValue('templateOnly','0')>
		<cfset local.returnFunction = arguments.event.getValue('retF','')>
		<cfset local.etIDAccepted = arguments.event.getValue('etIDAccept','0')>
		<cfset local.templateMsg = arguments.event.getValue('fEmailTemplateMsg','')>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfif local.emailTemplateID eq 0>
			<cfset local.dspTemplateStep = "selectTemp">
			<cfset local.formLink = this.link.startGenerateOffers>
			<cfif local.returnToMemberID neq 0>
				<cfset local.formLink = local.formLink & '&mid=' & local.returnToMemberID>
			</cfif>
			<cfset local.qryEmailTemplateTypes = CreateObject("component","model.admin.emailTemplates.emailTemplates").getCategoriesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="ETSUBS")>
			<cfif local.limitToSubscriberID gt 0>
				<cfset local.strFilters.fChkedSubs = local.limitToSubscriberID>
			</cfif>
		<cfelseif local.etIDAccepted neq '1'>
			<cfset local.dspTemplateStep = "showTemp">		
			<cfset local.formLink = this.link.startGenerateOffers>
			<cfif local.returnToMemberID neq 0>
				<cfset local.formLink = local.formLink & '&mid=' & local.returnToMemberID>
			</cfif>
			<cfset local.etGridXML = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=emailTemplateGridXML&mode=stream">
		<cfelse>
			<cfset local.dspTemplateStep = "processTemp">
			<cfset local.formLink = this.link.processGenerateOffers>
			<cfif local.returnToMemberID neq 0>
				<cfset local.formLink = local.formLink & '&mid=' & local.returnToMemberID>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_emailTemplateSelect.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processGenerateOffers" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>

		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.emailTemplateID = arguments.event.getValue('fEmailTemplateID','0')>
		<cfset local.returnToMemberID = arguments.event.getValue('mid','0')>
		<cfset local.msgHeader = "">
		<cfset local.msg = "">

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
			
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>
		<cfif len(local.subscriberIDList)>
			<cftry>

				<cfscript>
					local.baseTemplate = application.objEmailWrapper.getBaseTemplateHTML(siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), emailTitle="");
					local.subOfferInnerWrapper = '<div style="width:607px;padding:1px 1px 1px 1px;font-family:Verdana, Arial, Helvetica, sans-serif;font-size:8pt;color:##666;"><div style="margin:0 30px 15px 30px;">@@rawcontent@@</div></div>';
					local.emailWrapper = replacenocase(local.basetemplate,"<!--BodyContent-->",local.subOfferInnerWrapper,"all");
				</cfscript>

				<cfstoredproc procedure="sub_queueEmailOffers" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.emailTemplateID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.emailWrapper#">
					<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" null="true">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
				</cfstoredproc>
				<cfset local.msgHeader ='E-Mails Scheduled'>
				<cfset local.msg = '<div class="alert alert-info p-2">Your selected subscriptions have been queued for e-mailing and will begin processing soon.</div>'>
				
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				<cfset local.msgHeader ='Issue E-mailing Subscribers'>
				<cfset local.msg = '<div class="alert alert-warning p-2">
						We were not able to send the e-mail to the selected subscribers.<br/>
						MemberCentral has been notified of the issue; please follow up with Support for a status.
					</div>'>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.msgHeader ='Issue E-mailing Subscribers'>
			<cfset local.msg = '<div class="alert alert-warning p-2">We were not able to send the e-mail to the selected subscribers. No recipients found.</div>'>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>		
					top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
					top.$('##MCModalLabel').html('#local.msgHeader#');
					top.$('##MCModalBody').html('#local.msg#');
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Mark Billed --->
	<cffunction name="startMarkBilled" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_billSubs.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMarkBilled" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.returnToMemberID = arguments.event.getValue('mid','0')>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="sub_queueMarkBilled" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "Your selected subscriptions have been queued for billing and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalLabel').html('Subscription Billing Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "We were not able to queue the subscriptions for billing.<br/>
					MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalLabel').html('Issue Billing Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Mark Accepted --->
	<cffunction name="startMarkAccepted" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_markAcceptedSkip.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processAcceptSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>
		<cfset local.subRegObj = CreateObject("component","model.admin.subscriptions.subscriptionReg")>
		<cfset local.subscriberID = arguments.event.getValue('subId','')>
		<cfset local.loggedInMemberId = arguments.event.getValue('mid','')>
		<cfset local.skipEmailTemplateNotifications = arguments.event.getValue('fSkipEmail',0)>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.getActiveMemberID">
			select m.activememberID as memberID
			from sub_subscribers ss
			inner join ams_members m
				on m.memberID = ss.memberID
				and ss.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subscriberID#">
		</cfquery>
		
		<cfset local.acceptResult = local.subRegObj.autoAcceptSubscription(
				orgID=arguments.event.getValue('mc_siteinfo.orgid'), 
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				siteCode=arguments.event.getValue('mc_siteinfo.sitecode'),
				memberID=local.getActiveMemberID.memberID, 
				rootSubscriberID=local.subscriberID,
				skipEmailTemplateNotifications=local.skipEmailTemplateNotifications,
				bypassQueue=0)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.acceptResult.success>
					Processing the request...
					<script language="javascript">
						$(document).ready(function(){
							top.location.reload();
						});
					</script>
				<cfelse>
					Unable to process the request.
				</cfif>
			</cfoutput>
		</cfsavecontent>	
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMarkAccepted" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.skipEmailTemplateNotifications = arguments.event.getValue('fSkipEmail',0)>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
	
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>
		
		<cftry>
			<cfquery name="local.qryMarkSubsAsAccepted" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @recordedByMemberID int, @subscriberIDList varchar(max), @suppressEmails bit, @importResult xml;
				SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
				SET @subscriberIDList = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">;
				SET @suppressEmails = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.skipEmailTemplateNotifications#">;

				EXEC dbo.sub_queueMarkAccepted @recordedByMemberID=@recordedByMemberID, @subscriberIDList=@subscriberIDList, 
					@suppressEmails=@suppressEmails, @markQueueAsReady=1, @importResult=@importResult OUTPUT;
			</cfquery>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
						var msg = "Your selected subscriptions have been queued for acceptance and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Subscription Acceptance Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
						var msg = "We were not able to queue the subscriptions for acceptance.<br/>
					MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Issue Accepting Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Delete Renewals --->
	<cffunction name="startDeleteRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_delRenewals.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="processDeleteRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="sub_queueMassDelete" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "Your selected subscriptions have been queued for deletion and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalLabel').html('Subscription Deletion Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "We were not able to queue the subscriptions for deletion.<br/>
						MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalLabel').html('Issue Deleting Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- mark Inactive --->
	<cffunction name="startMarkInactive" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_inactivateSubs.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMarkInactive" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
	
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="sub_queueMassSetInactive" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>	
					<script>
					var msg = "Your selected subscriptions have been queued for inactivation and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalLabel').html('Subscription Inactivations Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
				<script>
					var msg = "We were not able to queue the subscriptions for inactivation.<br/>
					MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalLabel').html('Issue Inactivating Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Mark Active --->
	<cffunction name="startMarkActive" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_activateSubs.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMarkActive" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		
		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
	
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="sub_queueMassSetActive" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "Your selected subscriptions have been queued for activation and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalLabel').html('Subscription Activations Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "We were not able to queue the subscriptions for activation.<br/>
					MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalLabel').html('Issue Activating Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Mark Expired --->
	<cffunction name="startMarkExpired" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_expireSubs.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="processMarkExpired" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="1200">
		
		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>
		
		<cftry>
			<cfstoredproc procedure="sub_queueExpireSubscribers" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						top.mcg_reloadGrid();
						top.MCModalUtils.setTitle('Subscription Expirations Scheduled');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="p-3">
						Your selected subscriptions have been queued for expiration and will begin processing soon. You'll receive an email when they have been completed.
					</div>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						top.MCModalUtils.setTitle('Issue Expiring Selected Subscriptions');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="p-3">
						We were not able to queue the subscriptions for expiration.<br/>
						MemberCentral has been notified of the issue; please follow up with Support for a status.
					</div>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Renewals --->
	<cffunction name="getSubscriberIDListFromSelection" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>

		<cfset local.subscriberIDList = arguments.event.getValue('fSubscribers','')>

		<cfif arguments.event.getValue('chkAll','0') eq 1>
			<cfset local.subscriberIDList = "">
			<cfset local.objSubs = CreateObject("component","subscriptions")>
			<cfset local.SubRenewalsFilter = local.objSubs.getSubRenewalsFilter()>
			
			<cfset local.subStartFromDate = ''>
			<cfset local.subStartToDate = ''>
			<cfset local.subEndFromDate = ''>
			<cfset local.subEndToDate = ''>
			
			<cfif (len(local.SubRenewalsFilter.listFilter.fTermStartFrom))>
				<cfset local.subStartFromDate = dateFormat(local.SubRenewalsFilter.listFilter.fTermStartFrom,'m/d/yyyy')>
			</cfif>
			<cfif (len(local.SubRenewalsFilter.listFilter.fTermStartTo))>
				<cfset local.subStartToDate = dateFormat(local.SubRenewalsFilter.listFilter.fTermStartTo,'m/d/yyyy')>
			</cfif>
			<cfif (len(local.SubRenewalsFilter.listFilter.fTermEndFrom))>
				<cfset local.subEndFromDate = dateFormat(local.SubRenewalsFilter.listFilter.fTermEndFrom,'m/d/yyyy')>
			</cfif>
			<cfif (len(local.SubRenewalsFilter.listFilter.fTermEndTo))>
				<cfset local.subEndToDate = dateFormat(local.SubRenewalsFilter.listFilter.fTermEndTo,'m/d/yyyy')>
			</cfif>

			<cfif ((len(local.subStartFromDate) eq 0) AND (len(local.subStartToDate) gt 0))>
				<cfset local.subStartFromDate = local.subStartToDate>
				<cfset local.subStartToDate = ''>
			</cfif>
			
			<cfif (len(local.subStartToDate))>
				<cfset local.subStartToDate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subStartToDate), 0)))>
			</cfif>

			<cfif ((len(local.subEndFromDate) gt 0) AND (len(local.subEndToDate) eq 0))>
				<cfset local.subEndToDate = local.subEndFromDate>
				<cfset local.subEndFromDate = ''>
			</cfif>
			
			<cfif (len(local.subEndToDate))>
				<cfset local.subEndToDate = dateadd("l",-3,dateadd("d",1,DATEADD("d",DATEDIFF("d",0,local.subEndToDate), 0)))>
			</cfif>
			
			<cfset local.subList = local.objSubs.getSubRenewalsQuery(
				subStatus=local.SubRenewalsFilter.listFilter.fSubStatus,
				subPaymentStatus=local.SubRenewalsFilter.listFilter.fSubPaymentStatus,
				subStartFromDate=local.subStartFromDate,
				subStartToDate=local.subStartToDate,
				subEndFromDate=local.subEndFromDate,
				subEndToDate=local.subEndToDate,
				subType=local.SubRenewalsFilter.listFilter.fSubType,
				subID=local.SubRenewalsFilter.listFilter.fSubscription,
				freqID=local.SubRenewalsFilter.listFilter.fFreq,
				rateID=local.SubRenewalsFilter.listFilter.fRate,
				hasCard=local.SubRenewalsFilter.listFilter.fHasCardOnFile,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				notSubscribers=arguments.event.getValue('fNotSubscribers',''),
				associatedMemberID=local.SubRenewalsFilter.listFilter.associatedMemberID,
				associatedGroupID=local.SubRenewalsFilter.listFilter.associatedGroupID,
				linkedRecords=local.SubRenewalsFilter.listFilter.linkedRecords)>
			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfquery name="local.qryInvalidSubs" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			
			SELECT s.subscriberID, mActive.firstname + ' ' + mActive.lastname + ' ('+ mActive.membernumber +')' AS memberNameDisplay, subs.subscriptionName, st.statusName
			FROM dbo.sub_subscribers AS s
			INNER JOIN dbo.sub_subscriptions AS subs ON subs.subscriptionID = s.subscriptionID
			INNER JOIN dbo.sub_statuses st on st.statusID = s.statusID
			INNER JOIN dbo.ams_members AS m ON m.memberID = s.memberID
				AND m.orgID = @orgID
			INNER JOIN dbo.ams_members AS mActive ON mActive.memberID = m.activeMemberID
				AND mActive.orgID = @orgID
			LEFT OUTER JOIN dbo.sub_subscriptions AS validSub ON validSub.orgID = @orgID
				AND validSub.subscriptionID = subs.subscriptionID
				AND validSub.soldSeparately = 1
				AND validSub.[status] = 'A'
				AND s.parentSubscriberID IS NULL
			WHERE s.orgID = @orgID
			AND s.subscriberID IN (0#local.subscriberIDList#)
			AND validSub.subscriptionID IS NULL;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn { subscriberIDList:local.subscriberIDList, qryInvalidSubs:local.qryInvalidSubs }>
	</cffunction>

	<cffunction name="getInvalidSubsListMessage" access="private" output="false" returntype="string">
		<cfargument name="qryInvalidSubs" type="query" required="yes">

		<cfset var retHTML = "">

		<cfsavecontent variable="retHTML">
			<cfoutput>
				<h5>Invalid Entries found</h5>
				<div class="alert alert-danger">
					The following subscriptions from the selection are invalid based on the current setup. We are unable to continue the renewal process.
					<table class="table table-sm table-borderless my-2">
					<cfloop query="arguments.qryInvalidSubs">
						<tr>
							<td>#arguments.qryInvalidSubs.currentRow#.</td>
							<td>#arguments.qryInvalidSubs.memberNameDisplay#</td>
							<td>#arguments.qryInvalidSubs.subscriptionName# / #arguments.qryInvalidSubs.statusName#</td>
						</tr>
					</cfloop>
					</table>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn retHTML>
	</cffunction>

	<cffunction name="confirmGenerateRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.sid = arguments.event.getValue('sid', 0)>
		
		<cfif val(local.sid) neq 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubInfo">
				select sub.subscriptionName
				from dbo.sub_subscribers s
				inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
				where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.sid#">
			</cfquery>

			<cfset arguments.event.setValue('chkAll',0)>
			<cfset arguments.event.setValue('fSubscribers',local.sid)>
			<cfset arguments.event.setValue('fNotSubscribers','')>
		</cfif>

		<cfset local.strSubSelectionInfo = getSubscriberIDListFromSelection(event=arguments.event)>
		
		<cfif local.strSubSelectionInfo.qryInvalidSubs.recordCount>
			<cfset local.strInvalidSubsListMessage = getInvalidSubsListMessage(qryInvalidSubs=local.strSubSelectionInfo.qryInvalidSubs)>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriptionRenewalConfirm.cfm">
		</cfsavecontent>
		
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="startGenerateRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strSubSelectionInfo = getSubscriberIDListFromSelection(event=arguments.event)>

		<cfif local.strSubSelectionInfo.qryInvalidSubs.recordCount>
			<cfset local.data = getInvalidSubsListMessage(qryInvalidSubs=local.strSubSelectionInfo.qryInvalidSubs)>
		<cfelse>
			<cfset local.subscriberIDList = local.strSubSelectionInfo.subscriberIDList>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFindCalcSubs">
				select subscriberID
				from dbo.sub_subscribers s
				inner join sub_subscriptions subs on subs.subscriptionID = s.subscriptionID and subs.rateTermDateFlag = 'C'
				where s.subscriberID in (0#local.subscriberIDList#)
			</cfquery>

			<cfset local.calcFound = local.qryFindCalcSubs.recordCount gt 0>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_subscriptionRenewalGenerationDate.cfm">
			</cfsavecontent>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processGenerateRenewals" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.rescindDate = arguments.event.getValue('fRescindDate','')>
		<cfset local.overrideStartDate = arguments.event.getValue('fOverrideDate','')>
		<cfset local.retLocation = arguments.event.getValue('retLoc','list')>
		<cfset local.memberIDForLocation = arguments.event.getValue('mid',0)>
		<cfset local.errMsg = ''>
		<cfset local.warningMsg = ''>
		<cfset local.successMsg = ''>

		<cfif not isDate(local.rescindDate)>
			<cfset local.rescindDate= ''>
			<cfset arguments.event.setValue('fRescindDate','')>
		</cfif>
		<cfif not isDate(local.overrideStartDate)>
			<cfset local.overrideStartDate= ''>
			<cfset arguments.event.setValue('fOverrideDate','')>
		</cfif>

		<cfset local.strSubSelectionInfo = getSubscriberIDListFromSelection(event=arguments.event)>
		<cfset local.subscriberIDList = local.strSubSelectionInfo.subscriberIDList>

		<cfif not ListLen(local.subscriberIDList)>
			<cfsavecontent variable="local.data">
				<cfoutput>No subscriptions were renewed.</cfoutput>
			</cfsavecontent>
		<cfelseif local.strSubSelectionInfo.qryInvalidSubs.recordCount>
			<cfset local.data = getInvalidSubsListMessage(qryInvalidSubs=local.strSubSelectionInfo.qryInvalidSubs)>
		<cfelse>
			<cfif local.retLocation eq "member">
				<cfset local.objSubRenew = CreateObject("component","subscriptionRenew")>
				<cfset local.returnStr = { arrMessages=ArrayNew(1), arrRenewMessages=ArrayNew(1), successCount=0, errorCount=0, warningCount=0, exceptionCount=0, resultMsg='' }>
				
				<cfloop list="#local.subscriberIDList#" index="local.thisSubscriberID">
					<cfset local.renewResult = local.objSubRenew.doSubscribeRenew(
						subscriberID=local.thisSubscriberID,
						rescindDate=local.rescindDate,
						overrideStartDate=local.overrideStartDate,
						siteID=arguments.event.getValue('mc_siteinfo.siteID'),
						orgID=arguments.event.getValue('mc_siteinfo.orgID'),
						actorMemberID=session.cfcuser.memberdata.memberid,
						renewMode="singlemember"
					)>

					<cfif local.renewResult.success eq false and structKeyExists(local.renewResult,"isException") and local.renewResult.isException>
						<cfset local.returnStr.exceptionCount = local.returnStr.exceptionCount + 1>
					</cfif>
					
					<cfloop array="#local.renewResult.arrMessages#" index="local.thisMessage">
						<cfif local.thisMessage.errType eq "Warning">
							<cfset local.returnStr.warningCount = local.returnStr.warningCount + 1>
							<cfset ArrayAppend(local.returnStr.arrMessages, local.thisMessage)>
							<cfset local.warningMsg = local.warningMsg & '<li>' & local.thisMessage.errMessage & '</li>'>
						<cfelseif local.thisMessage.errType eq "Error">
							<cfset local.returnStr.errorCount = local.returnStr.errorCount + 1>
							<cfset ArrayAppend(local.returnStr.arrMessages, local.thisMessage)>
							<cfset local.errMsg = local.errMsg & '<li>' & local.thisMessage.errMessage & '</li>'>
						<cfelseif local.thisMessage.errType eq "Renewal">
							<cfset local.returnStr.successCount = local.returnStr.successCount + 1>
							<cfset ArrayAppend(local.returnStr.arrRenewMessages, local.thisMessage)>
							<cfset local.successMsg = local.successMsg & '<li>' & local.thisMessage.errMessage & '</li>'>
						</cfif>
					</cfloop>
				</cfloop>

				<cfsavecontent variable="local.returnStr.resultMsg">
					<cfoutput>
					<cfif local.returnStr.exceptionCount gt 0>
						<div class="alert alert-danger">
							An error occured while performing the renewal process. Contact MemberCentral for assistance.
						</div>
					</cfif>
					<div class="mt-2">
						Renewal Summary:<br/>
						#local.returnStr.successCount# Successful Renewal<cfif local.returnStr.successCount is not 1>s</cfif><br/><cfif local.returnStr.successCount GT 0><ul>#local.successMsg#</ul></cfif>
						#local.returnStr.warningCount# Warning<cfif local.returnStr.warningCount is not 1>s</cfif><br/><cfif local.returnStr.warningCount GT 0><ul>#local.warningMsg#</ul></cfif>
						#local.returnStr.errorCount# Error<cfif local.returnStr.errorCount is not 1>s</cfif><br/><cfif local.returnStr.errorCount GT 0><ul>#local.errMsg#</ul></cfif>
					</div>
					</cfoutput>
				</cfsavecontent>

				<cfif local.returnStr.exceptionCount eq 0>
					<cfset CreateObject('component','model.system.platform.history').addSubRenewalUpdateHistory(orgID=arguments.event.getValue('mc_siteinfo.orgID'),
						actorMemberID=session.cfcuser.memberdata.memberid, mainMessage=local.returnStr.resultMsg, messages=local.returnStr.arrMessages, 
						renewals=local.returnStr.arrRenewMessages)>
				</cfif>

				<cfset local.retFormLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit') & "&memberID=#local.memberIDForLocation#&tab=subscriptions">
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="JavaScript">
						$(function() {
							top.MCModalUtils.setTitle('Renewal Summary');
							top.MCModalUtils.buildFooter({
								classlist: 'd-flex',
								showclose: false,
								buttons: [ 
									{ class: "btn btn-primary btn-sm py-1", clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.top.location.reload', label: 'Return to Member Record', name: 'btnReturnMem', id: 'btnReturnMem' },
								]
							});
						});
					</script>
					<div class="p-3">
						<div class="mb-3">#local.returnStr.resultMsg#</div>
					</div>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cftry>
					<cfstoredproc procedure="sub_queueRenewals" datasource="#application.dsn.membercentral.dsn#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
						<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
						<cfif len(local.rescindDate)>
							<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.rescindDate#">
						<cfelse>
							<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
						</cfif>
						<cfif len(local.overrideStartDate)>
							<cfprocparam type="In" cfsqltype="CF_SQL_DATE" value="#local.overrideStartDate#">
						<cfelse>
							<cfprocparam type="In" cfsqltype="CF_SQL_DATE" null="true">
						</cfif>
						<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
					</cfstoredproc>
				<cfcatch type="Any">
					<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>
				</cfcatch>
				</cftry>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<div class="pl-4">
						Your subscription renewals have been scheduled and we'll begin to process them shortly.
						<cfif len(session.cfcuser.memberdata.email) gt 0>
							<br/>You will be e-mailed a renewals report upon completion.
						</cfif>
						<br/><br/>
						<button type="button" class="btn btn-sm btn-secondary" onClick="top.location.href='#this.link.listRenewals#';">Return to Generate Renewals</button>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- mass update offer expire date --->
	<cffunction name="startUpdateOfferExpirationDate" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_updateOfferExpirationDate.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processUpdateOfferExpirationDate" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.offerEndDate = arguments.event.getValue('offerEndDate','')>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfif listlen(local.subscriberIDList)>
			<cfif local.offerEndDate neq ''>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberUpdate">
					update dbo.sub_subscribers 
					set offerRescindDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#DateFormat(local.offerEndDate, "m/d/yyyy")#">
					where subscriberID IN (0#local.subscriberIDList#)
				</cfquery>
			<cfelse>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberUpdate">
					update dbo.sub_subscribers 
					set offerRescindDate = NULL 
					where subscriberID IN (0#local.subscriberIDList#)
				</cfquery>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.js">
			<cfoutput>
			<script language="javascript">
				$(document).ready(function(){
					top.mcg_reloadGrid();
					top.MCModalUtils.hideModal();
				});
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.js#">

		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Done.</h4>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- mass update grace end date --->
	<cffunction name="startMassUpdateGraceEndDate" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_updateGraceEndDate.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="processMassUpdateGraceEndDate" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.newGraceEndDate = arguments.event.getValue('newGraceEndDate','')>
		<cfset local.orgID = arguments.event.getValue('mc_siteinfo.orgID')>
		<cfset local.isValidGraceEndDate = true>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfif listlen(local.subscriberIDList) and len(local.newGraceEndDate)>			
			<cfset local.isValidGraceEndDate = local.objSubs.isValidGraceEndDate(orgID=local.orgID, subscriberIDList=local.subscriberIDList, newGraceEndDate=local.newGraceEndDate)>
			<cfif local.isValidGraceEndDate>
				<cfset local.objSubs.massUpdateGraceEndDate(orgID=local.orgID, subscriberIDList=local.subscriberIDList, newGraceEndDate=local.newGraceEndDate)>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.js">
			<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						<cfif local.isValidGraceEndDate>
							top.mcg_reloadGrid();
							setTimeout(function(){
								top.MCModalUtils.hideModal();
							},1500);
							
						<cfelse>
							mca_showAlert('err_graceDate', 'The provided Grace End Date is invalid; it must be on or after the end date of the subscription.');	
						</cfif>
					});
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.js#">

		<cfsavecontent variable="local.data">
			<cfif local.isValidGraceEndDate>
				<cfoutput><div class="alert alert-success">Grace End Date Updated.</div></cfoutput>
			<cfelse>
				<cfinclude template="frm_updateGraceEndDate.cfm">
			</cfif>
			
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Force Add Subscription --->
	<cffunction name="startForceAddSub" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>
		<cfset local.availableSubscriptionsListForForceAddLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAvailableSubscriptionsListForForceAdd&mode=stream">			
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_forceAddSub.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processForceAddSub" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.subRegObj = CreateObject("component","subscriptionReg")>

		<cfset local.arrAdded = ArrayNew(1)>
		<cfset local.arrNoAdd = ArrayNew(1)>

		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.forceAddSubscriptionID = arguments.event.getValue('forceAddSubID',0)>
		
		<cfset local.hasSubCount = 0>
		<cfset local.noRateCount = 0>
		<cfset local.unableToAddCount = 0>
		<cfset local.errorCount = 0>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
			
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfstoredproc procedure="sub_queueForceAddSubscribers" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.forceAddSubscriptionID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.subscriberIDList#">
			</cfstoredproc>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "Your selected subscriptions have been queued for adding and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Subscription Adding Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "We were not able to queue the subscriptions for adding.<br/>
					MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Issue Adding Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Generate Subscription --->
	<cffunction name="startGenerateSub" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>
		<cfset local.availableSubscriptionsListForGenerateLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAvailableSubscriptionsListForGenerate&mode=stream">

		<cfset local.startDate = DateFormat(now(), "m/d/yyyy")>
	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_startGenerateSub.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="processGenerateSub" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
	
		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>

		<cfset local.arrAdded = ArrayNew(1)>
		<cfset local.arrNoAdd = ArrayNew(1)>
		<cfset local.arrErrorValues=arrayNew(1)>
		<cfset local.arrProcessedValues=arrayNew(1)>

		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>		
		<cfset local.generateSubscriptionID = arguments.event.getValue('generateSubID',0)>
		<cfset local.startDateOverride = arguments.event.getValue('startDateOverride','')>
		<cfset local.offerExpireDate = arguments.event.getValue('offerExpireDate','')>

		<cfset local.hasSubCount = 0>
		<cfset local.noRateCount = 0>
		<cfset local.unableToAddCount = 0>
		<cfset local.errorCount = 0>

		<cfset local.subRegObj = CreateObject("component","model.admin.subscriptions.subscriptionReg")>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">
			
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="3000">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionInfo">
			select t.typeName, subs.subscriptionName
			from dbo.sub_subscriptions subs
			inner join dbo.sub_types t on t.typeID = subs.typeID
			where subs.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.generateSubscriptionID)#">
		</cfquery>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryOriginalList" result="local.qryOriginalListResult">
			select s.subscriberID, s.memberID, mActive.memberID as activeMemberID, r.rateID, r.isRenewalRate, rf.frequencyID,
				RTRIM(mActive.lastname + ' ' + isnull(mActive.suffix, '')) + ', ' + mActive.firstname + isnull(' (' + mActive.membernumber + ')','') AS memberName
			from dbo.ams_members m
			inner join dbo.ams_members mActive on mActive.memberID = m.activeMemberID
			inner join dbo.sub_subscribers s on s.memberID = m.memberID
				and s.subscriberID in (0#local.subscriberIDList#)
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
			inner join dbo.sub_types t on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.sub_rateFrequencies rf on rf.rfid = s.rfid
			inner join dbo.sub_rates r on r.rateID = rf.rateID
		</cfquery>

		<cfset local.qualifySubRateRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="qualify")>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRates">
			set nocount on;

			declare @forceAddSubscriptionID int;
			select @forceAddSubscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.generateSubscriptionID)#">;
			
			declare @FID int, @memberid int, @siteID int;
			set @FID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qualifySubRateRFID#">;
			set @siteid = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;
			
			select memberID, rfid, rateAmt, numInstallments, frequencyName, frequencyShortName, frequency, frequencyID, rateName, rateUID, GLAccountID
			from (
				select activeMember.memberID, rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequencyShortName, f.frequency, 
					f.frequencyID, r.rateName, r.UID as rateUID, r.GLAccountID, count(rfmp.rfmpid) as rfmpidCount
				from dbo.sub_subscriptions as subs
				inner join dbo.sub_rateSchedules as rs on rs.scheduleID = subs.scheduleID and rs.status = 'A'
					and subs.subscriptionID = @forceAddSubscriptionID
				inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID
					and r.status = 'A'
					and r.isRenewalRate = 0
					and getdate() between r.rateAFStartDate and dateadd(day, datediff(day, 0, r.rateAFEndDate)+1, 0)
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints srfrp ON srfrp.siteID = @siteid
					AND srfrp.siteResourceID = r.siteResourceID
					AND srfrp.functionID = @FID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints gprp on gprp.siteID = @siteid
					and srfrp.rightPrintID = gprp.rightPrintID
				inner join ams_members activeMember on activeMember.groupPrintID = gprp.groupPrintID
					and activeMember.memberID in (0#valueList(local.qryOriginalList.activeMemberID)#)
					and activeMember.memberID = activeMember.activeMemberID
				inner join ams_members m on m.activeMemberID = activeMember.memberID
				inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID
					and rf.status = 'A'
				inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
					and f.siteID = @siteID
					and f.status = 'A'
				inner join dbo.sub_subscribers s on s.memberID = m.memberID
					and s.subscriberID in (0#local.subscriberIDList#)
				inner join dbo.sub_rateFrequencies rfSS on rfSS.rfid = s.rfid
				inner join sub_rates rSS on rSS.rateID = rfSS.rateID
				left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid
					and rfmp.status = 'A' 
				group by activeMember.memberID, rf.rfid, rf.rateAmt, rf.numInstallments, f.frequencyName, f.frequencyShortName, f.frequency, 
					f.frequencyID, r.rateName, r.UID, r.GLAccountID
			) x 
			where x.rfmpidCount > 0
			order by memberID, frequencyID;
		</cfquery>

		<cfset local.hasRateMemberList = valueList(local.qryRates.memberID)>

		<cfset local.startD = DateFormat(local.startDateOverride, "yyyy-mm-dd") & " 00:00:00.000">
		<cfset local.endD = DateFormat(local.startDateOverride, "yyyy-mm-dd") & " 23:59:59.997">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptions" result="local.qrySubscriptionsResult">
			set nocount on;

			declare @forceAddSubscriptionID int;
			select @forceAddSubscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.generateSubscriptionID)#">;

			select mActive.memberID, s.rootSubscriberID
			from dbo.sub_subscribers s
			inner join dbo.sub_subscriptions subs on subs.subscriptionID = s.subscriptionID
				and s.subscriptionID = @forceAddSubscriptionID
			inner join dbo.sub_types t on t.typeID = subs.typeID
				and t.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">
			inner join dbo.ams_members m on m.memberID = s.memberID
			inner join dbo.ams_members mActive 
				on mActive.memberID = m.activeMemberID
			inner join dbo.sub_statuses st on st.statusID = s.statusID
				and st.statusCode in ('R','O','P','A')
			where mActive.memberID in (0#valueList(local.qryOriginalList.activeMemberID)#)
			and s.subStartDate <= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.startD#">
			and s.subEndDate >= <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.endD#">;
		</cfquery>
		
		<cfset local.hasSubMemberList = valueList(local.qrySubscriptions.memberID)>
		<cfset local.hasSubList = valueList(local.qrySubscriptions.rootSubscriberID)>

		<cfset local.doesNotHaveRateArray = listToArray(valueList(local.qryOriginalList.activeMemberID))>
		<cfset local.doesNotHaveRateArray.removeAll(listToArray(local.hasRateMemberList))>
		
		<!--- Report on hasSubList and doesNotHaveRateArray --->
		<cfif ListLen(local.hasSubMemberList) gt 0>
			<cfquery dbtype="query" name="local.qryHasSub">
				select subscriberID, activeMemberID, memberName
				from [local].qryOriginalList
				where memberID in (#local.hasSubMemberList#)
			</cfquery>
			<cfloop query="local.qryHasSub">
				<cfset local.hasSubCount = local.hasSubCount + 1>
				<cfset local.errStruct = {	memberID = local.qryHasSub.activeMemberID,
											memberName = local.qryHasSub.memberName,
											rootSubscriberID = local.qryHasSub.subscriberID,
											errType = "Already Has Subscription",
											errMessage = "Member already has subscription"
										}>
				<cfset ArrayAppend(local.arrNoAdd, local.errStruct)>
			</cfloop>
		</cfif>
		
		<cfif ArrayLen(local.doesNotHaveRateArray) gt 0>
			<cfquery dbtype="query" name="local.qryNoRate">
				select subscriberID, activeMemberID, memberName
				from [local].qryOriginalList
				where memberID in (#arrayToList(local.doesNotHaveRateArray)#)
			</cfquery>
			<cfloop query="local.qryNoRate">
				<cfset local.noRateCount = local.noRateCount + 1>
				<cfset local.errStruct = {	memberID = local.qryNoRate.activeMemberID,
											memberName = local.qryNoRate.memberName,
											rootSubscriberID = local.qryNoRate.subscriberID,
											errType = "No Rate",
											errMessage = "No rate available for member"
										}>
				<cfset ArrayAppend(local.arrNoAdd, local.errStruct)>
			</cfloop>
		</cfif>
		
		<cfset local.remainingArray = listToArray(valueList(local.qryOriginalList.activeMemberID))>
		<cfset local.remainingArray.removeAll(local.doesNotHaveRateArray)>
		<cfset local.remainingArray.removeAll(listToArray(local.hasSubMemberList))>
		<cfset local.remainingList = arrayToList(local.remainingArray)>

		<cfif Len(local.remainingList) gt 0>
			<cfquery dbtype="query" name="local.qryRemaining">
				select subscriberID, memberID, memberName, activeMemberID, frequencyID
				from [local].qryOriginalList
				where activeMemberID in (#local.remainingList#)
			</cfquery>
	
			<!--- loop over remaining subscribers and add subscription to them if possible (get rate from top 1 local.qryRates for member? ) --->
			<cfloop query="local.qryRemaining">

				<cfset local.subStruct = structNew()>
				
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriptionUID">
					select uid
					from dbo.sub_subscriptions
					where subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.generateSubscriptionID)#">
				</cfquery>
				
				<cfset local.subStruct.uid = local.qrySubscriptionUID.uid>
				<cfset local.hasMultipleRates = false>
				<cfquery dbtype="query" name="local.qryMemberRateCount">
					select rateUID
					from [local].qryRates
					where memberID = #local.qryRemaining.activeMemberID#
				</cfquery>
				<cfif local.qryMemberRateCount.recordCount gt 1>
					<cfset local.hasMultipleRates = true>
					<!--- don't fail due to multiple rates --->
					<cfquery dbtype="query" name="local.qryMemberRate" maxrows="1">
						select rateUID
						from [local].qryRates
						where memberID = #local.qryRemaining.activeMemberID#
					</cfquery>
					<cfset local.subStruct.rateUID = local.qryMemberRate.rateUID>
				</cfif>

				<cfset local.subReturn = local.subRegObj.autoSubscribe(
					event=arguments.event, 
					memberID=local.qryRemaining.activeMemberID, 
					subStruct=local.subStruct, 
					newAsRenewed=true, 
					startDateOverride=local.startDateOverride,
					offerExpireDate = local.offerExpireDate
				)>

				<cfif local.subReturn.success eq 1>
					<cfset local.msgAdditional = "">
					<cfif local.hasMultipleRates>
						<cfset local.msgAdditional = "(Multiple rates were available. Please check the subscription to validate the correct rate was selected.)">
					</cfif>

					<cfset local.errStruct = {	memberID = local.qryRemaining.activeMemberID,
												memberName = local.qryRemaining.memberName,
												rootSubscriberID = local.qryRemaining.subscriberID,
												errType = "Added",
												errMessage = "Added #local.qrySubscriptionInfo.typeName# / #local.qrySubscriptionInfo.subscriptionName# #local.msgAdditional#"
											}>
					<cfset ArrayAppend(local.arrAdded, local.errStruct)>
				<cfelse>
					<cfset local.errorCount = local.errorCount + 1>
					<cfset local.errStruct = {	memberID = local.qryRemaining.activeMemberID,
												memberName = local.qryRemaining.memberName,
												rootSubscriberID = local.qryRemaining.subscriberID,
												errType = "Error",
												errMessage = "Error occurred while trying to add subscription: #local.subReturn.errReason#"
											}>
					<cfset ArrayAppend(local.arrNoAdd, local.errStruct)>
				</cfif>

			</cfloop>
		</cfif>
		
		<cfset local.resultMsg = "Subscription Generate #local.qrySubscriptionInfo.typeName# / #local.qrySubscriptionInfo.subscriptionName# Results:<br>#ArrayLen(local.arrAdded)# successfully added<br>#local.unableToAddCount# Unable to be added<br>#local.hasSubCount# already had subscription<br>#local.noRateCount# did not have rates<br>#local.errorCount# errors<br>">

		<!--- build report --->
		<!--- Send email and notify the user (popup?) that an email has been sent --->
		<cfsavecontent variable="local.email_msgs">
			<cfoutput>
			<table style="border:0;padding:4px;width:100%;margin-bottom:10px;" cellspacing="0">
				<tr valign="top">
					<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
						#local.resultMsg#
					</td>
				</tr>
				<cfset local.currLoopMemberID = 0>
				<cfloop array="#local.arrNoAdd#" index="local.thisMessage">
					<cfif local.thisMessage.memberID neq local.currLoopMemberID>
						<tr valign="top">
							<td colspan="2" style="width:120px;font-weight:bold;border-bottom:1px solid ##ccc; font-size:9pt;">
								<br><nobr>#local.thisMessage.memberName#:</nobr>
								<cfset local.currLoopMemberID = local.thisMessage.memberID>
							</td>
						</tr>
					</cfif>
					<tr valign="top">
						<td style="width:2%">&nbsp;</td>
						<td style="border-bottom:1px solid ##ccc; font-size:9pt;">
							#local.thisMessage.errMessage#
						</td>
					</tr>
				</cfloop>
			</table>
			</cfoutput>
		</cfsavecontent>
		<cfset local.email_msgs = trim(replace(replace(replace(local.email_msgs,chr(9),"","ALL"),chr(13),"","ALL"),chr(10),"","ALL"))>

		<cfif len(session.cfcuser.memberdata.email)>
			<cfset local.responseStruct = application.objEmailWrapper.sendMailESQ(
				emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom')},
				emailto=[{ name:"", email:session.cfcuser.memberdata.email }],
				emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
				emailsubject="#arguments.event.getValue('mc_siteInfo.orgShortName')# Subscription Force Add Results",
				emailtitle="Subscription Force Add Results",
				emailhtmlcontent=local.email_msgs,
				emailAttachments=[],
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				memberID=arguments.event.getValue('mc_siteinfo.sysmemberid'),
				messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="SUBSFORCEADD"),
				sendingSiteResourceID=this.siteResourceID)>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>		
					top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
					top.$('##MCModalLabel').html('Subscription Addition Completed');
					top.$('##MCModalBody').html('#local.email_msgs#');
				</script>
			</cfoutput>	
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- Export subscriptions --->
	<cffunction name="startExportSubscriptions" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			selectorID="fsid", selectedFieldSetName='Subscriber Download Standard', inlinePreviewSectionID="exportFormContainer")>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriberExport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSubscriptions" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.objSubscriptionExport = createObject("component","subscriberExport")>
		
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.exportFormat = arguments.event.getValue("fmt","s")>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		
		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>
		
		<cfif local.exportFormat eq "m">
			<cfset local.stDownloadURL = local.objSubscriptionExport.exportSubscriptionsMailhouse(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), fieldSetID=arguments.event.getValue("fsid",0), excludeDeletedAddons=arguments.event.getValue("excludeDeletedAddons","N"), strFilters=local.strFilters, rootSubscriberIDList=local.subscriberIDList)>
		<cfelse>
			<cfset local.stDownloadURL = local.objSubscriptionExport.exportSubscriptionsStandard(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), fieldSetID=arguments.event.getValue("fsid",0), strFilters=local.strFilters, rootSubscriberIDList=local.subscriberIDList)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="startExportSubscriptionsAccounting" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			selectorID="fsid", selectedFieldSetName='Subscriber Download Standard', inlinePreviewSectionID="exportFormContainer")>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriberAcctExport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSubscriptionsAccounting" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.stDownloadURL = createObject("component","subscriberExport").exportSubscriptionsAccounting(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), fieldSetID=arguments.event.getValue("fsid",0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="startExportSubscriptionsChangeLog" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			selectorID="fsid", selectedFieldSetName='Subscriber Download Standard', inlinePreviewSectionID="exportFormContainer")>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriberChangeLogExport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportSubscriptionsChangeLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.stDownloadURL = createObject("component","subscriberExport").exportSubscriptionsChangeLog(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), fieldSetID=arguments.event.getValue("fsid",0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="showPaperStatementsForm" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>

 		<cfif arguments.event.getValue('act','email') eq "download">
			<cfset local.paperStatementsLink = this.link.downloadPaperStatements>
		<cfelse>
			<cfset local.paperStatementsLink = this.link.enqueuePaperStatements & "&downloadmode=#arguments.event.getValue('downloadmode','zip')#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_paperStatements.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="enqueuePaperStatements" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.mc_siteinfo = arguments.event.getValue('mc_siteinfo')>
		
		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">	
			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=local.mc_siteinfo.siteid,
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfset local.subStartFromDate = ''>
		<cfif len(local.strFilters.fTermStartFrom) gt 0>
			<cfset local.subStartFromDate = DateFormat(local.strFilters.fTermStartFrom, "m/d/yyyy")>
		</cfif>

		<cfset local.subStartToDate = ''>
		<cfif len(local.strFilters.fTermStartTo) gt 0>
			<cfset local.subStartToDate = DateFormat(local.strFilters.fTermStartTo, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.subEndFromDate = ''>
		<cfif len(local.strFilters.fTermEndFrom) gt 0>
			<cfset local.subEndFromDate = DateFormat(local.strFilters.fTermEndFrom, "m/d/yyyy")>
		</cfif>

		<cfset local.subEndToDate = ''>
		<cfif len(local.strFilters.fTermEndTo) gt 0>
			<cfset local.subEndToDate = DateFormat(local.strFilters.fTermEndTo, "m/d/yyyy") & " 23:59:59.997">
		</cfif>
		
		<cfset local.offerEndFromDate = ''>
		<cfif len(local.strFilters.fOffrExpFrom)>
			<cfset local.offerEndFromDate = DateFormat(local.strFilters.fOffrExpFrom, "m/d/yyyy")>
		</cfif>

		<cfset local.offerEndToDate = ''>
		<cfif len(local.strFilters.fOffrExpTo)>
			<cfset local.offerEndToDate = DateFormat(local.strFilters.fOffrExpTo, "m/d/yyyy") & " 23:59:59.997">
		</cfif>

		<cfset local.rfidList = ''>
		<cfif (val(local.strFilters.fRate) gt 0) OR (val(local.strFilters.fFreq) gt 0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRFIDs">
				select rf.rfid
				from dbo.sub_rateFrequencies rf
				inner join dbo.sub_rates r on r.rateID = rf.rateID 
				<cfif val(local.strFilters.fRate) gt 0>
					and r.rateID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.fRate#" list="yes">)
				</cfif>
				<cfif val(local.strFilters.fFreq) gt 0>
					where rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.fFreq#">
				</cfif>
			</cfquery>
			<cfset local.rfidList = valueList(local.qryRFIDs.rfid)>
			<cfif len(local.rfidList) eq 0>
				<cfset local.rfidList = 0>
			</cfif>
		</cfif>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteinfo.scheme & "://" & local.mc_siteinfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = local.mc_siteinfo.scheme & "://" & application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.renewalStatementTitle = arguments.event.getValue('renewalStatementTitle','')>
		<cfset local.frmSubscriberTopContent = arguments.event.getValue('frmSubscriberTopContent','')>
		<cfset local.frmRenewOnlineOptions = arguments.event.getValue('frmRenewOnlineOptions',0)>
		<cfset local.frmDisplayZeroDollarAddOns = arguments.event.getValue('frmDisplayZeroDollarAddOns',0)>
		<cfset local.frmSubscriberBottomContent = arguments.event.getValue('frmSubscriberBottomContent','')>		
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryQueueSubs">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @siteID int, @orgID int, @recordedByMemberID int, @downloadmode varchar(3);
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteid#">;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.orgid#">;
				set @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">;
				set @downloadmode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('downloadmode','zip')#">;

				-- put extra option fields into temp table
				declare @settings TABLE (name varchar(100), value varchar(max));
				insert into @settings (name, value) values ('renewalstatementtitle', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.renewalstatementtitle#">);
				insert into @settings (name, value) values ('frmsubscribertopcontent', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsubscribertopcontent#">);
				insert into @settings (name, value) values ('frmrenewonlineoptions', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmrenewonlineoptions#">);
				insert into @settings (name, value) values ('frmdisplayzerodollaraddons', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmdisplayzerodollaraddons#">);
				insert into @settings (name, value) values ('frmsubscriberbottomcontent', <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.frmsubscriberbottomcontent#">);
				insert into @settings (name, value) values ('invoiceheaderimgurl', '#replacenocase(application.paths.internalPlatform.url,'*SITECODE*','mc')#userassets/common/invoices/');
				insert into @settings (name, value) values ('renewalsuburl', '#local.thisHostname#/renewsub');

				<cfif val(local.strFilters.associatedMemberID) gt 0>
					declare @memberID int;
					declare @tblAssocMembers as TABLE (memberID int PRIMARY KEY);
					SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.associatedMemberID#">;

					INSERT INTO @tblAssocMembers
					SELECT @memberID as memberID
					FROM dbo.ams_members as m WHERE memberID = @memberID
					
					<cfif local.strFilters.linkedRecords is "all">
						UNION
							
						SELECT allchildMember.memberID
						FROM dbo.ams_members as m
						INNER JOIN dbo.ams_recordRelationships as rr ON rr.orgID = @orgID and rr.masterMemberID = m.memberID AND rr.isActive = 1
						INNER JOIN dbo.ams_members as childMember ON rr.childMemberID = childMember.memberID
						INNER JOIN dbo.ams_members as allchildMember ON allchildMember.activeMemberID = childMember.memberID
						WHERE childMember.status <> 'D'
						AND m.memberID = @memberID
					</cfif>
				</cfif>

				IF OBJECT_ID('tempdb..##subRoots') IS NOT NULL 
					DROP TABLE ##subRoots;
				IF OBJECT_ID('tempdb..##subReport') IS NOT NULL 
					DROP TABLE ##subReport;
				CREATE TABLE ##subRoots (rootSubscriberID int PRIMARY KEY); 
				CREATE TABLE ##subReport (rootSubscriberID int, subscriberID int, parentSubscriberID int, memberID int, subscriptionID int, typeID int, 
					typeName varchar(100), subscriptionName varchar(300), status varchar(1), paymentStatus varchar(1), subActivationCode varchar(1), 
					subStartDate datetime, subEndDate datetime, graceEndDate datetime, OfferExpirationDate datetime, directLinkCode varchar(8), 
					subscriberPath varchar(200), INDEX IX_tbltmpsubReport_memberID (memberID));

				INSERT INTO ##subRoots (rootSubscriberID)
				select distinct rs.rootSubscriberID
				from dbo.sub_subscribers rs 
				inner join dbo.sub_subscriptions rsub on rsub.subscriptionID = rs.subscriptionID 
					<cfif len(local.subStartFromDate) gt 0>
						AND rs.subStartDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subStartFromDate#">
					</cfif>
					<cfif len(local.subStartToDate) gt 0>
						AND rs.subStartDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subStartToDate#">
					</cfif>
					<cfif len(local.subEndFromDate) gt 0>
						AND rs.subEndDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subEndFromDate#">
					</cfif>
					<cfif len(local.subEndToDate) gt 0>
						AND rs.subEndDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.subEndToDate#">
					</cfif>
					<cfif len(local.offerEndFromDate) gt 0>
						AND rs.offerRescindDate >= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndFromDate#">
					</cfif>
					<cfif len(local.offerEndToDate) gt 0>
						AND rs.offerRescindDate <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.offerEndToDate#">
					</cfif>
					<cfif local.strFilters.fSubscription neq "0">
						AND rsub.subscriptionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.fSubscription#">
					</cfif>
					<cfif val(local.rfidList) gt 0>
						AND rs.rfid in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rfidList#" list="yes">)
					</cfif>
					<cfif local.strFilters.fSubType eq "0">
						AND rs.parentSubscriberID is null
					</cfif>
				inner join dbo.sub_statuses rst on rst.statusID = rs.statusID
					and rst.statusCode <> 'D'
					<cfif local.strFilters.fSubStatus neq "0">
						AND rst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strFilters.fSubStatus#">
					</cfif>
				inner join dbo.sub_paymentStatuses rpst on rpst.statusID = rs.paymentStatusID
					<cfif local.strFilters.fSubPaymentStatus neq "0">
						AND rpst.statusCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strFilters.fSubPaymentStatus#">
					</cfif>
				inner join dbo.sub_types rt on rt.typeID = rsub.typeID 
					and rt.siteID = @siteID
					<cfif local.strFilters.fSubType neq "0">
						AND rt.typeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.fSubType#">
					</cfif>
				<cfif listFindNoCase("Y,N", local.strFilters.fHasCardOnFile)>
					inner join sub_subscribers rootss on rootss.subscriberID = rs.rootSubscriberID
						<cfif local.strFilters.fHasCardOnFile eq 'Y'>
							AND rootss.payProfileID is not null
						<cfelseif local.strFilters.fHasCardOnFile eq 'N'>
							AND rootss.payProfileID is null
						</cfif>
				</cfif>
				<cfif len(local.subscriberIDList) gt 0>
					where rs.rootSubscriberID IN (0#local.subscriberIDList#)
				</cfif>;
				
				INSERT INTO ##subReport (rootSubscriberID, subscriberID, parentSubscriberID, memberID, subscriptionID, typeID, typeName, subscriptionName, 
					status, paymentStatus, subActivationCode, subStartDate, subEndDate, graceEndDate, OfferExpirationDate, directLinkCode, subscriberPath)
				select s.rootSubscriberID, s.subscriberID, s.parentSubscriberID, m.activeMemberID as memberID, s.subscriptionID, t.typeID, 
					t.typeName, sub.subscriptionName, st.statusCode as status, pst.statusCode as paymentStatus, o.subActivationCode, 
					CONVERT(VARCHAR,subStartDate,101) as subStartDate, CONVERT(VARCHAR,subEndDate,101) as subEndDate, 
					CONVERT(VARCHAR,graceEndDate,101) as graceEndDate, CONVERT(VARCHAR,offerRescindDate,101) as OfferExpirationDate, s.directLinkCode, s.subscriberPath
				from dbo.sub_subscribers s
				inner join ##subRoots as sr on sr.rootSubscriberID = s.rootSubscriberID AND s.parentSubscriberID is null
				inner join dbo.sub_subscriptions sub on sub.subscriptionID = s.subscriptionID
				inner join dbo.sub_activationOptions o on o.subActivationID = s.subActivationID
				inner join dbo.sub_statuses st on st.statusID = s.statusID and st.statusCode <> 'D'
				inner join dbo.sub_paymentStatuses pst on pst.statusID = s.paymentStatusID
				inner join dbo.sub_types t on t.typeID = sub.typeID and t.siteID = @siteID
				inner join dbo.ams_members as m on s.memberID = m.memberID	
				<cfif val(local.strFilters.associatedMemberID) gt 0>
					inner join @tblAssocMembers as m2 on m2.memberID = m.activeMemberID
				<cfelse>
					inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID and m2.status <> 'D'
				</cfif>
				<cfif val(local.strFilters.associatedGroupID) gt 0>
					inner join dbo.cache_members_groups mg on mg.memberID = m.activeMemberID and mg.groupid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strFilters.associatedGroupID#">
				</cfif>;

				declare @queueTypeID int, @insertingQueueStatusID int, @readyQueueStatusID int, @jobUID uniqueIdentifier = NEWID();
				EXEC platformQueue.dbo.queue_getQueueTypeID @queueType='PaperStatements', @queueTypeID=@queueTypeID OUTPUT;
				EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='insertingItems', @queueStatusID=@insertingQueueStatusID OUTPUT;
				EXEC platformQueue.dbo.queue_getStatusIDbyTypeID @queueTypeID=@queueTypeID, @queueStatus='readyToProcess', @queueStatusID=@readyQueueStatusID OUTPUT;

				-- create UID for each individual
				declare @tblMembers TABLE (memberID int PRIMARY KEY, uid uniqueIdentifier NOT NULL default newid());
				insert into @tblMembers (memberID)
				select distinct memberID
				from ##subReport;

				insert into platformQueue.dbo.tblQueueItems (itemUID, queueStatusID, dateAdded, dateUpdated)
				select tm.uid as itemUID, @insertingQueueStatusID, getdate(), getdate()
				from @tblMembers tm;

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueInteger)
				select @jobUID, @recordedByMemberID,@siteID, tm.uid as itemUID, dc.columnID, tm.memberID as dataKey, ms.rootSubscriberID as columnValueInteger
				from ##subReport ms
				inner join @tblMembers as tm on tm.memberID = ms.memberID
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.queueTypeID = @queueTypeID
					and dc.columnname = 'individualSub';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID, recordedByMemberID, siteID, itemUID, columnID, dataKey, columnValueString)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid, dc.columnID, tm.memberID, @downloadmode
				from ##subReport ms
				inner join @tblMembers as tm on tm.memberID = ms.memberID
				inner join platformQueue.dbo.tblQueueTypeDataColumns dc on dc.queueTypeID = @queueTypeID
					and dc.columnname = 'downloadmode';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueString)
				select @jobUID, @recordedByMemberID, @siteID, tm.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueString
				from platformQueue.dbo.tblQueueTypeDataColumns as dc
				inner join @settings as s on s.name in ('renewalstatementtitle','frmrenewonlineoptions','frmdisplayzerodollaraddons','invoiceheaderimgurl','renewalsuburl')
				cross join @tblMembers as tm
				where dc.queueTypeID = @queueTypeID
				and dc.columnname = 'configParam';

				insert into platformQueue.dbo.tblQueueItemData (itemGroupUID,recordedByMemberID,siteID,itemUID, columnID, dataKey, columnValueText)
				select @jobUID, @recordedByMemberID,@siteID, tm.uid as itemUID, dc.columnID, s.name as dataKey, s.value as columnValueText
				from platformQueue.dbo.tblQueueTypeDataColumns as dc
				inner join @settings as s on s.name in ('frmsubscribertopcontent','frmsubscriberbottomcontent')
				cross join @tblMembers as tm
				where dc.queueTypeID = @queueTypeID
				and dc.columnname = 'configText';

				-- resume task
				EXEC dbo.sched_resumeTask @name='Paper Statements Queue', @engine='BERLinux';

				update qi 
				set queueStatusID = @readyQueueStatusID
				from platformQueue.dbo.tblQueueItems as qi
				inner join platformQueue.dbo.tblQueueItemData as qid on qid.itemUID = qi.itemUID
					and qid.itemGroupUID = @jobUID 
					and queueStatusID = @insertingQueueStatusID;

				select count(*) as memberCount
				from @tblMembers;

				IF OBJECT_ID('tempdb..##subRoots') IS NOT NULL
					DROP TABLE ##subRoots;
				IF OBJECT_ID('tempdb..##subReport') IS NOT NULL
					DROP TABLE ##subReport;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfif local.qryQueueSubs.recordCount>
			<cfsavecontent variable="local.msg">
			<cfoutput>
				<div class="mb-2 p-2"><h5>Statements Have Been Scheduled</h5> 
					<p>The subscription statements have been scheduled for creation and will be generated shortly. You will be sent an e-mail with the statements attached.</p>
					<p>Please wait until you receive the emailed statements before contacting MemberCentral with any questions.</p> 
				</div>
			</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.msg">
			<cfoutput>
				<div class="mb-2 p-2"><h5>Error</h5> 
					<p>No record found. Please try again.</p>
				</div>
			</cfoutput>
			</cfsavecontent>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
				</script>
				#local.msg#
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="downloadPaperStatements" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>

		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.subscriberIDList = arguments.event.getValue('fChkedSubs','')>
		<cfset local.mc_siteinfo = arguments.event.getValue('mc_siteinfo')>

		<cfif application.MCEnvironment eq "production">
			<cfset local.thisHostname = local.mc_siteinfo.scheme & "://" & local.mc_siteinfo.mainhostname>
		<cfelse>
			<cfset local.thisHostname = local.mc_siteinfo.scheme & "://" & application.objPlatform.getCurrentHostname()>
		</cfif>

		<cfset local.renewalStatementTitle = arguments.event.getValue('renewalStatementTitle','')>
		<cfset local.frmSubscriberTopContent = arguments.event.getValue('frmSubscriberTopContent','')>
		<cfset local.frmRenewOnlineOptions = arguments.event.getValue('frmRenewOnlineOptions',0)>
		<cfset local.frmDisplayZeroDollarAddOns = arguments.event.getValue('frmDisplayZeroDollarAddOns',0)>
		<cfset local.frmSubscriberBottomContent = arguments.event.getValue('frmSubscriberBottomContent','')>
		<cfset local.invoiceheaderimgurl = "#replacenocase(application.paths.internalPlatform.url,'*SITECODE*','mc')#userassets/common/invoices/">	
		<cfset local.renewalsuburl = "#local.thisHostname#/renewsub">			
		
		<cfstoredproc procedure="sub_generatePaperStatement" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.siteid#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteinfo.orgid#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.renewalstatementtitle#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.frmsubscribertopcontent#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.frmrenewonlineoptions#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.frmdisplayzerodollaraddons#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.frmsubscriberbottomcontent#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.invoiceheaderimgurl#">
			<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.renewalsuburl#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.subscriberIDList#">
			<cfprocresult name="local.qrySubs">
		</cfstoredproc>

		<cfif isXML(local.qrySubs.xmlMembers)>
			<cfset local.paramsArr = arrayNew(1)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "string">
			<cfset local.tempStr.NAME = "invoiceheaderimgurl">
			<cfset local.tempStr.VALUE = local.invoiceheaderimgurl>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "string">
			<cfset local.tempStr.NAME = "renewalstatementtitle">
			<cfset local.tempStr.VALUE = local.renewalStatementTitle>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "string">
			<cfset local.tempStr.NAME = "renewalsuburl">
			<cfset local.tempStr.VALUE = local.renewalsuburl>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "boolean">
			<cfset local.tempStr.NAME = "frmrenewonlineoptions">
			<cfset local.tempStr.VALUE = local.frmRenewOnlineOptions>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "string">
			<cfset local.tempStr.NAME = "frmsubscribertopcontent">
			<cfset local.tempStr.VALUE = local.frmSubscriberTopContent>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "string">
			<cfset local.tempStr.NAME = "frmsubscriberbottomcontent">
			<cfset local.tempStr.VALUE = local.frmSubscriberBottomContent>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "boolean">
			<cfset local.tempStr.NAME = "showTax">
			<cfset local.tempStr.VALUE = "0">
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix="#local.mc_siteinfo.sitecode#_ps")>
			
			<cfif len(local.renewalsuburl) and local.frmRenewOnlineOptions>
				<cfset local.xmlData = XmlParse(local.qrySubs.xmlMembers)>
				<cfset local.accountNodes = XmlSearch(local.xmlData, "//account")>
				<cfif arrayLen(local.accountNodes)>
					<cftry>
						<cfset local.platformMergeTagLibrary = createObject("component","model.system.common.platformMergeTagLibrary")>
						<cfif structKeyExists(local.accountNodes[1].xmlAttributes,"renewalcode") AND len(local.accountNodes[1].XmlAttributes.renewalcode)>
							<cfset local.strQRCode = local.platformMergeTagLibrary.qrcode(text = local.renewalsuburl & "/" & local.accountNodes[1].XmlAttributes.renewalcode, width = 85, height = 85, format = 'struct')>
							<cfset structInsert(local.strQRCode, 'imagedir', local.strFolder.folderPath)>
							<cfset structInsert(local.strQRCode, 'imagefn', '#createUUID()#.png')>
							<cfhttp method="GET" url="#local.strQRCode.dataStruct.qrcode[1].url#" path="#local.strQRCode.imagedir#" file="#local.strQRCode.imagefn#" timeout="60" throwonerror="true" getasbinary="true">
							<cfset local.accountNodes[1].XmlAttributes["renewalsubqrcodeimgurl"] = "file://#local.strQRCode.imagedir#/#local.strQRCode.imagefn#">
						</cfif>
						<cfset local.qrcodeGenerated = true>
						<cfset local.qrySubs.xmlMembers = ToString(local.xmlData)>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
						<cfset local.qrcodeGenerated = false>
					</cfcatch>
					</cftry>
				</cfif>
			</cfif>
			
			<cfset local.birtReportXML =  local.qrySubs.xmlMembers />

			<cfset local.reportFileName = "#local.qrySubs.lastName#_#local.qrySubs.firstName#_#local.qrySubs.memberNumber#">
			<cfset local.reportOutputFileName = local.reportFileName & '.pdf'>
			<cfset local.reportXMLFilePath = local.strFolder.folderpath & '/' & local.reportFileName & '.xml'>
			<cfset local.reportParamsFilePath = local.strFolder.folderpath & '/' & local.reportFileName & '-params.json'>
			<cfset local.reportOutputFilePath = local.strFolder.folderpath & '/' & local.reportOutputFileName>

			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.NEEDSEVALUATION = false>
			<cfset local.tempStr.DATATYPE = "filepath">
			<cfset local.tempStr.NAME = "xmlFilePath">
			<cfset local.tempStr.VALUE = local.reportXMLFilePath>
			<cfset arrayAppend(local.paramsArr, local.tempStr)>

			<cffile action="write" output="#local.birtReportXML#" file="#local.reportXMLFilePath#" nameconflict="overwrite">
			<cffile action="write" output="#serializeJSON(local.paramsArr)#" file="#local.reportParamsFilePath#" nameconflict="overwrite">

			<cfset local.PDFresult = application.objCommon.runBirtReport(
				reportPath="models/reports/subscriptions/paperstatements.rptdesign",
				outputFilePath= local.reportOutputFilePath,
				reportParams=local.paramsArr,
				renderFormat='pdf')>

			<cfset local.stDownloadURL = "">
			<cfif local.PDFresult.success>
				<cfset local.strReturn.pdfDownloadPath = local.reportOutputFilePath>
				<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath=local.reportOutputFilePath, displayName=local.reportOutputFileName, deleteSourceFile=0)>
 				<cfset local.data = "<script language='Javascript'>top.doc_download('#local.stDownloadURL#');top.MCModalUtils.hideModal();</script>">
			<cfelse>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalBody').html('<div style="margin:10px;"><div class="alert alert-warning">No record found. Please try again.</div></div>');
						
						</script>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Export subscription structure --->
	<cffunction name="exportSubStructureCSV" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "SubscriptionSetup.csv">

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="sub_exportSetup">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#">
		</cfstoredproc>
		
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="exportSubStructureZIP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.zipFileName = "SubscriptionStructure.zip">
		<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="Qualify")>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="sub_exportSubscriptionStructure">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.strFolder.folderPathUNC#\">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
		</cfstoredproc>

		<!--- zip the bcp files --->
		<cfzip action="zip" file="#local.strFolder.folderPath#/#local.zipFileName#" source="#local.strFolder.folderPath#" filter="*.bcp" storePath="no" />

		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.zipFileName#", displayName=local.zipFileName, forceDownload=1, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.list#&tab=ex" addtoken="no">
		</cfif>
	</cffunction>

	<cffunction name="prepareSubStructureImport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rs = structNew()>
		<cfset local.rs.success = true>
		<cfset local.rs.errorCode = 999>
		<cfset local.rs.errorInfo = structNew()>

		<cfsetting requesttimeout="500">

		<!--- Attempt upload of zip --->
		<cftry>
			<cfset local.strImportFile = {}>
			<cfset local.strImportFile.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>

			<cffile action="upload" filefield="importfilename" destination="#local.strImportFile.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">

			<cfset local.strImportFile.uploadFilenameWithExt = local.uploadResult.ServerFile>
			<cfset local.strImportFile.uploadFilenameWithoutExt = local.uploadResult.ServerFileName>
			<cfset local.strImportFile.uploadFilenameExt = local.uploadResult.ServerFileExt>

			<cfif local.strImportFile.uploadFilenameExt neq "zip">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#">
				<cfset local.errMsg = "Uploaded file was not in the proper format (#local.strImportFile.uploadFilenameExt#).">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 1>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,local.errMsg)>
			<cfelseif "#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" neq "#local.strImportFile.strFolder.folderPath#/SubscriptionStructure.zip">
				<cffile action="rename" source="#local.strImportFile.strFolder.folderPath#/#local.strImportFile.uploadFilenameWithExt#" destination="#local.strImportFile.strFolder.folderPath#/SubscriptionStructure.zip">
			</cfif> 
		<cfcatch type="Any">
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem uploading the selected file. A valid backup file is required for your import.")>
		</cfcatch>
		</cftry>

		<!--- check zip file and extract --->
		<cfif local.rs.success>
			<cftry>
				<cfzip action="list" file="#local.strImportFile.strFolder.folderPath#/SubscriptionStructure.zip" name="local.qryFiles">
				<cfquery name="local.qryFilesCheck" dbtype="query">
					select count(*) as theCount
					from [local].qryFiles
					where name = 'sync_sub_frequencies.bcp' or name = 'sync_sub_types.bcp'
						 or name = 'sync_sub_rateSchedules.bcp' or name = 'sync_sub_rates.bcp'
						 or name = 'sync_sub_rateFrequenciesMerchantProfiles.bcp' or name = 'sync_sub_subscriptions.bcp'
						 or name = 'sync_sub_sets.bcp' or name = 'sync_sub_subscriptionSets.bcp'
						 or name = 'sync_sub_addons.bcp' or name = 'sync_sub_rateFrequencies.bcp'
						 or name = 'sync_sub_rateGroups.bcp' or name = 'sync_sub_supporting.bcp'
				</cfquery>
				<cfif local.qryFiles.recordcount neq 12>
					<cfthrow message="The backup file contains #local.qryFiles.recordcount# files when it should contain 12.">
				<cfelseif local.qryFilesCheck.theCount neq 12>
					<cfthrow message="One or more required files in the backup file is missing.">
				</cfif>

				<cfzip file="#local.strImportFile.strFolder.folderPath#/SubscriptionStructure.zip" action="unzip" filter="*.bcp" storepath="no" destination="#local.strImportFile.strFolder.folderPath#">
			<cfcatch type="Any">
				<cffile action="DELETE" file="#local.strImportFile.strFolder.folderPath#/SubscriptionStructure.zip">
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 6>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"#cfcatch.message# Try the upload again or contact us for assistance.")>
			</cfcatch>
			</cftry>
		</cfif>

  		<!--- prepare import --->
  		<cfif local.rs.success>
			<!--- parse, validate, and compare xml in another thread --->
			<cfset local.threadID = createUUID()>
			<cfset local.threadVars = { threadID=local.threadID, threadName="Subscription Structure Import #local.threadID#", strFolder=local.strImportFile.strFolder }>
			<cfset local.paramStruct = { threadID=local.threadID, orgID=arguments.event.getValue('mc_siteinfo.orgid'), siteID=arguments.event.getValue('mc_siteinfo.siteID') }>
			
			<cfset local.SubscriptionImportStruct = application.mcCacheManager.sessionGetValue(keyname='SubscriptionImportStruct', defaultValue={})>
			<cfset local.SubscriptionImportStruct[local.threadID] = local.strImportFile.strFolder>
			<cfset application.mcCacheManager.sessionSetValue(keyname='SubscriptionImportStruct', value=local.SubscriptionImportStruct)>

			<cfthread action="run" name="#local.threadVars.threadName#" threadid="#local.threadVars.threadID#" strFolder="#local.threadVars.strFolder#" paramStruct="#local.paramStruct#">
				<cftry>
					<cfset doPrepareSubStructureImport(paramStruct=attributes.paramStruct, strFolder=attributes.strFolder)>
				<cfcatch type="any">
					<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=attributes)>
				</cfcatch>
				</cftry>
			</cfthread>

			<!--- Echo message with local.threadID --->
			<cfsavecontent variable="local.impExData">
				<cfoutput>
				<div id="loadingGif" class="row mt-2">
					<div class="col-auto">
						<i class="fa-light fa-circle-notch fa-spin fa-4x"></i> 
					</div>
					<div class="col">
						<div class="pb-3">We're analyzing your import file.</div>
						<div class="text-dark">Hang tight -- this could take up to a few minutes to compare the data.<br/>Stay on this page to see the results of the comparison.</div>
						<div id="loadingStatement" class="pt-3"></div>
					</div>
				</div>
				<div id="importCompareReport"></div>
				</cfoutput>
			</cfsavecontent>

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(function() {
						isCompareReady('#local.threadID#');
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">
		<cfelse>
			<cfsavecontent variable="local.impExData">
				<cfoutput>
					<div class="alert alert-danger">#local.rs.errorInfo[local.rs.errorCode]#</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#this.link.list#&tab=ex';">Try Again</button> 
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfset local.data = list(event=arguments.event, impExData=local.impExData)>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="doPrepareSubStructureImport" access="private" output="false" returntype="void">
		<cfargument name="paramStruct" type="struct" required="yes">
		<cfargument name="strFolder" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.rs = { success=true, errorCode=999, errorInfo=StructNew() } >

		<cftry>
			<cfquery name="local.qryPrepareImport" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @siteID int, @pathToImport varchar(400), @importResult xml, @errCount int;
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.paramStruct.siteID#">;
					SET @pathToImport = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFolder.folderPathUNC#\">;

					EXEC dbo.sub_prepareSubscriptionStructureImport @siteID=@siteID, @pathToImport=@pathToImport, @importResult=@importResult OUTPUT;

					set @errCount = @importResult.value('count(/import/errors/error)','int');

					SELECT @importResult as importResult, @errCount as errCount;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset local.rs.importResultXML = xmlparse(local.qryPrepareImport.importResult)>
			<cfset local.rs.numFatalErrors = local.qryPrepareImport.errCount>

			<cfif local.rs.numFatalErrors gt 0>
				<cfset local.rs.success = false>
				<cfset local.rs.errorCode = 105>
				<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,'')>
			</cfif>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.rs.success = false>
			<cfset local.rs.errorCode = 1>
			<cfset StructInsert(local.rs.errorInfo,local.rs.errorCode,"There was a problem preparing the import.")>
		</cfcatch>
		</cftry>

		<cfset local.importCompareReport = showSubsStructureImportCompareResults(siteID=arguments.paramStruct.siteID, orgID=arguments.paramStruct.orgID, threadID=arguments.paramStruct.threadID, strResult=local.rs, doAgainURL="#this.link.list#&tab=ex")>

		<cffile action="write" file="#arguments.strFolder.folderPath#/StructureImportReport.html" output="#application.objcommon.minText(local.importCompareReport)#">
	</cffunction>

	<cffunction name="showSubsStructureImportCompareResults" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strResult" type="struct" required="yes">
		<cfargument name="doAgainURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.hasChanges = false>
		
		<cfif arguments.strResult.success>
			<cfset local.strImportResult = structNew()>
			<cfset local.strImportResult.arrNewSubFreqs = XMLSearch(arguments.strResult.importResultXML,"/import/newsubfreqs/freq")>
			<cfset local.strImportResult.arrUpdateSubFreqs = XMLSearch(arguments.strResult.importResultXML,"/import/updatesubfreqs/freq")>
			<cfset local.strImportResult.arrRemoveSubFreqs = XMLSearch(arguments.strResult.importResultXML,"/import/removesubfreqs/freq")>
			<cfset local.strImportResult.arrNewSubTypes = XMLSearch(arguments.strResult.importResultXML,"/import/newsubtypes/st")>
			<cfset local.strImportResult.arrUpdateSubTypes = XMLSearch(arguments.strResult.importResultXML,"/import/updatesubtypes/st")>
			<cfset local.strImportResult.arrRemoveSubTypes = XMLSearch(arguments.strResult.importResultXML,"/import/removesubtypes/st")>
			<cfset local.strImportResult.arrNewSubRateScheds = XMLSearch(arguments.strResult.importResultXML,"/import/newrateschedules/rs")>
			<cfset local.strImportResult.arrUpdateSubRateScheds = XMLSearch(arguments.strResult.importResultXML,"/import/updaterateschedules/rs")>
			<cfset local.strImportResult.arrNewSets = XMLSearch(arguments.strResult.importResultXML,"/import/newsets/set")>
			<cfset local.strImportResult.arrUpdateSets = XMLSearch(arguments.strResult.importResultXML,"/import/updatesets/set")>
			<cfset local.strImportResult.arrRemoveSubRateScheds = XMLSearch(arguments.strResult.importResultXML,"/import/removerateschedules/rs")>
			<cfset local.strImportResult.arrRemoveSubRates = XMLSearch(arguments.strResult.importResultXML,"/import/removerates/rate")>
			<cfset local.strImportResult.arrRemoveSubRateFreqs = XMLSearch(arguments.strResult.importResultXML,"/import/removeratefreqs/ratefreq")>
			<cfset local.strImportResult.arrRemoveSubRateGroups = XMLSearch(arguments.strResult.importResultXML,"/import/removerategroups/rategrp")>
			<cfset local.strImportResult.arrRemoveSubRateFreqMerchantProfiles = XMLSearch(arguments.strResult.importResultXML,"/import/removeratefreqmp/rfmp")>
			<cfset local.strImportResult.arrRemoveSubs = XMLSearch(arguments.strResult.importResultXML,"/import/removesubs/sub")>
			<cfset local.strImportResult.arrRemoveSets = XMLSearch(arguments.strResult.importResultXML,"/import/removesets/set")>
			<cfset local.strImportResult.arrRemoveSubSets = XMLSearch(arguments.strResult.importResultXML,"/import/removesubsets/subset")>
			<cfset local.strImportResult.arrRemoveSubAddons = XMLSearch(arguments.strResult.importResultXML,"/import/removesubaddons/subaddon")>

			<cfloop collection="#local.strImportResult#" item="local.thisArr">
				<cfif arrayLen(local.strImportResult[local.thisArr])>
					<cfset local.hasChanges = true>
					<cfbreak>
				</cfif>
			</cfloop>

			<cfif local.hasChanges>
				<cfset local.importReport = generateSubsStructureImportResultsReport(siteID=arguments.siteID, orgID=arguments.orgID, threadID=arguments.threadID, strImportResult=local.strImportResult)>
			</cfif>

		<!--- import errors --->
		<cfelseif arguments.strResult.errorCode eq 105>
			<cfset local.arrErrors = XMLSearch(arguments.strResult.importResultXML,"/import/errors/error")>
			<cfset local.errorReport = generateSubsStructureImportErrorReport(siteID=arguments.siteID, arrErrors=local.arrErrors)>
		</cfif>

		<!--- If fatal errors --->
		<cfif NOT arguments.strResult.success>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Subscription Structure Import Issue Report
								</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger">
									<cfif arguments.strResult.errorCode eq 105>
										<cfif len(local.errorReport)>
											<div>#local.errorReport#</div>
										<cfelse>
											<div class="font-weight-bold">An undetermined error occurred during the import.</div>
										</cfif>
									<cfelse>
										<div class="font-weight-bold">The import was stopped and requires your attention.</div>
										<div class="mt-2">#arguments.strResult.errorInfo[arguments.strResult.errorCode]#</div>
									</cfif>
									<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Try upload again</button>
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success but no changes needed --->
		<cfelseif arguments.strResult.success and not local.hasChanges>
			<cfset cancelSubsStructureImport(siteID=arguments.siteID)>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">
									Subscription Structure Import No Action Needed
								</div>
							</div>
							<div class="card-body pb-3">
								<div>There were no changes to process.</div>
								<button class="btn btn-sm btn-secondary mt-3" name="btnDoOver" type="button" onclick="self.location.href='#arguments.doAgainURL#';">Upload another file</button>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>

		<!--- if success with changes to confirm --->
		<cfelseif arguments.strResult.success and local.hasChanges>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<cfif len(local.importReport)>
					<div>#local.importReport#</div>
					<br/>
				</cfif>
				<br/>
				</cfoutput>
			</cfsavecontent>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<div class="row my-2">
					<div class="col">
						<div class="card card-box mb-1">
							<div class="card-header py-1 bg-light">
								<div class="card-header--title font-weight-bold font-size-md">Subscription Structure Import Issue Report</div>
							</div>
							<div class="card-body pb-3">
								<div class="alert alert-danger font-weight-bold">
									An undetermined error occurred during the import.
								</div>
							</div>
						</div>
					</div>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateSubsStructureImportResultsReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="orgID" type="numeric" required="yes">
		<cfargument name="threadID" type="string" required="yes">
		<cfargument name="strImportResult" type="struct" required="yes">

		<cfset var local = structNew()>

		<cfif arrayLen(arguments.strImportResult.arrUpdateSubFreqs)>
			<cfquery name="local.qryImportFileUpdateFreqs" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select [uid], frequencyName, frequency, frequencyShortName, rateRequired, hasInstallments, 
					monthlyInterval, isSystemRate, [status]
				from dbo.sync_sub_frequencies
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateFreqs" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select frequencyID, frequencyName, frequency, frequencyShortName, [uid], rateRequired, hasInstallments, 
					monthlyInterval, isSystemRate, [status]
				from dbo.sub_frequencies
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and status <> 'D'
				and uid in (#listQualify(valueList(local.qryImportFileUpdateFreqs.uid), "'")#)
				order by frequencyName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfif arrayLen(arguments.strImportResult.arrUpdateSubRateScheds)>
			<cfquery name="local.qryImportFileUpdateRateSchedules" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select [uid], scheduleName, [status]
				from dbo.sync_sub_rateSchedules
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateRateSchedules" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select scheduleID, [uid], scheduleName, [status]
				from dbo.sub_rateSchedules
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and status <> 'D'
				and uid in (#listQualify(valueList(local.qryImportFileUpdateRateSchedules.uid), "'")#)
				order by scheduleName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileNewRates" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select r.[uid], r.rateName, rs.[uid] as scheduleUID
				from dbo.sync_sub_rates as r
				inner join dbo.sync_sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID
				where r.siteID = @siteID
				and r.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateRates" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select sync_sr.[uid], rs.[uid] as scheduleUID, rs.scheduleName, sync_sr.rateStartDate, sync_sr.rateEndDate, 
					sync_rateStartDateAFID.useID as rateStartDateAFID, sync_rateStartDateAFID.itemType as rateStartDateAFName, 
					sync_sr.rateAFStartDate, sync_sr.rateAFEndDate, sync_sr.termStartDate, sync_sr.termEndDate, 
					sync_termStartDateAFID.useID as termStartDateAFID, sync_termStartDateAFID.itemType as termStartDateAFName, 
					sync_termEndDateAFID.useID as termEndDateAFID, sync_termEndDateAFID.itemType as termEndDateAFName, 
					sync_sr.termAFStartDate, sync_sr.termAFEndDate, sync_sr.graceEndDate, sync_graceAFID.useID as graceAFID, 
					sync_graceAFID.itemType as graceAFName, sync_sr.rateName, sync_rateEndDateAFID.useID as rateEndDateAFID, 
					sync_rateEndDateAFID.itemType as rateEndDateAFName, sync_sr.rateAdvanceOnTermEnd, 
					sync_sr.isRenewalRate, sync_sr.forceUpfront, sync_sr.reportCode, sync_GLAccountID.useID as GLAccountID, 
					gl.thePathExpanded as GLAccountPath, sync_sr.frontEndAllowChangePrice, 
					sync_linkedNonRenewalRateID.[uid] as linkedNonRenewalRateUID, sync_linkedNonRenewalRateID.rateName as linkedNonRenewalRateName,
					sync_fallbackRenewalRateID.[uid] as fallbackRenewalRateUID, sync_fallbackRenewalRateID.rateName as fallbackRenewalRateName, 
					sync_sr.keepChangedPriceOnRenewal, sync_sr.frontEndChangePriceMin, sync_sr.frontEndChangePriceMax, 
					sync_sr.recogStartDate, sync_sr.recogEndDate, sync_recogStartDateAFID.useID as recogStartDateAFID, 
					sync_recogStartDateAFID.itemType as recogStartDateAFName, sync_recogEndDateAFID.useID as recogEndDateAFID, 
					sync_recogEndDateAFID.itemType as recogEndDateAFName, sync_sr.recogAFStartDate, sync_sr.recogAFEndDate, 
					sync_sr.rateOrder, sync_sr.[status]
				from dataTransfer.dbo.sync_sub_rates as sync_sr
				inner join dbo.sync_sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = sync_sr.scheduleID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_rateStartDateAFID on sync_rateStartDateAFID.siteID = @siteID 
					and sync_rateStartDateAFID.cat = 'af' 
					and sync_rateStartDateAFID.itemID = sync_sr.rateStartDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_termStartDateAFID on sync_termStartDateAFID.siteID = @siteID 
					and sync_termStartDateAFID.cat = 'af' 
					and sync_termStartDateAFID.itemID = sync_sr.termStartDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_termEndDateAFID on sync_termEndDateAFID.siteID = @siteID 
					and sync_termEndDateAFID.cat = 'af' 
					and sync_termEndDateAFID.itemID = sync_sr.termEndDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_graceAFID on sync_graceAFID.siteID = @siteID 
					and sync_graceAFID.cat = 'af' 
					and sync_graceAFID.itemID = sync_sr.graceAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_rateEndDateAFID on sync_rateEndDateAFID.siteID = @siteID 
					and sync_rateEndDateAFID.cat = 'af' 
					and sync_rateEndDateAFID.itemID = sync_sr.rateEndDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_recogStartDateAFID on sync_recogStartDateAFID.siteID = @siteID 
					and sync_recogStartDateAFID.cat = 'af' 
					and sync_recogStartDateAFID.itemID = sync_sr.recogStartDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_recogEndDateAFID on sync_recogEndDateAFID.siteID = @siteID 
					and sync_recogEndDateAFID.cat = 'af' 
					and sync_recogEndDateAFID.itemID = sync_sr.recogEndDateAFID
				left outer join datatransfer.dbo.sync_sub_supporting as sync_GLAccountID 
					inner join memberCentral.dbo.fn_getRecursiveGLAccounts(@orgID) as gl on gl.GLAccountID = sync_GLAccountID.useID
					on sync_GLAccountID.siteID = @siteID 
						and sync_GLAccountID.cat = 'gl' 
						and sync_GLAccountID.itemID = sync_sr.GLAccountID
				left outer join dataTransfer.dbo.sync_sub_rates as sync_linkedNonRenewalRateID on sync_linkedNonRenewalRateID.siteID = @siteID 
					and sync_linkedNonRenewalRateID.rateID = sync_sr.linkedNonRenewalRateID
				left outer join dataTransfer.dbo.sync_sub_rates as sync_fallbackRenewalRateID on sync_fallbackRenewalRateID.siteID = @siteID 
					and sync_fallbackRenewalRateID.rateID = sync_sr.fallbackRenewalRateID
				where sync_sr.siteID = @siteID
				and sync_sr.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryImportFileUpdateRates.recordCount>
				<cfquery name="local.qrySiteUpdateRates" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
						@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select r.[uid], rs.[uid] as scheduleUID, rs.scheduleName, r.rateStartDate, r.rateEndDate, 
						r.rateStartDateAFID, af_rateStartDateAFID.afName as rateStartDateAFName, 
						r.rateAFStartDate, r.rateAFEndDate, r.termStartDate, r.termEndDate, 
						r.termStartDateAFID, af_termStartDateAFID.afName as termStartDateAFName, 
						r.termEndDateAFID, af_termEndDateAFID.afName as termEndDateAFName, 
						r.termAFStartDate, r.termAFEndDate, r.graceEndDate, r.graceAFID, 
						af_graceAFID.afName as graceAFName, r.rateName, r.rateEndDateAFID, 
						af_rateEndDateAFID.afName as rateEndDateAFName, r.rateAdvanceOnTermEnd, 
						r.isRenewalRate, r.forceUpfront, r.reportCode, r.GLAccountID, 
						gl.thePathExpanded as GLAccountPath, r.frontEndAllowChangePrice, 
						linkedNonRenewalRateID.[uid] as linkedNonRenewalRateUID, linkedNonRenewalRateID.rateName as linkedNonRenewalRateName,
						fallbackRenewalRateID.[uid] as fallbackRenewalRateUID, fallbackRenewalRateID.rateName as fallbackRenewalRateName, 
						r.keepChangedPriceOnRenewal, r.frontEndChangePriceMin, r.frontEndChangePriceMax, 
						r.recogStartDate, r.recogEndDate, r.recogStartDateAFID, af_recogStartDateAFID.afName as recogStartDateAFName, 
						r.recogEndDateAFID, af_recogEndDateAFID.afName as recogEndDateAFName, r.recogAFStartDate, r.recogAFEndDate, 
						r.rateOrder, r.[status]
					from dbo.sub_rates as r
					inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID
					left outer join dbo.af_advanceFormulas as af_rateStartDateAFID on af_rateStartDateAFID.siteID = @siteID 
						and af_rateStartDateAFID.AFID = r.rateStartDateAFID
					left outer join dbo.af_advanceFormulas as af_termStartDateAFID on af_termStartDateAFID.siteID = @siteID 
						and af_termStartDateAFID.AFID = r.termStartDateAFID
					left outer join dbo.af_advanceFormulas as af_termEndDateAFID on af_termEndDateAFID.siteID = @siteID 
						and af_termEndDateAFID.AFID = r.termEndDateAFID
					left outer join dbo.af_advanceFormulas as af_graceAFID on af_graceAFID.siteID = @siteID 
						and af_graceAFID.AFID = r.graceAFID
					left outer join dbo.af_advanceFormulas as af_rateEndDateAFID on af_rateEndDateAFID.siteID = @siteID 
						and af_rateEndDateAFID.AFID = r.rateEndDateAFID
					left outer join dbo.af_advanceFormulas as af_recogStartDateAFID on af_recogStartDateAFID.siteID = @siteID 
						and af_recogStartDateAFID.AFID = r.recogStartDateAFID
					left outer join dbo.af_advanceFormulas as af_recogEndDateAFID on af_recogEndDateAFID.siteID = @siteID 
						and af_recogEndDateAFID.AFID = r.recogEndDateAFID
					left outer join dbo.fn_getRecursiveGLAccounts(@orgID) as gl on gl.GLAccountID = r.GLAccountID
					left outer join dbo.sub_rates as linkedNonRenewalRateID on linkedNonRenewalRateID.rateID = r.linkedNonRenewalRateID
					left outer join dbo.sub_rates as fallbackRenewalRateID on fallbackRenewalRateID.rateID = r.fallbackRenewalRateID
					where r.[uid] in (#listQualify(valueList(local.qryImportFileUpdateRates.uid), "'")#);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>

			<cfquery name="local.qryImportFileNewRateFreqs" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select f.frequencyName, r.[uid] as rateUID
				from dbo.sync_sub_rateFrequencies as rf
				inner join dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rf.rateID
				inner join dbo.sync_sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID
				where rf.siteID = @siteID
				and rf.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateRateFreqs" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select distinct rf.useID as rfid, f.frequencyName, r.[uid] as rateUID, rf.rateAmt, 
					rf.numInstallments, rf.allowFrontEnd, rf.[status]
				from dbo.sync_sub_rateFrequencies as rf
				inner join dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rf.rateID
				inner join dbo.sync_sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID
				where rf.siteID = @siteID
				and rf.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryImportFileUpdateRateFreqs.recordcount>
				<cfquery name="local.qrySiteUpdateRateFreqs" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

					select distinct rf.rfid, f.frequencyName, r.rateName, r.[uid] as rateUID, rf.rateAmt, 
						rf.numInstallments, rf.allowFrontEnd, rf.[status]
					from dbo.sub_rateFrequencies as rf
					inner join dbo.sub_rates as r on r.rateID = rf.rateID
					inner join dbo.sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID
					where rf.rfid in (#valueList(local.qryImportFileUpdateRateFreqs.rfid)#);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery name="local.qryImportFileNewRateFreqPaymentProfiles" datasource="#application.dsn.datatransfer.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

					select distinct rf.useID as rfid, f.frequencyName, r.[uid] as rateUID, mp.profileName
					from dbo.sync_sub_rateFrequenciesMerchantProfiles as rfmp
					inner join dbo.sync_sub_rateFrequencies as rf on rf.siteID = @siteID and rf.rfid = rfmp.rfid
					inner join dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rf.rateID
					inner join dbo.sync_sub_frequencies as f on f.siteID = @siteID and f.frequencyID = rf.frequencyID
					inner join dbo.sync_sub_supporting as ss on ss.siteID = @siteID and ss.cat = 'mp' and ss.itemID = rfmp.profileID
					inner join memberCentral.dbo.mp_profiles as mp on mp.siteID = @siteID and mp.profileID = ss.useID
					where rfmp.siteID = @siteID
					and rfmp.finalAction = 'A';

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>

			<cfquery name="local.qryImportFileNewRateGroups" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select rg.groupPathExpanded, r.[uid] as rateUID
				from dbo.sync_sub_rateGroups as rg
				inner join dbo.sync_sub_rates as r on r.siteID = @siteID and r.rateID = rg.rateID
				where rg.siteID = @siteID
				and rg.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfif arrayLen(arguments.strImportResult.arrUpdateSubTypes)>
			<cfquery name="local.qryImportFileUpdateSubTypes" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select typeName, [status], [uid], typeCode, frontEndContent, frontEndCompletedContent, feEmailNotification
				from dbo.sync_sub_types
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateSubTypes" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select t.typeID, t.typeName, t.[status], t.[uid], t.typeCode, frontEndContent.rawContent as frontEndContent, 
					frontEndCompletedContent.rawContent as frontEndCompletedContent, t.feEmailNotification
				from dbo.sub_types as t
				cross apply dbo.fn_getContent(t.frontEndContentID,1) as frontEndContent
				cross apply dbo.fn_getContent(t.frontEndCompletedContentID,1) as frontEndCompletedContent
				where t.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and t.status <> 'D'
				and t.uid in (#listQualify(valueList(local.qryImportFileUpdateSubTypes.uid), "'")#)
				order by t.typeName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileNewSubs" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select s.[uid], s.subscriptionName, st.[uid] as typeUID
				from dbo.sync_sub_subscriptions as s
				inner join dbo.sync_sub_types as st on st.siteID = @siteID and st.typeID = s.typeID
				where s.siteID = @siteID
				and s.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateSubs" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
					@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

				select s.[uid], st.[uid] as typeUID, st.typeName, s.subscriptionName, rs.[uid] as scheduleUID, rs.scheduleName, 
					s.autoExpire, s.soldSeparately, s.rateTermDateFlag, s.paymentOrder, s.reportCode, s.frontEndContent, s.frontEndCompletedContent, 
					s.frontEndParentSubContent, s.[status], gl.GLAccountID, gl.thePathExpanded as GLAccountPath,
					sao.subActivationCode, sao.subActivationName, sao_alt.subActivationCode as subAlternateActivationCode, 
					sao_alt.subActivationName as subAlternateActivationName, s.allowRateGLAccountOverride
				from dbo.sync_sub_subscriptions as s
				inner join dbo.sync_sub_types as st on st.siteID = @siteID and st.typeID = s.typeID
				inner join dbo.sync_sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = s.scheduleID
				left outer join dbo.sync_sub_supporting as ss 
					inner join memberCentral.dbo.fn_getRecursiveGLAccounts(@orgID) as gl on gl.GLAccountID = ss.useID
					on ss.siteID = @siteID and ss.cat = 'gl' and ss.itemID = s.GLAccountID
				inner join memberCentral.dbo.sub_activationOptions as sao on sao.subActivationID = s.subActivationID
				inner join memberCentral.dbo.sub_activationOptions as sao_alt on sao_alt.subActivationID = s.subAlternateActivationID
				where s.siteID = @siteID
				and s.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryImportFileUpdateSubs.recordCount>
				<cfquery name="local.qrySiteUpdateSubs" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">,
						@orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;

					select s.[uid], st.[uid] as typeUID, st.typeName, s.subscriptionName, rs.[uid] as scheduleUID, rs.scheduleName, 
						s.autoExpire, s.soldSeparately, s.rateTermDateFlag, s.paymentOrder, s.reportCode, 
						frontEndContent.rawContent as frontEndContent, frontEndCompletedContent.rawContent as frontEndCompletedContent,
						frontEndParentSubContent.rawContent as frontEndParentSubContent, s.[status], gl.GLAccountID, gl.thePathExpanded as GLAccountPath, 
						sao.subActivationCode, sao.subActivationName, sao_alt.subActivationCode as subAlternateActivationCode, 
						sao_alt.subActivationName as subAlternateActivationName, s.allowRateGLAccountOverride
					from dbo.sub_subscriptions as s
					inner join dbo.sub_types as st on st.siteID = @siteID and st.typeID = s.typeID
					inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = s.scheduleID
					left outer join dbo.fn_getRecursiveGLAccounts(@orgID) as gl on gl.GLAccountID = s.GLAccountID
					inner join dbo.sub_activationOptions as sao on sao.subActivationID = s.subActivationID
					inner join dbo.sub_activationOptions as sao_alt on sao_alt.subActivationID = s.subAlternateActivationID
					cross apply dbo.fn_getContent(s.frontEndContentID,1) as frontEndContent
					cross apply dbo.fn_getContent(s.frontEndCompletedContentID,1) as frontEndCompletedContent
					cross apply dbo.fn_getContent(s.frontEndParentSubContentID,1) as frontEndParentSubContent
					where s.status <> 'D'
					and s.[uid] in (#listQualify(valueList(local.qryImportFileUpdateSubs.uid), "'")#);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.strRateTermDateFlag = {
					"A":"Adhere to term dates in rate",
					"S":"Use current day as term start date (adhere to term end date in rate)",
					"C":"Calculate term end date for term length (term start date is current day)"
				}>
			</cfif>

			<cfquery name="local.qryImportFileNewSubSets" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select s.setName, sub.[uid] as subscriptionUID
				from dbo.sync_sub_subscriptionSets as ss
				inner join dbo.sync_sub_sets as s on s.siteID = @siteID and s.setID = ss.setID
				inner join dbo.sync_sub_subscriptions as sub on sub.siteID = @siteID and sub.subscriptionID = ss.subscriptionID
				where ss.siteID = @siteID
				and ss.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateSubSets" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select ss.useID as subscriptionSetID, s.setName, sub.[uid] as subscriptionUID, ss.orderNum
				from dbo.sync_sub_subscriptionSets as ss
				inner join dbo.sync_sub_sets as s on s.siteID = @siteID and s.setID = ss.setID
				inner join dbo.sync_sub_subscriptions as sub on sub.siteID = @siteID and sub.subscriptionID = ss.subscriptionID
				where ss.siteID = @siteID
				and ss.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryImportFileUpdateSubSets.recordCount>
				<cfquery name="local.qrySiteUpdateSubSets" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

					select ss.subscriptionSetID, s.setName, sub.[uid] as subscriptionUID, ss.orderNum
					from dbo.sub_subscriptionSets as ss
					inner join dbo.sub_sets as s on s.siteID = @siteID and s.setID = ss.setID
					inner join dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
					where ss.subscriptionSetID in (#valueList(local.qryImportFileUpdateSubSets.subscriptionSetID)#);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>

			<cfquery name="local.qryImportFileNewSubAddOns" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select s.setName, sub.[uid] as subscriptionUID, sub.subscriptionName
				from dbo.sync_sub_addons as sa
				inner join dbo.sync_sub_sets as s on s.siteID = @siteID and s.setID = sa.childSetID
				inner join dbo.sync_sub_subscriptions as sub on sub.siteID = @siteID and sub.subscriptionID = sa.subscriptionID
				where sa.siteID = @siteID
				and sa.finalAction = 'A';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qryImportFileUpdateSubAddOns" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

				select sa.useID as addonID, s.setName, sub.[uid] as subscriptionUID, sub.subscriptionName,
					sa.orderNum, sa.minAllowed, sa.maxAllowed, sa.useAcctCodeInSet, sa.useTermEndDateInSet, sa.PCnum,
					sa.PCPctOffEach, sa.frontEndAllowSelect, sa.frontEndAllowChangePrice, 
					sa.frontEndContent, sa.frontEndAddAdditional
				from dbo.sync_sub_addons as sa
				inner join dbo.sync_sub_sets as s on s.siteID = @siteID and s.setID = sa.childSetID
				inner join dbo.sync_sub_subscriptions as sub on sub.siteID = @siteID and sub.subscriptionID = sa.subscriptionID
				where sa.siteID = @siteID
				and sa.finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfif local.qryImportFileUpdateSubAddOns.recordCount>
				<cfquery name="local.qrySiteUpdateSubAddOns" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;

					select ao.addonID, subs.[uid] as subscriptionUID, subs.subscriptionName, s.setName, ao.orderNum,
						ao.minAllowed, ao.maxAllowed, ao.useAcctCodeInSet, ao.useTermEndDateInSet, ao.PCnum, ao.PCPctOffEach,
						ao.frontEndAllowSelect, ao.frontEndAllowChangePrice, frontEndContent.rawContent as frontEndContent, 
						ao.frontEndAddAdditional as frontEndAddAdditional
					from dbo.sub_addons as ao
					inner join dbo.sub_sets as s on s.setID = ao.childSetID 
						and s.siteID = @siteID
						and s.status <> 'D'
					inner join dbo.sub_subscriptions as subs on subs.subscriptionID = ao.subscriptionID and subs.status <> 'D'
					cross apply dbo.fn_getContent(ao.frontEndContentID,1) as frontEndContent
					where ao.addonID in (#valueList(local.qryImportFileUpdateSubAddOns.addonID)#);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>
		</cfif>

		<cfif arrayLen(arguments.strImportResult.arrUpdateSets)>
			<cfquery name="local.qryImportFileUpdateSets" datasource="#application.dsn.datatransfer.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select [uid], setName, [status]
				from dbo.sync_sub_sets
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and finalAction = 'C';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfquery name="local.qrySiteUpdateSets" datasource="#application.dsn.memberCentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select setID, [uid], setName, [status]
				from dbo.sub_sets
				where siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
				and status <> 'D'
				and uid in (#listQualify(valueList(local.qryImportFileUpdateSets.uid), "'")#)
				order by setName;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = " #application.objSiteInfo.getSiteInfo(session.mcstruct.sitecode).defaultCurrencyType#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importCompare.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="generateSubsStructureImportErrorReport" access="private" output="false" returntype="string">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="arrErrors" type="array" required="yes">

		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importErrors.cfm">
		</cfsavecontent>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="doImportSubscriptionStructure" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.success = false>
		<cfset local.resultMessage = "">

		<cfsetting requesttimeout="1800">

		<cfset local.threadID = arguments.event.getTrimValue('threadID','')>
		<cfset local.SubscriptionImportStruct = application.mcCacheManager.sessionGetValue(keyname='SubscriptionImportStruct', defaultValue={})>
		<cfif NOT application.mcCacheManager.sessionValueExists('SubscriptionImportStruct') OR NOT structKeyExists(local.SubscriptionImportStruct,local.threadID)>
			<cfset local.resultMessage = "There was a problem importing the Subscription Structure. The import data is no longer available.">
		<cfelse>
			<cftry>
				<cfset local.QualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="SubscriptionRate", functionName="Qualify")>
				<cfstoredproc procedure="sub_importSubscriptionStructure" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.QualifyRFID#">
				</cfstoredproc>

				<cfset local.success = true>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.success = false>
				<cfset local.resultMessage = "There was a problem importing the Subscription Structure file.<br/>" & cfcatch.message>
			</cfcatch>
			</cftry>

			<!--- when done, remove from session --->
			<cfset StructDelete(local.SubscriptionImportStruct, local.threadID)>
			<cfset application.mcCacheManager.sessionSetValue(keyname='SubscriptionImportStruct', value=local.SubscriptionImportStruct)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_importReport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cancelSubsStructureImport" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="yes">

		<cfset var qryDeleteSyncData = "">

		<cfquery name="qryDeleteSyncData" datasource="#application.dsn.datatransfer.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DECLARE @siteID INT = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
				
				BEGIN TRAN;
					DELETE FROM dbo.sync_sub_frequencies WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_types WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_rateSchedules WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_rates WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_rateFrequencies WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_rateFrequenciesMerchantProfiles WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_rateGroups WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_subscriptions WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_sets WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_subscriptionSets WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_addons WHERE siteID = @siteID;
					DELETE FROM dbo.sync_sub_supporting WHERE siteID = @siteID;
				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="fetchReportData" access="public" output="false" returntype="struct">
		<cfargument name="reportuid" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.success = false>
		
		<cftry>
			<cfset local.SubscriptionImportStruct = application.mcCacheManager.sessionGetValue(keyname='SubscriptionImportStruct', defaultValue={})>
			<cfif structKeyExists(local.SubscriptionImportStruct,arguments.reportuid)>
				<cfset local.reportFileName = local.SubscriptionImportStruct[arguments.reportuid].folderPath & "/StructureImportReport.html">
				<cfset local.returnStruct.reportOutput = "">
				<cfif fileExists(local.reportFileName)>
					<cffile action="read" file="#local.reportFileName#" variable="local.returnStruct.reportOutput">
					<cfset local.returnStruct.success = true>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			<cfset local.returnStruct.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>	

	<cffunction name="showSubTree" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfscript>
			var local = structNew();
			
			local.memberID = arguments.event.getValue('mid','0');
			local.subscriberID = arguments.event.getValue('sid','0');

			// build XML LINKS --------------------------------------------------------------------------
			local.showSubTreeXML = '#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_ajaxlib=dhtmlGrid&com=subscriptionXML&meth=showSubTreeXML&mode=stream&mid=#local.memberID#&sid=#local.subscriberID#';
		</cfscript>

		<cfset local.memberInfo = application.objMember.getMemberInfo(memberID=local.memberID, orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		<cfset local.memberName = RTrim(local.memberInfo.firstName & " " & local.memberInfo.middleName) & " " & RTrim(local.memberInfo.lastName & " " & local.memberInfo.suffix)>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_showSubscriptionTree.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>
	
	<!--- Frequencies --->
	<cffunction name="getFrequencies" access="public" output="false" returntype="Query" hint="get frequencies">
		<cfargument name="siteID" type="numeric" required="true">	

		<cfset var qryFreq = "">

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="qryFreq">
			select frequencyID, frequencyName, frequency, frequencyShortName, uid, rateRequired, 
				hasInstallments, isnull(monthlyInterval,1) as monthlyInterval, 
				isNull(isSystemRate,0) as isSystemRate, siteID
			from dbo.sub_frequencies
			where siteID = <cfqueryparam value="#arguments.siteID#" cfsqltype="cf_sql_integer">
			and status = 'A'
			order by frequencyName
		</cfquery>
		
		<cfreturn qryFreq>
	</cffunction>

	<cffunction name="editFrequency" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();
			local.fID = int(val(arguments.event.getValue('fid',0)));
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryGetFreq">
			select frequencyID, frequencyName, frequency, frequencyShortName, uid, rateRequired, 
				hasInstallments, isnull(monthlyInterval,1) as monthlyInterval, 
				isNull(isSystemRate,0) as isSystemRate, siteID
			from dbo.sub_frequencies
			where siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="cf_sql_integer">
			and frequencyID = <cfqueryparam value="#local.fID#" cfsqltype="cf_sql_integer">
			and [status] = 'A'
		</cfquery>

		<cfset local.displayOnly = local.qryGetFreq.isSystemRate EQ 1 ? true : false>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_rateFrequency.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveFrequency" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.objSubs = CreateObject("component","subscriptions");

		local.frequencyID = arguments.event.getValue('fid',0);
		local.frequencyUID = arguments.event.getTrimValue('freqUID','');
		local.frequencyName = arguments.event.getTrimValue('frequencyName', '');
		local.frequencyShortName = arguments.event.getTrimValue('frequencyShortName', '');
		local.frequency = arguments.event.getValue('frequency',0);
		local.rateRequired = arguments.event.getValue('rateRequired',0);
		if(not len(trim(local.rateRequired))) local.rateRequired = 0;
		local.monthlyInterval = arguments.event.getValue('monthlyInterval',0);
		local.hasInstallments = 0;
		if (local.frequency gt 0){
			local.hasInstallments = 1;
		}

		if (local.frequencyID GT 0) {
			local.updateRateFreq = local.objSubs.updateFrequency(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), uid=local.frequencyUID, frequencyName=local.frequencyName, 
										frequencyShortName=local.frequencyShortName, frequency=local.frequency, rateRequired=local.rateRequired, monthlyInterval=local.monthlyInterval);
			
			// update uid
			if (local.updateRateFreq.success AND application.objUser.isSuperUser(cfcuser=session.cfcuser) AND len(arguments.event.getTrimValue('newFreqUID','')))
				local.objSubs.updateFrequencyUID(mcproxy_siteID = arguments.event.getValue('mc_siteinfo.siteid'), frequencyID=local.frequencyID, frequencyUID=arguments.event.getTrimValue('newFreqUID'));
		} else {
			local.addRateFreq = local.objSubs.addFrequency(siteCode=arguments.event.getValue('mc_siteinfo.siteCode'), frequencyName=local.frequencyName, frequencyShortName=local.frequencyShortName,
									frequency=local.frequency, rateRequired=local.rateRequired, monthlyInterval=local.monthlyInterval);
		}	
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.reloadRateFreq();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>	
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="linkAcceptSubscription" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_acceptSubscription.cfm">	
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Credit Card Association --->
	<cffunction name="linkCCToSubscriber" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		
		<cfset local.memberID = arguments.event.getValue('mid','0')>
		<cfset local.subscriberID = arguments.event.getValue('sid','0')>
		<cfset local.formLink = this.link.saveCCToSubscriber>
		<cfset local.extrapayJS = "">
		
		<cfset local.qryPayee = CreateObject("component","model.admin.transactions.transactionAdmin").getPayeeInfo(memberID=local.memberID)>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscriberInfo">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @rootSubscriberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.subscriberID#">,
				@eligibleProfileIDList varchar(200);

			SELECT @eligibleProfileIDList = STRING_AGG(rfmp.profileID, ',')
			FROM dbo.sub_subscribers AS s
			INNER JOIN dbo.sub_rateFrequenciesMerchantProfiles AS rfmp ON rfmp.rfID = s.rfID
				AND rfmp.[status] = 'A'
			WHERE s.subscriberID = @rootSubscriberID;

			SELECT subs.subscriptionName, s.subStartDate, s.subEndDate, s.MPProfileID, s.payProfileID, s.payProcessFee,
				ISNULL(@eligibleProfileIDList,'') AS eligibleProfileIDList
			FROM dbo.sub_subscribers AS s
			INNER JOIN dbo.sub_subscriptions AS subs on subs.subscriptionID = s.subscriptionID
			WHERE s.subscriberID = @rootSubscriberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<!--- get a site's merchant profiles --->
		<cfquery name="local.qryMerchantProfiles" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			select p.profileID, p.profileName, p.profileCode, g.gatewayClass, g.gatewayID, p.enableProcessingFeeDonation, 
				p.processFeeDonationFeePercent, pfm.title as processFeeDonationFETitle, pfm.message as processFeeDonationFEMsg, 
				p.processFeeSubscriptionsFELabel, p.processFeeSubscriptionsFEDenyLabel, p.status
			from dbo.mp_profiles as p
			inner join dbo.mp_gateways as g on g.gatewayID = p.gatewayID
				and g.isActive = 1
			left outer join dbo.tr_solicitationMessages as pfm on pfm.siteID = @siteID
				and pfm.messageID = p.solicitationMessageID
			where p.siteID = @siteID
			and p.status in ('A','I')
			and p.profileID in (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="0#local.qrySubscriberInfo.eligibleProfileIDList#">);

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subCCSelect.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="saveCCToSubscriber" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		
		<cfset local.paySource = arguments.event.getValue('paySource','0')>
		<cfset local.payProfileID = arguments.event.getValue('p_#local.paySource#_mppid','0')>
		<cfset local.memberID = arguments.event.getValue('pmid','0')>
		<cfset local.subscriberID = arguments.event.getValue('sid','0')>
		<cfset local.payProcessFee = arguments.event.getValue('processFeeDonation#local.paySource#',0)>
		
		<cfstoredproc procedure="sub_updatePaymentProfile" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.subscriberID#">
			<cfif local.payProfileID GT 0>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.paySource#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.payProfileID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.payProcessFee#">
			<cfelse>
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" null="true">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			</cfif>
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="0">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
		</cfstoredproc>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script>
					top.closeAssocCC();
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>


	<!--- Subscriber Import --->
	<cffunction name="sampleSubscriberImportTemplate" access="public" output="false" returntype="struct" hint="Sample Import Template">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.ranCustom = false>
		<cfif arguments.event.getValue('impmode','') eq 'custom' and FileExists(ExpandPath("model/admin/subscriptions/custom/#arguments.event.getValue('mc_siteinfo.orgcode')#.cfc"))>
			<cfset local.objCustomImport = CreateObject("component","model.admin.subscriptions.custom.#arguments.event.getValue('mc_siteinfo.orgcode')#")>
			<cfif isDefined("local.objCustomImport.sampleSubscriberImportTemplate")>
				<cfset local.ranCustom = true>
				<cfset local.data = local.objCustomImport.sampleSubscriberImportTemplate(event=arguments.event)>
			</cfif>
		</cfif>

		<cfif not local.ranCustom>
			<cfset local.data = CreateObject("component","subscriberImport").generateSubscriberImportTemplate(useAccrualAccounting=arguments.event.getValue('mc_siteInfo.useAccrualAcct'))>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>		

	<cffunction name="processSubscriberImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSubImport = CreateObject("component","subscriberImport")>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objSubImport.importSubscribers(
															sitecode=arguments.event.getValue('mc_siteinfo.sitecode'),
															siteID=arguments.event.getValue('mc_siteinfo.siteID'),
															frmEmAction_a=arguments.event.getValue('frmEmAction_a','s'), 
															recordRevenueTransaction=arguments.event.getValue('frmRecordRevTrans',0),
															useAccrualAccounting=arguments.event.getValue('mc_siteInfo.useAccrualAcct'))>
		<cfset local.data = local.objSubImport.showImportResults(strResult=local.processResult, doAgainURL='#this.link.listReports#&tab=import', data="subImport")>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="processSubCardsOnFileImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objSubImport = CreateObject("component","subscriberImport")>

		<!--- extend CF timeout --->
		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objSubImport.importSubCardsOnFile(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'),
															siteID=arguments.event.getValue('mc_siteinfo.siteID'),
															subStatus=arguments.event.getValue('subStatus','P,A,O,R'))>
		<cfset local.data = local.objSubImport.showImportResults(strResult=local.processResult, doAgainURL='#this.link.listReports#&tab=import', data="subCOFImport")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="editMemberDate" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions");

			local.udid = arguments.event.getValue('udid',0);

			// Build breadCrumb Trail
			if (local.udid gt 0)
				appendBreadCrumbs(arguments.event,{ link='', text='Edit Member Date Rule' });
		</cfscript>
		
		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.qryMemberDates">
			SELECT udid, joinDateFieldName, rejoinDateFieldName, droppedDateFieldName, paidThruDateFieldName, renewalDateFieldName, isActive
			FROM dbo.schedTask_memberJoinDates
			WHERE udid = <cfqueryparam value="#local.udid#" cfsqltype="cf_sql_integer">
			AND siteCode = '#arguments.event.getValue('mc_siteinfo.sitecode')#'
		</cfquery>

		<cfquery datasource="#application.dsn.customApps.dsn#" name="local.qryMemberJoinDateSubTypes">
			SELECT st.subscriptionTypeUID, st.subscriptionUID
			FROM schedTask_memberJoinDateSubTypes as st
			INNER JOIN schedTask_memberJoinDates as jd ON jd.udid = st.memberJoinDateUDID
			WHERE jd.udid = <cfqueryparam value="#local.udid#" cfsqltype="cf_sql_integer">
			AND jd.siteCode = '#arguments.event.getValue('mc_siteinfo.sitecode')#'
		</cfquery>
		
		<cfset local.qrySubscriptionTypes = local.objSubs.getSubscriptionTypes(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfset local.qrySubscriptions = local.objSubs.getSubscriptions(siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMemberDataCustomFields">
			SELECT mdc.columnName
			FROM dbo.ams_memberDataColumns as mdc
			INNER JOIN dbo.ams_memberDataColumnDataTypes as mdt on mdt.dataTypeID = mdc.dataTypeID
			WHERE mdc.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			AND mdt.dataTypeCode = 'DATE'
			ORDER BY columnName;
		</cfquery>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_memberDates.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveMemberDate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objSubs = CreateObject("component","model.admin.subscriptions.subscriptions")>

		<cfset local.arrSubTypes = arrayNew(1)>
		<cfloop collection="#arguments.event.getCollection()#" item="local.thisEl">
			<cfif left(local.thisEl,8) eq "subType_">
				<cfset local.thisSubTypeNum = getToken(local.thisEl,2,"_")>
				<cfif arguments.event.getValue('subType_#local.thisSubTypeNum#') gt 0>
					<cfset local.strTemp = { typeID=arguments.event.getValue('subType_#local.thisSubTypeNum#'), subID=arguments.event.getValue('sub_#local.thisSubTypeNum#','') }>
					<cfset arrayAppend(local.arrSubTypes,local.strTemp)>
				</cfif>
			</cfif>
		</cfloop>

		<cfif arguments.event.getValue('udid',0) gt 0>
			<cfset local.udid = arguments.event.getValue('udid')>
			<cfset local.objSubs.updateMemberDate(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), 
					udid=arguments.event.getValue('udid'), joinDate=arguments.event.getValue('joinDate',''), 
					rejoinDate=arguments.event.getValue('rejoinDate',''), dropDate=arguments.event.getValue('dropDate',''),
					paidThruDate=arguments.event.getValue('paidThruDate',''), renewalDate=arguments.event.getValue('renewalDate',''),
					isActive=arguments.event.getValue('isMemberDateActive',0))>
		<cfelse>
			<cfset local.udid = local.objSubs.insertMemberDate(sitecode=arguments.event.getValue('mc_siteinfo.sitecode'), joinDate=arguments.event.getValue('joinDate',''), 
					rejoinDate=arguments.event.getValue('rejoinDate',''), dropDate=arguments.event.getValue('dropDate',''),
					paidThruDate=arguments.event.getValue('paidThruDate',''), renewalDate=arguments.event.getValue('renewalDate',''),
					isActive=arguments.event.getValue('isMemberDateActive',0), lastMemberRunDate=arguments.event.getValue('lastMemberRunDate','1/1/1980'))>
		</cfif>
		<cfset local.objSubs.updateMemberDateSubTypes(siteID=arguments.event.getValue('mc_siteinfo.siteID'), udid=local.udid, arrSubTypes=local.arrSubTypes)> 

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadMemberDateRule();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<!--- Remove Payment Method --->
	<cffunction name="removePaymentMethod" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_removePayMethod.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="processRemovePaymentMethod" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpSubs') IS NOT NULL 
						DROP TABLE ##tmpSubs;
					CREATE TABLE ##tmpSubs (subscriberID int PRIMARY KEY, payProfileID int);

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">,
						@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">,
						@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
						@payProfileIDList varchar(max);

					INSERT INTO ##tmpSubs (subscriberID, payProfileID)
					SELECT DISTINCT ss.subscriberID, ss.payProfileID
					FROM dbo.sub_subscribers AS ss
					INNER JOIN dbo.sub_statuses AS st ON st.statusID = ss.statusID
					WHERE ss.subscriberID IN (#local.subscriberIDList#)
					AND st.statusCode IN ('R','O','E','X','D')
					AND ss.payProfileID IS NOT NULL;

					SELECT @payProfileIDList = COALESCE(@payProfileIDList + ',', '') + cast(ss.payProfileID as varchar(20))
					FROM dbo.sub_subscribers AS ss
					INNER JOIN ##tmpSubs AS tmp ON tmp.subscriberID = ss.subscriberID
					WHERE ss.payProfileID IS NOT NULL
					GROUP BY ss.payProfileID;

					BEGIN TRAN;
						UPDATE ss
						SET ss.payProfileID = NULL,
							ss.MPProfileID = NULL,
							ss.payProcessFee = 0,
							ss.processFeePercent = NULL
						FROM dbo.sub_subscribers AS ss
						INNER JOIN ##tmpSubs AS tmp ON tmp.subscriberID = ss.subscriberID;

						INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
						SELECT '{ "c":"auditLog", "d": {
							"AUDITCODE":"SUBS",
							"ORGID":' + cast(@orgID as varchar(10)) + ',
							"SITEID":' + cast(@siteID as varchar(10)) + ',
							"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
							"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
							"MESSAGE":"' + replace(dbo.fn_cleanInvalidXMLChars('Pay Profile ' + mpp.detail + ' removed from ' + 'Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(s.subscriberID AS varchar(10)) + ')'),'"','\"') + '" } }'
						FROM ##tmpSubs as tmp
						INNER JOIN dbo.sub_subscribers as s on s.subscriberID = tmp.subscriberID
						INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = tmp.payProfileID
						INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID;
					COMMIT TRAN;

					-- trigger reprocessing of credit card expiration conditions (if limiting to subscriptions)
					IF @payProfileIDList IS NOT NULL BEGIN
						EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittosub';
					END

					IF OBJECT_ID('tempdb..##tmpSubs') IS NOT NULL 
						DROP TABLE ##tmpSubs;
					
				END TRY
				BEGIN CATCH
					IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

		

			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					$(document).ready(function(){
						top.mcg_reloadGrid();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.js#">

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "Payment methods have been removed from your selected subscriptions."
						top.$('##MCModalLabel').html('Payment Method Removal Completed');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

			<cfsavecontent variable="local.data">
				<cfoutput>
					<script>
					var msg = "We were not able to remove payment methods from the selected subscriptions.<br/>MemberCentral has been notified of the issue; please follow up with Support for a status."
					top.$('##MCModalLabel').html('Issue Removing Payment Methods');
					top.$('##MCModalBody').html('<div class="p-2">'+msg+'</div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>
	
	<cffunction name="copySubscription" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.subID = int(val(arguments.event.getValue('subID',0)))>
	
		<cfif local.subID eq 0>
			<cflocation url="#this.link.list#" addtoken="no">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubscription">
			select subscriptionName, typeID
			from dbo.sub_subscriptions
			where subscriptionID = <cfqueryparam value="#local.subID#" cfsqltype="cf_sql_integer">
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriptionCopy.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveCopySubscription" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.subID = int(val(arguments.event.getValue('subID',0)));
		local.typeID = arguments.event.getTrimValue('typeID',0);
		local.crlf = "#Chr(13)##Chr(10)#";

		local.arrSubValues = listToArray(arguments.event.getTrimValue('subscriptionNameList',''),local.crlf);
		</cfscript>

		<cfif local.subID gt 0 and local.typeID gt 0 and arrayLen(local.arrSubValues)>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCopySubscription">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						declare @orgID int, @siteID int, @typeID int, @subId int, @newSubID int, @newSubName varchar(300), @recordedByMemberID int;
						set @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
						set @siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteID')#" cfsqltype="CF_SQL_INTEGER">;
						set @typeID = <cfqueryparam value="#local.typeID#" cfsqltype="CF_SQL_INTEGER">;
						set @subId = <cfqueryparam value="#local.subID#" cfsqltype="CF_SQL_INTEGER">;
						set @recordedByMemberID = <cfqueryparam value="#session.cfcuser.memberdata.memberID#" cfsqltype="CF_SQL_INTEGER">;
						
						BEGIN TRAN;
							<cfloop array="#local.arrSubValues#" index="local.thisSub">
								set @newSubName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#trim(local.thisSub)#">;

								IF len(@newSubName) > 0 and NOT EXISTS (
									select 1
									from dbo.sub_subscriptions s
									inner join dbo.sub_types t on t.typeID = s.typeID and t.typeID = @typeID
									where s.subscriptionName = @newSubName
									and s.status = 'A'
									)
								 	EXEC dbo.sub_copySubscription @orgID=@orgID, @siteid=@siteID, @typeID=@typeID, @subID=@subId, 
								 		@newSubName=@newSubName, @recordedByMemberID=@recordedByMemberID;
							</cfloop>
						COMMIT TRAN;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.mcg3_reloadGrid();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- Remove Addons --->
	<cffunction name="startRemoveAddons" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = StructNew()>
		<cfset local.strFilters = CreateObject("component","subscriptions").getSubReportListFilters(event=arguments.event)>
		<cfset local.addOnSubsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getAddOnSubsForMassAction&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_startRemoveAddons.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="processRemoveAddons" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>
		<cfset local.subscriptionID = arguments.event.getValue('subscriptionID','0')>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfquery name="local.qrySubscribers" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			
			DECLARE @rootsubscriberIDList VARCHAR(MAX), @siteID INT;
			SET @rootsubscriberIDList = '#local.subscriberIDList#';
			SET @siteID = #arguments.event.getValue('mc_siteinfo.siteid')#;
			
			SELECT rec.subscriberID
			FROM dbo.fn_intListToTable(@rootsubscriberIDList, ',') AS tmp
			CROSS APPLY dbo.fn_getRecursiveSubscriptionsByID(@siteID, tmp.listitem) AS rec
			WHERE rec.subscriptionID = <cfqueryparam value="#local.subscriptionID#" cfsqltype="cf_sql_integer">
			AND rec.status <> 'D';
			
			SET NOCOUNT OFF;
		</cfquery>

		<cfset local.removeSubscriberIDList = valuelist(local.qrySubscribers.subscriberID)>
		
		<cfif listlen(local.removeSubscriberIDList)>
			<cftry>
				<cfstoredproc procedure="sub_queueMassDelete" datasource="#application.dsn.membercentral.dsn#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
					<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
					<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.removeSubscriberIDList#">
				</cfstoredproc>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<script>
					var msg = "Your selected subscriptions have been queued for addon removal and will begin processing soon. You'll receive an email when they have been completed."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Subscription AddOn Removal Scheduled');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
					</cfoutput>
				</cfsavecontent>

			<cfcatch type="Any">
				<cfset application.objError.sendError(cfcatch=cfcatch, objectToDump=local)>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<script>
					var msg = "We were not able to queue the subscriptions for removal.<br/>
						MemberCentral has been notified of the issue; please follow up with Support for a status."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Issue Removing AddOns from Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
					</cfoutput>
				</cfsavecontent>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfsavecontent variable="local.data">
				<cfoutput>
				<script>
					var msg = "There were no eligible addons to remove from the selected subscription trees."
						top.$('##MCModalFooter').removeClass('d-flex').addClass('d-none');
						top.$('##MCModalLabel').html('Issue Removing AddOns from Selected Subscriptions');
						top.$('##MCModalBody').html('<div class="p-2">'+msg+'  </div>');
					</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>
	
	<cffunction name="sampleAssociateCOFImportTemplate" access="public" output="false" returntype="struct" hint="Sample Import Template">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.data = CreateObject("component","subscriberImport").generateAssociateCOFImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveSubscriptionSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset CreateObject("component","subscriptions").updateSubscriptionSettings(event=arguments.event)>		

		<!--- reload siteinfo object since subscriptionIssuesEmail could have changed --->
		<cfset application.objSiteInfo.triggerClusterWideReload()>
		
		<cflocation url="#this.link.list#&tab=settings" addtoken="no">
	</cffunction>
	
	<cffunction name="startExportSubscriptionsSchedule" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.strMemberDataFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'),
			selectorID="fsid", selectedFieldSetName='Subscriber Download Standard', inlinePreviewSectionID="exportFormContainer")>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subscriberScheduleExport.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportSubscriptionsSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.stDownloadURL = createObject("component","subscriberExport").exportSubscriptionsSchedule(siteCode=arguments.event.getValue('mc_siteInfo.sitecode'), fieldSetID=arguments.event.getValue("fsid",0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href="/tsdd/#local.stDownloadURL#";
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="showSubRenewalLink" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>

		<cfquery name="local.qryRenewLink" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select s.directLinkCode
			from dbo.sub_subscribers as s
			inner join dbo.sub_statuses as st on st.statusID = s.statusID
			where s.subscriberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('sid',0)#">
			and s.orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">
			and st.statusCode = 'O';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.renewLink = "#application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).scheme#://#application.objSiteInfo.getSiteInfo(arguments.event.getValue('mc_siteInfo.sitecode')).mainhostName#/renewsub/#local.qryRenewLink.directLinkCode#">
		<cfset local.renewLinkCode = local.qryRenewLink.directLinkCode>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_subRenewalLink.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getSubSetUpAuditLogs" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.auditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=subscriptionJSON&meth=getSubSetupAuditLogs&mode=stream";

			// Build breadCrumb Trail
			appendBreadCrumbs(arguments.event,{ link='', text='Audit Log' });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_auditLog.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
			
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this function.</b></cfcase>
							<cfcase value="2"><b>That subscription was not found.</b></cfcase>
							<cfcase value="3">
								<b>There was a problem saving this subscriptions's information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
							</cfcase>
							<cfcase value="4">
								<cfif arguments.event.getValue('errCode','') EQ 'RATESCHNAMEEXISTS'>
									<b>Rate Schedule Name already exists.</b>
								<cfelse>
									<b>There was a problem saving this rate schedule's information.</b><br/><br/>
									If you continue to see this error, contact Support for further assistance.
								</cfif>
							</cfcase>
							<cfcase value="6">
								<b>There was a problem saving this subscriptions type's information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
							</cfcase>
							<cfcase value="7">
								<b>There was a problem saving this subscriptions addon's information.</b><br/><br/>
								If you continue to see this error, contact Support for further assistance.
							</cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>	

	<cffunction name="dspRemovePaymentMethod" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.objSubs = CreateObject("component","subscriptions")>
		<cfset local.strFilters = local.objSubs.getSubReportListFilters(event=arguments.event)>
		<cfset local.subscriberIDList = local.strFilters.fChkedSubs>
		<cfset local.notSubscriberIDList = local.strFilters.fUnchkedSubs>

		<cfif local.strFilters.fChkAll eq 1>
			<cfset local.subscriberIDList = "">

			<cfset local.subList = local.objSubs.getSubReportsQuery(subStatus=local.strFilters.fSubStatus,
				subPaymentStatus=local.strFilters.fSubPaymentStatus,
				subStartFromDate=local.strFilters.fTermStartFrom,
				subStartToDate=local.strFilters.fTermStartTo,
				subEndFromDate=local.strFilters.fTermEndFrom,
				subEndToDate=local.strFilters.fTermEndTo,
				subType=local.strFilters.fSubType,
				subID=local.strFilters.fSubscription,
				freqID=local.strFilters.fFreq,
				rateID=local.strFilters.fRate,
				hasCard=local.strFilters.fHasCardOnFile,
				associatedMemberID=local.strFilters.associatedMemberID,
				associatedGroupID=local.strFilters.associatedGroupID,
				linkedRecords=local.strFilters.linkedRecords,
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				offerEndFromDate=local.strFilters.fOffrExpFrom,
				offerEndToDate=local.strFilters.fOffrExpTo,
				notSubscribers=local.notSubscriberIDList)>

			<cfset local.subscriberIDList = valuelist(local.subList.qry.subscriberID)>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_removePayMethodFromSubscription.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>		
	</cffunction>

</cfcomponent>