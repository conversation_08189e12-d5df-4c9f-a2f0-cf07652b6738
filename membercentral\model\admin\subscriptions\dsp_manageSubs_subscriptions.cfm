<cfoutput>
<cfset local.freeSubIDList = "">
<cfloop from="1" to="#arrayLen(local.arrSubs)#" index="local.i">
	<cfset local.thisSub = duplicate(local.arrSubs[local.i])>
	<cfset local.hasSubAddOns = structKeyExists(local.thisSub,'addOns') AND StructCount(local.thisSub.addOns)>
	<cfset local.hasSingleRate = local.thisSub.qryRates.recordCount EQ 1>
	<cfset local.offerAsFreeAddOnSub = false>
	<cfset local.isAlreadySubscribed = structKeyExists(arguments,'listSubscribed') ? listFind(arguments.listSubscribed, local.thisSub.subscriptionID) gt 0 : false>
	<cfset local.rateCanEditPrice = false>
	
	<!--- Add On Sub --->
	<cfif structCount(arguments.strAddOn)>
		<cfset local.setID = arguments.strAddOn.setID>
		<cfset local.addOnID = arguments.strAddOn.addOnID>
		<cfset local.subInputFieldName = "subAddOn#arguments.strAddOn.addOnID#">
		<cfset local.subInputFieldID = "subAddOn#arguments.strAddOn.addOnID#_#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldType = arguments.strAddOn.maxAllowed NEQ 1 ? "checkbox" : "radio">
		<cfset local.addOnAllowChangePrice = arguments.strAddOn.frontEndAllowCHangePrice>
		<cfset local.thisSubSelected = structKeyExists(local.thisSub,'isSelected') and local.thisSub.isSelected>
		<cfset local.ratePctOff = val(arguments.strAddOn.PCPctOffEach)>
		<cfset local.freeSubsCount = val(arguments.strAddOn.PCNum)>
	
	<!--- Root Sub --->
	<cfelse>
		<cfset local.setID = 0>
		<cfset local.addOnID = 0>
		<cfset local.subInputFieldName = "sub#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldID = "sub#local.thisSub.subscriptionID#">
		<cfset local.subInputFieldType = "checkbox">
		<cfset local.addOnAllowChangePrice = false>
		<cfset local.thisSubSelected = true>
		<cfset local.ratePctOff = 0>
		<cfset local.freeSubsCount = 0>
	</cfif>

	<!--- single sub rate display vars --->
	<cfif local.hasSingleRate>
		<cfif local.addOnAllowChangePrice OR local.thisSub.qryRates.frontEndAllowChangePrice>
			<cfset local.rateCanEditPrice = true>
		<cfelse>
			<cfset local.rateCanEditPrice = false>
		</cfif>

		<cfset local.rateNameForDisplay = local.thisSub.qryRates.rateName>
		<cfset local.rateAmtDisplay = "">
		<cfset local.rateNumInstallments = local.thisSub.qryRates.numInstallments>
		
		<cfif arguments.parentSubscriptionID EQ 0>
			<cfset local.numPaymentsToUse = local.thisSub.qryRates.numInstallments>
			<cfset local.rateFreqName = local.thisSub.qryRates.frequencyName>
			<cfset local.rateFreqShortName = local.thisSub.qryRates.frequencyShortName>
		<cfelse>
			<cfset local.numPaymentsToUse = arguments.strParentFreq.numPaymentsToUse>
			<cfset local.rateFreqName = arguments.strParentFreq.frequencyName>
			<cfset local.rateFreqShortName = arguments.strParentFreq.frequencyShortName>
		</cfif>

		<!--- Must Pay on 1st invoice ---->
		<cfif local.thisSub.qryRates.forceUpfront>
			<cfset local.numPaymentsToUse = 1>
			<cfset local.rateFreqName = 'Full'>
			<cfset local.rateFreqShortName = 'F'>
			<cfset local.rateNumInstallments = 1>
		</cfif>

		<cfif local.rateCanEditPrice>
			<cfset local.thisRateAmt = local.thisSub.qryRates.rateAmt>
			<cfif arguments.parentSubscriptionID GT 0 AND arguments.strParentFreq.frequencyShortName NEQ local.thisSub.qryRates.frequencyShortName>
				<cfset local.thisRateToUse = local.thisRateAmt / local.numPaymentsToUse>
			<cfelse>
				<cfset local.thisRateToUse = local.thisRateAmt>
			</cfif>

			<cfset local.strRateAmt = { "rateToUse": numberFormat(local.thisRateToUse,"0.00"), "rateTotal": numberFormat(local.thisRateToUse * local.numPaymentsToUse,"0.00") }>
		<cfelse>
			<cfset local.strRateAmt = manageSubscription_getRateToUse(rateAmt=local.thisSub.qryRates.rateAmt, rateInstallments=local.rateNumInstallments, 
				numPaymentsToUse=local.numPaymentsToUse, pcPctOff=local.ratePctOff)>
		</cfif>
		
		<cfif local.strRateAmt.rateToUse GT 0>
			<cfset local.rateAmtDisplay = dollarFormat(local.strRateAmt.rateToUse)>
		<cfelseif len(arguments.freeRateDisplay)>
			<cfset local.rateAmtDisplay = arguments.freeRateDisplay>
		</cfif>

		<cfif NOT local.rateCanEditPrice>
			<cfif len(local.rateAmtDisplay)>
				<cfset local.rateNameForDisplay = "#local.rateNameForDisplay# - #local.rateAmtDisplay#">
			</cfif>
			<cfif local.rateFreqShortName NEQ 'F'>
				<cfset local.rateNameForDisplay = "#local.rateNameForDisplay# #local.rateFreqName#">
			</cfif>
		</cfif>
	</cfif>

	<!--- free sub? --->
	<cfif local.thisSubSelected AND arguments.strEditSubs.keyExists(local.thisSub.subscriptionID) AND arguments.strEditSubs[local.thisSub.subscriptionID].keyExists('PCFree') AND arguments.strEditSubs[local.thisSub.subscriptionID].PCFree EQ 1>
		<cfset local.offerAsFreeAddOnSub = true>
		<cfset local.freeSubIDList = listAppend(local.freeSubIDList,local.thisSub.subscriptionID)>
	</cfif>

	<!--- Subscription --->
	<div class="d-flex align-items-center no-gutters mb-2 flex-wrap">
		<input type="#local.subInputFieldType#" 
			name="#local.subInputFieldName#" 
			id="#local.subInputFieldID#" 
			value="#local.thisSub.subscriptionID#" 
			data-subscriptionname="#encodeForHTMLAttribute(local.thisSub.subscriptionname)#" 
			data-setid="#local.setID#"
			data-addonid="#local.addOnID#"
			data-parentsubscriptionid="#arguments.parentSubscriptionID#"
			<cfif local.offerAsFreeAddOnSub>
				data-isfreeaddonsub="1"
			</cfif>
			<cfif arguments.parentSubscriptionID EQ 0>
				class="subSelector d-none" 
				checked
			<cfelse>
				onchange="chooseSub(this);"
				class="my-auto mr-1 subSelector" 
				<cfif local.thisSubSelected> checked</cfif>
			</cfif>
			data-issubscribed="#int(local.isAlreadySubscribed)#"
			<cfif local.isAlreadySubscribed> disabled title="Already Subscribed"</cfif>
		>
		<cfif local.addOnID GT 0>
			<div class="<cfif local.hasSingleRate>col-auto<cfelse>col</cfif> pl-2">
				<label for="#local.subInputFieldID#" class="d-inline-block mb-0<cfif local.isAlreadySubscribed> text-dim</cfif>"<cfif local.isAlreadySubscribed> title="Already Subscribed"</cfif>>
					#local.thisSub.subscriptionname#
					<cfif local.hasSingleRate AND NOT local.rateCanEditPrice AND len(local.rateAmtDisplay)>
						<span id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#_rateAmtDisp" class="addOnSub#local.thisSub.subscriptionID#SingleRateLabel addOnSubSingleRateLabel<cfif NOT local.thisSubSelected OR local.offerAsFreeAddOnSub> d-none</cfif>">
							- #local.rateAmtDisplay#
							<cfif local.strRateAmt.rateToUse GT 0 AND local.rateFreqShortName NEQ 'F'>
								#local.rateFreqName#
							</cfif>
						</span>
					</cfif>
				</label>
			</div>
		</cfif>
		<cfif local.hasSingleRate AND local.rateCanEditPrice>
			<div class="col pl-0">
				<div class="d-flex align-items-center flex-wrap sub#local.thisSub.subscriptionID#_rateWrapper">
					<cfset local.thisDivClassNameList = "col-auto pl-3 editRatePrices editRatePrice#local.thisSub.qryRates.rfid#">
					<cfif NOT local.thisSubSelected> 
						<cfset local.thisDivClassNameList = "#local.thisDivClassNameList# d-none">
					</cfif>

					<div class="#local.thisDivClassNameList#">
						<div class="input-group flex-nowrap">
							<div class="input-group-prepend">
								<span class="input-group-text px-2">$</span>
							</div>
							<div class="form-label-group flex-grow-1 mb-0">
								<input type="text" 
									id="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#" 
									name="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#" 
									class="form-control editableRateFields" 
									value="#val(local.strRateAmt.rateToUse)#" 
									data-origrateamt="#val(local.strRateAmt.rateToUse)#"
									data-subscriptionid="#local.thisSub.subscriptionID#" 
									data-rfid="#local.thisSub.qryRates.rfid#"
									data-ratemin="#val(local.thisSub.qryRates.frontEndChangePriceMin)#"
									data-ratemax="#val(local.thisSub.qryRates.frontEndChangePriceMax)#"
									data-rateinstallments="#local.numPaymentsToUse#"
									onkeyup="validateRateAmt(this);"
									onblur="onBlurRateAmt(this);"
									onfocus="onFocusEditRateField(this)"
									size="10">
								<label for="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#">Enter Amount</label>
							</div>
						</div>
						<cfif local.rateFreqShortName NEQ 'F'>
							<div class="font-size-sm text-dim text-center">
								Total: <span id="totalPrice#local.thisSub.qryRates.rfid#">#dollarFormat(local.strRateAmt.rateTotal)#</span>
							</div>
						</cfif>
					</div>
					<cfif local.rateFreqShortName NEQ 'F'>
						<div class="col-auto pl-0 editRatePrices editRatePrice#local.thisSub.qryRates.rfid#<cfif NOT local.thisSubSelected> d-none</cfif>">
							#local.rateFreqName#
						</div>
					</cfif>
					<cfif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0 OR val(local.thisSub.qryRates.frontEndChangePriceMax) GT 0>
						<div class="col-auto pl-5 editRatePrices editRatePrice#local.thisSub.qryRates.rfid#<cfif NOT local.thisSubSelected> d-none</cfif>">
							<span class="editRatePriceRange text-dim">
								<cfif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0 AND val(local.thisSub.qryRates.frontEndChangePriceMax) GT 0>
									#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Min - #dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Max
								<cfelseif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0>
									#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Minimum
								<cfelse>
									#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Maximum
								</cfif>
							</span>
							<span class="editRatePriceRangeErr d-none font-weight-bold text-danger"></span>
						</div>
					</cfif>
				</div>
			</div>
		</cfif>
	</div>

	<!--- Subscription Rates --->
	<cfif local.hasSingleRate>
		<cfset local.strRate = duplicate(local.thisSub.qryRates)>

		<cfif arguments.parentSubscriptionID EQ 0>
			<cfset local.subTermDatesCD = getSubTermDates(termDateRFID=local.strRate.rfid, subTermFlag=local.thisSub.rateTermDateFlag)>
			<cfset local.subTermStartDate = local.subTermDatesCD.subTermStartDate>
			<cfset local.subTermEndDate = local.subTermDatesCD.subTermEndDate>
			<cfset local.subTermGraceEndDate = local.subTermDatesCD.subTermGraceEndDate>
		<cfelse>
			<cfset local.subTermStartDate = local.strRate.termAFStartDate>
			<cfset local.subTermEndDate = local.strRate.termAFEndDate>
			<cfset local.subTermGraceEndDate = local.strRate.graceEndDate>
		</cfif>
		
		<input type="radio" 
			name="sub#local.thisSub.subscriptionID#_rfid" 
			id="sub#local.thisSub.subscriptionID#_rfid_#local.strRate.rfid#" 
			class="subRateRadio d-none"
			value="#local.strRate.rfid#" 
			data-rateid="#val(local.strRate.rateID)#" 
			data-freqid="#val(local.strRate.frequencyID)#" 
			data-freq="#local.rateFreqShortName#"
			data-freqname="#local.rateFreqName#"
			data-price="#val(local.strRateAmt.rateToUse)#" 
			data-termprice="#val(local.strRateAmt.rateTotal)#" 
			rate-subscriptionid="#local.thisSub.subscriptionID#" 
			data-linkedsubinputid="#local.subInputFieldID#"
			data-ratename="#encodeForHTMLAttribute(local.strRate.rateName)#"
			data-rateinstallments="#local.numPaymentsToUse#"
			data-termstartdate="#dateformat(local.subTermStartDate,'m/d/yyyy')#"
			data-termenddate="#dateformat(local.subTermEndDate,'m/d/yyyy')#"
			data-graceenddate="#dateformat(local.subTermGraceEndDate,'m/d/yyyy')#"
			data-recogstartdate="#dateformat(local.strRate.recogAFStartDate,'m/d/yyyy')#"
			data-recogenddate="#dateformat(local.strRate.recogAFEndDate,'m/d/yyyy')#"
			checked>
	<cfelse>
		<cfquery name="local.strRateFreqCount" dbtype="query" returntype="struct" columnKey="rateID">
			SELECT rateID, MAX(rateFreqOrder) AS freqCount
			FROM [local].thisSub.qryRates
			GROUP BY rateID
		</cfquery>

		<div id="sub#local.thisSub.subscriptionID#_rates" class="mb-3 <cfif arguments.parentSubscriptionID GT 0>pl-5<cfelse>pl-1</cfif>">
			<cfloop query="local.thisSub.qryRates">
				<cfif local.addOnAllowChangePrice OR local.thisSub.qryRates.frontEndAllowChangePrice>
					<cfset local.rateCanEditPrice = true>
				<cfelse>
					<cfset local.rateCanEditPrice = false>
				</cfif>

				<cfset local.numPaymentsToUse = arguments.parentSubscriptionID EQ 0 ? local.thisSub.qryRates.numInstallments : arguments.strParentFreq.numPaymentsToUse>
				<cfset local.rateFreqName = local.thisSub.qryRates.frequencyName>
				<cfset local.rateFreqShortName = local.thisSub.qryRates.frequencyShortName>
				<cfset local.rateNumInstallments = local.thisSub.qryRates.numInstallments>

				<!--- Must Pay on 1st invoice ---->
				<cfif local.thisSub.qryRates.forceUpfront>
					<cfset local.numPaymentsToUse = 1>
					<cfset local.rateFreqName = 'Full'>
					<cfset local.rateFreqShortName = 'F'>
					<cfset local.rateNumInstallments = 1>
				</cfif>

				<cfif local.rateCanEditPrice>
					<cfset local.thisRateAmt = local.thisSub.qryRates.rateAmt>
					<cfif arguments.parentSubscriptionID GT 0 AND arguments.strParentFreq.frequencyShortName NEQ local.thisSub.qryRates.frequencyShortName>
						<cfset local.thisRateToUse = local.thisRateAmt / local.numPaymentsToUse>
					<cfelse>
						<cfset local.thisRateToUse = local.thisRateAmt>
					</cfif>
		
					<cfset local.strRateAmt = { "rateToUse": numberFormat(local.thisRateToUse,"0.00"), "rateTotal": numberFormat(local.thisRateToUse * local.numPaymentsToUse,"0.00") }>
				<cfelse>
					<cfset local.strRateAmt = manageSubscription_getRateToUse(rateAmt=local.thisSub.qryRates.rateAmt, rateInstallments=local.rateNumInstallments, 
							numPaymentsToUse=local.numPaymentsToUse, pcPctOff=local.ratePctOff)>
				</cfif>

				<cfset local.thisRateFreqCount = local.strRateFreqCount[local.thisSub.qryRates.rateID].freqCount>
				<cfset local.thisRateSelected = false>

				<cfif arguments.parentSubscriptionID EQ 0>
					<cfset local.subTermDatesCD = getSubTermDates(termDateRFID=local.thisSub.qryRates.rfid, subTermFlag=local.thisSub.rateTermDateFlag)>
					<cfset local.subTermStartDate = local.subTermDatesCD.subTermStartDate>
					<cfset local.subTermEndDate = local.subTermDatesCD.subTermEndDate>
					<cfset local.subTermGraceEndDate = local.subTermDatesCD.subTermGraceEndDate>
				<cfelse>
					<cfset local.subTermStartDate = local.thisSub.qryRates.termAFStartDate>
					<cfset local.subTermEndDate = local.thisSub.qryRates.termAFEndDate>
					<cfset local.subTermGraceEndDate = local.thisSub.qryRates.graceEndDate>
				</cfif>

				<cfset local.rateWrapperClassNames = "sub#local.thisSub.subscriptionID#_rateWrapper">

				<cfif local.rateCanEditPrice>
					<cfset local.editRateClassNames = local.thisRateFreqCount EQ 1 ? "editRatePrices editRatePrice#local.thisSub.qryRates.rfid#" : "">
					
					<cfif local.thisRateFreqCount GT 1>
						<cfset local.rateWrapperClassNames = "#local.rateWrapperClassNames# pl-3">
						<cfif local.thisSub.qryRates.rateFreqOrder EQ local.thisRateFreqCount>
							<cfset local.rateWrapperClassNames = "#local.rateWrapperClassNames# mb-4">
						</cfif>
					</cfif>

					<cfif local.thisRateFreqCount GT 1 AND local.thisSub.qryRates.rateFreqOrder EQ 1>
						<div class="d-flex mb-2">
							<span class="font-weight-bold">#local.thisSub.qryRates.rateName#</span>
							<cfif local.thisSub.qryRates.frontEndChangePriceMin GT 0 OR local.thisSub.qryRates.frontEndChangePriceMax GT 0>
								<span class="pl-5 text-dim">
									<cfif local.thisSub.qryRates.frontEndChangePriceMin GT 0 AND local.thisSub.qryRates.frontEndChangePriceMax GT 0>
										#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Min - #dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Max
									<cfelseif local.thisSub.qryRates.frontEndChangePriceMin GT 0>
										#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Minimum
									<cfelse>
										#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Maximum
									</cfif>
								</span>
							</cfif>
						</div>
					</cfif>
				</cfif>
				
				<div class="d-flex align-items-center no-gutters mb-2 flex-wrap #local.rateWrapperClassNames#">
					<input type="radio" 
						name="sub#local.thisSub.subscriptionID#_rfid" 
						id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#" 
						value="#local.thisSub.qryRates.rfid#" 
						onchange="chooseRate(this)" 
						data-rateid="#val(local.thisSub.qryRates.rateID)#" 
						data-freqid="#val(local.thisSub.qryRates.frequencyID)#" 
						data-freq="#local.rateFreqShortName#"
						data-freqname="#local.rateFreqName#"
						data-price="#val(local.strRateAmt.rateToUse)#" 
						data-termprice="#val(local.strRateAmt.rateTotal)#" 
						rate-subscriptionid="#local.thisSub.subscriptionID#" 
						data-linkedsubinputid="#local.subInputFieldID#"
						data-ratename="#encodeForHTMLAttribute(local.thisSub.qryRates.rateName)#"
						data-rateinstallments="#local.numPaymentsToUse#"
						data-termstartdate="#dateformat(local.subTermStartDate,'m/d/yyyy')#"
						data-termenddate="#dateformat(local.subTermEndDate,'m/d/yyyy')#"
						data-graceenddate="#dateformat(local.subTermGraceEndDate,'m/d/yyyy')#"
						data-recogstartdate="#dateformat(local.thisSub.qryRates.recogAFStartDate,'m/d/yyyy')#"
						data-recogenddate="#dateformat(local.thisSub.qryRates.recogAFEndDate,'m/d/yyyy')#"
						<cfif arguments.parentSubscriptionID GT 0 and local.thisSubSelected and local.thisSub.qryRates.currentRow eq 1>checked</cfif> <!--- simulating addon selection action by auto selecting first option if addon sub is already selected --->
						class="subRateRadio my-auto mr-2">
					
					<cfif local.rateCanEditPrice>
						<cfif local.thisRateFreqCount EQ 1>
							<div class="col-auto">
								<label for="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#" class="d-inline-block mb-0">
									#local.thisSub.qryRates.rateName#
									<span class="dspRatePrices dspRatePrice#local.thisSub.qryRates.rfid#<cfif local.thisRateSelected> d-none</cfif>">
										<cfif local.strRateAmt.rateToUse GT 0>
											- #dollarFormat(local.strRateAmt.rateToUse)#
										<cfelseif len(arguments.freeRateDisplay)>
											- #arguments.freeRateDisplay#
										</cfif>
										<cfif local.rateFreqShortName NEQ 'F'>
											#local.rateFreqName#
										</cfif>
									</span>
								</label>
							</div>
						</cfif>
						<div class="col-auto pl-3 #local.editRateClassNames#<cfif NOT local.thisRateSelected AND local.thisRateFreqCount EQ 1> d-none</cfif>">
							<div class="input-group flex-nowrap">
								<div class="input-group-prepend">
									<span class="input-group-text px-2">$</span>
								</div>
								<div class="form-label-group flex-grow-1 mb-0">
									<input type="text" 
										id="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#" 
										name="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#" 
										class="form-control editableRateFields" 
										value="#val(local.strRateAmt.rateToUse)#" 
										data-origrateamt="#val(local.strRateAmt.rateToUse)#"
										data-subscriptionid="#local.thisSub.subscriptionID#" 
										data-rfid="#local.thisSub.qryRates.rfid#"
										data-ratemin="#val(local.thisSub.qryRates.frontEndChangePriceMin)#"
										data-ratemax="#val(local.thisSub.qryRates.frontEndChangePriceMax)#"
										data-rateinstallments="#local.rateNumInstallments#"
										onkeyup="validateRateAmt(this);"
										onblur="onBlurRateAmt(this);"
										onfocus="onFocusEditRateField(this)"
										size="10">
									<label for="newRateTotal_#local.thisSub.subscriptionID#_#local.thisSub.qryRates.rfid#">Enter Amount</label>
								</div>
							</div>
							<cfif local.rateFreqShortName NEQ 'F'>
								<div class="font-size-sm text-dim text-center">
									Total: <span id="totalPrice#local.thisSub.qryRates.rfid#">#dollarFormat(local.strRateAmt.rateTotal)#</span>
								</div>
							</cfif>
						</div>
						<cfif local.rateFreqShortName NEQ 'F'>
							<div class="col-auto pl-2 #local.editRateClassNames#<cfif NOT local.thisRateSelected AND local.thisRateFreqCount EQ 1> d-none</cfif>">
								#local.rateFreqName#
							</div>
						</cfif>
						<cfif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0 OR val(local.thisSub.qryRates.frontEndChangePriceMax) GT 0>
							<div class="col-auto pl-5 #local.editRateClassNames#<cfif NOT local.thisRateSelected AND local.thisRateFreqCount EQ 1> d-none</cfif>">
								<cfif local.thisRateFreqCount EQ 1>
									<span class="editRatePriceRange text-dim">
										<cfif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0 AND val(local.thisSub.qryRates.frontEndChangePriceMax) GT 0>
											#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Min - #dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Max
										<cfelseif val(local.thisSub.qryRates.frontEndChangePriceMin) GT 0>
											#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMin)# Minimum
										<cfelse>
											#dollarFormat(local.thisSub.qryRates.frontEndChangePriceMax)# Maximum
										</cfif>
									</span>
								</cfif>
								<span class="editRatePriceRangeErr d-none font-weight-bold text-danger"></span>
							</div>
						</cfif>
					<cfelse>
						<div class="col">
							<label for="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#" class="d-inline-block mb-0">
								#local.thisSub.qryRates.rateName#
								<span id="sub#local.thisSub.subscriptionID#_rfid_#local.thisSub.qryRates.rfid#_rateAmtDisp" class="subRateAmtDisp sub#local.thisSub.subscriptionID#RateAmtDisp<cfif local.offerAsFreeAddOnSub> d-none</cfif>">
									<cfif local.strRateAmt.rateToUse GT 0>
										- #dollarFormat(local.strRateAmt.rateToUse)#
									<cfelseif len(arguments.freeRateDisplay)>
										- #arguments.freeRateDisplay#
									</cfif>
									<cfif local.rateFreqShortName NEQ 'F'>
										#local.rateFreqName#
									</cfif>
								</span>
							</label>
						</div>
					</cfif>
				</div>
			</cfloop>
		</div>
	</cfif>
	
	<!--- Subscription AddOns --->
	<cfif local.hasSubAddOns AND local.thisSub.currAddOnRecursionLevel + 1 EQ local.thisSub.maxAddOnRecursionLevel>
		<div id="sub#local.thisSub.subscriptionID#_addons" class="pt-2 d-none">
			#manageSubscription_renderAddOnForm(strSubAddOns=local.thisSub.addOns, subscriptionID=local.thisSub.subscriptionID, strParentFreq=arguments.strParentFreq,
				freeRateDisplay=arguments.freeRateDisplay, strEditSubs=arguments.strEditSubs, inlineAddOn=true, listSubscribed=arguments.listSubscribed)#
		</div>
	</cfif>
</cfloop>

<cfif structCount(arguments.strAddOn)>
	<input type="hidden" name="addOn#arguments.strAddOn.addOnID#_freeSubs" id="addOn#arguments.strAddOn.addOnID#_freeSubs" value="#local.freeSubIDList#">
</cfif>
</cfoutput>