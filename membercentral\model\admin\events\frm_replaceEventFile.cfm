<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.replaceEventFileHead">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/plupload.full.min.js"></script>
	<script type="text/javascript" src="/assets/common/javascript/jQueryAddons/plupload/3.1.2/jquery.plupload.queue/jquery.plupload.queue.min.js"></script>
	<script type="text/javascript">
		const #ToScript(application.MCEnvironment,"mc_environment")#
		const #ToScript(local.eventDocumentID,"ev_eventdocumentid")#
		const #ToScript(local.qryEventDocumentInfo.docTitle,"ev_doctitle")#
		const #ToScript(local.documentID,"ev_olddocumentid")#
		const #ToScript(local.qryEventDocumentInfo.eventID,"ev_eventid")#
		const #ToScript(arguments.event.getValue('mc_siteInfo.sitecode'),"ev_sitecode")#
		const #ToScript(arguments.event.getValue('mc_siteInfo.siteid'),"ev_siteid")#
		const #ToScript(local.allowedMimeTypes,"ev_allowedmimetypes")#
		let ev_documentversionid = 0;
		
		$(function() {
			top.MCModalUtils.buildFooter({
				classlist: 'd-flex',
				showclose: true,
				buttons: [ 
					{ class: "btn-success ml-auto py-1", clickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnUploadEventFile").click', label: 'Upload', name: 'btnUploadEventFile', id: 'btnUploadEventFile' },
				]
			});
			
			initEventFileReplace(JSON.parse('#local.fileUploadSettingsJSON#'));
		});

		function initEventFileReplace(obj) {
			let uploader = new plupload.Uploader({
				runtimes : 'html5',
				browse_button : 'btnReplaceEventFile',
				container: $('##replaceEventFileBtnContainer')[0],
				drop_element: $('##EventFileContainer')[0],
				url : obj.url,
				file_data_name: 'filename',
				multi_selection: false,
				multipart: true,
				multipart_params: {
					eventDocumentID: ev_eventdocumentid,
					oldDocumentID: ev_olddocumentid
				},
				filters : {
					max_file_size : '750mb',
					mime_types: [ {title:"Replace File", extensions:ev_allowedmimetypes} ]
				},
				init : {
					PostInit: function() {
						$('##btnUploadEventFile').prop('disabled',true);

						// Add click handler for the upload button
						$('##btnUploadEventFile').click(function(e) {
							e.preventDefault();
							if (uploader.files.length > 0 && !$('##btnUploadEventFile').prop('disabled')) 
								uploader.start();
						});
					},
					FilesAdded: function(up, files) {
						plupload.each(files, function(file) {
							$('##newEventFile').html('<div id="' + file.id + '"><b>' + file.name + ' (' + plupload.formatSize(file.size) + ')</b></div>');
						});
						$('##btnUploadEventFile').prop('disabled',false);
					},
					FileUploaded: function(up, file, response) {
						try {
							let retRegex = /[\r\n]/ig;
							let cleanResponse = response.response.replaceAll(retRegex,'');
							let result = JSON.parse(cleanResponse);
							if (result.SUCCESS) {
								ev_documentversionid = result.DOCUMENTVERSIONID || 0;
							} else {
								$('##errReplaceEventFile').removeClass('d-none').html('Upload failed: ' + (result.MESSAGE || 'Unknown error'));
							}
						} catch (e) {
							$('##errReplaceEventFile').removeClass('d-none').html('Upload failed: Invalid response from server - ' + e.message);
						}
					},
					UploadComplete: function(up, files) {
						let replaceFileResult = function(r) {
							if (r.success && r.success.toLowerCase() == 'true') {
								top.reloadDocumentsTable();
								top.MCModalUtils.hideModal();
							} else {
								alert('We were unable to replace the file. Try again.');
							}
						};

						$('##replaceFileLoading').html(mca_getLoadingHTML()).removeClass('d-none');
						$('##frmReplaceEventFile').addClass('d-none');

						let objParams = {
							eventDocumentID: ev_eventdocumentid,
							oldDocumentID: ev_olddocumentid,
							newDocumentID: ev_olddocumentid, // Same document ID since we're using insertVersion
							eventID: ev_eventid
						};
						TS_AJX_SYNC('ADMINEVENT','replaceEventFileProcess',objParams,replaceFileResult,replaceFileResult,30000,replaceFileResult);
					},
					Error: function(up, error) {
						$('##errReplaceEventFile').removeClass('d-none').html('Upload error: ' + error.message + ' (Code: ' + error.code + ')');
					}
				}
			});

			uploader.bind('Error', function(up, error) {
				$('##errReplaceEventFile').removeClass('d-none').html('Initialization error: ' + error.message + ' (Code: ' + error.code + ')');
			});

			try {
				uploader.init();
			} catch (e) {
				$('##errReplaceEventFile').removeClass('d-none').html('Failed to initialize file uploader: ' + e.message);
			}
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.replaceEventFileHead#">

<cfoutput>
<form id="frmReplaceEventFile" name="frmReplaceEventFile" class="p-3">
	<div id="EventFileContainer" class="dropzone">
		<div class="dz-preview dz-preview-single"></div>
		<div class="dz-default dz-message">
			<div id="newEventFile"></div>
			<div id="replaceEventFileBtnContainer"><button type="button" id="btnReplaceEventFile" class="btn btn-sm btn-secondary"><i class="fa-solid fa-cloud-arrow-up"></i> Choose File to Upload</button></div>
			<div class="mt-2 small">or drag and drop them here</div>
		</div>
	</div>
	<div id="errReplaceEventFile" class="alert alert-danger d-none my-4"></div>
	<div class="alert d-flex align-items-center pl-2 align-content-center alert-info mt-3" role="alert">
		<span class="font-size-lg d-block d-40 mr-2 text-center">
			<i class="fa-solid fa-circle-info"></i>
		</span>
		<span>
			<strong class="d-block">IMPORTANT</strong> The file's Title, Description, Grouping, and Author will remain unchanged. Only the file itself will be replaced.
		</span>
	</div>
	<button type="button" name="btnUploadEventFile" id="btnUploadEventFile" class="d-none"></button>
</form>
<div id="replaceFileLoading" class="d-none p-3"></div>
</cfoutput>
