ALTER PROC dbo.acbar_createInsertionOrder
@advertiserMemberID int,
@rateCardConfigID int,
@runDatesXML xml,
@PLJNumber varchar(200),
@referenceInfo varchar(500),
@statsSessionID int,
@enteredByMemberID int,
@orderID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @siteID int, @invoiceID int, @revenueGLAccountID int, @invoiceProfileID int, @invoiceNumber varchar(19),
		@dateDue datetime, @nowDate datetime = GETDATE(), @runDate datetime, @price decimal(18,2), @transactionID int,
		@rateCardName varchar(250), @configName varchar(250), @salesDetail varchar(max), @xmlSchedule xml;
	DECLARE @tblRuns TABLE (rowID int IDENTITY(1,1), runDate datetime, price decimal(18,2));

	SELECT @siteID = siteID, @orgID = orgID
	FROM membercentral.dbo.sites
	WHERE siteCode = 'ACBAR';

	SELECT @revenueGLAccountID = cc.revenueGLAccountID, @rateCardName = c.rateCardName, @configName = cc.configName
	FROM dbo.ACBAR_AdInvoicing_rateCardConfigs AS cc
	INNER JOIN dbo.ACBAR_AdInvoicing_rateCards AS c ON c.rateCardID = cc.rateCardID
	WHERE cc.rateCardConfigID = @rateCardConfigID;
	
	SELECT @invoiceProfileID = profileID
	FROM membercentral.dbo.fn_tr_getInvoiceProfileForGL(@revenueGLAccountID);

	SELECT @dateDue = MIN(x.runDate.value('@date', 'DATE'))
	FROM @runDatesXML.nodes('/runs/run') AS x(runDate);

	INSERT INTO @tblRuns (runDate, price)
	SELECT x.runDate.value('@date', 'DATE'),
		x.runDate.value('@price', 'DECIMAL(18,2)')
	FROM @runDatesXML.nodes('/runs/run') AS x(runDate);

	BEGIN TRAN;
		
		EXEC membercentral.dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@enteredByMemberID,
			@assignedToMemberID=@advertiserMemberID, @dateBilled=@nowDate, @dateDue=@dateDue,
			@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

		SELECT @runDate = MIN(runDate) FROM @tblRuns;
		WHILE @runDate IS NOT NULL BEGIN
			SELECT @price = price FROM @tblRuns WHERE runDate = @runDate;
			SET @transactionID = NULL;
			SET @salesDetail = @PLJNumber + ' - ' + @rateCardName + ' - ' + @configName + ' - ' + convert(varchar(10), @runDate, 101);
			IF LEN(ISNULL(@referenceInfo,'')) > 0
				SET @salesDetail = @salesDetail + ' - ' + @referenceInfo;
			SET @xmlSchedule = '<rows><row amt="' + CAST(@price AS VARCHAR(50)) + '" dt="' + convert(varchar(10), @runDate, 101) + '" /></rows>';

			EXEC membercentral.dbo.tr_createTransaction_sale @ownedByOrgID=@orgID, @recordedOnSiteID=@siteID,
				@assignedToMemberID=@advertiserMemberID, @recordedByMemberID=@enteredByMemberID,
				@statsSessionID=@statsSessionID, @status='Active', @detail=@salesDetail, @parentTransactionID=NULL,
				@amount=@price, @transactionDate=@nowDate, @creditGLAccountID=@revenueGLAccountID, @invoiceID=@invoiceID, @stateIDForTax=NULL,
				@zipForTax=NULL, @taxAmount=0, @bypassTax=0, @bypassInvoiceMessage=0,
				@bypassAccrual=0, @xmlSchedule=@xmlSchedule, @transactionID=@transactionID OUTPUT;

			SELECT @runDate = MIN(runDate) FROM @tblRuns WHERE runDate > @runDate;
		END

		EXEC membercentral.dbo.tr_closeInvoice @orgID=@orgID, @enteredByMemberID=@enteredByMemberID, @invoiceIDList=@invoiceID;

		INSERT INTO dbo.ACBAR_AdInvoicing_insertionOrders (PLJNumber, advertiserMemberID, rateCardConfigID,
			runDatesXML, referenceInfo, invoiceID, enteredByMemberID, createdDate)
		VALUES (@PLJNumber, @advertiserMemberID, @rateCardConfigID, @runDatesXML,
			NULLIF(@referenceInfo,''), @invoiceID, @enteredByMemberID, GETDATE());
		SELECT @orderID = SCOPE_IDENTITY();

	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
