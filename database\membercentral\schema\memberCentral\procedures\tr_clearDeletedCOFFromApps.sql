ALTER PROC dbo.tr_clearDeletedCOFFromApps
@itemCount int OUTPUT
AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;
	CREATE TABLE #tmpAuditLog (siteID int, orgID int, auditCode varchar(10), msg varchar(max));

	declare @orgID int, @sysMemberID int, @itemCountInvoices int, @itemCountSubs int, @itemCountContrib int, @itemCountTasks int,
		@itemCountMPP int, @payProfileIDList varchar(max);
	select @sysMemberID = dbo.fn_ams_getMCSystemMemberID();

	-- invoices tied to deleted COF
	/* this query was changed to split the status into separate queries and to include payprofileID not null to 
	take advantage of performance benefits of these indexes: 
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusD,
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusI
	FIX_tr_invoices__payProfileID__Includes1__payProfileIDNotNull 
	*/
	declare @tblInvoices TABLE (invoiceID int PRIMARY KEY, orgID int);

	INSERT INTO @tblInvoices (invoiceID)
	select i.invoiceID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.tr_invoices as i on i.payProfileID = mpp.payProfileID and i.payProfileID is not null
	where mpp.status = 'D'
		union all
	select i.invoiceID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.tr_invoices as i on i.payProfileID = mpp.payProfileID and i.payProfileID is not null
	where mpp.status = 'I';

	update tbl
	set tbl.orgID = i.orgID
	from @tblInvoices as tbl
	inner join dbo.tr_invoices as i on i.invoiceID = tbl.invoiceID;

	SET @itemCountInvoices = @@ROWCOUNT;

	IF @itemCountInvoices > 0 BEGIN
		INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
		SELECT s.siteID, s.orgID, 'INV', 'Pay Profile ' + mpp.detail + ' removed from Invoice ' + i.fullInvoiceNumber
		FROM dbo.tr_invoices as i
		INNER JOIN @tblInvoices as tmp on tmp.invoiceID = i.invoiceID
		INNER JOIN dbo.organizations as o on o.orgID = i.orgID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = i.MPProfileID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID;

		-- this loops by orgID because the update to tr_invoices kicks off its trigger and then condition processing which has to be org specific
		select @orgID = min(orgID) from @tblInvoices;
		while @orgID is not null begin
			update i
			set i.payProfileID = null,
				i.MPProfileID = null,
				i.payProcessFee = 0,
				i.processFeePercent = null
			from dbo.tr_invoices as i
			inner join @tblInvoices as tmp on tmp.orgID = @orgID and tmp.invoiceID = i.invoiceID 
			where i.orgID = @orgID
			AND i.payProfileID is not null;

			select @orgID = min(orgID) from @tblInvoices where orgID > @orgID;
		END
	END

	-- subscriptions tied to deleted COF
	/* this query was changed to split the status into separate queries and to include payprofileID not null to 
	take advantage of performance benefits of these indexes: 
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusD,
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusI
	FIX_sub_subscribers__payProfileID__Includes1__payProfileIDNotNull 
	*/
	declare @tblSubs TABLE (subscriberID int PRIMARY KEY, payProfileID int, orgID int);

	INSERT INTO @tblSubs (subscriberID, payProfileID, orgID)
	select ss.subscriberID, mpp.payProfileID, ss.orgID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.sub_subscribers as ss on ss.payProfileID = mpp.payProfileID and ss.payProfileID is not null
	where mpp.status = 'I'
		union all
	select ss.subscriberID, mpp.payProfileID, ss.orgID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.sub_subscribers as ss on ss.payProfileID = mpp.payProfileID and ss.payProfileID is not null
	where mpp.status = 'D';

	SET @itemCountSubs = @@ROWCOUNT;

	IF @itemCountSubs > 0 BEGIN
		update s
		set s.payProfileID = null,
			s.MPProfileID = null,
			s.payProcessFee = 0,
			s.processFeePercent = null
		from dbo.sub_subscribers as s
		inner join @tblSubs as tmp on tmp.subscriberID = s.subscriberID
		where s.payProfileID is not null;

		INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
		SELECT s.siteID, s.orgID, 'SUBS', 'Pay Profile ' + mpp.detail + ' removed from ' + 'Subscription [' + sub.subscriptionName + '] (SubscriberID: ' + CAST(ss.subscriberID AS varchar(10)) + ')'
		FROM @tblSubs as tmp
		INNER JOIN dbo.sub_subscribers as ss on ss.subscriberID = tmp.subscriberID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = tmp.payProfileID
		INNER JOIN dbo.sub_subscriptions as sub on sub.subscriptionID = ss.subscriptionID
		INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = ss.MPProfileID
		INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID;

		-- trigger reprocessing of credit card expiration conditions (if limiting to subscription)
		SELECT @orgID = MIN(orgID) FROM @tblSubs;
		WHILE @orgID IS NOT NULL BEGIN
			SET @payProfileIDList = NULL;

			SELECT @payProfileIDList = COALESCE(@payProfileIDList + ',', '') + cast(payProfileID as varchar(20))
			FROM @tblSubs
			WHERE orgID = @orgID
			GROUP BY payProfileID;

			EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittosub';

			SELECT @orgID = MIN(orgID) FROM @tblSubs WHERE orgID > @orgID;
		END
	END


	-- contributions tied to deleted COF
	/* this query was changed to split the status into separate queries to take advantage of performance benefits of these indexes: 
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusD,
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusI
	*/
	declare @tblContrib TABLE (cppid int PRIMARY KEY, payProfileID int, orgID int);

	INSERT INTO @tblContrib (cppid, payProfileID, orgID)
	select cpp.contributionPayProfileID, mpp.payProfileID, m.orgID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.cp_contributionPayProfiles as cpp on cpp.payProfileID = mpp.payProfileID
	inner join dbo.ams_members as m on m.memberID = mpp.memberID
	where mpp.status = 'I'
		union all
	select cpp.contributionPayProfileID, mpp.payProfileID, m.orgID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.cp_contributionPayProfiles as cpp on cpp.payProfileID = mpp.payProfileID
	inner join dbo.ams_members as m on m.memberID = mpp.memberID
	where mpp.status = 'D';

	SET @itemCountContrib = @@ROWCOUNT;

	IF @itemCountContrib > 0 BEGIN
		INSERT INTO #tmpAuditLog (siteID, orgID, auditCode, msg)
		SELECT DISTINCT s.siteID, s.orgID, 'CP', 
			'Pay Profile ' + mpp.detail + ' removed from Contribution Program [' 
				+ cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID: ' + CAST(c.contributionID AS varchar(10)) + ')'
		FROM dbo.cp_contributionPayProfiles AS cpp 
		INNER JOIN @tblContrib as tmp on tmp.cppid = cpp.contributionPayProfileID
		INNER JOIN dbo.cp_contributions AS c ON c.contributionID = cpp.contributionID
		INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
		INNER JOIN dbo.cms_siteResources AS sr ON sr.siteResourceID = cp.siteResourceID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = cpp.payProfileID
		INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
		LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID;

		delete cpp
		from dbo.cp_contributionPayProfiles as cpp
		inner join @tblContrib as tmp on tmp.cppid = cpp.contributionPayProfileID;

		-- trigger reprocessing of credit card expiration conditions (if limiting to contributions)
		SELECT @orgID = MIN(orgID) FROM @tblContrib;
		WHILE @orgID IS NOT NULL BEGIN
			SET @payProfileIDList = NULL;

			SELECT @payProfileIDList = COALESCE(@payProfileIDList + ',', '') + cast(payProfileID as varchar(20))
			FROM @tblContrib
			WHERE orgID = @orgID
			GROUP BY payProfileID;

			EXEC dbo.tr_reprocessCCExpConditions @orgID=@orgID, @payProfileIDList=@payProfileIDList, @lookupMode='limittocp';

			SELECT @orgID = MIN(orgID) FROM @tblContrib WHERE orgID > @orgID;
		END
	END

	-- tasks tied to deleted COF
	/* this query was changed to split the status into separate queries and to include payprofileID not null to 
	take advantage of performance benefits of these indexes: 
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusD,
	FIX_ams_memberPaymentProfiles__status__Includes1__StatusI
	FIX_tasks_tasks__payProfileID__Includes1__payProfileIDNotNull 
	*/
	declare @tblTasks TABLE (taskID int PRIMARY KEY);

	INSERT INTO @tblTasks (taskID)
	select t.taskID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.tasks_tasks as t on t.payProfileID = mpp.payProfileID and t.payProfileID is not null
	where mpp.status = 'I'
		union all
	select t.taskID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.tasks_tasks as t on t.payProfileID = mpp.payProfileID and t.payProfileID is not null
	where mpp.status = 'D';
	SET @itemCountTasks = @@ROWCOUNT;

	IF @itemCountTasks > 0
		update t
		set t.payProfileID = null,
			t.MPProfileID = null
		from dbo.tasks_tasks as t
		inner join @tblTasks as tmp on tmp.taskID = t.taskID
		where t.payProfileID is not null;


	-- active COF tied to merged members
	/* this query was changed to to take advantage of performance benefits of these indexes: 
	FIX_ams_memberPaymentProfiles__status__Includes2__StatusA
	*/
	declare @tblMPP TABLE (payProfileID int PRIMARY KEY);
	declare @payProfileID int;

	insert into @tblMPP (payProfileID)
	select mpp.payProfileID
	from dbo.ams_memberPaymentProfiles as mpp
	inner join dbo.ams_members as m on m.memberID = mpp.memberID and m.memberID <> m.activeMemberID
	where mpp.status = 'A';
	SET @itemCountMPP = @@ROWCOUNT;

	IF @itemCountMPP > 0 BEGIN
		SELECT @payProfileID = MIN(payProfileID) FROM @tblMPP;
		WHILE @payProfileID IS NOT NULL BEGIN
			EXEC dbo.ams_deleteCardOnFile @payProfileID=@payProfileID, @recordedByMemberID=@sysMemberID;
			SELECT @payProfileID = MIN(payProfileID) FROM @tblMPP WHERE payProfileID > @payProfileID;
		END
	END
	
	SET @itemCount = @itemCountInvoices + @itemCountSubs + @itemCountContrib + @itemCountTasks + @itemCountMPP;
	-- audit log
	IF EXISTS (SELECT 1 FROM #tmpAuditLog) BEGIN
		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT '{ "c":"auditLog", "d": {
			"AUDITCODE":"' + auditCode + '",
			"ORGID":' + cast(orgID as varchar(10)) + ',
			"SITEID":' + cast(siteID as varchar(10)) + ',
			"ACTORMEMBERID":' + cast(@sysMemberID as varchar(20)) + ',
			"ACTIONDATE":"' + convert(varchar(20),GETDATE(),120) + '",
			"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }'
		FROM #tmpAuditLog;
	END


	IF OBJECT_ID('tempdb..#tmpAuditLog') IS NOT NULL 
		DROP TABLE #tmpAuditLog;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
