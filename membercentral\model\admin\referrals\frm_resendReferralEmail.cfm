<cfparam name="local.msgText" default="" />
<cfsavecontent variable="local.resendReferralFormJS">
<cfoutput>
	<style type="text/css">
	.saveerr { color:##f00; font-weight:bold; padding: 0 10px 0 0; }
	.saveok { color:##090; font-weight:normal; padding: 0 10px 0 0; }
	##resendFormTbl th, ##resendFormTbl td {  height:30px; }
	##resendFormTbl th.longDesc, ##resendFormTbl td.longDesc {  height:78px; }
	</style>
	<script>
	$(document).ready(function(){
		$('##btnResendEmail').click(function(){
			var errorMsg = "";
			var strURL = "#local.formLink#";
			var referralID = document.getElementById('referralID').value;
			var _selectedOptions = [];
			var _sendToClient = false;
			var _attorneyMemberIds = "";
			var _sendToAllAttorneys = false;
			$('##saveMsg').html('');
			$('##saveArea').removeClass('alert-success alert-danger').addClass('d-none');
			$('##sectionIMG').removeClass('fa-solid fa-circle-check text-green');
			
			$("##btnResendEmail").attr("disabled","disabled");

			if( $('##resendEmailSelect :selected').length > 0){
				var selectednumbers = [];
				$('##resendEmailSelect :selected').each(function(i, selected) {
					_thisVal =$(selected).val();
					
					if(_thisVal.indexOf('client') != -1){
						_sendToClient = true;
					}
					if(_thisVal.indexOf('AllAttorneys') != -1){
						_sendToAllAttorneys = true;
					}
					if(_thisVal.indexOf('memberid') != -1){
						_memberId = _thisVal.split('_')[1];
						if(!isNaN(_memberId) && _memberId != '') {
							_attorneyMemberIds = _attorneyMemberIds + ',' + _memberId;
						}
					}
					_selectedOptions[i] = $(selected).val();
				});				
				_attorneyMemberIds = _attorneyMemberIds.replace(/^,/, '');

				top.$('##btnMCModalSave').prop('disabled',true).html('Please wait...');
				
				$.ajax({
					url: strURL,
					data:{ 'clientid':#local.clientId#, 'clientreferralid':#local.clientreferralid#, 'sendToClient':_sendToClient, 'attorneyMemberIds':_attorneyMemberIds, 'sendToAllAttorneys':_sendToAllAttorneys },
					type: 'POST',
					dataType: 'json',
					success: function(response){
						if(response.success){
							$('##sectionIMG').removeClass('fa-solid fa-circle-exclamation text-danger').addClass('fa-solid fa-circle-check text-green');
							$('##saveMsg').html(response.msg);
							$('##saveArea').removeClass('alert-danger d-none').addClass('alert-success');
							top.$('##btnMCModalSave').prop('disabled',false).html('Send');
						} else {
							alert(response.msg);
						}
					},
					error: function(ErrorMsg){
						alert('We were unable to resend the referral emails to selected user(s).');
						top.$('##btnMCModalSave').prop('disabled',false).html('Send');
					}
				})
			
			} else {
				$('##sectionIMG').removeClass('fa-solid fa-circle-check text-green');
				$('##saveMsg').html('Please select at least one option.');
				$('##saveArea').removeClass('alert-success d-none').addClass('alert-danger');
				top.$('##btnMCModalSave').prop('disabled',false).html('Send');
			}
		});	

		top.$('##MCModalLabel').html('#encodeForJavaScript(local.pageTitle)#');
		<cfif local.clientEmail or local.qryGetAttorneysReferred.recordCount>
			top.$('##btnMCModalSave').removeClass('d-none');
			mca_setupSelect2($('##frmResendEmail'));
		</cfif>
		
	});	
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.resendReferralFormJS)#" />
<cfif arguments.event.valueExists('msg')>
	<cfswitch expression="#arguments.event.getValue('msg')#">
		<cfcase value="1"><cfset local.msgText = "Referral emails sent to selected user(s)" /></cfcase>
	</cfswitch>		
</cfif>

<cfoutput>
<form name="frmResendEmail" class="p-2"  id="frmResendEmail" onsubmit="return false;">
	<input type="hidden" name="referralID"  id="referralID" value="#this.referralID#" />	
	
	<div class="alert alert-dismissible d-none" role="alert" id="saveArea">		
		<i class="" id="sectionIMG"></i> <span id="saveMsg"></span>	
		<button type="button" class="close" onClick="$('##saveArea').addClass('d-none');" aria-label="Close">
			<span aria-hidden="true">&times;</span>
		</button>
	</div>
	
	<cfif local.clientEmail or local.qryGetAttorneysReferred.recordCount>
		<div class="form-row">
				<div class="col">
					<div class="form-label-group">
						<select name="resendEmailSelect" id="resendEmailSelect" multiple="yes" class="custom-select" data-toggle="custom-select2" size="5" placeholder="Choose options below">
							<cfif local.clientEmail>
								<option value="client_#local.clientId#">Client </option>
							</cfif>
							<cfloop query="local.qryGetAttorneysReferred">
								<option value="memberid_#local.qryGetAttorneysReferred.memberId#">
									#local.qryGetAttorneysReferred.firstName# #local.qryGetAttorneysReferred.middleName# #local.qryGetAttorneysReferred.lastName#
								</option>
							</cfloop>
							<cfif local.qryGetAttorneysReferred.recordCount GT 0>
								<option value="AllAttorneys">All Attorneys Referred</option>
							</cfif>
						</select>
						<label for="resendEmailSelect">Resend Email to</label>
					</div>
				</div>
		</div>		
		<button id="btnResendEmail" id="btnResendEmail" type="button" class="d-none">Send</button>	
	<cfelse>
		No existing e-mail address on file. <br /><br />	
	</cfif>
	<br />
</form>	

<script type="text/javascript">
	document.getElementById('saveArea').style.display = '';
</script>
</cfoutput>