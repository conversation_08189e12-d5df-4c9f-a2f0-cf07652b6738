
<cfset local.creditTotal = 0>
<cfif arguments.strcredit.qrywddxcredits.recordcount>
    <cfset local.creditTotal = arraySum(valuearray(arguments.strcredit.qrywddxcredits.numcredits))>
</cfif>
<cfoutput>
	<html>
	<head>
		<title>Official Certificate of Attendance</title>
		<style>
			@font-face { font-family:Tahoma;panose-1:2 11 6 4 3 5 4 4 2 4;}
			@font-face { font-family:Verdana;panose-1:2 11 6 4 3 5 4 4 2 4; }
			.borders{position:relative; z-index:10; margin:4px;  border:2px solid ##740730; }
			p.<PERSON>, td.<PERSON>, li.<PERSON>, div.<PERSON><PERSON><PERSON> { margin:0in; margin-bottom:.0001pt;font-size:12.0pt;font-family:Verdana; }
			.Section1{ size:8.5in 11.0in; border:8px solid ##1e7cab; padding:15px; margin-right:30px;
			background-image: url(#local.assetsSiteURL#CCCBA_LOGO_round_90_years.png); background-position:center;background-repeat: no-repeat;}
			div.Section1{ page:Section1; }
			tr { vertical-align:top; }
			div.MsoNormal, td.MsoNormal { font-size:8.0pt; font-family:Verdana; }
			table { page-break-inside:auto }
			tr    { page-break-inside:avoid; page-break-after:auto }
			.headWrapTr td div{	
					text-align:left;
					padding-top: 8px;
					font-family: Verdana;
				}
			.midheading{
				color:##1e7cab;
			}
			.text-center{
				text-align:center;
			}
			.text-right{
				text-align:right;
			}
			.text-left{
				text-align:left;
			}
			div.MsoNormal, td.MsoNormal { font-size:7.2pt; font-family:Verdana; }
			body { height:11in;width:8.5in;padding:.2in;}
			
		</style>
	</head>
	
	<body>
		<div class="Section1">	
			<p  align=center style="margin:0px;"><img src="#local.assetsSiteURL#certHeading.jpg" height="50px"></p>
			<table width="100%" cellpadding="2" cellspacing="0" style="margin-bottom: 0px; " >
						
				<tr class="headWrapTr">
					<td class="text-left" style="font-size:8pt;">
						<b>Please keep this copy for your record</b>
					</td>
					<td class="text-center midheading">
						<b>for California MCLE</b>
					</td>
					<td class="text-right" style="font-size:8pt;">
							<b>PROVIDER ##393</b> <br/> 
							Contra Costa County Bar Association <br/>2300 Clayton Rd., 520, Concord, CA 94520 <br/> 
							(************* <br/>	
					</td>
				</tr>
			</table>
			<br/>
			<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Certificate of Attendance:</b> 
				This is an Official Certificate of Attendance which is serialized and can be verified by SeminarWeb. The certificate includes a list of continuing education authorities which have approved this program for credit. This information may be useful for presumptive approval in other jurisdictions, however, this Certificate may or may not be accepted by other continuing education authorities in other jurisdictions. Please consult the specific rules of your continuing education authority for clarification. SeminarWeb cannot provide additional information or file for certification in other continuing education jurisdictions once a program has completed.You are responsible for keeping a copy of the materials and Certificate of Attendance for your records and for following the continuing education guidelines with your continuing education authority.
			</span></p>
			<p class=MsoNormal><span style='font-size:8pt;'>&nbsp;</span></p>
			<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Registrar Statement of Authenticity:</b> 
				This certificate is submitted to <b>#UCASE(arguments.enrollmentInfo.fullname)#</b> because SeminarWeb can certify that the participant attended this program for the specified duration of the program (minutes are shown), and <b>#UCASE(arguments.enrollmentInfo.fullname)#</b> additionally attests to his/her/their attendance by his/her/their signature. This Certificate of Attendance is NOT provided to participants who did not attend the program; this policy is strictly enforced. Additional materials and evaluations may be obtained by calling SeminarWeb.
			</span></p>
			<p class=MsoNormal><span style='font-size:8pt;'>&nbsp;</span></p>
			<p class=MsoNormal><span style='font-size:8pt;line-height:9pt;'><b>Program Status by Continuing Education Accrediting Authority: (as of #dateformat(now(),"mmmm d, yyyy")#)</b><br/>
				Credit Approved: <cfif arguments.strSAC.qryApproved.recordcount><cfloop query="arguments.strSAC.qryApproved"><nobr>#arguments.strSAC.qryApproved.authoritycode#<cfif len(arguments.strSAC.qryApproved.courseApproval)> - #arguments.strSAC.qryApproved.courseApproval#</cfif><cfif len(arguments.strSAC.qryApproved.credits)> - #arguments.strSAC.qryApproved.credits#</cfif></nobr><cfif arguments.strSAC.qryApproved.currentrow neq arguments.strSAC.qryApproved.recordcount>; </cfif></cfloop><cfelse>(none)</cfif><br/>
				Credit Pending: <cfif arguments.strSAC.qryPending.recordcount><cfloop query="arguments.strSAC.qryPending">#arguments.strSAC.qryPending.authoritycode#<cfif arguments.strSAC.qryPending.currentrow neq arguments.strSAC.qryPending.recordcount>, </cfif></cfloop><cfelse>(none)</cfif><br/>
			</span></p>
			<br/>
			<table width="100%" cellpadding="1" cellspacing="0">
				<tr>
					<td class=MsoNormal><b>Title:</b>#arguments.enrollmentInfo.contentName#</td>
				</tr>
				<cfif arraylen(arguments.arrSpeakers)>
					<tr>
						<td class=MsoNormal><b>Speaker(s):</b>#arrayToList(arguments.arrSpeakers,"; ")#</td>
					</tr>
				</cfif>
				<tr><td> &nbsp;</td></tr>
				<tr>
					<td class=MsoNormal><b>Date Completed:</b>#DateFormat(arguments.enrollmentInfo.dateCompleted,"mm/dd/yyyy")# at #timeformat(arguments.enrollmentInfo.datecompleted,"h:mm tt")#</td>
					</tr>
				<tr>
					<td class=MsoNormal><b>Completion Time:</b> #val(arguments.enrollmentInfo.calcTimeSpent)# minutes</td>
					
				</tr>
				<tr><td> &nbsp;</td></tr>
				<tr>
					<td class=MsoNormal><b>Format:</b>Self-Paced Online</td>
				</tr>
				<tr>
					<td class=MsoNormal><b>Total Eligible California MCLE Credit Hours:</b>#local.creditTotal#</td>
				</tr>
				<tr><td> &nbsp;</td></tr>
				<tr><td class="MsoNormal" > To Be Completed by the Attorney after Participation in the Above-Named Activity</td></tr>
				<tr><td> &nbsp;</td></tr>
				<tr><td class="MsoNormal" > By signing below, I certify that I participated in the activity described above and am entitled to claim the following California <b>MCLE Credit Hours</b>:<br/>
					<cfif arguments.strcredit.qrywddxcredits.recordcount>
						<cfloop query="arguments.strcredit.qrywddxcredits">
							<span>#arguments.strcredit.qrywddxcredits.numcredits# - #arguments.strcredit.qrywddxcredits.displayname#</span> <br>
						</cfloop>
					</cfif>
				</td></tr>
				<tr><td> &nbsp;</td></tr>
				<tr><td class=MsoNormal>Print your name clearly: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
				
				<tr><td class=MsoNormal>Your State Bar Number: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
				
				<tr><td class=MsoNormal>Signature: <div style="border-bottom: 1px solid ##000; display: inline-block; width: 200px;"> &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;</div></td></tr>
				<tr><td> &nbsp;</td></tr>
				<tr><td> &nbsp;</td></tr>	
				<tr>
					<td class="MsoNormal" ><b>Reminder:</b>&nbsp;Sign and keep this record of attendance for 4 years. In the event that you are audited by the State Bar, you may be requested to submit this record of attendance to the State Bar.
					<br/>
					Send this to the State Bar only if you are audited. If the provider has not granted credit for legal ethics, elimination of bias or competence issues, you cannot claim credit in this area.</td>
				</tr>
			</table>
		</div>
	</body>
	</html>
</cfoutput>