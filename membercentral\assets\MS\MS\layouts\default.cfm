<cfoutput>
<!doctype html>
<html lang="en">
    <head>
        <cfinclude template="head.cfm">		
    </head>
	
    <cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
       <body class="innerPage">
            <!-- wrapper start -->
            <div id="wrapper" class="wrapper">
                <!--Header Start-->
                <cfinclude template="header.cfm">
                <!--Header End-->  
				<cfset local.bannerImage =  ''>
				<cfif application.objCMS.getZoneItemCount(zone='L',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['L'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['L'][1].data))>							
							<cfset local.bannerImage = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['L'][1].data,"<p>",""),"</p>",""))>
						</cfif>
					</cfif>
				</cfif>
				<cfif local.bannerImage eq ''>
					<cfif application.objCMS.getZoneItemCount(zone='K',event=event)>
						<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['K'],1)>
							<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['K'][1].data))>							
								<cfset local.bannerImage = trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones['K'][1].data,"<p>",""),"</p>",""))>
							</cfif>
						</cfif>
					</cfif>
				</cfif>			
                 <!-- Inner Banner Start -->
				<div class="bannerInner">
					#local.bannerImage#
					<div class="banner-content">
						<div class="container">
							<h1 class="TitleText">#event.getValue('mc_pageDefinition.pageTitle',event.getValue('mc_pageDefinition.pageName'))#</h1>
						</div>
					</div>
				</div>
				<!-- Inner Banner End -->

				
				<cfset local.PageClass = 'span12 removeFlex'>
				<cfif application.objCMS.getZoneItemCount(zone='M',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['M'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['M'][1].data))>
							<cfset local.PageClass = 'span8'>
						</cfif>
					</cfif>
				</cfif>
				<cfif application.objCMS.getZoneItemCount(zone='N',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['N'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['N'][1].data))>
							<cfset local.PageClass = 'span8'>
						</cfif>
					</cfif>
				</cfif>
				<cfif application.objCMS.getZoneItemCount(zone='O',event=event)>
					<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['O'],1)>
						<cfif len(trim(event.getValue("mc_pageDefinition").pageZones['O'][1].data))>
							<cfset local.PageClass = 'span8'>
						</cfif>
					</cfif>
				</cfif>
				<cfif event.getValue('mc_pageDefinition.layoutMode','normal') eq "full">
					<cfset local.PageClass = 'span12 removeFlex'>
				</cfif>

				<cfset local.hasZoneM = 0>
				<cfset local.hasZoneN = 0>
				<cfset local.hasZoneO = 0>
				<cfset local.zone = "M">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneM = 1>
				</cfif>	
				<cfset local.zone = "N">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneN = 1>
				</cfif>
				<cfset local.zone = "O">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<cfset local.hasZoneO = 1>
				</cfif>
				<cfif local.hasZoneM eq 0 && local.hasZoneN eq 0 && local.hasZoneO eq 0>
					<cfset local.PageClass = 'span12 removeFlex'>
				</cfif>

		
				<!-- Main Content Start -->
				<div class="content">
					<div class="inner-page-content">
					   <div class="container">
						  <div class="row-fluid">
							 <div class="#local.PageClass# inner-content-area">								
								<cfif application.objCMS.getZoneItemCount(zone='Main',event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['Main'], 1)>
									#application.objCMS.renderZone(zone='Main',event=event, mode='div')#
								</cfif>	
		   					</div>
							 <cfif local.PageClass eq 'span8'>
							 	<div class="span4 sidebar">
									<cfif local.hasZoneM eq 1>
										<div class="quicklink-desktop quickLinkLg hideNone">
											<div class="side-title-center quickLinkLgImg">												
												<h3 class="ColumnHeader"></h3>
											</div>
											<div class="DiamondBullets">
												<ul> </ul>
											</div>
										</div>
									</cfif>
									<cfif local.hasZoneN eq 1>	
										<div class="events leftcolumnEventsWrap hideNone">
											<div class="side-title-center leftEventsWrapImg">
												
											</div>
											<div class="eventbox-list leftEventsWrap">									
												
												
												<div class="event-box event-btn">
													
												</div>
											</div>
										
										</div>
									</cfif>
									<cfif local.hasZoneO eq 1>	
										<div class="siedebar-blog leftcolumnBlogsWrap hideNone">
											<div class="side-title-center leftBlogssWrapImg">
											</div>
											<div class="eventbox-list leftBlogsWrap">


											<div class="event-box event-btn">
											
											</div>

											</div>	
											
										</div>										
									</cfif>
							 	</div>
							</cfif>	
						  </div>
					   </div>
					</div>
				 </div>
				 <!-- Main Content End -->
				 <div class="sponsors-sec pd_50 CLPHomeWrap hideNone">
					<div class="container">
						<div class="row flex-row">
						<div class="span12">
							<h2 class="SectionHeader text-center"></h2>
						</div>
						<div class="span12">
							<div class="sponsorSlider">
								<ul>
									
								</ul>
							</div>
						</div>
						</div>
					</div>
				</div>

				<cfset local.zone = "M">
				 <cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					 <div id="zoneMObj" class="hide">
						 #trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					 </div>
				 </cfif>

				<cfset local.zone = "N"><!-- left Column Upcoming Events -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneNObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

				<cfset local.zone = "O"><!-- left Column Blog -->
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneOObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>

				<cfset local.zone = "G">
				<cfif (application.objCMS.getZoneItemCount(zone=local.zone,event=event) AND arrayIsDefined(event.getValue("mc_pageDefinition").pageZones[local.zone],1))>
					<div id="zoneGObj" class="hide">
						#trim(REReplace(REReplace(event.getValue("mc_pageDefinition").pageZones[local.zone][1].data,"<p>",""),"</p>",""))#
					</div>
				</cfif>
                <!--Footer Start-->
                <cfinclude template="footer.cfm">				
                <!--Footer End-->                
                <cfinclude template="toolBar.cfm">
            </div>
            <!-- wrapper end --> 
        </body>
    <cfelse>
        <body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
            #application.objCMS.renderZone(zone='Main',event=event)#
        </body>		
    </cfif>
    <cfinclude template="foot.cfm">   
</html>
</cfoutput>