/* tsApps styles and overrides */
@import url("/assets/common/css/tsApps.css");


/* Include in Editor: Start */.TitleText {font-size: 48px;font-weight: 700;line-height: 1.2;font-family: 'Poppins', sans-serif;color: #1F2225;display: block;}
.HeaderText {font-weight: 700;font-size: 40px;color: #1F2225;text-align: center;font-family: 'Poppins', sans-serif;}
.HeaderTextSmall {font-weight: 700;font-size: 32px;line-height: 1.4;font-family: 'Poppins', sans-serif;color: #1F2225;margin: 0 0 15px;}
.SubHeading {font-size: 17px;color: #2D56A1;font-weight: 700;font-family: 'Poppins', sans-serif;text-transform: uppercase;display: block;} 
.HighlightHeading {font-size: 32px;font-weight: 700;font-family: 'Poppins', sans-serif;color: #1F2225;}
.BodyText {font-family: 'Montserrat', sans-serif;font-weight: 500;font-size: 17px;line-height: 1.4;color: #1F2225;}
.BodyTextLarge { font-size: 19px; }
.InfoText { font-size: 13px; }
.Poppins{font-family: 'Poppins', sans-serif;}
.Montserrat{font-family: 'Montserrat', sans-serif;}
/* Include in Editor: Stop */