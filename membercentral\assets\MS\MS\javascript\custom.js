$(document).ready(function () {

  //SiteMenu Prerender
  if($('#menuHolder > ul').length > 0){
    $('.headerloginWrap').after($('#menuHolder > ul').html());
  }

  $('.nav-collapse ul > li').each(function() {
    if($(this).children('ul').length) {
      $(this).addClass('dropdown').children('a').addClass('dropdown-toggle');
      $(this).children('ul').addClass('dropdown-menu');
    }
  });

  $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li').addClass('droplist-1');
  $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1 > ul').addClass('droplist-2');
  $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1 > ul.droplist-2 > li > ul').addClass('subMenu');
  $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1:has(form)').addClass('formDiv clearfix');
  $('.nav-collapse > ul > li.dropdown > ul.dropdown-menu >  li.droplist-1.formDiv > div ').addClass('formframe clearfix');
  $('.droplist-2 > li:has(ul)').addClass('subMenuParent');


  $(document).on('click', ".btn-navbar", function () {
    $("body").toggleClass("overlay");
  });

  $(document).on('click', '.menu-arrow', function () {
    $(this).parent(".dropdown").toggleClass('open-droupdown');
    $(this).parent(".dropdown").children(".dropdown-menu").slideToggle();
  });

  function mobclickmenu() {
    if ($(window).width() < 980) {
      $(".dropdown").each(function() {
        var this__ = $(this);
        if(!this__.find('.menu-arrow').length) {
          this__.append("<span class='menu-arrow'></span>");
        }
      });
    } else {
      $(".dropdown").find('.menu-arrow').remove();
    }
  }
  mobclickmenu();

  /*****************/
 

  $(".member-slider").owlCarousel({
    items: 1,
    margin: 0,
    loop: true,
    autoplay: true,
    autoplayTimeout: 7000,
    autoplayHoverPause: true,
    animateIn: 'fadeIn',
    animateOut: 'fadeOut',
    touchDrag: false,
    mouseDrag: false
  });

  

  $('.testimonial-slider').owlCarousel({ 
    loop:true,
    dots:false,
    drag:false,
    mouseDrag:false,
    autoplay: true,
    autoplayTimeout: 7000,
      smartSpeed: 400,
    margin:30,
    nav: true,
    items:1,    
    navText: ['<i class="fa-solid fa-chevron-left"></i>','<i class="fa-solid fa-chevron-right"></i>'],
  responsive:{
        0:{
            items:1
        },
        768:{
            items:2
        },
        991:{
            items:2
        }
    }
  });
  
  $('.upcoming-slider').owlCarousel({ 
    loop:true,
    dots:false,
    drag:false,
    mouseDrag:false,
    autoplay: true,
    autoplayTimeout: 6000,
      smartSpeed: 300,
    margin:30,
    nav: true,
    items:1,    
    navText: ['<i class="fa-solid fa-chevron-left"></i>','<i class="fa-solid fa-chevron-right"></i>'],
  responsive:{
        0:{
            items:1
        },
        768:{
            items:2
        },
        991:{
            items:3
        },
        1199:{
            items:4
        }
    }
  });

  $(document).on('click', '.searchBtnFn>a',  function(e) {
    e.preventDefault();
    $(this).closest('li').addClass('show-search-bar');
  });
  
  $(document).on('click', '.searchclose',  function(e) {
    e.preventDefault();
    $(this).closest('li.show-search-bar').removeClass('show-search-bar');
  });

  $(document).on('click', '.toggle-form',  function(e) {
    e.preventDefault();
    $(this).closest('li').toggleClass('show-form');
  });

  $(window).on('resize', function() {
    mobclickmenu();
  });


  //Zone A
  if( $('.mobile-contactus').prev().find('img').length > 0){    
    $('.mobile-contactus').prev().addClass('navbar-brand');
  }

  //Zone B
  var zoneBObj = $('#zoneBObj').html();
  if(zoneBObj != undefined){
    strLeftCnt = '';
    strRightCnt = '';
    strLeftCntLg = '';
    $('#zoneBObj > ul > li').each(function(key,val){
      objLiCnt =  $(this);
      strLiCnt = isset(objLiCnt.html())?objLiCnt.html():''; 
      strLinkText = $(strLiCnt).clone().find('i').remove().end().text().trim();
      
      if(isset(strLinkText) && strLinkText.length > 0){
        strLeftCnt += strLiCnt;
        strLeftCntLg += '<li>'+strLiCnt+'</li>';
      }else if(strLinkText.length == 0){
        strRightCnt += '<li>'+strLiCnt+'</li>';
      }
    });
    if(strLeftCnt != '' || strRightCnt != ''){
      $('.zoneBMobileWrap > .dropdown > a.dropdown-toggle').removeClass('hideNone')
    }
    if(strLeftCnt != ''){
      $('.zoneBMobileWrap ul.social-list').before(strLeftCnt);
      $('.zoneBLgWrap .toplist').html(strLeftCntLg);
      $('.zoneBMobileWrap .dropdown-menu div > a').addClass('item-link');
    }
    if(strRightCnt != ''){
      $('.zoneBMobileWrap ul.social-list').html(strRightCnt);
      $('.zoneBLgWrap .searchBtnFn').before(strRightCnt);
    }
  }

  $('.headerBtnWrap > a').addClass('MAJButton');


  //Footer Section
  var zonePObj = $('#zonePObj').html();
  logoFound = 0;
  if(zonePObj != undefined){
    $('#zonePObj > ul > li').each(function(key,val){
      objLiCnt =  $(this);
      strLink = isset(objLiCnt.html())?objLiCnt.html():''; 
      
      if(objLiCnt.find('img').length > 0 && logoFound == 0){
        $('.footerLogoWrap').html(strLink);
        $('.footerLogoWrap img').addClass('footlogo');
        $('.footerLogoWrap').removeClass('hideNone');
        logoFound = 1;
      }else{         
        $('.footerZonePbtn').before(strLink);
        $('.footerZonePbtnWrap > a').addClass('MAJButton');

        strIsLoggedIn = $('#isLoggedIn').val();
        if(strIsLoggedIn == 'loggedIn'){
          $('.footerZonePbtnWrap > a').not('.footerZonePbtn').remove();
        }
      }
    });
  }

  var zoneQObj = $('#zoneQObj').html();
  if(zoneQObj != undefined){
    $('#zoneQObj > ul > li').each(function(key,val){
      objLiCnt =  $(this);
      strLink = isset(objLiCnt.html())?objLiCnt.html():''; 
      
      $('.footerZonePbtnWrap.hidden-desktop').before('<p>'+strLink+'</p>');
    });
  }

  $('.zoneRWrap > ul > li > a').prepend('<i class="fas fa-chevron-right"></i> ');

  var zoneTObj = $('#zoneTObj').html();
  if(zoneTObj != undefined){
    $('#zoneTObj > ul').each(function(key,val){
      objLiCnt =  $(this);
      if(key == 0){
        strZoneTLeft = '';
        $(objLiCnt.find('li')).each(function(key1,val1){
          if(strZoneTLeft != ''){
            strZoneTLeft += '<span class="px_5"></span>';
          }
          strZoneTLeft += $(val1).html();
        });
        $('.zoneTleftColumn').html(strZoneTLeft);
        $('.zoneTWrap').removeClass('hideNone');
      }else{
        $('.zoneTRightColumn').html($(val).html());
        $('.zoneTWrap').removeClass('hideNone');
      }
    });
  }


  //Home page Banner
  if($('#zoneMainObj').html() != undefined)
    var zoneMainObj = $('#zoneMainObj').html().trim();
    liCount = 0;
    if(zoneMainObj != undefined && zoneMainObj.length > 0){
      $('#zoneMainObj > ul').each(function(key,val){
        objLiCnt =  $(this);
        liCount = liCount + 1;
        strImage = '';
        strTitle = '';
        strDesc = '';
        strLink = '';

        $(objLiCnt.find('li')).each(function(key1,val1){
          if($(val1).find('img').length > 0){
            strImage = $(val1).html();
          }
          if($(val1).find('a').length > 0){
            strLink = $(val1).html();
          }
          
          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strDesc == ''){
            strDesc = $(val1).html();
          } 

          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
            strTitle = $(val1).html();
          }    

        });
        classOverFlow = '';
        if(strTitle.length > 100){
          classOverFlow = 'bannerTitleOverflow';
        }
        strBannerItem = '<div class="item">'+strImage;
        strBannerItem +=  '<div class="carousel-caption"><div class="captionFrame"><ul>';
        strBannerItem +=  '<li><span class="TitleText">'+strTitle+'</span></li>';
        strBannerItem += '<li>'+strDesc+'</li>';
        strBannerItem += '<li class="bannerLinks">'+strLink+'</li>';
        strBannerItem +=  '</ul></div></div>';
        strBannerItem +=  '</div>';
        $('.sliderBanner').append(strBannerItem);
        
      });
      $('.sliderBanner .bannerLinks a').addClass('MAJButton');
      $(".slider .owl-carousel").owlCarousel({
        items: 1,
        margin: 0,
        loop: true,
        autoplay: true,
        autoplayTimeout: 7000,
        autoplayHoverPause: false,
        animateIn: 'fadeIn',
        animateOut: 'fadeOut',
        touchDrag: false,
        mouseDrag: false,
        dots: false,
        nav: true,
        navText: ['<i class="fa-solid fa-chevron-left"></i>','<i class="fa-solid fa-chevron-right"></i>'],
      });
    }

    var zoneDObj = $('#zoneDObj').html();
    if(zoneDObj != undefined){
      $('#zoneDObj > ul').each(function(key,val){
        objLiCnt =  $(this);
        strZoneDCnt = '';
        strImage = '';
        strTitle = '';
        strDesc = '';
        strLink = '';

        $(objLiCnt.find('li')).each(function(key1,val1){
          if($(val1).find('img').length > 0){
            strImage = $(val1).html();
          }
          if($(val1).find('a').length > 0){
            strLink = $(val1).html();
          }
          
          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle != '' && strDesc == ''){
            strDesc = $(val1).html();
          } 

          if($(val1).find('img').length == 0 && $(val1).find('a').length == 0 && strTitle == ''){
            strTitle = $(val1).html();
          }    
             
        });
        strZoneDCnt += `
          <li>
              <div class="sbm-iconbox">
                ${strImage}
                <h3 class="HighlightTitle"> ${strTitle}</h3>
                <p>${strDesc}</p>
                <div class="btns zoneDBtns">
                  ${strLink}
                </div>
              </div>
          </li>            
        `;
        if(strZoneDCnt != ''){
          $('.quickLinksHomeWrap .quickLinksHome').append(strZoneDCnt);
          $('.quickLinksHomeWrap').removeClass('hideNone');
          $('.zoneDBtns a').addClass('WhiteBorder');
        }

        if(key == 0 && strZoneDCnt != ''){
          isLoggedIn = $('#isLoggedIn').val();
          if(isLoggedIn == 'loggedIn' ){
            $('.zoneDBtns a').addClass('hideNone');
            $('.zoneDBtns').html('<a href="/?logout" class="MAJButton">MEMBER LOGOUT</a>');
          }else{
            $('.zoneDBtns').append(' <a href="/?pg=login" class="MAJButton">MEMBER LOGIN</a>');
            $('.zoneDBtns a').addClass('WhiteBorder');
          }
          
           
        }
       
      });
    }

    var zoneEObj = $('#zoneEObj').html();
    if(zoneEObj != undefined){
      $('#zoneEObj  > ul').each(function(key,val){
        objLiCnt =  $(this);
        strImage2 = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
        strTitle1 = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():''; 
        strTitle2 = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():''; 
        strDesc = isset(objLiCnt.children().eq(3).html())?objLiCnt.children().eq(3).html():''; 
        strLink = isset(objLiCnt.children().eq(4).html())?objLiCnt.children().eq(4).html():''; 
        strImage1 = isset(objLiCnt.children().eq(5).html())?objLiCnt.children().eq(5).html():''; 

        
        strZoneECntTop = `
          <h2 class="SectionHeader">${strTitle1} <br> <span class="text-yellow">${strTitle2}</span></h2>    
          <p>${strDesc}</p>    
          ${strLink}  
      ` ;
        $('.zoneEmainCnt').append(strZoneECntTop);
        $('.magazineHomeWrap').removeClass('hideNone');
        $('.zoneEmainCnt a').addClass('MAJButton');
        $('.magazineHomeWrap .img-slider').html(strImage2);
        $('.magazineHomeWrap .inner-sec-wrap').prepend(strImage1);
        $('.magazineHomeWrap .inner-sec-wrap > img').addClass('fixed-bg');
      });
    }

    var zoneFObj = $('#zoneFObj').html();
    strBlogsCnt = '';
    if(zoneFObj != undefined){
      $('#zoneFObj .mcMergeTemplate > ul').each(function(key,val){
       objLiCnt =  $(this);
       newsDate = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():''; 
       newsLink = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():'javascript:void(0);';  
       newsImg = isset(objLiCnt.children().eq(3).html())?objLiCnt.children().eq(3).html():''; 
       if(newsLink == ''){
         newsLink = "javascript:void(0);";
         newsTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
       }else{
         newsTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
       }
       newsAuthor = isset(objLiCnt.children().eq(4).html())?objLiCnt.children().eq(4).html():''; 
       strAuthor = '';
       if(newsAuthor.length > 0){
        strAuthor = `<span class="px_5">|</span>   BY <span class="text-yellow"> ${newsAuthor}</span> `;
       }

       strBlogsCnt = `
            <div class="span3">
              <div class="img-card">
                <div class="img-holder">
                  <a href="${newsLink}">
                    <img src="${newsImg}" alt="">
                  </a>
                </div>
                <div class="img-card-content">
                  <a href="${newsLink}"><h3>${newsTitle}</h3> </a>
                  <div class="datebx"><span class="color-gray">${newsDate}</span>${strAuthor}</div>
                </div>
              </div>
            </div>       
       `;
 
        $('.blogHomeWrapCnt').append(strBlogsCnt);
      });
      $('#zoneFObj .mcMergeTemplate').remove();
      zoneFHeading = isset($('#zoneFObj').children().eq(0).html())?$('#zoneFObj').children().eq(0).html():''; 
      
      $('.blogHomeWrapCnt .SectionHeader').html(zoneFHeading);
      $('.blogHomeWrap').removeClass('hideNone');
    }


    var zoneGObj = $('#zoneGObj').html();
    if(zoneGObj != undefined){
      zoneGSliderCount = $('#zoneGObj > ul > li').length;
      $('#zoneGObj > ul').each(function(key,val){
       objLiCnt =  $(this);
       strLinks = isset(objLiCnt.html())?objLiCnt.html():''; 

        $('.CLPHomeWrap .sponsorSlider > ul').html(strLinks);
        $('.CLPHomeWrap').removeClass('hideNone');
      });
      $('#zoneGObj > ul').remove();
      zoneGHeading = isset($('#zoneGObj').children().eq(0).html())?$('#zoneGObj').children().eq(0).html():'';       
      $('.CLPHomeWrap .SectionHeader').html(zoneGHeading);
      // Sponsor slider
      $('.sponsorSlider ul').addClass('owl-carousel owl-theme').owlCarousel({ 
        loop: (zoneGSliderCount > 4)?true:false,
        dots:false,
        drag:false,
        mouseDrag:false,
        autoplay: true,
        autoPlaySpeed: 7000,
        smartSpeed: 1000,
        margin:30,
        nav: true,
        items:(zoneGSliderCount > 4)?4:zoneGSliderCount,    
        navText: ['<i class="fa-solid fa-chevron-left"></i>','<i class="fa-solid fa-chevron-right"></i>'],
        responsive:{
            0:{
                items:1,
                loop: (zoneGSliderCount > 1)?true:false,
            },
            767:{
                items:2,
                loop: (zoneGSliderCount > 2)?true:false,
            },
            991:{
                items:4,
                loop: (zoneGSliderCount > 4)?true:false,
            }
        }
      });
    }

    var zoneHObj = $('#zoneHObj').html();
    if(zoneHObj != undefined){

      if($('#zoneHObj > ul > li > ul').length > 0){
        strHcontText = '';
        $('#zoneHObj > ul > li > ul').each(function(key,val){
          objLiCnt =  $(this);
          strLi1 = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():'';  
          strLi2 = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():'';  
          strLi3 = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():''; 
          strLi2Char = '';
          if((isNaN(strLi2))){
            strLi2Char = strLi2.match(/[^\w\s]/g)[0]; 
            strLi2 = strLi2.match(/\d+(\.\d+)?/g)[0];
            strLi2Char = `<sup>${strLi2Char}</sup>`;
            
          }
          strHcontText = `
            <div class="span3">
                  <div class="counter-box">
                     <div class="icon">
                        ${strLi1}
                     </div>
                     <div class="counter">
                        <span class="count">${strLi2}</span>${strLi2Char}
                     </div>
                     <p> ${strLi3}</p>
                  </div>              
              </div>
          `;

          $('.zoneHWrap .zoneHWrapCnt').append(strHcontText);
          $('.zoneHWrap').removeClass('hideNone');
        });
        $('#zoneHObj > ul > li > ul').closest('li').remove();

        strImage = '';
        strTitle = '';
        strDesc = '';

        $('#zoneHObj > ul').each(function(key,val){
            objLiCnt =  $(this);
            $(objLiCnt.find('li')).each(function(key1,val1){
              if($(val1).find('img').length > 0){
                strImage = $(val1).html();
              }              
              
              if($(val1).find('img').length == 0 && strTitle != '' && strDesc == ''){
                strDesc = $(val1).html();
              } 
    
              if($(val1).find('img').length == 0  && strTitle == ''){
                strTitle = $(val1).html();
              }   
    
            });
            $('.zoneHWrap').prepend(strImage);
            $('.zoneHWrap > img').addClass('fixed-bg');
            $('.zoneHWrap .zoneHWrapCnt h2').prepend(strTitle);
            $('.zoneHWrap .zoneHWrapCnt .span12 p').prepend(strDesc);
        });     

      }
    }


    var zoneIObj = $('#zoneIObj').html();
    if(zoneIObj != undefined){
      
      objLiCnt =  $('#zoneIObj');
      strTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():'';       

      $('.zoneIWrap .SectionHeader').html(strTitle);
      $('.zoneIWrap').removeClass('hideNone');
      
    }

    $('.bannerInner > img').addClass('fixed-bg');


    var zoneMObj = $('#zoneMObj').html();
    if(zoneMObj != undefined){

      if($('#zoneMObj > ul > li > ul').length > 0){
        strHcontText = '';
        $('#zoneMObj > ul > li > ul').each(function(key,val){
          objLiCnt =  $(this);
          strLis = isset(objLiCnt.html())?objLiCnt.html():'';  

          $('.quicklink-desktop.quickLinkLg .DiamondBullets > ul').html(strLis);
          $('.quicklink-desktop.quickLinkLg').removeClass('hideNone');
        });
        $('#zoneMObj > ul > li > ul').closest('li').remove();

        strImage = '';
        strTitle = '';

        $('#zoneMObj > ul').each(function(key,val){
            objLiCnt =  $(this);
            $(objLiCnt.find('li')).each(function(key1,val1){
              if($(val1).find('img').length > 0){
                strImage = $(val1).html();
              }   
              if($(val1).find('img').length == 0  && strTitle == ''){
                strTitle = $(val1).html();
              }   
    
            });
            $('.quickLinkLgImg').prepend(strImage);
            $('.quickLinkLgImg .ColumnHeader').prepend(strTitle);
        });  
        
        $('.quicklink-desktop.quickLinkLg .DiamondBullets > ul > li').each(function(key2,val2){
          objLiCnt =  $(this);
          if(objLiCnt.find('> ul').length > 0){
            objLiCnt.find('>ul').addClass('dropdown-menu');
            objLiCnt.addClass('dropdown');
            objLiCnt.find('> a').addClass('dropdown-toggle');
            objLiCnt.find('> a').attr('data-toggle','dropdown');
          }
      });  

      }
    }

    var zoneNObj = $('#zoneNObj').html();
    strCnt = '';
    if(zoneNObj != undefined){
      $('#zoneNObj .mcMergeTemplate > ul').each(function(key,val){
       objLiCnt =  $(this);
       strDate = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
       strTitle = isset(objLiCnt.children().eq(1))?objLiCnt.children().eq(1).html():''; 
       strCategory = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():'';
       strLink = isset(objLiCnt.children().eq(3))?objLiCnt.children().eq(3).html():'javascript:void(0);';   
      
       if(strTitle == undefined){
        strTitle = '';
       }
       
       strCnt = `
            <div class="event-box zoneNEventBox">
              <span class="e-date">${strDate}</span> <br>
              <span class="e-cle">${strCategory}</span>
              <a href="${strLink}"> <p>${strTitle}</p></a>
            </div>  
       `;
 
        $('.leftEventsWrap .event-btn').before(strCnt);
      });
      $('#zoneNObj .mcMergeTemplate').remove();


      $($('#zoneNObj').children()).each(function(key,val){
        if($(val).is('img')){
          $('.leftEventsWrapImg').append(val);
        }else if($(val).is('h3')){
          $('.leftEventsWrapImg').append(val);
          $('.leftEventsWrapImg  > h3').addClass('ColumnHeader');
        }else if($(val).is('a')){
          $('.leftEventsWrap .event-btn').html(val);
          $('.leftEventsWrap .event-btn a').addClass('TextButton');
        }else{
         if($(val).find('a').length > 0){
            strLink = $(val).find('a').attr('href');
            strText = $(val).find('a').html();
            $('.leftEventsWrap .event-btn').html(`
                <a href="${strLink}" class="TextButton">${strText}</a>
            `);
         }
        }
       
      });

      zoneNHeading = isset($('#zoneNObj').html())?$('#zoneNObj').html():'';       
      $('.leftcolumnEventsWrap .SectionHeader').html(zoneNHeading);
      $('#zoneNObj  a')
      strLink = isset($('#zoneNObj  a'))?$('#zoneNObj  a').attr('href'):'javascript:void(0);';  
      $('#zoneNObj  a').remove();


      $('.leftcolumnEventsWrap').removeClass('hideNone');
    }


    var zoneOObj = $('#zoneOObj').html();
    strCnt = '';
    if(zoneOObj != undefined){
      $('#zoneOObj .mcMergeTemplate > ul').each(function(key,val){
       objLiCnt =  $(this);
       newsDate = isset(objLiCnt.children().eq(1).html())?objLiCnt.children().eq(1).html():''; 
       newsLink = isset(objLiCnt.children().eq(2).html())?objLiCnt.children().eq(2).html():'javascript:void(0);';  
       newsImg = isset(objLiCnt.children().eq(3).html())?objLiCnt.children().eq(3).html():''; 
       newsAuthor = isset(objLiCnt.children().eq(4).html())?objLiCnt.children().eq(4).html():''; 
       if(newsLink == ''){
         newsLink = "javascript:void(0);";
         newsTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
       }else{
         newsTitle = isset(objLiCnt.children().eq(0).html())?objLiCnt.children().eq(0).html():''; 
       }
       strAuthor = '';
       if(newsAuthor.length > 0){
        strAuthor = `BY ${newsAuthor}`;
       }
       
       strBlogsCnt = `
          <div class="event-box">
            <span class="e-date">${newsDate}</span> <br>
            <span class="e-cle">${strAuthor}</span>
            <a href="${newsLink}"> <p>${newsTitle}</p></a>
          </div>
       `;
 
        $('.leftBlogsWrap .event-btn').before(strBlogsCnt);
      });
      $('#zoneOObj .mcMergeTemplate').remove();


      $($('#zoneOObj').children()).each(function(key,val){
        if($(val).is('img')){
          $('.leftBlogssWrapImg').append(val);
        }else if($(val).is('h3')){
          $('.leftBlogssWrapImg').append(val);
          $('.leftBlogssWrapImg  > h3').addClass('ColumnHeader');
        }else if($(val).is('a')){
          $('.leftBlogsWrap .event-btn').html(val);
          $('.leftBlogsWrap .event-btn a').addClass('TextButton');
        }else{
         if($(val).find('a').length > 0){
            strLink = $(val).find('a').attr('href');
            strText = $(val).find('a').html();
            $('.leftBlogsWrap .event-btn').html(`
                <a href="${strLink}" class="TextButton">${strText}</a>
            `);
         }
        }
       
      });



      zoneOHeading = isset($('#zoneOObj').html())?$('#zoneOObj').html():'';       
      $('.leftcolumnBlogsWrap .SectionHeader').html(zoneOHeading);
      $('#zoneOObj  a')
      strLink = isset($('#zoneOObj  a'))?$('#zoneOObj  a').attr('href'):'javascript:void(0);';  
      $('#zoneOObj  a').remove();


      $('.leftcolumnBlogsWrap').removeClass('hideNone');
    }


    $('.TextButton').append('<i class="fas fa-arrow-right"></i>');
    $('.BulletList ul li').prepend('<i class="fas fa-chevron-right"></i> ');

});


function isset(el){	
  if(el !=  undefined && el != null && el != ''){		
    return true;
  }else{
    return false;
  }	
}
function imgError(_this){	
  $(_this).attr('src','/images/default-event.png');
}
function decodeHtml(str)
{
    var map =
    {
        '&amp;': '&',
        '&lt;': '<',
        '&gt;': '>',
        '&quot;': '"',
        '&#039;': "'"
    };
    return str.replace(/&amp;|&lt;|&gt;|&quot;|&#039;/g, function(m) {return map[m];});
}
function resizeContactIframe(){
	$("#contactIframe").height( $("#contactIframe").contents().find('.contactFrmHolder').outerHeight()+25);
}
function scrollContact(){
	$("html, body").animate({ scrollTop: $('.zoneIWrap').offset().top - 100 }, "slow");
}