ALTER PROC dbo.tr_viewTransaction_void
@transactionID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @orgID int, @tr_OffsetTrans int, @tr_AllocSaleTrans int;
	declare @allGLs TABLE (GLAccountID int, thePathExpanded varchar(max), accountCode varchar(200), accountType varchar(30), GLCode varchar(30), thePath varchar(max));
	declare @tblTrans TABLE (transactionID int, glAccountID int, debitAmount decimal(18,2), creditAmount decimal(18,2));

	select @orgID = ownedByOrgID from dbo.tr_transactions where transactionID = @transactionID;
	set @tr_OffsetTrans = dbo.fn_tr_getRelationshipTypeID('OffsetTrans');
	set @tr_AllocSaleTrans = dbo.fn_tr_getRelationshipTypeID('AllocSaleTrans');

	insert into @allGLS
	select rgl.GLAccountID, rgl.thePathExpanded, rgl.accountCode, rgl.accountType, rgl.GLCode, rgl.thePath
	from dbo.fn_getRecursiveGLAccountsWithAccountTypes(@orgID) as rgl;

	-- transaction info
	select TOP 1 t.transactionid, t.ownedByOrgID, t.recordedOnSiteID, tt.type, t.amount, 
		case
		when tVoidee.typeID = 3 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Negative Adjustment of ' + isnull(tVoidee.detail,'')
		when tVoidee.typeID = 3 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Adjustment of ' + isnull(tVoidee.detail,'')
		when tVoidee.typeID = 6 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of WriteOff of ' + isnull(tVoidee.detail,'')
		when tVoidee.typeID = 6 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Negative WriteOff of ' + isnull(tVoidee.detail,'')
		when tVoidee.typeID = 9 then 'VOID of NSF of ' + isnull(tVoidee.detail,'')
		when tVoidee.typeID = 5 and glVoidee.glCode = 'ACCOUNTSRECEIVABLE' then 'VOID of Allocation to ' + isnull(tVoideeSale.detail,'')
		when tVoidee.typeID = 5 and glVoidee.glCode <> 'ACCOUNTSRECEIVABLE' then 'VOID of Deallocation from ' + isnull(tVoideeSale.detail,'')
		else 'VOID of ' + isnull(t.detail,'')
		end as detail,
		t.transactionDate, t.dateRecorded, ts.status, mAss2.memberID as assignedTomemberID, 
		mAss2.firstname + isnull(' ' + nullif(mAss2.middlename,''),'') + ' ' + mAss2.lastname + isnull(' ' + nullif(mAss2.suffix,''),'') + ' (' + mAss2.membernumber + ')' as assignedToMember,
		mAss2.company as assignedToMemberCompany,
		m2.firstname + isnull(' ' + nullif(m2.middlename,''),'') + ' ' + m2.lastname + isnull(' ' + nullif(m2.suffix,''),'') + ' (' + m2.membernumber + ')' as recordedByMember,
		m2.company as recordedByMemberCompany
	from dbo.tr_transactions as t
	inner join dbo.tr_types as tt on tt.typeID = t.typeID
	inner join dbo.tr_statuses as ts on ts.statusID = t.statusID
	inner join dbo.ams_members as mAss on mAss.orgID = @orgID and mAss.memberid = t.assignedToMemberID
	inner join dbo.ams_members as mAss2 on mAss2.orgID = @orgID and mAss2.memberID = mAss.activeMemberID
	inner join dbo.ams_members as m on m.memberid = t.recordedByMemberID
	inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
	inner join dbo.tr_relationships AS rVoid on rVoid.orgID = @orgID and rVoid.typeID = @tr_OffsetTrans and rVoid.transactionID = t.transactionID
	inner join dbo.tr_transactions as tVoidee on tVoidee.ownedByOrgID = @orgID and tVoidee.transactionID = rVoid.appliedToTransactionID
	inner join @allGLs as glVoidee on glVoidee.GLAccountID = tVoidee.creditGLAccountID
	left outer join dbo.tr_relationships AS rVoideeAllocSale 
		inner join dbo.tr_transactions as tVoideeSale on tVoideeSale.ownedByOrgID = @orgID and tVoideeSale.transactionID = rVoideeAllocSale.appliedToTransactionID
		on rVoideeAllocSale.orgID = @orgID and rVoideeAllocSale.typeID = @tr_AllocSaleTrans and rVoideeAllocSale.transactionID = tVoidee.transactionID and tVoidee.typeID = 5
	where t.transactionID = @transactionID;
	
	-- void info
	IF (
		select tVoidee.typeID
		from dbo.tr_transactions as t
		inner join dbo.tr_relationships AS rVoid on rVoid.orgID = @orgID and rVoid.typeID = @tr_OffsetTrans and rVoid.transactionID = t.transactionID
		inner join dbo.tr_transactions as tVoidee on tVoidee.ownedByorgID = @orgID and tVoidee.transactionID = rVoid.appliedToTransactionID
		where t.transactionID = @transactionID
		) in (1,7,3)
		select it.itID, it.messageContentVersionID,
			i.invoiceID, ins.status, i.fullInvoiceNumber as invoiceNumber, i.dateDue, i.invoiceProfileID, ip.profileName, i.invoiceCode
		from dbo.tr_invoiceTransactions as it
		inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
		inner join dbo.tr_invoiceStatuses as ins on ins.statusID = i.statusID
		inner join dbo.tr_invoiceProfiles as ip on ip.profileID = i.invoiceProfileID
		inner join dbo.organizations as o on o.orgID = @orgID
		where it.orgID = @orgID
		and it.transactionID = @transactionID
	ELSE
		select b.batchID, b.batchName, b.depositDate, bs.status
		from dbo.tr_transactions as t
		inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = t.transactionID
		inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID
		inner join dbo.tr_batchStatuses as bs on bs.statusID = b.statusID
		where t.transactionID = @transactionID;

	-- current gl spread
	insert into @tblTrans
	select transactionID, debitglAccountID, amount, 0
	from dbo.tr_transactions
	WHERE transactionID = @transactionID
	and statusID = 1
		union all
	select transactionID, creditglAccountID, 0, amount
	from dbo.tr_transactions
	WHERE transactionID = @transactionID
	and statusID = 1;

	select gl.thePathExpanded + isnull(' (' + nullIf(gl.accountCode,'') + ')','') as glexpanded,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount > 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount < 0 then abs(sd.creditAmount - sd.debitAmount)
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount >= 0 then sd.debitAmount - sd.creditAmount
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount <= 0 then abs(sd.creditAmount - sd.debitAmount)
		else null
		end as debits,
		case 
		when gl.accountType = 'Cash' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Asset' and gl.GLCode = 'ACCOUNTSRECEIVABLE' and sd.debitAmount - sd.creditAmount <= 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and gl.GLCode = 'DEPOSITS' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Revenue' and sd.creditAmount - sd.debitAmount >= 0 then sd.creditAmount - sd.debitAmount
		when gl.accountType = 'Expense' and sd.debitAmount - sd.creditAmount < 0 then abs(sd.debitAmount - sd.creditAmount)
		when gl.accountType = 'Liability' and sd.creditAmount - sd.debitAmount > 0 then sd.creditAmount - sd.debitAmount
		else null
		end as credits
	from (
		select glAccountID, sum(debitAmount) as debitAmount, sum(creditAmount) as creditAmount
		from @tblTrans
		group by glAccountID
		having (sum(debitAmount) > 0 OR sum(creditAmount) > 0) and sum(debitAmount) <> sum(creditAmount)
	) as sd
	inner join @allGLS as gl on gl.GLAccountID = sd.GLAccountID
	order by gl.thePath;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
