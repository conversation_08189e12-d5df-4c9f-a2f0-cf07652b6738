<cfset local.assetCachingKey = application.objCMS.getPlatformCacheBusterKey()>

<cfsavecontent variable="local.mpJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/admin/javascript/merchantProfileAdmin.js#local.assetCachingKey#"></script>
	#local.strCashGLAcctWidget.js#
	#local.strSurchargeGLAcctWidget.js#
	#local.strPFDGLAcctWidget.js#
	<cfloop array="#local.arrCardTypeGLWidgets#" index="local.cardTypeWidget">
		#local.cardTypeWidget.widget.js#
	</cfloop>
	<script language="javascript">
	const mca_siteid = #arguments.event.getValue('mc_siteInfo.siteID')#;
	const #toScript(arguments.event.getValue('mc_currentHostname'), "mca_hostname")#
	const #toScript(buildLinkToTool(toolType="GLAccountSelector",mca_ta="showSelector"), "mca_link_glselector")#
	const #toScript(lCase(application.MCEnvironment), "mc_env")#
	const mc_enableMCPay = #val(local.merchantProfile.enableMCPay)#;

	var gatewayFields_0 = [];
	<cfloop query="local.qryAllGateways">
		var gatewayFields_#local.qryAllGateways.gatewayid# = [#local.qryAllGateways.fieldList#];
	</cfloop>
	function onMCPaySettingsTabChangeHandler(ActiveTab) {
		switch(ActiveTab.id) {
			case 'surchargeSettingsTab':
				initEnableSurchargeToggleBtn();
				break;
			case 'applePaySettingsTab':
				initEnableApplePayToggleBtn();
				break;
			case 'googlePaySettingsTab':
				initEnableGooglePayToggleBtn();
				break;
		}
	}

	$(function() {
		<cfif val(local.merchantProfile.profileID)>
			doSelectGateway();
			mca_setupCustomFileControls('frmMP');

			<cfif arguments.event.getValue('err','') is 1>
				mca_showAlert('mpfrm_err', 'There was an error processing the uploaded image.<br/>Be sure the image is a PNG or GIF and is exactly 400x150 pixels.');
			</cfif>
		</cfif>

		$('##divSuperKey').show();
		let MCPaySettingsSelectedTab = $('##MCPaySettingsTabs li:visible a:first').length ? $('##MCPaySettingsTabs li:visible a:first').data('tabname') : 'processingFeeSettings';
		mca_initNavPills('MCPaySettingsTabs',MCPaySettingsSelectedTab, '', onMCPaySettingsTabChangeHandler);
	});
	</script>
	<cfif val(local.merchantProfile.profileID)>
		<cfset local.acceptedCardTypes = valueList(local.creditcards.cardType)>
		<cfset local.applePayCardTypes = []>
		<cfset local.googlePayCardTypes = []>
		<cfif listFindNoCase(local.acceptedCardTypes,"American Express")>
			<cfset local.applePayCardTypes.append("amex")>
			<cfset local.googlePayCardTypes.append("AMEX")>
		</cfif>
		<cfif listFindNoCase(local.acceptedCardTypes,"Discover")>
			<cfset local.applePayCardTypes.append("discover")>
			<cfset local.googlePayCardTypes.append("DISCOVER")>
		</cfif>
		<cfif listFindNoCase(local.acceptedCardTypes,"MasterCard")>
			<cfset local.applePayCardTypes.append("masterCard")>
			<cfset local.googlePayCardTypes.append("MASTERCARD")>
		</cfif>
		<cfif listFindNoCase(local.acceptedCardTypes,"VISA")>
			<cfset local.applePayCardTypes.append("visa")>
			<cfset local.googlePayCardTypes.append("VISA")>
		</cfif>
	</cfif>

	<cfif val(local.merchantProfile.profileID) gt 0 and local.merchantProfile.enableApplePay EQ 1 AND session.cfcuser.memberData.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
		<script id="applePaySDK" type="application/javascript" async crossorigin src="https://applepay.cdn-apple.com/jsapi/1.latest/apple-pay-sdk.js"></script>
		<script type="application/javascript">
			applePaySDK.addEventListener('load', function () {
				$(function() {
					if (window.ApplePaySession) {
						var applePayMID = '#local.merchantProfile.applePayMerchantID#';
						ApplePaySession.applePayCapabilities(applePayMID).then(function(capabilities) {
							switch (capabilities.paymentCredentialStatus) {
								case "paymentCredentialsAvailable":
									// Display an Apple Pay button and offer Apple Pay as the primary payment option. 
									$('##applePayAllowed').show();
									break;
								case "paymentCredentialStatusUnknown":
									// Display an Apple Pay button and offer Apple Pay as a payment option.
									$('##applePayAllowed').show();
									break;
								case "paymentCredentialsUnavailable":
									// Consider displaying an Apple Pay button.
									$('##applePayNotAllowed').show();
									break;
								case "applePayUnsupported":
									// Don't show an Apple Pay button or offer Apple Pay.
									$('##applePayNotAllowed').show();
									break;
							}
						})
						<cfif NOT application.objPlatform.isRequestSecure()>
							$('##applePayNotAllowed').show();
						</cfif>
					}
				});
			}); 

			var applePaySession;
					
			function applePayTest() {
				let MCMPID = #local.merchantProfile.profileID#;
				let MCMPCODE = '#local.merchantProfile.profileCode#';
				var request = {
					countryCode: 'US',
					currencyCode: 'USD',
					supportedNetworks: #serializeJson(local.applePayCardTypes)#,
					merchantCapabilities: ["supports3DS","supportsDebit","supportsCredit"],
					total: { label: 'Test ApplePay Payment', amount: (1+Math.random()).toFixed(2) },
				}
				$('##applePayResultMessage').html('').hide();

				applePaySession = new ApplePaySession(3, request);
				applePaySession.onvalidatemerchant = async function (event) {
					var objParams = { profileID: MCMPID };
					$.getJSON('/?event=proxy.ts_json&c=GATEPAY&m=applePayOVM',objParams)
						.done(function (validationResponse) {
							let merchantSession = validationResponse.data;
							applePaySession.completeMerchantValidation(merchantSession);
						}).fail(function( jqxhr, textStatus, error ) {
							$('##applePayResultMessage').html(JSON.stringify(error)).addClass('alert alert-danger').show();
							applePaySession.abort();
						});
				};
				applePaySession.onpaymentauthorized = async function(event) {
					$.post('/?event=proxy.ts_json&c=GATEPAY&m=applePayTestOPA', { profileID: MCMPID, profileCode: MCMPCODE, payload: JSON.stringify(event.payment)})
						.then(function (paymentResponse) {
							const result = {
								"status": ApplePaySession.STATUS_SUCCESS
							};
							applePaySession.completePayment(result);
							applePaySession = null;
							$('##applePayResultMessage').html('Payment successful.').addClass('alert alert-success').show().hide(6000);
						}, function (error) {
							$('##applePayResultMessage').html(JSON.stringify(error)).addClass('alert alert-danger').show();
							applePaySession.abort();
						});
				};

				applePaySession.oncancel = function(event) {};
				applePaySession.begin();
			}
		</script>
		<style>
			apple-pay-button {
				--apple-pay-button-width: 150px;
				--apple-pay-button-height: 30px;
				--apple-pay-button-border-radius: 3px;
				--apple-pay-button-padding: 0px 0px;
				--apple-pay-button-box-sizing: border-box;
			}
		</style>
	</cfif>
	<cfif val(local.merchantProfile.profileID) gt 0 and local.merchantProfile.enableGooglePay EQ 1 AND session.cfcuser.memberData.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
		<script type="application/javascript">
			let googlePayTestTransactionAmount = '0.00';
			const baseRequest = { apiVersion: 2, apiVersionMinor: 0 };
			const tokenizationSpecification = {
				type: 'PAYMENT_GATEWAY',
				parameters: {
					'gateway': 'authorizenet',
					'gatewayMerchantId': '#local.merchantProfile.gatewayAccountID#'
				}
			};
			const allowedCardNetworks = #serializeJson(local.googlePayCardTypes)#;
			const allowedCardAuthMethods = ["PAN_ONLY", "CRYPTOGRAM_3DS"];
			const baseCardPaymentMethod = {
				type: 'CARD',
				parameters: {
					allowedAuthMethods: allowedCardAuthMethods,
					allowedCardNetworks: allowedCardNetworks,
					assuranceDetailsRequired: true,
					billingAddressRequired: true,
					billingAddressParameters: {
						format: "MIN"
					}
				}
			};
			const cardPaymentMethod = Object.assign({tokenizationSpecification: tokenizationSpecification},baseCardPaymentMethod);

			let paymentsClient = null;

			function getGoogleIsReadyToPayRequest() {
				return Object.assign({},baseRequest,{allowedPaymentMethods: [baseCardPaymentMethod]});
			}

			function getGooglePaymentDataRequest() {
				const paymentDataRequest = Object.assign({}, baseRequest);
				paymentDataRequest.allowedPaymentMethods = [cardPaymentMethod];
				paymentDataRequest.transactionInfo = getGoogleTransactionInfo();
				paymentDataRequest.merchantInfo = {
					merchantId: '#local.merchantProfile.googlePayMerchantID#',
					merchantName: '#EncodeForJavaScript(arguments.event.getValue('mc_siteInfo.orgName'))#'
				};
				return paymentDataRequest;
			}
			function getGooglePaymentsClient() {
				if ( paymentsClient === null ) {
					paymentsClient = new google.payments.api.PaymentsClient({environment: <cfif application.MCEnvironment NEQ 'production'>'TEST'<cfelse>'PRODUCTION'</cfif>});
				}
				return paymentsClient;
			}
			function onGooglePayLoaded() {
				<cfif NOT application.objPlatform.isRequestSecure()>
					$('##googlePayNotAllowed').show();
				<cfelse>
					const paymentsClient = getGooglePaymentsClient();
					paymentsClient.isReadyToPay(getGoogleIsReadyToPayRequest())
						.then(function(response) {
							if (response.result) {
								addGooglePayButton();
								prefetchGooglePaymentData();
							}
						})
						.catch(function(err) {
							$('##googlePayResultMessage').html(JSON.stringify(err)).addClass('alert alert-danger').show();
						});
				</cfif>
			}
			function addGooglePayButton() {
				const paymentsClient = getGooglePaymentsClient();
				const button =
					paymentsClient.createButton({
						buttonColor: 'black',
						buttonType: 'pay',
						buttonRadius: 4,
						buttonSizeMode: 'fill',
						onClick: onGooglePaymentButtonClicked,
						allowedPaymentMethods: [baseCardPaymentMethod]
					});
				document.getElementById('googlePayAllowed').appendChild(button);
			}
			function getGoogleTransactionInfo() {
				googlePayTestTransactionAmount = (1+Math.random()).toFixed(2).toString();
				return {
					countryCode: 'US',
					currencyCode: 'USD',
					totalPriceStatus: 'FINAL',
					totalPrice: googlePayTestTransactionAmount
				};
			}
			function prefetchGooglePaymentData() {
				const paymentDataRequest = getGooglePaymentDataRequest();
				paymentDataRequest.transactionInfo = {
					totalPriceStatus: 'NOT_CURRENTLY_KNOWN',
					currencyCode: 'USD'
				};
				const paymentsClient = getGooglePaymentsClient();
				paymentsClient.prefetchPaymentData(paymentDataRequest);
			}
			function onGooglePaymentButtonClicked() {
				const paymentDataRequest = getGooglePaymentDataRequest();
				paymentDataRequest.transactionInfo = getGoogleTransactionInfo();
				const paymentsClient = getGooglePaymentsClient();
				paymentsClient.loadPaymentData(paymentDataRequest)
					.then(function(paymentData) {
						processPayment(paymentData);
					})
					.catch(function(err) {
						console.error(err);
					});
			}
			function processPayment(paymentData) {
				let MCMPID = #local.merchantProfile.profileID#;
				let MCMPCODE = '#local.merchantProfile.profileCode#';
				$.post('/?event=proxy.ts_json&c=GATEPAY&m=googlePayTestPP', { profileID: MCMPID, profileCode: MCMPCODE, amount: googlePayTestTransactionAmount, payload: JSON.stringify(paymentData)})
					.then(function (paymentResponse) {
						if (paymentResponse.success)
							$('##googlePayResultMessage').html('Payment successful.').removeClass('alert-danger').addClass('alert alert-success').show().hide(6000);
						else 
							$('##googlePayResultMessage').html('Payment unsuccessful').removeClass('alert-success').addClass('alert alert-danger').show().hide(6000);
					}, function (error) {
						$('##googlePayResultMessage').html(JSON.stringify(error)).removeClass('alert-success').addClass('alert alert-danger').show();
					});
			}
		</script>
		<script async src="https://pay.google.com/gp/p/js/pay.js" onload="onGooglePayLoaded()"></script>
	</cfif>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.mpjs#">

<cfoutput>
<cfif arguments.event.getValue('profileID')>
	<h4>Edit Payment Profile</h4>
<cfelse>
	<h4>Add Payment Profile</h4>
</cfif>

<div id="mpfrm_err" class="alert alert-danger my-3 d-none"></div>

<form action="#this.link.save#" method="post" name="frmMP" id="frmMP" onsubmit="return _validateMPForm();" enctype="multipart/form-data" autocomplete="off">
	<input type="hidden" name="profileID" id="profileID" value="#val(local.merchantProfile.profileID)#">
	<input type="hidden" name="paymentInstructionsContentID" id="paymentInstructionsContentID" value="#local.merchantProfile.paymentInstructionsContentID#">
	<input type="hidden" name="old_enableSurcharge" id="old_enableSurcharge" value="#val(local.merchantProfile.enableSurcharge)#">

	<div class="card card-box mt-2">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Basic Information</div>
		</div>
		<div class="card-body">
			<div class="form-group">
				<div class="form-label-group">
					<cfif val(local.merchantProfile.profileID) gt 0>
						<cfloop query="local.qryAllGateways">
							<cfif local.merchantProfile.gatewayID EQ local.qryAllGateways.gatewayID>
								<input type="hidden" name="gatewayID" id="gatewayID" value="#local.qryAllGateways.gatewayID#_#local.qryAllGateways.gatewayclass#">
								
								<input type="text" name="Listname" id="Listname" value="#local.qryAllGateways.gatewayTypeDesc#" class="form-control" readOnly>
								<cfbreak>
							</cfif>
						</cfloop>
					<cfelse>	
						<select id="gatewayID" name="gatewayID" class="form-control" onchange="doSelectGateway();">
							<option value=""></option>
							<cfloop query="local.qryAllGateways">
								<option value="#local.qryAllGateways.gatewayID#_#local.qryAllGateways.gatewayclass#"<cfif local.merchantProfile.gatewayID EQ local.qryAllGateways.gatewayID> selected</cfif>>#local.qryAllGateways.gatewayTypeDesc#</option>
							</cfloop>
						</select>
					</cfif>
					<label for="gatewayID">Payment Gateway:</label>
				</div>
			</div>

			<div id="trAffinipayConnect" style="display:none;">
				<div class="form-group row">
					<div class="col-sm-4 offset-sm-3">
						<button type="button" class="btn btn-sm btn-outline-dark" onClick="connectToAffinipay();">
							<img style="vertical-align: middle; padding-right: 10px;" src="/assets/common/images/sign-in-affinipay-logo.png"> Connect with AffiniPay
						</button>
					</div>
				</div>
			</div>

			<div id="trProfileInfo" style="display:none;">
				<div class="form-row">
					<div class="col-6">
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" name="profileName" id="profileName" class="form-control" value="#local.merchantProfile.profileName#" onBlur="fillTabTitle();">
								<label for="profileName">Profile Name:</label>
							</div>
						</div>
					</div>
					<div class="col-6">
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" name="tabTitle" id="tabTitle" class="form-control" value="#local.merchantProfile.tabTitle#">
								<label for="tabTitle">Checkout On Screen Label:</label>
							</div>
						</div>
					</div>
				</div>

				<div class="form-row">
					<div class="col-6">
						<div class="form-group">
							<div class="form-label-group">
								<input type="text" name="new.profileCode" id="new.profileCode" class="form-control" value="#local.merchantProfile.profileCode#" onchange="checkProfileCode();">
								<input type="hidden" name="old.profileCode" id="old.profileCode" value="#local.merchantProfile.profileCode#">
								<input type="hidden" name="profileCode.check" id="profileCode.check" value="true">
								<label for="new.profileCode" class="col-sm-3 col-form-label-sm font-size-md">Unique Profile Code:</label>
							</div>
						</div>
						<div id="pCodeMSG" class="alert alert-danger" style="display:none;"></div>
					</div>
					<cfif val(local.merchantProfile.profileID) gt 0>
						<div class="col-6">
							<div class="form-group">
								<div class="form-label-group">
									<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
										<input type="text" autocomplete="off" size="38" class="form-control" maxlength="60" name="profileUID" id="profileUID" required="yes" message="Enter the API ID" value="#local.merchantProfile.uid#">
									<cfelse>
										<input type="text" name="uid" id="uid" value="#local.merchantProfile.uid#" class="form-control" readOnly>	
									</cfif>
									<label for="profileUID" class="col-sm-3 col-form-label-sm font-size-md">API ID:</label>
								</div>
							</div>
						</div>
					</cfif>	
				</div>
			</div>	

			<div class="form-row">
				<div id="trGatewayUsername" style="display:none;" class="col-6">
					<div class="form-group">
						<div class="form-label-group">
							<cfif local.merchantProfile.hasCardsLinked is 1>
								<input type="hidden" name="gatewayUsername" id="gatewayUsername" value="#local.merchantProfile.gatewayUserName#">
								<input type="text" name="username" id="username" value="#local.merchantProfile.gatewayUserName#" class="form-control" readOnly>	
							<cfelse>
								<input type="text" name="gatewayUsername" id="gatewayUsername" class="form-control" value="#local.merchantProfile.gatewayUserName#">
							</cfif>
							<label for="gatewayUsername" class="col-sm-3 col-form-label-sm font-size-md">Gateway Username:</label>
						</div>
						<cfif local.merchantProfile.hasCardsLinked is 1>
							<div class="form-text small text-right">
								<a class="font-size-sm" href="javascript:void(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" data-html="true" title="There are credit cards associated with this payment profile.<br/>Changing this information could cause those cards to no longer be usable on the website.<br/>Contact MemberCentral for assistance.">Why can't I change this?</a>
							</div>
						</cfif>
					</div>
				</div>
				<div id="trGatewayPassword" style="display:none;" class="col-6">
					<div class="form-group">
						<div class="form-label-group">
							<cfif local.merchantProfile.gatewayType EQ 'AuthorizeCCCIM' OR local.merchantProfile.hasCardsLinked NEQ 1>
								<input type="text" name="gatewayPassword" id="gatewayPassword" class="form-control" value="#local.merchantProfile.gatewayPassword#">
							<cfelseif local.merchantProfile.hasCardsLinked is 1>
								<input type="hidden" name="gatewayPassword" id="gatewayPassword" value="#local.merchantProfile.gatewayPassword#">
								<input type="text" name="username" id="username" value="#local.merchantProfile.gatewayPassword#" class="form-control" readOnly>	
							</cfif>
							<label for="gatewayPassword" class="col-sm-3 col-form-label-sm font-size-md">Gateway Password:</label>
						</div>
					</div>
				</div>
			</div>

			<!--- edit pay profile --->
			<cfif local.merchantProfile.gatewayType EQ 'AuthorizeCCCIM'>
				<cfif local.keyExists("authCIMMerchantError")>
					<div class="alert alert-danger p-2 mx-2 my-3"><i class="fa-solid fa-circle-exclamation"></i> #local.authCIMMerchantError#</div>
				</cfif>
				
				<div id="trGatewayAccountID" class="form-row" style="display:none;">
					<div class="col-6">
						<div class="form-label-group">
							<input type="text" name="gatewayAccountID" id="gatewayAccountID" value="#local.merchantProfile.gatewayAccountID#" class="form-control" readOnly>
							<label for="gatewayAccountID" class="col-sm-3 col-form-label-sm font-size-md">Gateway ID:</label>
						</div>
					</div>
				</div>
			</cfif>

			<div class="form-row">
				<div id="trGatewayMerchantID" style="display:none;" class="col-6">
					<div class="form-group">
						<div class="form-label-group">
							<cfif local.merchantProfile.hasCardsLinked is 1>
								<input type="hidden" name="gatewayMerchantId" id="gatewayMerchantId" value="#local.merchantProfile.gatewayMerchantId#">
								<input type="text" name="merchantid" id="merchantid" value="#local.merchantProfile.gatewayMerchantId#" class="form-control" readOnly>	
							<cfelse>
								<input type="text" name="gatewayMerchantId" id="gatewayMerchantId" class="form-control" value="#local.merchantProfile.gatewayMerchantId#">
							</cfif>
							<label for="gatewayMerchantId" class="col-sm-3 col-form-label-sm font-size-md">Gateway Merchant ID:</label>
						</div>
					</div>
				</div>
			</div>

			<div id="mpOrgIdentityRow" class="mb-3" style="display:none;">
				<div class="small">This payment profile belongs to the following Organization Identity:</div>
				#local.strOrgIdentitySelector.html#	
			</div>

			<cfif application.MCEnvironment NEQ 'production'>
				<div id="gatewayCredsInfo" class="col-12 alert alert-info p-2 mx-2 mb-0" style="display:none;">
					<i class="fa-solid fa-circle-info pr-2"></i> API Login and Transaction Keys are only needed in production so we won't ask for them here. We'll use #local.merchantProfile.gatewayType# sandbox keys on #application.MCEnvironment#. 
				</div>
			</cfif>
		</div>
	</div>

	<div id="acctSettings" class="card card-box mt-2" style="display:none;">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Accounting Settings</div>	
		</div>
		<div class="card-body">
			<div id="trCashGL" style="display:none;">#local.strCashGLAcctWidget.html#</div>
			<div id="trBankAcctName" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group d-flex">
						<input type="text" name="bankAccountName" id="bankAccountName" class="form-control" value="#local.merchantProfile.bankAccountName#" maxlength="200">		
						<span class="mpBDGatewayFields m-2" style="display:none;">
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="right" data-trigger="hover" title="" data-original-title="Maps to Immediate Destination Name in ACH file."></i>
						</span>
						<label for="bankAccountName" class="col-sm-3 col-form-label-sm font-size-md">Bank Account Name:</label>
					</div>
				</div>
			</div>
			<div id="trTransLabel" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<input type="text" name="transactionLabel" id="transactionLabel" class="form-control" value="#local.merchantProfile.transactionLabel#" maxlength="25">	
						<label for="gatewayID">Transactions Appear As:</label>
					</div>
				</div>
			</div>
			<div id="trDraftQ" class="mb-4 mpBDGatewayFields" style="display:none;">
				<div class="form-group">
					<div class="form-label-group d-flex">
						<input type="text" name="bankCompanyName" id="bankCompanyName" class="form-control" value="#local.merchantProfile.bankCompanyName#" maxlength="16">
						<span class="m-2">
							<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="right" data-trigger="hover" title="" 
							data-original-title="Maps to Batch Header Record Company Name in ACH file."></i>
						</span>
						<label for="bankCompanyName" class="col-sm-3 col-form-label-sm font-size-md">Company Name:</label>
					</div>
					<div class="form-text small text-dim text-right">(16 Character Limit)</div>
				</div>
				<div class="form-group row">
					<label class="col-sm-3 col-form-label-sm font-size-md">Bank Routing Number:</label>
					<div class="col-sm-auto pr-1">
						<cfloop from="1" to="9" index="local.thisIt">
							<input type="text" name="bankTRN_#local.thisIt#" id="bankTRN_#local.thisIt#" value="#mid(local.merchantProfile.bankTRN,local.thisIt,1)#" size="1" maxlength="1" onfocus="select();" class="font-size-sm">
						</cfloop>
						&nbsp; &nbsp;&nbsp; (9 digits)
					</div>
					<span >
						<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="right" data-trigger="hover" title="" 
						data-original-title="Maps to Immediate Destination in ACH file. This is the bank receiving the file."></i>
					</span>
				</div>
				
				<div class="form-group row">
					<label class="col-sm-3 col-form-label-sm font-size-md">Immediate Origin:</label>
					<div class="col-sm-9">
						<cfloop from="1" to="10" index="local.thisIt">
							<input type="text" name="bankImmediateOrigin_#local.thisIt#" id="bankImmediateOrigin_#local.thisIt#" value="#mid(local.merchantProfile.bankImmediateOrigin,local.thisIt,1)#" size="1" maxlength="1" onfocus="select();" class="font-size-sm">
						</cfloop>
						(10 digits, often 1 + 9 digit federal tax ID)
					</div>
				</div>
				<div class="form-group row">
					<label for="bankImmediateOriginName" class="col-sm-3 col-form-label-sm font-size-md">Immediate Origin Name:</label>
					<div class="col-sm-4 pr-1">
						<input type="text" name="bankImmediateOriginName" id="bankImmediateOriginName" class="form-control form-control-sm" value="#local.merchantProfile.bankImmediateOriginName#" maxlength="23">
						<div class="form-text small text-dim text-right">(23 Character Limit)</div>
					</div>
				</div>
				<div class="form-group row">
					<label class="col-sm-3 col-form-label-sm font-size-md">Bank Company Number:</label>
					<div class="col-sm-auto pr-1">
						<cfloop from="1" to="10" index="local.thisIt">
							<input type="text" name="bankCompanyNumber_#local.thisIt#" id="bankCompanyNumber_#local.thisIt#" value="#mid(local.merchantProfile.bankCompanyNumber,local.thisIt,1)#" size="1" maxlength="1" onfocus="select();" class="font-size-sm">
						</cfloop>
						(10 digits, often 1 + 9 digit federal tax ID)
					</div>
					<span>
						<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="right" data-trigger="hover" title="" 
						data-original-title="Maps to Company Identification in ACH file."></i>
					</span>
				</div>
				<div class="form-group row">
					<label for="bankCompanyEnterDesc" class="col-sm-3 col-form-label-sm font-size-md">Company Entry Description:</label>
					<div class="col-sm-4 pr-1">
						<input type="text" name="bankCompanyEnterDesc" id="bankCompanyEnterDesc" class="form-control form-control-sm" value="#local.merchantProfile.bankCompanyEnterDesc#" maxlength="10">
						<div class="form-text small text-dim text-right">(10 Character Limit)</div>
					</div>
					<span class="pt-1">
						<i class="fa-solid fa-circle-question" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="right" data-trigger="hover" title="" 
						data-original-title="This displays after the Standard Entry Class Code (PPD etc) in the ACH File."></i>
					</span>
				</div>
			</div>
			<div id="trPayInvoices" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<select name="allowPayInvoicesOnline" id="allowPayInvoicesOnline" class="custom-select">
							<option value="0" <cfif local.merchantProfile.allowPayInvoicesOnline is 0>selected</cfif>>No, this profile is not available when paying invoices online.</option>
							<option value="1" <cfif local.merchantProfile.allowPayInvoicesOnline is 1>selected</cfif>>Yes, allow members to use this profile when paying invoices online.</option>
						</select>
						<label for="allowPayInvoicesOnline">Paying Invoices:</label>
					</div>
				</div>	
			</div>
			<div id="trPayments" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
					<select name="allowPayments" id="allowPayments" class="custom-select">
							<option value="0" <cfif local.merchantProfile.allowPayments is 0>selected</cfif>>No, do not allow payments to use this profile.</option>
							<option value="1" <cfif local.merchantProfile.allowPayments is 1>selected</cfif>>Yes, allow payments to use this profile.</option>
						</select>
					<label for="allowPayments" class="col-sm-3 col-form-label-sm font-size-md">Payments:</label>
					</div>
				</div>	
			</div>
			<div id="trRefunds" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<select name="allowRefundsFromAnyProfile" id="allowRefundsFromAnyProfile" class="custom-select">
							<option value="0" <cfif local.merchantProfile.allowRefunds is 0>selected </cfif>>No, no refunds can use this profile</option>
							<option value="1" <cfif local.merchantProfile.allowRefunds is 1 AND local.merchantProfile.allowRefundsFromAnyProfile IS 0>selected </cfif>>Yes, but only if the original payment used this profile</option>
							<option value="2" <cfif local.merchantProfile.allowRefunds is 1 AND local.merchantProfile.allowRefundsFromAnyProfile IS 1>selected </cfif>>Yes, allow refunds from this profile even if the original payment used a different profile</option>
							
						</select>
						<label for="allowRefundsFromAnyProfile">Refunds:</label>
					</div>
				</div>	

				<div id="divRefundsImageUpload" class="form-group row mt-4" <cfif local.merchantProfile.allowRefunds is 0>style="display:none;"</cfif>>
					<label class="col-sm-auto col-form-label-sm font-size-md">Image for Refund Statement and Payment Allocation Statement:</label>
					<div class="col-sm-auto">
						<div class="custom-file form-control-sm mr-2">
							<input type="file" name="refundImage" id="refundImage" class="custom-file-input importFileControl">
							<label for="importFileName" class="custom-file-label">Choose File</label>
						</div>
						<div class="mt-1 text-dim small">
							We accept GIF or PNG image files that are 400 pixels wide x 150 pixels high.<br/>It is best if the images have a white background.
						</div>
						<cfif val(local.merchantProfile.profileID) gt 0 and FileExists('#application.paths.RAIDUserAssetRoot.path#common/refunds/#local.merchantProfile.profileID#.#local.merchantProfile.refundImageExt#')>
							<div class="mt-2">
								<img class="pendingImage border" src="/userassets/common/refunds/#local.merchantProfile.profileID#.#local.merchantProfile.refundImageExt#?rand=#randrange(1,1000)#" data-imgsrc="/userassets/common/refunds/#local.merchantProfile.profileID#.#local.merchantProfile.refundImageExt#?rand=#randrange(1,1000)#">
								<div class="mt-1">
									<div class="form-check">
										<input type="checkbox" name="deleteImage" id="deleteImage" value="true" class="form-check-input">
										<label for="deleteImage" class="form-check-label">Delete Current Image</label>
									</div>
								</div>
							</div>
						</cfif>
					</div>
				</div>
			</div>
			<div id="secCodes" class="my-4" style="display:none;">
				<div class="form-group row">
					<label class="col-sm-3 col-form-label-sm font-size-md">Standard Entry Class Codes:</label>
					<div class="col-sm-9">
						You are required to have a pre-existing banking agreement with your depository bank to process ACH Bank Drafts.
						MemberCentral provides a methodology defined by NACHA for CCD, PPD, and WEB -classed transactions. 
						Your bank can assist you in determining the types of ACH transactions you wish to process.
						<br/><br/>
						<table class="table table-sm table-bordered">
						<tr><th>SEC&nbsp;Code</th><th>&nbsp;</th></tr>
						<cfloop query="local.qryAllSECCodes"> 
							<tr valign="top">
								<td nowrap>
									<input type="checkbox" class="secCode" id="secCodeID_#local.qryAllSECCodes.seccodeID#" name="secCodeID_#local.qryAllSECCodes.seccodeID#" value="1" <cfif listFind(valueList(local.seccodes.secCodeID),local.qryAllSECCodes.secCodeID)> CHECKED</cfif>> <label for="secCodeID_#local.qryAllSECCodes.seccodeID#">#local.qryAllSECCodes.secCode#</label>
								</td>
								<td>#local.qryAllSECCodes.notes#</td>
							</tr>
						</cfloop>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>

	<div id="MCPaySettings" class="card card-box mt-2" style="display:none;">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">MemberCentral Pay Settings</div>
		</div>
		<div class="card-body">
			<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				<div class="form-check pl-0 mb-3">
					<input type="checkbox" name="enableMCPay" id="enableMCPay" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" data-style="mr-2" <cfif val(local.merchantProfile.enableMCPay) EQ 1>checked</cfif> onchange="toggleMCPaySettings(this);" autocomplete="off">
					<label for="enableMCPay" class="form-check-label">Enable MemberCentral Pay</label>
					<span class="superuser font-size-xs"></span>
				</div>
			</cfif>

			<div id="MCPaySettingsContainer"<cfif NOT val(local.merchantProfile.enableMCPay)> class="d-none"</cfif>>
				<ul id="MCPaySettingsTabs" class="nav nav-line nav-line-alt" style="background-color:##f4f5fd;padding:15px 15px 0px 15px;border:1px solid ##d6d6df;">
					<cfset local.thisTabName = "processingFeeSettings">
					<cfset local.thisTabID = "processingFeeSettingsTab">
					<li class="nav-item processFeeDonationSettings">
						<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
							Voluntary Processing Fee Donation
							<div class="divider"></div>
						</a>
					</li>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						<cfset local.thisTabName = "surchargeSettings">
						<cfset local.thisTabID = "surchargeSettingsTab">
						<li class="nav-item">
							<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
								Surcharge<span class="superuser ml-2" style="font-size:8px;"></span>
								<div class="divider"></div>
							</a>
						</li>
					</cfif>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) OR val(local.merchantProfile.enableApplePay)>
						<cfset local.thisTabName = "applePaySettings">
						<cfset local.thisTabID = "applePaySettingsTab">
						<li class="nav-item">
							<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
								Apple Pay<span class="ml-2" style="font-size:8px;"></span>
								<div class="divider"></div>
							</a>
						</li>
					</cfif>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) OR val(local.merchantProfile.enableGooglePay)>
						<cfset local.thisTabName = "googlePaySettings">
						<cfset local.thisTabID = "googlePaySettingsTab">
						<li class="nav-item">
							<a class="nav-link" id="#local.thisTabID#" data-toggle="tab" href="##tab-#local.thisTabID#" role="tab" aria-controls="tab-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">
								Google Pay<span class="ml-2" style="font-size:8px;"></span>
								<div class="divider"></div>
							</a>
						</li>
					</cfif>
				</ul>
				<div class="tab-content p-3" style="border:1px solid ##d6d6df;border-top:0;">
					<div class="tab-pane fade" id="tab-processingFeeSettingsTab" role="tabpanel" aria-labelledby="processingFeeSettings">
						<div class="processFeeDonationSettings" style="display:none;">
							<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
								<div id="activateProcFeeSettingController" class="form-group" style="display:none;">
									<div class="form-label-group">
										<select name="activateProcessingFeeSettings" id="activateProcessingFeeSettings" class="custom-select" onchange="activateProcessFeeDonationSettings();" autocomplete="off">
											<option value="0" <cfif local.merchantProfile.activateProcessingFeeSettings is 0>selected</cfif>>No, do not activate Voluntary Processing Fee Donation settings for this profile</option>
											<option value="1" <cfif local.merchantProfile.activateProcessingFeeSettings is 1>selected</cfif>>Yes, activate Voluntary Processing Fee Donation settings for this profile</option>
										</select>
										<label for="activateProcessingFeeSettings" class="superuser">Activate Voluntary Processing Fee Donation Settings</label>
									</div>
								</div>
							<cfelse>
								<input type="hidden" name="activateProcessingFeeSettings" id="activateProcessingFeeSettings" value="#val(local.merchantProfile.activateProcessingFeeSettings)#">
							</cfif>
							<div id="enabledProcFeeSettingControllers" style="display:none;">
								<div class="form-group">
									<div class="form-label-group">
										<select name="enableProcessingFeeDonation" id="enableProcessingFeeDonation" class="custom-select" onchange="onChangeProcessFeeDonationSettings();" autocomplete="off">
											<option value="0" <cfif local.merchantProfile.enableProcessingFeeDonation is 0>selected</cfif>>No, do not ask members to voluntarily pay Processing Fee Donation when paying via this profile</option>
											<option value="1" <cfif local.merchantProfile.enableProcessingFeeDonation is 1>selected</cfif>>Yes, ask members to voluntarily pay Processing Fee Donation when paying via this profile</option>
										</select>
										<label for="enableProcessingFeeDonation">Enable Voluntary Processing Fee Donation</label>
									</div>
								</div>
								<div id="procFeeDonationSettings"<cfif val(local.merchantProfile.enableProcessingFeeDonation) is 0> class="d-none"</cfif>>
									<div class="form-row mt-5">
										<div class="col-2">
											<div class="form-group">
												<div class="form-label-group">
													<div class="input-group input-group-seamless">
														<input type="text" class="form-control" name="processFeeDonationFeePercent" id="processFeeDonationFeePercent" value="#local.merchantProfile.processFeeDonationFeePercent#" maxlength="6">
														<div class="input-group-append">
															<span class="input-group-text"><i class="fa-solid fa-percent"></i></span>
														</div>
														<label for="processFeeDonationFeePercent">Percentage of Final Payment Amount</label>
													</div>
												</div>
											</div>
										</div>
										<div class="col-5">
											<div class="form-group">
												<div class="form-label-group">
													<input type="text" class="form-control" name="processingFeeLabel" id="processingFeeLabel" value="#len(local.merchantProfile.processingFeeLabel) ? local.merchantProfile.processingFeeLabel : 'Payment Donation'#" maxlength="30">
													<label for="processingFeeLabel">Label Displayed Next to the Amount on the Payment Receipt</label>
												</div>
											</div>
										</div>
										<div class="col-5">
											<div class="form-group">
												<div class="form-label-group">
													<input type="text" name="processFeeDonationRevTransDesc" id="processFeeDonationRevTransDesc" class="form-control" value="#len(local.merchantProfile.processFeeDonationRevTransDesc) ? local.merchantProfile.processFeeDonationRevTransDesc : 'Processing Fee Donation - THANK YOU'#" maxlength="100">
													<label for="processFeeDonationRevTransDesc">Revenue Transaction Description</label>
												</div>
											</div>
										</div>
									</div>
									#local.strPFDGLAcctWidget.html#
				
									<div class="mt-4 mb-2">
										Customize the messaging shown to your members when requesting a voluntary processing fee donation.<br/>
										<div class="small">
											<i>Please note:</i> These settings will be the default for all payments made using this payment profile. 
											If desired, the Voluntary Processing Fee Donation can be disabled for specific Invoice Profiles. 
											Additionally, Solicitation Title and Message Shown During the Payment Process as well as the Default Selection can be customized for each Invoice Profile.
										</div>
									</div>
									<cfset local.strSolicitationMsgSelector = createObject("component","model.admin.common.modules.solicitationMessageSelector.solicitationMessageSelector").getMessageSelector(
										siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="solicitationMessageID", selectedValueID=val(local.merchantProfile.solicitationMessageID), usageMode='paymentProfile')>
									<div class="form-group">
										<div class="pb-0 pl-3 small">Solicitation Title and Message Shown During the Payment Process</div>
										<div>#local.strSolicitationMsgSelector.html#</div>
									</div>
				
									<cfif arguments.event.getValue('mc_siteInfo.sf_contributions') EQ 1>
										<div class="form-row">
											<div class="col-6">
												<div class="form-group">
													<div class="form-label-group">
														<input type="text" class="form-control processFeeYesLabel" name="processFeeContributionsFELabel" id="processFeeContributionsFELabel" data-mc="p" value="#len(local.merchantProfile.processFeeContributionsFELabel) ? local.merchantProfile.processFeeContributionsFELabel : 'Additionally pay {{PERCENT}} to cover processing fees when automatically paying invoices for this contribution.'#" maxlength="200" onBlur="validateYesLabel($(this),1);">
														<label id="lblprocessFeeContributionsFELabel" for="processFeeContributionsFELabel">YES Option Label for Contributions Payments</label>
													</div>
												</div>
											</div>
											<div class="col-6">
												<div class="form-group">
													<div class="form-label-group">
														<input type="text" class="form-control" name="processFeeContributionsFEDenyLabel" id="processFeeContributionsFEDenyLabel" value="#len(local.merchantProfile.processFeeContributionsFEDenyLabel) ? local.merchantProfile.processFeeContributionsFEDenyLabel : 'Not at this time.'#" maxlength="200">
														<label for="processFeeContributionsFEDenyLabel">NO Option Label for Contributions Payments</label>
													</div>
												</div>
											</div>
										</div>
									</cfif>
									<cfif arguments.event.getValue('mc_siteInfo.sf_subscriptions') EQ 1>
										<div class="form-row">
											<div class="col-6">
												<div class="form-group">
													<div class="form-label-group">
														<input type="text" class="form-control processFeeYesLabel" name="processFeeSubscriptionsFELabel" id="processFeeSubscriptionsFELabel" data-mc="p" value="#len(local.merchantProfile.processFeeSubscriptionsFELabel) ? local.merchantProfile.processFeeSubscriptionsFELabel : 'Additionally pay {{PERCENT}} to cover processing fees when automatically paying invoices for this subscription.'#" maxlength="200" onBlur="validateYesLabel($(this),1);">
														<label id="lblprocessFeeSubscriptionsFELabel" for="processFeeSubscriptionsFELabel">YES Option Label for Subscriptions Payments</label>
													</div>
												</div>
											</div>
											<div class="col-6">
												<div class="form-group">
													<div class="form-label-group">
														<input type="text" class="form-control" name="processFeeSubscriptionsFEDenyLabel" id="processFeeSubscriptionsFEDenyLabel" value="#len(local.merchantProfile.processFeeSubscriptionsFEDenyLabel) ? local.merchantProfile.processFeeSubscriptionsFEDenyLabel : 'Not at this time.'#" maxlength="200">
														<label for="processFeeSubscriptionsFEDenyLabel">NO Option Label for Subscriptions Payments</label>
													</div>
												</div>
											</div>
										</div>
									</cfif>
				
									<div class="form-row">
										<div class="col-6">
											<div class="form-group">
												<div class="form-label-group">
													<input type="text" class="form-control processFeeYesLabel" name="processFeeOtherPaymentsFELabel" id="processFeeOtherPaymentsFELabel" data-mc="a" value="#len(local.merchantProfile.processFeeOtherPaymentsFELabel) ? local.merchantProfile.processFeeOtherPaymentsFELabel : 'Optionally pay {{AMOUNT}} to cover processing fees.'#" maxlength="200" onBlur="validateYesLabel($(this),1);">
													<label id="lblprocessFeeOtherPaymentsFELabel" for="processFeeOtherPaymentsFELabel">YES Option Label for All Other Payments (Events, etc.)</label>
												</div>
											</div>
										</div>
										<div class="col-6">
											<div class="form-group">
												<div class="form-label-group">
													<input type="text" class="form-control" name="processFeeOtherPaymentsFEDenyLabel" id="processFeeOtherPaymentsFEDenyLabel" value="#len(local.merchantProfile.processFeeOtherPaymentsFEDenyLabel) ? local.merchantProfile.processFeeOtherPaymentsFEDenyLabel : 'Not at this time.'#" maxlength="200">
													<label for="processFeeOtherPaymentsFEDenyLabel">NO Option Label for All Other Payments (Events, etc.)</label>
												</div>
											</div>
										</div>
									</div>
									<div class="form-group">
										<div class="form-label-group">
											<select name="processFeeDonationDefaultSelect" id="processFeeDonationDefaultSelect" class="custom-select" autocomplete="off">
												<option value="0" <cfif local.merchantProfile.processFeeDonationDefaultSelect is 0>selected</cfif>>No default selection, but member is required to select Yes or No option when paying.</option>
												<option value="1" <cfif local.merchantProfile.processFeeDonationDefaultSelect is 1>selected</cfif>>Pre-select the YES option by default but members may still opt-out.</option>
											</select>
											<label for="processFeeDonationDefaultSelect">Default Selection</label>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!--- Surcharge --->
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
						<div class="tab-pane fade" id="tab-surchargeSettingsTab" role="tabpanel" aria-labelledby="surchargeSettings">
							<div class="form-check pl-0">
								<input type="checkbox" name="enableSurcharge" id="enableSurcharge" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" data-style="mr-2" <cfif val(local.merchantProfile.enableSurcharge) EQ 1>checked</cfif> onchange="toggleSurchargeSettings(this);" autocomplete="off">
								<label for="enableSurcharge" class="form-check-label">Enable Surcharge</label>
							</div>
							<div id="surchargeSettingFields" class="mt-3<cfif val(local.merchantProfile.enableSurcharge) EQ 0> d-none</cfif>">
								<div class="form-row mt-5">
									<div class="col-2">
										<div class="form-group">
											<div class="form-label-group">
												<div class="input-group input-group-seamless">
													<input type="text" class="form-control" name="surchargePercent" id="surchargePercent" value="#local.merchantProfile.surchargePercent#" maxlength="6">
													<div class="input-group-append">
														<span class="input-group-text"><i class="fa-solid fa-percent"></i></span>
													</div>
													<label for="surchargePercent">Percentage of Final Payment Amount</label>
												</div>
											</div>
										</div>
									</div>
								</div>
								#local.strSurchargeGLAcctWidget.html#
							</div>
						</div>
					</cfif>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) OR val(local.merchantProfile.enableApplePay)>
						<div class="tab-pane fade" id="tab-applePaySettingsTab" role="tabpanel" aria-labelledby="applePaySettings">
							<div class="form-check pl-0">
								<input type="checkbox" name="enableApplePay" id="enableApplePay" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" data-style="mr-2" <cfif val(local.merchantProfile.enableApplePay) EQ 1>checked</cfif> onchange="toggleApplePaySettings(this);" autocomplete="off"<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)> disabled</cfif>>
								<label for="enableApplePay" class="form-check-label">Enable Apple Pay</label>
								<span class="superuser font-size-xs"></span>
							</div>
							<div id="applePaySettingFields" class="mt-3<cfif val(local.merchantProfile.enableApplePay) EQ 0> d-none</cfif>">
								<cfif local.merchantProfile.enableApplePay EQ 1>
									<cfif session.cfcuser.memberData.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
										<div id="applePayAllowed" class="d-flex mb-0" style="display:none">
											<div class="col-auto align-self-center">Test ApplePay:</div>
											<apple-pay-button buttonstyle="black" type="plain" locale="en-US" onclick="applePayTest();"></apple-pay-button>
										</div>
										<div id="applePayNotAllowed" style="display:none" class="alert alert-info mt-2">Apple Pay Test is not available on this device.</div>
										<div id="applePayResultMessage" style="display:none"></div>
									<cfelse>
										<div class="alert alert-danger mt-2">Login as a site administrator to test this payment method.</div>
									</cfif>
								</cfif>
							</div>
						</div>
					</cfif>
					<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) OR val(local.merchantProfile.enableGooglePay)>
						<div class="tab-pane fade" id="tab-googlePaySettingsTab" role="tabpanel" aria-labelledby="googlePaySettings">
							<cfif local.merchantProfile.recordcount>
								<div class="form-check pl-0">
									<input type="checkbox" name="enableGooglePay" id="enableGooglePay" value="1" class="form-check-input" data-toggle="toggle" data-size="sm" data-style="mr-2" <cfif val(local.merchantProfile.enableGooglePay) EQ 1>checked</cfif> onchange="toggleGooglePaySettings(this);" autocomplete="off"<cfif NOT application.objUser.isSuperUser(cfcuser=session.cfcuser)> disabled</cfif>>
									<label for="enableGooglePay" class="form-check-label">Enable Google Pay</label>
									<span class="superuser font-size-xs"></span>
								</div>
								<div id="googlePaySettingFields" class="mt-3<cfif val(local.merchantProfile.enableGooglePay) EQ 0> d-none</cfif>">
									<cfif application.MCEnvironment EQ 'production' AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
										<div class="form-label-group">
											<input type="text" name="googlePayMerchantID" id="googlePayMerchantID" class="form-control" value="#local.merchantProfile.googlePayMerchantID#" maxlength="20">
											<label for="googlePayMerchantID">Google Pay Merchant ID</label>
										</div>
									</cfif>
									<cfif local.merchantProfile.enableGooglePay EQ 1>
										<cfif session.cfcuser.memberData.orgcode EQ arguments.event.getValue('mc_siteInfo.orgcode')>
											<div class="d-flex mb-0">
												<div class="col-auto align-self-center">Test Google Pay:</div>
												<div id="googlePayAllowed" class="mb-0" style="width:200px;"></div>
											</div>
											<div id="googlePayNotAllowed" style="display:none" class="alert alert-info mt-2">Google Pay Test is not available on this device.</div>
											<div id="googlePayResultMessage" style="display:none"></div>
										<cfelse>
											<div class="alert alert-danger mt-2">Login as a site administrator to test this payment method.</div>
										</cfif>
									</cfif>
								</div>
							<cfelse>
								<div class="alert alert-info">Google Pay settings will be available after adding the payment profile.</div>
							</cfif>
						</div>
					</cfif>
				</div>
			</div>
		</div>
	</div>
	
	<div id="autoSettings" class="card card-box mt-2 " style="display:none;">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Automatic Actions For Failed Payments</div>	
		</div>
		<div class="card-body">
			<div id="trDaysBetweenAutoAttempts" class="mb-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<select name="daysBetweenAutoAttempts" id="daysBetweenAutoAttempts" class="custom-select">
							<option value="0" <cfif val(local.merchantProfile.daysBetweenAutoAttempts) IS 0>selected </cfif> >Reattempt the next day</option>
							<cfloop from="1" to="30" index="local.counter">
								<option value="#local.counter#" <cfif val(local.merchantProfile.daysBetweenAutoAttempts) IS local.counter >selected </cfif>>Skip #local.counter# Days</option>
							</cfloop>
						</select>
						<label for="daysBetweenAutoAttempts">Days to Skip Between Reattempts on Failed Invoices</label>
					</div>
				</div>
			</div>
			<div id="trMaxFailedAutoAttempts" class="my-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<select name="maxFailedAutoAttempts" id="maxFailedAutoAttempts" class="custom-select">
							<option value="0" <cfif val(local.merchantProfile.maxFailedAutoAttempts) IS 0> selected </cfif>>No Limit</option>
							<cfloop from="1" to="30" index="local.counter">
								<option value="#local.counter#" <cfif val(local.merchantProfile.maxFailedAutoAttempts) IS local.counter >selected </cfif>>Stop after #local.counter# Failed Attempts</option>
							</cfloop>
						</select>
						<label for="maxFailedAutoAttempts">Maximum Autopay attempts per Invoice</label>
					</div>
				</div>
			</div>
			<div id="trMinDaysFailedCleanup" class="my-4" style="display:none;">
				<div class="form-group">
					<div class="form-label-group">
						<select name="minDaysFailedCleanup" id="minDaysFailedCleanup" class="custom-select">
							<cfloop from="1" to="99" index="local.counter">
								<option value="#local.counter#" <cfif val(local.merchantProfile.minDaysFailedCleanup) EQ local.counter >selected </cfif>>After #local.counter# Days</option>
							</cfloop>
						</select>
						<label for="minDaysFailedCleanup">Days to Wait Before Auto Deleting Failed Cards with zero successful payments</label>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div id="cardTypes" class="card card-box mt-2 " style="display:none;">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Card Types</div>	
		</div>
		<div class="card-body">
			<div class="form-group row">
				<div class="col-sm-12">
					<table class="table table-sm table-bordered">
					<tr><th class="w-25">Card Type</th><th>GL Account</th></tr>
					<cfloop query="local.qryAllCreditCards">
						<cfset local.cardTypeWidget = "">
						<cfloop array="#local.arrCardTypeGLWidgets#" index="local.widget">
							<cfif local.widget.cardTypeID EQ local.qryAllCreditCards.cardTypeID>
								<cfset local.cardTypeWidget = local.widget.widget>
								<cfbreak>
							</cfif>
						</cfloop>
						<tr valign="top">
							<td nowrap>
								<input type="checkbox" class="cardType" id="cardTypeID_#local.qryAllCreditCards.cardTypeID#" name="cardTypeID_#local.qryAllCreditCards.cardTypeID#" onClick="toggleCardTypeGL(#local.qryAllCreditCards.cardTypeID#);" value="1" <cfif listFind(valueList(local.creditcards.cardTypeID),local.qryAllCreditCards.cardTypeID)> CHECKED</cfif>> <label for="cardTypeID_#local.qryAllCreditCards.cardTypeID#">#local.qryAllCreditCards.cardType#</label>
							</td>
							<td>
								<div id="div_ct_#local.qryAllCreditCards.cardTypeID#_gllink" <cfif not listFind(valueList(local.creditcards.cardTypeID),local.qryAllCreditCards.cardTypeID)>style="display:none;"</cfif>>
									#local.cardTypeWidget.html#
								</div>
								<div id="div_ct_#local.qryAllCreditCards.cardTypeID#_glna" <cfif listFind(valueList(local.creditcards.cardTypeID),local.qryAllCreditCards.cardTypeID)>style="display:none;"</cfif>>
									not applicable
								</div>
							</td>
						</tr>
					</cfloop>
					</table>
				</div>
			</div>
		</div>
	</div>
	<div id="paymentDetails" class="card card-box mt-2 " style="display:none;">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Payment Fields</div>	
		</div>
		<div class="card-body">
			<div id="trPaymentInstructions" class="my-4" style="display:none;">
				<div class="form-group row">
					<div class="col-sm-12">
						#application.objWebEditor.showContentBoxWithLinks(fieldname='paymentInstructionsContent', fieldlabel='Payment Instructions', contentID=val(local.merchantProfile.paymentInstructionsContentID), content=local.merchantProfile.paymentInstructionsContent, allowMergeCodes=0, supportsBootstrap=true, allowVersioning=true)#
					</div>
				</div>
			</div>

			<div id="trPaymentFields" class="my-4" style="display:none;">
				<div class="form-group row">
					
					<div class="col-sm-9">
						<!--- need to get all field types --->
						<!--- need to get selected fields to be checked --->
						<table class="table table-sm table-bordered">
						<tr>
							<th>Field Name</th>
							<th>Type</th>
							<th align="center">Use Field</th>
							<th align="center">Required</th>
						</tr>
						<cfloop query="local.qryAllFields">
							<tbody id="fields_#local.qryAllFields.fieldID#" class="fieldscbox #local.qryAllFields.fieldTypeClass#">
								<tr>
									<td>#local.qryAllFields.fieldName#</td>
									<td>#local.qryAllFields.fieldType#</td>
									<td align="center"><input onClick="checkFieldID(this.name);" type="checkbox" id="fieldID_#local.qryAllFields.fieldID#" name="fieldID_#local.qryAllFields.fieldTypeClass#_#local.qryAllFields.fieldID#" value="1" <cfif listFind(valueList(local.fields.fieldID),local.qryAllFields.fieldID)> CHECKED</cfif>></td>
									<td align="center"><input onClick="checkIsRequired(this.name);" type="checkbox" id="isRequired_#local.qryAllFields.fieldID#" name="isRequired_#local.qryAllFields.fieldTypeClass#_#local.qryAllFields.fieldID#" value="1" <cfif listFind(valueList(local.requiredFields.fieldID),local.qryAllFields.fieldID)> CHECKED</cfif>></td>
								</tr>
							</tbody>
							<tbody id="fields_#local.qryAllFields.fieldID#_gateway" class="fieldsgateway #local.qryAllFields.fieldTypeClass#">
								<tr>
									<td>#local.qryAllFields.fieldName#</td>
									<td>#local.qryAllFields.fieldType#</td>
									<td align="center" colspan="2">Automatically included</td>
								</tr>
							</tbody>
						</cfloop>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>		

	<div id="trSaveButton" class="my-4 text-right" style="display:none;">
		<button type="submit" name="Submit" class="btn btn-sm btn-primary">Save Profile</button>
	</div>
</form>
</cfoutput>