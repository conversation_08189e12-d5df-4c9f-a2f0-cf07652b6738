<cfsavecontent variable="local.registrantJS">
	<cfoutput>
	<script language="javascript">
	let evDocumentsTable;

	function initializeDocumentsTable(){
		let evDocsListTemplateSource = $('##evDocsListTemplate').html().replace(/[\t\n]+/g,'');
		let evDocsListTemplate = Handlebars.compile(evDocsListTemplateSource);

		evDocumentsTable = $('##evDocumentsTable').DataTable({
			"processing": true,
			"serverSide": true,
			"paging": false,
			"language": {
				"lengthMenu": "_MENU_",
				"emptyTable": "No event documents found"
			},
			"info": false,
			"ajax": { 
				"url": "#local.eventDocumentsLink#",
				"type": "post",
			},
			"autoWidth": false,
			"columns": [
				{ "className": 'DocGrpControl border-right-0 text-center',
					"orderable": false,
					"data": null,
					"defaultContent": '',
					"width": "4%",
					"render": function ( data, type, row, meta ) {
						return type === 'display' ? '<a href="javascript:void(0);"><i class="fa-regular fa-square-plus font-size-lg DocGrpControlIcon"></i></a>' : data;
					}
				},
				{ "data": "eventDocumentGrouping", "className": 'border-right', "width": "51%", "orderable": false },
				{ "orderable": false, "data": null, "defaultContent": '', "width": "20%" },
				{ "data": null,
					"render": function ( data, type, row, meta ) {
						let renderData = '';
						if (type === 'display') {
							if (data.eventDocumentGroupingID > 0) {
								renderData += '<a href="##" class="btn btn-xs btn-outline-primary px-1 m-1" onclick="editDocumentGrouping('+data.eventDocumentGroupingID+');return false;"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs btn-outline-danger px-1 m-1" onclick="removeDocumentGrouping('+data.eventDocumentGroupingID+');return false;"><i class="fa-solid fa-trash-can"></i></a>';
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs btn-outline-success px-1 m-1 evDocGrpMoveUpLink'+(data.canmoveup ? "" : " invisible")+'" title="Move Up" onclick="moveEvDocGrp('+data.eventDocumentGroupingID+',\'up\');return false;"><i class="fa-solid fa-arrow-up"></i></a>';
								renderData += '<a href="##" class="btn btn-xs btn-outline-success px-1 m-1 evDocGrpMoveDownLink'+(data.canmovedown ? "" : " invisible")+'" title="Move Down" onclick="moveEvDocGrp('+data.eventDocumentGroupingID+',\'down\');return false;"><i class="fa-solid fa-arrow-down"></i></a>';
							} else {
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs px-1 m-1 invisible"><i class="fa-solid fa-pencil"></i></a>';
							}
						}
						return type === 'display' ? renderData : data;
					},
					"width": "25%",
					"orderable": false,
					"className":"text-center"
				}
			],
			"order": false,
			"searching": false,
			"createdRow": function( row, data, index ) {
				if ( data.arrEvDocs.length == 0 ) {
					$(row).find('td.DocGrpControl a').hide();
				}
				if (data.eventDocumentGroupingID > 0) {
					$(row).addClass('evDocGrpParentRow evDocGrpRow'+data.eventDocumentGroupingID);
				}
			},
			"drawCallback": function(settings) {
				$('##evDocumentsTable tbody td.DocGrpControl').trigger('click');
			}
		});

		$('##evDocumentsTable tbody').on('click', 'td.DocGrpControl', function () {
			var tr = $(this).closest('tr');
			$(this).find('.DocGrpControlIcon').toggleClass('fa-square-plus fa-square-minus');
			var row = evDocumentsTable.row( tr );
			if (row.child.isShown()) {
				row.child.hide();
				tr.removeClass('shown');
			} else {
				row.child(evDocsListTemplate(row.data()), 'border-top-0 p-0 evDocGrpRow'+row.data().eventDocumentGroupingID).show();
				tr.addClass('shown');
			}

			/* hide default - no grouping row if no other groupings found */
			if ($('td.DocGrpControl').length == 1) {
				$('##evDocGrpRow0').addClass('d-none');
				$('##tblEvDocGrp0').find('tr td:first-child').remove();
			}
		});
	}
	function resetEvDocsActionIcons(evDocGrpID) {
		$('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow td a.evDocMoveUpLink, ##tblEvDocGrp'+evDocGrpID+' tr.evDocRow td a.evDocMoveDownLink').addClass('invisible');
		if ($('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow').length > 1) {
			$('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow td').find('a.evDocMoveUpLink').removeClass('invisible');
			$('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow td').find('a.evDocMoveDownLink').removeClass('invisible');
			$('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow:first').find('a.evDocMoveUpLink').addClass('invisible');
			$('##tblEvDocGrp'+evDocGrpID+' tr.evDocRow:last').find('a.evDocMoveDownLink').addClass('invisible');
		}
	}
	function moveEvDoc(evDocID,evDocGrpID,dir) {
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var tr = $('##evDocument'+evDocID);
				if(dir =='up'){
					var trprev = tr.prev();
					tr.remove().insertBefore(trprev);
				}
				else {
					var trnext = tr.next();
					tr.remove().insertAfter(trnext);
				}
				resetEvDocsActionIcons(evDocGrpID);
			}
			else {
				alert('We were unable to move this document.');
			}
		};
		var objParams = { eventDocumentID:evDocID, eventID:#arguments.event.getValue('eID')#, dir:dir };
		TS_AJX('ADMINEVENT','doEventDocMove',objParams,moveResult,moveResult,10000,moveResult);
	}
	function reloadDocumentsTable(){
		evDocumentsTable.draw();
	}
	function replaceEventFile(eventDocumentID, documentID, eventID) {
		let title = 'Replace Event File';
		let replaceEventFileLink = '#this.link.replaceEventFile#&evdid=' + eventDocumentID + '&did=' + documentID + '&eid=' + eventID;
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: title,
			iframe: true,
			contenturl: replaceEventFileLink,
			strmodalfooter: {
				classlist: 'd-none'
			}
		});
	}
	function editDocument(documentID,eventID,evdid) {
		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: evdid > 0 ? 'Edit Event Document' : 'Add Event Document',
			iframe: true,
			contenturl: '#this.link.editDocument#&documentID=' + documentID + '&eventID=' + eventID + '&evdid=' + evdid,
			strmodalfooter : {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-primary',
				extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmDocument :submit").click',
				extrabuttonlabel: 'Save Document',
			}
		});
	} 
	function confirmDeleteDocument(documentID,eventID,evdid) {
		var removeDocumentResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
			} else {
				alert('We were unable to remove this event document.');
				delBtnElement.attr('data-confirm',0).removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
			}
		};

		let delBtnElement = $('##btnDelDoc'+documentID);
		mca_initConfirmButton(delBtnElement, function(){
			let objParams = { eventID:eventID, eventDocumentID:evdid, documentID:documentID, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteEventDocument',objParams,removeDocumentResult,removeDocumentResult,10000,removeDocumentResult);		
		});
	}
	function removeDocumentGrouping(evDocGrpID) {
		MCModalUtils.showModal({
			verticallycentered: true,
			size: 'md',
			title: 'Remove Document Grouping',
			strmodalbody: {
				content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this document grouping?<br/>Any documents in this event document grouping will be ungrouped.</span></div>',
			},
			strmodalfooter : {
				classlist: 'text-right',
				showclose: false,
				showextrabutton: true,
				extrabuttonclass: 'btn-outline-danger',
				extrabuttonlabel: 'Remove Document Grouping',
			}
		});

		$('##btnMCModalSave').on('click', function(){
			doRemoveDocumentGrouping(evDocGrpID);
		});
	}
	function doRemoveDocumentGrouping(evDocGrpID) {
		var removeDocumentGroupingResult = function(r) {
			MCModalUtils.hideModal();
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
			} else {
				alert('We were unable to remove this event document grouping.');
			}
		};
		$('##btnMCModalSave').prop('disabled',true).html('Removing...');
		var objParams = { eventDocumentGroupingID:evDocGrpID, eventID:#arguments.event.getValue('eID')# };
		TS_AJX('ADMINEVENT','deleteEventDocumentGrouping',objParams,removeDocumentGroupingResult,removeDocumentGroupingResult,10000,removeDocumentGroupingResult);
	}
	function resetEvDocGrpActionIcons(evDocGrpID) {
		$('tr.evDocGrpParentRow td a.evDocGrpMoveUpLink, tr.evDocGrpParentRow td a.evDocGrpMoveDownLink').addClass('invisible');
		if ($('tr.evDocGrpParentRow').length > 1) {
			$('tr.evDocGrpParentRow td').find('a.evDocGrpMoveUpLink').removeClass('invisible');
			$('tr.evDocGrpParentRow td').find('a.evDocGrpMoveDownLink').removeClass('invisible');
			$('tr.evDocGrpParentRow:first').find('a.evDocGrpMoveUpLink').addClass('invisible');
			$('tr.evDocGrpParentRow:last').find('a.evDocGrpMoveDownLink').addClass('invisible');
		}
	}
	function moveEvDocGrp(evDocGrpID,dir) {
		var moveResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true') {
				var tr = $('tr.evDocGrpRow'+evDocGrpID);
				if(dir=='up'){
					var trprev = tr.prevAll('tr.evDocGrpParentRow:first');
					tr.remove().insertBefore(trprev);
				}
				else {
					var trNextGrpID = tr.nextAll('tr.evDocGrpParentRow:first').attr('id').replace('evDocGrpRow','');
					var trnext = $('tr.evDocGrpRow'+trNextGrpID+':last');
					tr.remove().insertAfter(trnext);
				}
				resetEvDocGrpActionIcons(evDocGrpID);
			}
			else {
				alert('We were unable to move this event document grouping.');
			}
		};
		var objParams = { eventDocumentGroupingID:evDocGrpID, eventID:#arguments.event.getValue('eID')#, dir:dir };
		TS_AJX('ADMINEVENT','doEventDocGroupingMove',objParams,moveResult,moveResult,10000,moveResult);
	}
	function editDocumentGrouping(evDocGrpID){
		mca_hideAlert('err_docgrouping');
		var editDocGrpResult = function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				let evDocGrpFrmTemplate = Handlebars.compile($('##evDocGroupingFormTemplate').html());
				$('##MCModalBody').html(evDocGrpFrmTemplate({eventDocumentGroupingID:evDocGrpID,eventDocumentGrouping:r.eventdocumentgrouping}));
			} else {
				alert('We were unable to load event document grouping form. Try again.')
			}
		};

		MCModalUtils.showModal({
			isslideout: true,
			modaloptions: {
				backdrop: 'static',
				keyboard: false
			},
			size: 'lg',
			title: 'Edit Event Document Grouping',
			strmodalfooter : {
				classlist: 'd-flex',
				showextrabutton: true,
				extrabuttonclass: 'btn-primary ml-auto',
				extrabuttononclickhandler: 'saveEventDocGrouping',
				extrabuttonlabel: 'Save',
			}
		});

		var objParams = { eventDocumentGroupingID:evDocGrpID };
		TS_AJX('ADMINEVENT','getEventDocumentGroupingName',objParams,editDocGrpResult,editDocGrpResult,10000,editDocGrpResult);
	}
	function saveEventDocGrouping() {
		mca_hideAlert('err_docgrouping');
		var saveResult	= function(r) {
			if (r.success && r.success.toLowerCase() == 'true'){
				reloadDocumentsTable();
				MCModalUtils.hideModal();
			} else {
				mca_showAlert('err_docgrouping', (r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to save this event document grouping. Try again.'));
				$('##btnMCModalSave').prop('disabled',false).html('Save');
			}
		};
		if($('##eventDocumentGrouping').val().trim()!=''){
			$('##btnMCModalSave').prop('disabled',true).html('Saving...');
			var objParams = { eventDocumentGroupingID:$('##eventDocumentGroupingID').val(),
				eventID:#arguments.event.getValue('eID')#, eventDocumentGrouping:$('##eventDocumentGrouping').val().trim() };
			TS_AJX('ADMINEVENT','saveEventDocumentGrouping',objParams,saveResult,saveResult,10000,saveResult);
		} else {
			mca_showAlert('err_docgrouping', 'Enter a name for this Document Grouping.');
			return false;
		}
	}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.registrantJS)#">

<cfoutput>
<div class="row mb-1">
	<div class="col">
		This tab allows you to upload documents that will be shared with event attendees, such as event materials for download. 
		Multiple files can be added, ordered, and grouped however you like.
		Only registrants will be allowed to download the files via confirmation emails or the event details page.
	</div>
</div>
<div class="row mb-1">
	<div class="col text-right">
		<button type="button" class="btn btn-sm btn-primary" onclick="editDocument(0,#arguments.event.getValue('eID')#,0);">
			<span class="btn-wrapper--icon">
			<i class="fa-regular fa-circle-plus"></i>
			</span>
			<span class="btn-wrapper--label">Add Document</span>
		</button>
	</div>
</div>
<table id="evDocumentsTable" class="table table-sm table-bordered" style="width:100%">
	<thead>
		<tr>
			<th></th>
			<th>Document Title</th>
			<th>Author</th>
			<th>Actions</th>
		</tr>
	</thead>
</table>

<script id="evDocsListTemplate" type="text/x-handlebars-template">
	<table id="tblEvDocGrp{{eventDocumentGroupingID}}" class="table table-sm table-borderless m-0">
		{{##each arrEvDocs}}
			<tr id="evDocument{{eventDocumentID}}" class="evDocRow">
				<td style="width:4%;"></td>
				<td style="width:51%;" class="border-right pl-4">{{{docTitle}}}</td>
				<td style="width:20%;" class="border-right pl-4">{{{author}}}</td>
				<td style="width:25%;" class="text-center">
					<a href="##" class="btn btn-xs btn-outline-primary px-1 m-1" onclick="editDocument({{documentID}},{{eventID}},{{eventDocumentID}});return false;"><i class="fa-solid fa-pencil"></i></a>
					<a href="##" class="btn btn-xs btn-outline-primary px-1 m-1" title="Replace Event File" onclick="replaceEventFile({{eventDocumentID}},{{documentID}},{{eventID}});return false;"><i class="fa-solid fa-arrow-right-arrow-left"></i></a>
					<a href="##" class="btn btn-xs btn-outline-danger px-1 m-1" id="btnDelDoc{{documentID}}" onclick="confirmDeleteDocument({{documentID}},{{eventID}},{{eventDocumentID}});return false;"><i class="fa-solid fa-trash-can"></i></a>
					<a href="{{previewLink}}" class="btn btn-xs btn-outline-info px-1 m-1" target="_blank"><i class="fa-solid fa-eye"></i></a>
					<a href="##" class="btn btn-xs btn-outline-primary px-1 m-1 evDocMoveUpLink{{##unless canmoveup}} invisible{{/unless}}" title="Move Up" onclick="moveEvDoc({{eventDocumentID}},{{../eventDocumentGroupingID}},'up');return false;"><i class="fa-solid fa-arrow-up"></i></a>
					<a href="##" class="btn btn-xs btn-outline-primary px-1 m-1 evDocMoveDownLink{{##unless canmovedown}} invisible{{/unless}}" title="Move Down" onclick="moveEvDoc({{eventDocumentID}},{{../eventDocumentGroupingID}},'down');return false;"><i class="fa-solid fa-arrow-down"></i></a>
				</td>
			</tr>
		{{/each}}
	</table>
</script>

<script id="evDocGroupingFormTemplate" type="text/x-handlebars-template">
	<div id="err_docgrouping" class="alert alert-danger mb-2 d-none"></div>
	<form name="frmDocGrouping" id="frmDocGrouping" onsubmit="saveEventDocGrouping();return false;">
	<input type="hidden" name="eventDocumentGroupingID" id="eventDocumentGroupingID" value="{{eventDocumentGroupingID}}">
	<div class="form-group">
		<div class="form-label-group">
			<input type="text" name="eventDocumentGrouping" id="eventDocumentGrouping" value="{{eventDocumentGrouping}}" class="form-control" maxlength="200" autocomplete="off">
			<label for="eventDocumentGrouping">Document Grouping</label>
		</div>
	</div>
	</form>
</script>
</cfoutput>