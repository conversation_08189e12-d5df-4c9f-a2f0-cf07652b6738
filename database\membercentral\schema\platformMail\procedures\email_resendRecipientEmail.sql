ALTER PROC dbo.email_resendRecipientEmail
@siteID int,
@recipientID int,
@toEmail varchar(200)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @newRecipientID int, @emailStatusIDQueued int, @messageID int;
	SELECT @emailStatusIDQueued = statusID FROM dbo.email_statuses WHERE statusCode = 'Q';
	SELECT @messageID = messageID FROM dbo.email_messageRecipientHistory WHERE siteID = @siteID AND recipientID = @recipientID;

	BEGIN TRAN;
		INSERT INTO dbo.email_messageRecipientHistory (messageID, memberID, dateLastUpdated, toName, toEmail, emailStatusID, emailTypeID, siteID,queuePriority)
		SELECT r.messageID, m.activeMemberID, GETDATE(), r.toName, @toEmail, @emailStatusIDQueued, r.emailTypeID, @siteID, 1
		FROM dbo.email_messageRecipientHistory AS r
		INNER JOIN dbo.email_messages AS em 
			ON em.siteID = @siteID
			and em.messageID = @messageID
			AND r.siteID = @siteID
			and r.messageID = @messageID
		INNER JOIN memberCentral.dbo.ams_members AS m ON m.memberID = r.memberID
		WHERE r.recipientID = @recipientID
		;

		SELECT @newRecipientID = SCOPE_IDENTITY();

		INSERT INTO dbo.email_messageRecipientReferences (recipientID, referenceType, referenceID)
		SELECT @newRecipientID, referenceType, referenceID
		FROM dbo.email_messageRecipientReferences
		WHERE recipientID = @recipientID;

		INSERT INTO dbo.email_messageRecipientAttachments(recipientID, attachmentID)
		SELECT @newRecipientID, attachmentID
		FROM dbo.email_messageRecipientAttachments
		WHERE recipientID = @recipientID;

		INSERT INTO dbo.email_messageMetadataFields (messageID, fieldID, memberID, fieldValue, fieldTextToReplace, recipientID)
		SELECT messageID, fieldID, memberID, fieldValue, fieldTextToReplace, @newRecipientID
		FROM dbo.email_messageMetadataFields
		WHERE messageID = @messageID 
        AND recipientID = @recipientID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
