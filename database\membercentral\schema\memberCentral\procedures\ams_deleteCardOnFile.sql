ALTER PROC dbo.ams_deleteCardOnFile 
@payProfileID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY
	
	DECLARE @orgID int, @siteID int, @profileID int, @customerProfileID varchar(50), 
		@payProfileGatewayID int, @AuthCCCIMGatewayID int, @cofMemberID int, @nowDate datetime = GETDATE(),
		@gatewayType varchar(30);

	SELECT @orgID = s.orgID, @siteID = s.siteID, @profileID = mp.profileID, 
		@customerProfileID = mpp.customerProfileID, @payProfileGatewayID = mp.gatewayID, 
		@cofMemberID = mpp.memberID, @gatewayType = g.gatewayType
	FROM dbo.ams_memberPaymentProfiles AS mpp
	INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = mpp.profileID
	INNER JOIN dbo.mp_gateways AS g ON g.gatewayID = mp.gatewayID
	INNER JOIN dbo.sites AS s ON s.siteID = mp.siteID
	WHERE mpp.payProfileID = @payProfileID;

	SELECT @AuthCCCIMGatewayID = dbo.fn_mp_getGatewayID('AuthorizeCCCIM');
	
	IF @orgID IS NOT NULL BEGIN	
		IF OBJECT_ID('tempdb..#tmpAuditLogDELCOF') IS NOT NULL 
			DROP TABLE #tmpAuditLogDELCOF;
		CREATE TABLE #tmpAuditLogDELCOF (auditCode varchar(10), msg varchar(max));

		-- subs
		INSERT INTO #tmpAuditLogDELCOF (auditCode, msg)
		SELECT 'SUBS', 'Pay Profile ' + mpp.detail + ' removed from Subscription [' + ss.subscriptionName + '] (SubscriberID: ' + CAST(s.subscriberID AS varchar(10)) + ')'
		FROM dbo.sub_subscribers as s
		INNER JOIN dbo.sub_subscriptions as ss on ss.subscriptionID = s.subscriptionID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = s.payProfileID
		WHERE s.payProfileID = @payProfileID;

		-- cp
		INSERT INTO #tmpAuditLogDELCOF (auditCode, msg)
		SELECT DISTINCT 'CP', 'Pay Profile ' + mpp.detail + ' removed from Contribution Program [' 
			+ cp.programName + ISNULL(' - ' + cpc.campaignName,'') + '] (ContributionID: ' + CAST(c.contributionID AS varchar(10)) + ')'
		FROM dbo.cp_contributionPayProfiles AS cpp 
		INNER JOIN dbo.cp_contributions AS c ON c.contributionID = cpp.contributionID
		INNER JOIN dbo.cp_programs AS cp ON cp.programID = c.programID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = cpp.payProfileID
		LEFT OUTER JOIN dbo.cp_campaigns AS cpc ON cpc.campaignID = c.campaignID
		WHERE cpp.payProfileID = @payProfileID;

		-- inv
		INSERT INTO #tmpAuditLogDELCOF (auditCode, msg)
		SELECT 'INV', 'Pay Profile ' + mpp.detail + ' removed from Invoice ' + i.fullInvoiceNumber
		FROM dbo.tr_invoices as i
		INNER JOIN dbo.organizations as o on o.orgID = i.orgID
		INNER JOIN dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = i.payProfileID
		WHERE i.payProfileID = @payProfileID;

		INSERT INTO #tmpAuditLogDELCOF (auditCode, msg)
		SELECT 'COF', 'Pay Profile ' + detail + ' deleted (PayProfileID: ' + CAST(@payProfileID as varchar(10)) + ')'
		FROM dbo.ams_memberPaymentProfiles
		WHERE payProfileID = @payProfileID;

		BEGIN TRAN;
			UPDATE dbo.sub_subscribers
			SET payProfileID = NULL,
				MPProfileID = NULL,
				payProcessFee = 0,
				processFeePercent = NULL
			WHERE payProfileID = @payProfileID;

			UPDATE dbo.tasks_tasks
			SET payProfileID = NULL,
				MPProfileID = NULL
			WHERE payProfileID = @payProfileID;

			UPDATE dbo.tr_invoices
			SET payProfileID = NULL,
				MPProfileID = NULL,
				payProcessFee = 0,
				processFeePercent = NULL
			WHERE payProfileID = @payProfileID;

			DELETE FROM dbo.cp_contributionPayProfiles
			WHERE payProfileID = @payProfileID;

			-- since the lastUpdatedDate is modified along with status, update trigger for ams_memberPaymentProfiles will reprocess all related conditions based on the profileID. 
			-- this will ensure that all conditions for cards including those limited to invoices, CP, and subscriptions are covered.
			UPDATE dbo.ams_memberPaymentProfiles
			SET [status] = 'D',
				lastUpdatedDate = getdate(),
				lastUpdatedByMemberID = @recordedByMemberID
			WHERE payProfileID = @payProfileID;
		COMMIT TRAN;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
		SELECT ('{ "c":"auditLog", "d": {
			"AUDITCODE":"'+ auditCode +'",
			"ORGID":' + cast(@orgID as varchar(10)) + ',
			"SITEID":' + cast(@siteID as varchar(10)) + ',
			"ACTORMEMBERID":' + cast(@recordedByMemberID as varchar(20)) + ',
			"ACTIONDATE":"' + convert(varchar(20),@nowDate,120) + '",
			"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(msg),'"','\"') + '" } }')
		FROM #tmpAuditLogDELCOF;

		IF @payProfileGatewayID = @AuthCCCIMGatewayID AND 
			NOT EXISTS (
				SELECT TOP 1 mpp.payProfileID
				FROM dbo.ams_memberPaymentProfiles AS mpp
				INNER JOIN dbo.mp_profiles AS mp ON mp.profileID = mpp.profileID 
					AND mp.gatewayID = @AuthCCCIMGatewayID
				WHERE mpp.[status] <> 'D'
				AND mpp.customerProfileID = @customerProfileID
			) BEGIN

			DECLARE @gatewayUsername varchar(50), @gatewayPassword varchar(75);

			SELECT @gatewayUsername = gatewayUsername, @gatewayPassword = gatewayPassword
			FROM dbo.mp_profiles
			WHERE profileID = @profileID;

			EXEC platformQueue.dbo.queue_authCIMCustCheck_load @profileID=@profileID, @gatewayUsername=@gatewayUsername, 
				@gatewayPassword=@gatewayPassword, @customerProfileID=@customerProfileID;
		END

		IF OBJECT_ID('tempdb..#tmpAuditLogDELCOF') IS NOT NULL 
			DROP TABLE #tmpAuditLogDELCOF;
	END

	RETURN 0;
END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
