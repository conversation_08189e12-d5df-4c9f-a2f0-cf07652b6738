<cfcomponent extends="model.admin.reports.report" output="no">
	<cfset variables.defaultEvent = 'controller'>
	<cfset variables.runformats = [ 'screen','pdf','customcsv' ]>

	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
		var local = structNew();

		// call common report controller
		reportController(event=arguments.event);

		local.methodToRun = this[arguments.event.getValue('mca_ta')];
		return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="showReport" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
			<cfset local.strGLAccountWidgetData = { title='Define Optional Revenue Account Filter',
				description='Optionally filter the GL Accounts appearing on this report using the defined criteria below.',
				gridext="#this.siteResourceID#_1", gridwidth=660, gridheight=150, gridClassList='mb-5 stepDIV', 
				initGridOnLoad=true, controllingSRID=this.siteResourceID, reportID=arguments.event.getValue('qryReportInfo').reportID,
				glatid=3, extraNodeName='gllist', showIcons=1, widgetMode='report' }>
			<!--- hide icons if unable to change report --->
			<cfif NOT hasReportEditRights(event=arguments.event)>
				<cfset local.strGLAccountWidgetData.showIcons = 0>
			</cfif>
			<cfset local.strGLAccountWidget = createObject("component","model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strGLAccountWidgetData)>

			<cfsavecontent variable="local.dataHead">
				<cfoutput>
				#local.strGLAccountWidget.js#

				<script language="javascript">
					function forceDate() {
						if ($('##frmDRFrom').val() == '' || $('##frmDRTo').val() == '') {
							rptShowAlert('This report requires you to select a batch deposit date range.'); 
							return false;
						}
						return true;
					}
					function dispFieldsets() {
						$('##divReportShowScreenLoading').hide();
						$('##divReportShowScreen').html('').hide();
						if ($('##frmReportView').val() == 'member' || $('##frmReportView').val() == 'raw' || $('##frmReportView').val() == 'memdetail') $('div##stepFieldsetsDIVfieldsets').show(); 
						else $('div##stepFieldsetsDIVfieldsets').hide();
						if ($('##frmReportView').val() == 'raw') {
							$('button##btnReportBarscreen, button##btnReportBarpdf, div##rptOrder').hide();
							$('div##rptSubData').show();
						} else { 
							$('button##btnReportBarscreen, button##btnReportBarpdf, div##rptOrder').show();
							$('div##rptSubData').hide();
						}
					}
					$(function() {
						dispFieldsets();
						setupRptFilterDateRange('frmDRFrom','frmDRTo');
						mca_setupCalendarIcons('frmReport');
					});
				</script>
				</cfoutput>
			</cfsavecontent>		
			<cfhtmlhead text="#application.objCommon.minText(local.dataHead)#">
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<div id="reportDefs">
				#showCommonTop(event=arguments.event)#
				
				<cfif arguments.event.getValue('qryReportInfo').reportID gt 0>
					<cfset local.frmDRFrom = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmdrfrom/text())")>
					<cfset local.frmDRTo = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmdrto/text())")>
					<cfset local.frmReportView = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmreportview/text())")>
					<cfset local.frmReportOrder = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmreportorder/text())")>
					<cfif not len(local.frmDRFrom) and not len(local.frmDRTo)>
						<cfset local.frmDRFrom = "#month(now())#/1/#year(now())#">
						<cfset local.frmDRTo = dateformat(now(),"m/d/yyyy")>
					</cfif>
					<cfset local.frmLinkAllocType = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmlinkalloctype/text())")>
					<cfset local.frmIncSub = XMLSearch(arguments.event.getValue('qryReportInfo').otherXML,"string(/report/extra/frmincsub/text())")>

					<cfform name="frmReport"  id="frmReport" method="post">
					<input type="hidden" name="reportAction" id="reportAction" value="">
					#local.strGLAccountWidget.html#
					#showStepMemberCriteria(event=arguments.event, title="Define Optional Member Filter", desc="Optionally filter the members appearing on this report using the defined criteria below.")#
					<div class="mb-5 stepDIV">
						<h5>Define Extra Options</h5>
						<div class="row mt-2">
							<div class="col-sm-12">
								<div class="form-group row">
									<label for="frmDRFrom" class="col-md-4 col-sm-12 col-form-label">Batch Deposit Date</label>
									<div class="col-md-8 col-sm-12">
										<div class="row">
											<div class="col-md col-sm-12 pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmDRFrom" id="frmDRFrom" value="#local.frmDRFrom#" mcrdtxt="Batch Deposit Start Date" class="form-control form-control-sm dateControl rolldate" placeholder="Start Date">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmDRFrom"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-md-auto col-sm-12 px-md-2 d-flex align-items-center">to</div>
											<div class="col-md col-sm-12 pl-md-0">
												<div class="input-group input-group-sm">
													<input type="text" name="frmDRTo" id="frmDRTo" value="#local.frmDRTo#" mcrdtxt="Batch Deposit End Date" class="form-control form-control-sm dateControl rolldate" placeholder="End Date">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="frmDRTo"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmLinkAllocType" class="col-md-4 col-sm-12 col-form-label">Link Allocation Type</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmLinkAllocType" id="frmLinkAllocType" class="form-control form-control-sm">
											<option value="LinkAllocCash" <cfif local.frmLinkAllocType eq "LinkAllocCash">selected</cfif>>Members Linked to Allocation's Cash</option>
											<option value="LinkAllocRevenue" <cfif local.frmLinkAllocType eq "LinkAllocRevenue">selected</cfif>>Members Linked to Allocation's Revenue</option>	
										</select>
									</div>
								</div>
								<div class="form-group row">
									<label for="frmReportView" class="col-md-4 col-sm-12 col-form-label">Report View</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmReportView" id="frmReportView" onChange="dispFieldsets()" class="form-control form-control-sm">
											<option value="RevenueGL" <cfif local.frmReportView eq "RevenueGL">selected</cfif>>Group by Revenue GL Account</option>
											<option value="SaleDesc" <cfif local.frmReportView eq "SaleDesc">selected</cfif>>Group by Description of Sale</option>
											<option value="batchDate" <cfif local.frmReportView eq "batchDate">selected</cfif>>Group by Batch Deposit Date</option>
											<option value="member" <cfif local.frmReportView eq "member">selected</cfif>>Group by Member with Totals</option>
											<option value="memdetail" <cfif local.frmReportView eq "memdetail">selected</cfif>>Group by Member with Account Detail</option>
											<option value="firm" <cfif local.frmReportView eq "firm">selected</cfif>>Group by Company with Totals</option>
											<option value="firmdetail" <cfif local.frmReportView eq "acctdetail">selected</cfif>>Group by Company with Account Detail</option>
											<option value="raw" <cfif local.frmReportView eq "raw">selected</cfif>>Raw Data Behind Report</option>
										</select>
									</div>
								</div>
								<div class="form-group row" id="rptSubData">
									<label for="frmIncSub" class="col-md-4 col-sm-12 col-form-label">Subscription Data</label>
									<div class="col-md-8 col-sm-12">
										<div class="form-check">
											<input type="checkbox" name="frmIncSub" id="frmIncSub" value="1" class="form-check-input" <cfif local.frmIncSub eq "1">checked</cfif>>
											<label class="form-check-label" for="frmIncSub">Include subscription data tied to this revenue, if applicable.</label>
										</div>
									</div>
								</div>
								<div class="form-group row" id="rptOrder">
									<label for="frmReportOrder" class="col-md-4 col-sm-12 col-form-label">Report Order</label>
									<div class="col-md-8 col-sm-12">
										<select name="frmReportOrder" id="frmReportOrder" class="form-control form-control-sm">
											<option value="group" <cfif local.frmReportOrder eq "group">selected</cfif>>Order by Grouping Criteria</option>
											<option value="amountasc" <cfif local.frmReportOrder eq "amountasc">selected</cfif>>Order by Ascending Allocated Amount</option>
											<option value="amountdesc" <cfif local.frmReportOrder eq "amountdesc">selected</cfif>>Order by Descending Allocated Amount</option>
										</select>
									</div>
								</div>
							</div>
						</div>
					</div>

					#showStepRollingDates(event=arguments.event)#
					#showStepFieldsets(event=arguments.event, desc="Fieldsets will be included in the report only when ""Group by Member"" or ""Raw Data Behind Report"" is selected as the Report Grouping.")#
					#showButtonBar(event=arguments.event,validateFunction='forceDate')#
					</cfform>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="qryReport" access="public" output="false" returntype="struct">
		<cfargument name="strSQLPrep" type="struct" required="true">
		<cfargument name="orgID" type="numeric" required="true">
		<cfargument name="frmReportView" type="string" required="true">
		<cfargument name="frmReportOrder" type="string" required="true">
		<cfargument name="frmDRFrom" type="date" required="true">
		<cfargument name="frmDRTo" type="date" required="true">
		<cfargument name="frmLinkAllocType" type="string" required="true">
		<cfargument name="suppressTotals" type="boolean" required="true">

		<cfset var local = structNew()>
		<cfset local.returnStruct=structNew()>
		<cfset local.tempTableName = "rpt#getTickCount()#">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.returnStruct.qryData" result="local.returnStruct.qryDataResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				<cfif len(arguments.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(arguments.strSQLPrep.ruleSQL)#</cfif>
				
				declare @orgID int, @startdate datetime, @enddate datetime, @tr_DITSaleTrans int, @ARGLAccountID int, @outputFieldsXML xml;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.orgID#">;
				set @startdate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.frmDRFrom#">;
				set @enddate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#arguments.frmDRTo# 23:59:59.997">;
				set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
				EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAccountID OUTPUT;
				
				declare @tblAllGLs TABLE (glOrder varchar(max), GLAccountID int, AccountTypeID int, accountName varchar(max), accountCode varchar(200));
				insert into @tblAllGLs (glOrder, GLAccountID, AccountTypeID, accountName, accountCode)
				SELECT gl.thePath, gl.GLAccountID, gl.AccountTypeID, gl.thePathExpanded, gl.accountCode
				FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
				where gl.status <> 'D'
				ORDER BY gl.thePath;

				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
					DROP TABLE ##tmpMembersFS;
				CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
				
				<cfif arguments.frmReportView eq "RevenueGL">
					select creditGLAccountID, accountName, accountCode, glOrder, sum(allocatedAmount) as allocatedAmount
					into ###local.tempTableName#
					from (
						select case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end as creditGLAccountID, 
							gl.accountName, gl.accountCode, gl.glOrder, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where alloc.orgID = @orgID
							union all
						select case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end as creditGLAccountID, 
							gl.accountName, gl.accountCode, gl.glOrder, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8
					) as tmp
					group by creditGLAccountID, accountName, accountCode, glOrder;
										
					select ROW_NUMBER() OVER (
						<cfif arguments.frmReportOrder eq "group">
							order by glOrder
						<cfelseif arguments.frmReportOrder eq "amountasc">
							order by allocatedAmount, glOrder
						<cfelseif arguments.frmReportOrder eq "amountdesc">
							order by allocatedAmount desc, glOrder
						</cfif>						
						) as row, CreditGLAccountID, accountName, accountCode, allocatedAmount
					from ###local.tempTableName#
					<cfif NOT arguments.suppressTotals>
							union all
						select distinct ********, null, null, null, null
						from ###local.tempTableName#
							union all
						select ********, null, 'Report Total', null, sum(allocatedAmount)
						from ###local.tempTableName#
					</cfif>
					order by 1;

				<cfelseif arguments.frmReportView eq "SaleDesc">
					select detail, sum(allocatedAmount) as allocatedAmount
					into ###local.tempTableName#
					from (
						select coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as detail, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
						left outer join dbo.tr_relationships as ditR 
							inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
							on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where alloc.orgID = @orgid
							union all
						select coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as detail, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate					
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
						left outer join dbo.tr_relationships as ditR 
							inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
							on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8
					) as tmp
					group by detail;
					
					select ROW_NUMBER() OVER (
						<cfif arguments.frmReportOrder eq "group">
							order by detail
						<cfelseif arguments.frmReportOrder eq "amountasc">
							order by allocatedAmount, detail
						<cfelseif arguments.frmReportOrder eq "amountdesc">
							order by allocatedAmount desc, detail
						</cfif>						
						) as row, detail, allocatedAmount
					from ###local.tempTableName#
					<cfif NOT arguments.suppressTotals>
							union all
						select distinct ********, null, null
						from ###local.tempTableName#
							union all
						select ********, 'Report Total', sum(allocatedAmount)
						from ###local.tempTableName#
					</cfif>
					order by 1;

				<cfelseif arguments.frmReportView eq "batchDate">
					select batchDate, sum(allocatedAmount) as allocatedAmount
					into ###local.tempTableName#
					from (
						select CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as batchDate, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where alloc.orgID = @orgID
							union all
						select CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as batchDate, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8
					) as tmp
					group by batchDate;

					select ROW_NUMBER() OVER (
						<cfif arguments.frmReportOrder eq "group">
							order by batchDate
						<cfelseif arguments.frmReportOrder eq "amountasc">
							order by allocatedAmount, batchDate
						<cfelseif arguments.frmReportOrder eq "amountdesc">
							order by allocatedAmount desc, batchDate
						</cfif>						
						) as row, batchDate, allocatedAmount
					from ###local.tempTableName#
					<cfif NOT arguments.suppressTotals>
							union all
						select distinct ********, null, null
						from ###local.tempTableName#
							union all
						select ********, null, sum(allocatedAmount)
						from ###local.tempTableName#
					</cfif>
					order by 1;

				<cfelseif ListFindNoCase("member,memdetail",arguments.frmReportView)>
					select 
						<cfif arguments.frmReportView eq "member"> 
							1 as ttlrow, 
							ROW_NUMBER() OVER (
								order by
								<cfif arguments.frmReportOrder eq "group">
									lastname, firstname, membernumber
								<cfelseif arguments.frmReportOrder eq "amountasc">
									sum(allocatedAmount), lastname, firstname, membernumber
								<cfelseif arguments.frmReportOrder eq "amountdesc">
									sum(allocatedAmount) desc, lastname, firstname, membernumber
								</cfif>
							) as row, 
						</cfif>
						memberid as reportMemberID, memberid, lastname, firstname, membernumber, company, hasMemberPhotoThumb, sum(allocatedAmount) as allocatedAmount
						<cfif ListFindNoCase("memdetail",arguments.frmReportView)> 
							, GLAccountID, AccountName, AccountCode, GLOrder
						</cfif>
					into ###local.tempTableName#_2
					from (
						select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, m.hasMemberPhotoThumb, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
							<cfif ListFindNoCase("memdetail",arguments.frmReportView)> 
								, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
							</cfif>
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where alloc.orgID = @orgID
							union all
						select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, m.hasMemberPhotoThumb, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
							<cfif ListFindNoCase("memdetail",arguments.frmReportView)> 
								, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
							</cfif>
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate					
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8
					) as tmp
					group by memberid, lastname, firstname, membernumber, company, hasMemberPhotoThumb
						<cfif ListFindNoCase("memdetail,firmdetail",arguments.frmReportView)> 
							, GLAccountID, AccountName, AccountCode, GLOrder
						</cfif>;

					CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);
					
					-- get members fieldset data and set back to snapshot because proc ends in read committed
					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
						@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.fieldSetIDList#">,
						@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
						@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strSQLPrep.ovNameFormat#">,
						@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.strSQLPrep.ovMaskEmails#">,
						@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', 
						@linkedMembers=0, @mode='report', @outputFieldsXML=@outputFieldsXML OUTPUT;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT tm.lastname, tm.firstname, tm.membernumber, tm.company, tm.hasMemberPhotoThumb, tm.allocatedAmount, tm.reportMemberID, 
						<cfif arguments.frmReportView eq "member">
							tm.ttlrow, tm.row, 
						<cfelseif arguments.frmReportView eq "memdetail">
							tm.GLAccountID, tm.AccountName, tm.AccountCode, tm.GLOrder,
						</cfif>
						m.*
					INTO ###local.tempTableName#
					FROM ###local.tempTableName#_2 AS tm
					INNER JOIN ##tmpMembersFS AS m ON m.memberID = tm.memberID;

					#makeTempTableAcceptNULLs(local.tempTableName)#
					
					<cfif arguments.frmReportView eq "member">
						<cfif NOT arguments.suppressTotals>
							insert into ###local.tempTableName# (ttlrow)
							values (********);
							
							insert into ###local.tempTableName# (ttlrow, allocatedAmount, [Extended MemberNumber])
							select ********, sum(allocatedAmount), 'Report Total'
							from ###local.tempTableName#;
						</cfif>
		
						SELECT *, CASE WHEN mc_row = 1 then @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT *, ROW_NUMBER() OVER (ORDER BY ttlrow, row) AS mc_row
							FROM ###local.tempTableName#
						) as finalData
						ORDER BY mc_row;
					<cfelseif arguments.frmReportView eq "memdetail">
						<cfif NOT arguments.suppressTotals>
							insert into ###local.tempTableName# (memberid, allocatedAmount, GLAccountID, [Extended MemberNumber], AccountName, glOrder)
							select memberid, sum(allocatedAmount), ********, [Extended MemberNumber], 'Member Total', '********'
							from ###local.tempTableName#
							group by memberid, [Extended MemberNumber];
						
							insert into ###local.tempTableName# (memberid, allocatedAmount, GLAccountID, [Extended MemberNumber], glOrder)
							select memberid, null, ********, [Extended MemberNumber], '********'
							from ###local.tempTableName#
							group by memberid, [Extended MemberNumber];
						
							insert into ###local.tempTableName# (memberid, allocatedAmount, GLAccountID, AccountName, AccountCode, glOrder)
							select ********, sum(allocatedAmount), GLAccountID, case when AccountName = 'Member Total' then 'Report Total' else AccountName end, AccountCode, glOrder
							from ###local.tempTableName#
							where GLOrder <> '********'
							group by GLAccountID, AccountName, AccountCode, glOrder;
						</cfif>

						<cfif arguments.frmReportOrder eq "group">
							SELECT *, CASE WHEN reportOrder = 1 then @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
							FROM (
								select *, ROW_NUMBER() OVER (PARTITION by memberID ORDER BY case when memberID = ******** then 0 else 1 end desc, [Extended MemberNumber], glOrder) as rowMember,
									ROW_NUMBER() OVER (ORDER BY case when memberID = ******** then 0 else 1 end desc, [Extended MemberNumber], glOrder) as reportOrder
								from ###local.tempTableName#
							) as finalData
							ORDER BY reportOrder;
						<cfelseif arguments.frmReportOrder eq "amountasc">
							SELECT *, CASE WHEN rowMember = 1 then @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
							FROM (
								select rpt.*, ROW_NUMBER() OVER (PARTITION by rpt.memberID ORDER BY case when rpt.memberID = ******** then 0 else 1 end desc, tmp.row, rpt.glOrder) as rowMember
								from ###local.tempTableName# as rpt
								inner join (
									select memberid, row_number() over (order by allocatedAmount, [Extended MemberNumber]) as row
									from ###local.tempTableName#
									where GLAccountID = ********
								) as tmp on tmp.memberID = rpt.memberID
							) as finalData
							ORDER BY rowMember;
						<cfelseif arguments.frmReportOrder eq "amountdesc">
							SELECT *, CASE WHEN rowMember = 1 then @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
							FROM (
								select rpt.*, ROW_NUMBER() OVER (PARTITION by rpt.memberID ORDER BY case when rpt.memberID = ******** then 0 else 1 end desc, tmp.row, rpt.glOrder) as rowMember
								from ###local.tempTableName# as rpt
								inner join (
									select memberid, row_number() over (order by allocatedAmount desc, [Extended MemberNumber]) as row
									from ###local.tempTableName#
									where GLAccountID = ********
								) as tmp on tmp.memberID = rpt.memberID
							) as finalData
							ORDER BY rowMember;
						</cfif>						
					</cfif>

				<cfelseif ListFindNoCase("firm,firmdetail",arguments.frmReportView)>
					select 
						<cfif ListFindNoCase("firm",arguments.frmReportView)> 
							1 as ttlrow, 
							ROW_NUMBER() OVER (
								order by
								<cfif arguments.frmReportOrder eq "group">
									case when company <> '[No company listed]' then 1 else 0 end desc, company
								<cfelseif arguments.frmReportOrder eq "amountasc">
									case when company <> '[No company listed]' then 1 else 0 end desc, sum(allocatedAmount), company
								<cfelseif arguments.frmReportOrder eq "amountdesc">
									case when company <> '[No company listed]' then 1 else 0 end desc, sum(allocatedAmount) desc, company
								</cfif>
							) as row, 
						</cfif>
						company, sum(allocatedAmount) as allocatedAmount
						<cfif ListFindNoCase("firmdetail",arguments.frmReportView)> 
							, GLAccountID, AccountName, AccountCode, GLOrder
						</cfif>
					into ###local.tempTableName#
					from (
						select case when len(isnull(m.company,'')) = 0 then '[No company listed]' else m.company end as company, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
							<cfif ListFindNoCase("firmdetail",arguments.frmReportView)> 
								, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
							</cfif>
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where alloc.orgID = @orgID
							union all
						select case when len(isnull(m.company,'')) = 0 then '[No company listed]' else m.company end as company, 
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
							<cfif ListFindNoCase("firmdetail",arguments.frmReportView)> 
								, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
							</cfif>
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						<cfif arguments.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						<cfif len(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(arguments.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8
					) as tmp
					group by company
						<cfif ListFindNoCase("firmdetail",arguments.frmReportView)> 
							, GLAccountID, AccountName, AccountCode, GLOrder
						</cfif>;
					
					<cfif arguments.frmReportView eq "firm">
						<cfif NOT arguments.suppressTotals>
							insert into ###local.tempTableName# (ttlrow)
							values (********);
						
							insert into ###local.tempTableName# (ttlrow, allocatedAmount, company)
							select ********, sum(allocatedAmount), 'Report Total'
							from ###local.tempTableName#;
						</cfif>

						select *
						from ###local.tempTableName#
						order by ttlrow, row;		

					<cfelseif arguments.frmReportView eq "firmdetail">
						<cfif NOT arguments.suppressTotals>
							insert into ###local.tempTableName# (company, allocatedAmount, GLAccountID, AccountName, glOrder)
							select company, sum(allocatedAmount), ********, 'Company Total', '********'
							from ###local.tempTableName#
							group by company;
						
							insert into ###local.tempTableName# (company, allocatedAmount, GLAccountID, glOrder)
							select company, null, ********, '********'
							from ###local.tempTableName#
							group by company;
						
							insert into ###local.tempTableName# (company, allocatedAmount, GLAccountID, AccountName, AccountCode, glOrder)
							select 'MC********', sum(allocatedAmount), GLAccountID, case when AccountName = 'Company Total' then 'Report Total' else AccountName end, AccountCode, glOrder
							from ###local.tempTableName#
							where GLOrder <> '********'
							group by GLAccountID, AccountName, AccountCode, glOrder;
						</cfif>

						<cfif arguments.frmReportOrder eq "group">
							select company, GlAccountID, allocatedAmount, AccountName, AccountCode, glOrder, ROW_NUMBER() OVER (PARTITION by company ORDER BY case when company = 'MC********' then 0 when company = '[No company listed]' then 1 else 2 end desc, company, glOrder) as rowMember
							from ###local.tempTableName#
							order by case when company = 'MC********' then 0 when company = '[No company listed]' then 1 else 2 end desc, company, glOrder;
						<cfelseif arguments.frmReportOrder eq "amountasc">
							select rpt.company, rpt.allocatedAmount, rpt.GLAccountID, rpt.AccountName, rpt.AccountCode, rpt.glOrder, 
								ROW_NUMBER() OVER (PARTITION by rpt.company ORDER BY case when rpt.company = 'MC********' then 0 when rpt.company = '[No company listed]' then 1 else 2 end desc, tmp.row, rpt.glOrder) as rowMember
							from ###local.tempTableName# as rpt
							inner join (
								select company, row_number() over (order by allocatedAmount, company) as row
								from ###local.tempTableName#
								where GLAccountID = ********
							) as tmp on tmp.company = rpt.company
							order by case when rpt.company = 'MC********' then 0 when rpt.company = '[No company listed]' then 1 else 2 end desc, tmp.row, rpt.glOrder;
						<cfelseif arguments.frmReportOrder eq "amountdesc">
							select rpt.company, rpt.allocatedAmount, rpt.GLAccountID, rpt.AccountName, rpt.AccountCode, rpt.glOrder, 
								ROW_NUMBER() OVER (PARTITION by rpt.company ORDER BY case when rpt.company = 'MC********' then 0 when rpt.company = '[No company listed]' then 1 else 2 end desc, tmp.row, rpt.glOrder) as rowMember
							from ###local.tempTableName# as rpt
							inner join (
								select company, row_number() over (order by allocatedAmount desc, company) as row
								from ###local.tempTableName#
								where GLAccountID = ********
							) as tmp on tmp.company = rpt.company
							order by case when rpt.company = 'MC********' then 0 when rpt.company = '[No company listed]' then 1 else 2 end desc, tmp.row, rpt.glOrder;
						</cfif>						
					</cfif>
				</cfif>

				<cfif len(arguments.strSQLPrep.ruleSQL)>
					IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
						DROP TABLE ##tmpVGRMembers;
				</cfif>
				IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
					DROP TABLE ###local.tempTableName#;
				IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
					DROP TABLE ###local.tempTableName#_2;
				IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;	

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>	

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="screenReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="", isReportEmpty=false }>
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		<cftry>
			<cfset local.memberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
			<cfset local.memberPhotoPath = application.paths.localUserAssetRoot.path & LCASE(local.mc_siteInfo.orgcode) & "/memberphotosth/">

			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML, 
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>
			
			<!--- get extra fields --->
			<cfset local.frmDRFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdrfrom/text())")>
			<cfset local.frmDRTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdrto/text())")>
			<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportview/text())")>
			<cfset local.frmReportOrder = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportorder/text())")>
			<cfif not len(local.frmDRFrom) and not len(local.frmDRTo)>
				<cfset local.frmDRFrom = "#month(now())#/1/#year(now())#">
				<cfset local.frmDRTo = dateformat(now(),"m/d/yyyy")>
			</cfif>
			<cfset local.frmLinkAllocType = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmlinkalloctype/text())")>
			<cfset local.frmIncSub = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmincsub/text())")>
			<cfset local.frmShowPhotos = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@img)")>
			<cfset local.frmShowMemberNumber = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mn)")>
			<cfset local.frmShowCompany = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/fieldsets/@mc)")>

			<cfif local.frmReportView eq "raw">
				<cfthrow message="This report view is only available as a CSV.">
			</cfif>
			
			<cfset local.qryReport = qryReport(strSQLPrep=local.strSQLPrep, orgID=local.mc_siteInfo.orgID, frmReportView=local.frmReportView, 
				frmReportOrder=local.frmReportOrder, frmDRFrom=local.frmDRFrom, frmDRTo=local.frmDRTo, frmLinkAllocType=local.frmLinkAllocType, 
				suppressTotals=false)>

			
			<cfif local.qryReport.qryData.recordcount AND ListFindNoCase("member,memdetail",local.frmReportView)>
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfset local.qryOutputFields = getOutputFieldsFromXML(outputFieldsXML=local.qryReport.qryData.mc_outputFieldsXML)>
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					select *
					from [local].qryOutputFields
					where (fieldcodeSect NOT IN ('mc','m','ma','mat') or fieldCode IN ('m_recordtypeid','m_membertypeid','m_status','m_earliestdatecreated'))
				</cfquery>
			</cfif>

			<cfsavecontent variable="local.strReturn.data">
				<cfoutput>
				<div id="screenreport">
					#showReportHeader(siteID=local.mc_siteInfo.siteid, reportAction=arguments.reportAction, reportName=arguments.qryReportInfo.reportName)#
				</cfoutput>
	
				<cfif local.qryReport.qryData.recordcount is 0>
					<cfset local.strReturn.isReportEmpty = true>
					<cfoutput><div>No results to report.</div></cfoutput>
				<cfelse>
					<cfoutput>
					<table class="table table-sm table-borderless">
					</cfoutput>

					<cfif local.frmReportView eq "RevenueGL">
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left">Revenue GL Account</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.row eq ********>
								<tr><td colspan="2">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.row eq ********>
									<cfset local.ttlRow = "font-weight-bold">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<tr>
									<td class="align-text-top #local.ttlRow#">#local.qryReport.qryData.AccountName#<cfif len(local.qryReport.qryData.accountCode)> (#local.qryReport.qryData.accountCode#)</cfif></td>
									<td class="align-text-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</cfoutput>
						</tbody>
					<cfelseif local.frmReportView eq "SaleDesc">
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left">Description of Sale</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.row eq ********>
								<tr><td colspan="2">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.row eq ********>
									<cfset local.ttlRow = "font-weight-bold">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<tr>
									<td class="align-text-top #local.ttlRow#">#local.qryReport.qryData.detail#</td>
									<td class="align-text-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					<cfelseif local.frmReportView eq "batchDate">
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left">Batch Deposit Date</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.row eq ********>
								<tr><td colspan="2">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.row eq ********>
									<cfset local.ttlRow = "ttlrow">
									<cfset local.cellvalue = "Report Total">
								<cfelse>
									<cfset local.ttlRow = "">
									<cfset local.cellvalue = DateFormat(local.qryReport.qryData.batchDate,'m/d/yyyy')>
								</cfif>
								<tr>
									<td class="align-text-top #local.ttlRow#">#local.cellvalue#</td>
									<td class="align-text-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					<cfelseif listFindNoCase("member",local.frmReportView)>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left" <cfif local.frmShowPhotos is 1>colspan="2"</cfif>>Member</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.ttlrow eq ********>
								<tr><td colspan="3">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.ttlrow eq ********>
									<cfset local.ttlRow = "font-weight-bold">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<tr>
									<cfif local.frmShowPhotos is 1 and local.qryReport.qryData.ttlrow neq ********>
										<td class="align-top" style="width:80px;">
											<cfif local.qryReport.qryData.hasMemberPhotoThumb is 1>
												<cfif arguments.reportAction eq "screen">
													<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryReport.qryData.MemberNumber)#.jpg">
												<cfelse>
													<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryReport.qryData.MemberNumber)#.jpg">
												</cfif>
											<cfelse>
												<cfif arguments.reportAction eq "screen">
													<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
												<cfelse>
													<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
												</cfif>
											</cfif>
										</td>
									</cfif>
									<td class="align-top #local.ttlRow#" <cfif local.frmShowPhotos is 1 and local.qryReport.qryData.ttlrow eq ********>colspan="2"</cfif>>
										<cfif arguments.reportAction eq "screen" and local.qryReport.qryData.ttlrow neq ********>
											<a href="#local.memberLink#&memberid=#local.qryReport.qryData.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qryReport.qryData["Extended MemberNumber"]#<cfelse>#local.qryReport.qryData["Extended Name"]#</cfif></a><br/>
										<cfelse>
											<b><cfif local.frmShowMemberNumber is 1>#local.qryReport.qryData["Extended MemberNumber"]#<cfelse>#local.qryReport.qryData["Extended Name"]#</cfif></b><br/>
										</cfif>
										<cfif local.frmShowCompany is 1 AND len(local.qryReport.qryData.company)>#local.qryReport.qryData.company#<br/></cfif>
										<cfloop query="local.qryOutputFields">
											<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
												<cfset local.AddrToShow = local.qryReport.qryData[local.qryOutputFields.dbfield][local.qryReport.qryData.currentrow]>
												<cfloop condition="Find(', , ',local.AddrToShow)">
													<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
												</cfloop>
												<cfif len(local.AddrToShow)>
													#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
												</cfif>
											</cfif>
										</cfloop>
										<cfloop query="local.qryOutputFieldsForLoop">
											<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
												#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow])#<br/>
											<cfelseif len(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryReport.qryData.currentrow])>
												<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
													#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow], "m/d/yyyy")#<br/>
												<cfelse>
													#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow]#<br/>
												</cfif>
											</cfif>
										</cfloop>
									</td>
									<td class="align-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					<cfelseif listFindNoCase("firm",local.frmReportView)>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left">Company</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.ttlrow eq ********>
								<tr><td colspan="2">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.ttlrow eq ********>
									<cfset local.ttlRow = "font-weight-bold">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<tr>
									<td class="align-text-top #local.ttlRow#">
										<cfif len(local.qryReport.qryData.company)>#local.qryReport.qryData.company#<cfelse>[No company listed]</cfif>
									</td>
									<td class="align-text-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					<cfelseif listFindNoCase("memdetail",local.frmReportView)>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left" <cfif local.frmShowPhotos is 1>colspan="2"</cfif>>Member</th>
							<th class="border-bottom border-dark">&nbsp;</th>
							<th class="border-bottom border-dark text-right" nowrap>Allocated Amount</th>
						</tr>
						</thead>
						<tbody>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.GlAccountID eq ********>
								<tr><td colspan="4">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.GLAccountID eq ********>
									<cfset local.ttlRow = "ttlrow">
								<cfelseif local.qryReport.qryData.memberID eq ******** and local.qryReport.qryData.rowMember is 1>
									<cfset local.ttlRow = "ttlrow">
								<cfelseif local.qryReport.qryData.memberID eq ********>
									<cfset local.ttlRow = "b">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<cfif local.qryReport.qryData.rowMember is 1>
									<tr>
									<cfif local.frmShowPhotos is 1 and local.qryReport.qryData.memberID neq ********>
										<td class="align-top" style="width:80px;">
											<cfif local.qryReport.qryData.hasMemberPhotoThumb is 1>
												<cfif arguments.reportAction eq "screen">
													<img class="mc_memthumb" src="/memberphotosth/#LCASE(local.qryReport.qryData.MemberNumber)#.jpg">
												<cfelse>
													<img class="mc_memthumb" src="file:///#local.memberPhotoPath##LCASE(local.qryReport.qryData.MemberNumber)#.jpg">
												</cfif>
											<cfelse>
												<cfif arguments.reportAction eq "screen">
													<img src="/assets/common/images/directory/default.jpg" width="80" height="100">
												<cfelse>
													<img class="mc_memthumb" src="file:///#application.paths.RAIDAssetRoot.path#common/images/directory/default.jpg">
												</cfif>
											</cfif>
										</td>
									</cfif>
									<td colspan="3" class="align-top">
										<cfif arguments.reportAction eq "screen" and local.qryReport.qryData.memberid neq ********>
											<a href="#local.memberLink#&memberid=#local.qryReport.qryData.memberid#" target="_blank"><cfif local.frmShowMemberNumber is 1>#local.qryReport.qryData["Extended MemberNumber"]#<cfelse>#local.qryReport.qryData["Extended Name"]#</cfif></a><br/>
										<cfelse>
											<b><cfif local.frmShowMemberNumber is 1>#local.qryReport.qryData["Extended MemberNumber"]#<cfelse>#local.qryReport.qryData["Extended Name"]#</cfif></b><br/>
										</cfif>
										<cfif local.frmShowCompany is 1 AND len(local.qryReport.qryData.company)>#local.qryReport.qryData.company#<br/></cfif>
										<cfloop query="local.qryOutputFields">
											<cfif local.qryOutputFields.dbObjectAlias eq "mc" and left(local.qryOutputFields.fieldCode,18) eq "mc_combinedAddress">
												<cfset local.AddrToShow = local.qryReport.qryData[local.qryOutputFields.dbfield][local.qryReport.qryData.currentrow]>
												<cfloop condition="Find(', , ',local.AddrToShow)">
													<cfset local.AddrToShow = replace(local.AddrToShow,", , ",", ","ALL")>
												</cfloop>
												<cfif len(local.AddrToShow)>
													#local.qryOutputFields.fieldlabel#: #local.AddrToShow#<br/>
												</cfif>
											</cfif>
										</cfloop>
										<cfloop query="local.qryOutputFieldsForLoop">
											<cfif left(local.qryOutputFieldsForLoop.dbField,13) eq "acct_balance_">
												#local.qryOutputFieldsForLoop.fieldlabel#: #DollarFormat(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow])#<br/>
											<cfelseif len(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldLabel][local.qryReport.qryData.currentrow])>
												<cfif local.qryOutputFieldsForLoop.dataTypeCode eq "DATE">
													#local.qryOutputFieldsForLoop.fieldlabel#: #DateFormat(local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow], "m/d/yyyy")#<br/>
												<cfelse>
													#local.qryOutputFieldsForLoop.fieldlabel#: #local.qryReport.qryData[local.qryOutputFieldsForLoop.fieldlabel][local.qryReport.qryData.currentrow]#<br/>
												</cfif>
											</cfif>
										</cfloop>
									</td>
									</tr>
								</cfif>
								<tr>
									<cfif local.frmShowPhotos is 1>
										<td></td>
										<td></td>
									<cfelse>
										<td></td>
									</cfif>
									<!--- DO NOT remove the span tag in next line. It was added to correct nowrap bug --->
									<td nowrap class="align-top #local.ttlRow#"><span style="visibility:hidden;">-</span> #local.qryReport.qryData.accountName#<cfif len(local.qryReport.qryData.AccountCode)> (#local.qryReport.qryData.AccountCode#)</cfif></td>
									<td class="align-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					<cfelseif listFindNoCase("firmdetail",local.frmReportView)>
						<cfoutput>
						<thead>
						<tr>
							<th class="text-left">Company</th>
							<th>&nbsp;</th>
							<th class="text-right" nowrap>Allocated Amount</th>
						</tr>
						<cfloop query="local.qryReport.qryData">
							<cfif local.qryReport.qryData.GlAccountID eq ********>
								<tr><td colspan="3">&nbsp;</td></tr>
							<cfelse>
								<cfif local.qryReport.qryData.GLAccountID eq ********>
									<cfset local.ttlRow = "ttlrow">
								<cfelseif local.qryReport.qryData.company eq "MC********" and local.qryReport.qryData.rowMember is 1>
									<cfset local.ttlRow = "ttlrow">
								<cfelseif local.qryReport.qryData.company eq "MC********">
									<cfset local.ttlRow = "b">
								<cfelse>
									<cfset local.ttlRow = "">
								</cfif>
								<cfif local.qryReport.qryData.rowMember is 1>
									<tr>
									<td class="align-text-top" colspan="3">
										<cfif local.qryReport.qryData.company eq "MC********">Report Total
										<cfelse>
											<cfif len(local.qryReport.qryData.company)>#local.qryReport.qryData.company#<cfelse>[No company listed]</cfif>
										</cfif>
									</td>
									</tr>
								</cfif>
								<tr>
									<td width="100%" class="align-text-top <cfif local.qryReport.qryData.GLAccountID eq ******** and local.qryReport.qryData.company eq "MC********">#local.ttlrow#</cfif>">&nbsp;</td>
									<td nowrap class="align-text-top #local.ttlRow#">#local.qryReport.qryData.accountName#<cfif len(local.qryReport.qryData.AccountCode)> (#local.qryReport.qryData.AccountCode#)</cfif></td>
									<td class="align-text-top text-right #local.ttlRow#">#dollarformat(local.qryReport.qryData.allocatedAmount)#</td>
								</tr>
							</cfif>
						</cfloop>
						</tbody>
						</cfoutput>
					</cfif>

					<cfoutput>
					</table>
					</cfoutput>
				</cfif>

				<cfoutput>
				#showReportFooter(reportAction=arguments.reportAction, defaultTimeZoneID=local.mc_siteInfo.defaultTimeZoneID)#
				#showRawSQL(reportAction=arguments.reportAction, qryName="local.qryReport.qryData", strQryResult=local.qryReport.qryDataResult)#
				</div>
				</cfoutput>
			</cfsavecontent>
		
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="pdfReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">
		<cfreturn SUPER.pdfReport(reportAction=arguments.reportAction, qryReportInfo=arguments.qryReportInfo, siteCode=arguments.siteCode, pdfOrientation="landscape")>
	</cffunction>

	<cffunction name="csvReport" access="private" output="false" returntype="struct">
		<cfargument name="reportAction" type="string" required="true">
		<cfargument name="qryReportInfo" type="query" required="true">
		<cfargument name="siteCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { data="", success=true, errMsg="" }>
		<cfset local.tempTableName = "rpt#getTickCount()#">
		<cfset local.mc_siteInfo = application.objSiteInfo.getSiteInfo(arguments.siteCode)>
		
		<cftry>
			<cfset local.strSQLPrep = prepSQL(orgID=local.mc_siteInfo.orgID, siteID=local.mc_siteInfo.siteID,
				reportRuleID=arguments.qryReportInfo.ruleID, reportOtherXML=arguments.qryReportInfo.otherXML,
				existingFields="m_lastname,m_firstname,m_membernumber,m_company", existingAliases="m")>
			<cfif local.strSQLPrep.ruleErr>
				<cfthrow message="There was an error in the report criteria.">
			</cfif>

			<!--- get extra fields --->
			<cfset local.frmDRFrom = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdrfrom/text())")>
			<cfset local.frmDRTo = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmdrto/text())")>
			<cfset local.frmReportView = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportview/text())")>
			<cfset local.frmReportOrder = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmreportorder/text())")>
			<cfif not len(local.frmDRFrom) and not len(local.frmDRTo)>
				<cfset local.frmDRFrom = "#month(now())#/1/#year(now())#">
				<cfset local.frmDRTo = dateformat(now(),"m/d/yyyy")>
			</cfif>
			<cfset local.frmLinkAllocType = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmlinkalloctype/text())")>
			<cfset local.frmIncSub = XMLSearch(arguments.qryReportInfo.otherXML,"string(/report/extra/frmincsub/text())")>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData" result="local.qryDataResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					<cfif len(local.strSQLPrep.ruleSQL)>#PreserveSingleQuotes(local.strSQLPrep.ruleSQL)#</cfif>
				
					declare @orgID int, @startdate datetime, @enddate datetime, @tr_DITSaleTrans int, @tr_AdjustTrans int, @ARGLAccountID int, @outputFieldsXML xml;
					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.mc_siteInfo.orgID#">;
					set @startdate = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.frmDRFrom#">;
					set @enddate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.frmDRTo# 23:59:59.997">;
					set @tr_DITSaleTrans = dbo.fn_tr_getRelationshipTypeID('DITSaleTrans');
					set @tr_AdjustTrans = dbo.fn_tr_getRelationshipTypeID('AdjustTrans');
					EXEC dbo.tr_getGLAccountByGLCode @orgID=@orgID, @GLCode='ACCOUNTSRECEIVABLE', @GLAccountID=@ARGLAccountID OUTPUT;

					declare @tblAllGLs TABLE (glOrder varchar(max), GLAccountID int, AccountTypeID int, accountName varchar(max), accountCode varchar(200));
					insert into @tblAllGLs (glOrder, GLAccountID, AccountTypeID, accountName, accountCode)
					SELECT gl.thePath, gl.GLAccountID, gl.AccountTypeID, gl.thePathExpanded, gl.accountCode
					FROM dbo.fn_getRecursiveGLAccounts(@orgID) as gl
					where gl.status <> 'D'
					ORDER BY gl.thePath;
					
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..###local.tempTableName#Final') IS NOT NULL
						DROP TABLE ###local.tempTableName#Final;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;
					CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);	

					<cfif local.frmReportView eq "RevenueGL">
						select creditGLAccountID, accountName as [Account], accountCode as [Account Code], glOrder, sum(allocatedAmount) as [Allocated Amount]
						into ###local.tempTableName#
						from (
							select case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end as creditGLAccountID, 
								gl.accountName, gl.accountCode, gl.glOrder, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
							from dbo.cache_tr_allocations as alloc
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where alloc.orgID = @orgID
								union all
							select case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end as creditGLAccountID, 
								gl.accountName, gl.accountCode, gl.glOrder, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
							from dbo.tr_transactions as VOT 
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
							inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where VOT.ownedByOrgID = @orgid
							and VOT.typeID = 8
						) as tmp
						group by creditGLAccountID, accountName, accountCode, glOrder;

						<cfif local.frmReportOrder eq "group">
							SELECT [Account], [Account Code], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY glOrder) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountasc">
							SELECT [Account], [Account Code], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY case when glOrder >= '********' then 0 else 1 end desc, [Allocated Amount], glOrder) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountdesc">
							SELECT [Account], [Account Code], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY case when glOrder = '********' then 2 when glOrder = '********' then 1 else 0 end, [Allocated Amount] desc, glOrder) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						</cfif>
					<cfelseif local.frmReportView eq "SaleDesc">
						select 1 as dtlrow, detail as [Sale Detail], sum(allocatedAmount) as [Allocated Amount]
						into ###local.tempTableName#
						from (
							select coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as detail, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
							from dbo.cache_tr_allocations as alloc
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
							left outer join dbo.tr_relationships as ditR 
								inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
								on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where alloc.orgID = @orgID
								union all
							select coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as detail, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
							from dbo.tr_transactions as VOT 
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
							inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
							inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
							left outer join dbo.tr_relationships as ditR 
								inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
								on ditR.orgID = @orgID and ditR.typeID = @tr_DITSaleTrans and ditR.transactionID = saleAdjTaxDitT.transactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where VOT.ownedByOrgID = @orgid
							and VOT.typeID = 8
						) as tmp
						group by detail;

						<cfif local.frmReportOrder eq "group">
							SELECT [Sale Detail], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Sale Detail]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountasc">
							SELECT [Sale Detail], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Allocated Amount], [Sale Detail]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountdesc">
							SELECT [Sale Detail], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Allocated Amount] desc, [Sale Detail]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						</cfif>
					<cfelseif local.frmReportView eq "batchDate">
						select 1 as dtlrow, batchDate as [Batch Deposit Date], sum(allocatedAmount) as [Allocated Amount]
						into ###local.tempTableName#
						from (
							select CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as batchDate, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
							from dbo.cache_tr_allocations as alloc
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where alloc.orgID = @orgID
								union all
							select CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as batchDate, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
							from dbo.tr_transactions as VOT 
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
							inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where VOT.ownedByOrgID = @orgid
							and VOT.typeID = 8
						) as tmp
						group by batchDate;

						<cfif local.frmReportOrder eq "group">
							SELECT [Batch Deposit Date], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Batch Deposit Date]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountasc">
							SELECT [Batch Deposit Date], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Allocated Amount], [Batch Deposit Date]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportOrder eq "amountdesc">
							SELECT [Batch Deposit Date], [Allocated Amount], ROW_NUMBER() OVER(ORDER BY dtlrow, [Allocated Amount] desc, [Batch Deposit Date]) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						</cfif>
					<cfelseif listFindNoCase("member,memdetail",local.frmReportView)>
						select 
							<cfif local.frmReportView eq "member"> 
								1 as ttlrow, 
								ROW_NUMBER() OVER (
									order by
									<cfif local.frmReportOrder eq "group">
										lastname, firstname, membernumber
									<cfelseif local.frmReportOrder eq "amountasc">
										sum(allocatedAmount), lastname, firstname, membernumber
									<cfelseif local.frmReportOrder eq "amountdesc">
										sum(allocatedAmount) desc, lastname, firstname, membernumber
									</cfif>
								) as row, 
							</cfif>
							memberid, lastname as [Last Name], firstname as [First Name], MemberNumber, company as [Company], 
							sum(allocatedAmount) as [Allocated Amount]
							<cfif ListFindNoCase("memdetail",local.frmReportView)> 
								, GLAccountID, AccountName as [Account Name], AccountCode as [Account Code], GLOrder
							</cfif>
						into ###local.tempTableName#_2
						from (
							select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
								<cfif ListFindNoCase("memdetail",local.frmReportView)> 
									, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
								</cfif>
							from dbo.cache_tr_allocations as alloc
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where alloc.orgID = @orgID
								union all
							select m.memberid, m.lastname, m.firstname, m.membernumber, m.company, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
								<cfif ListFindNoCase("memdetail",local.frmReportView)> 
									, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
								</cfif>
							from dbo.tr_transactions as VOT 
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
							inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where VOT.ownedByOrgID = @orgid
							and VOT.typeID = 8
						) as tmp
						group by memberid, lastname, firstname, membernumber, company
							<cfif ListFindNoCase("memdetail,firmdetail",local.frmReportView)> 
								, GLAccountID, AccountName, AccountCode, GLOrder
							</cfif>;
						
						CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);
						
						-- get members fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', 
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select 
							<cfif local.frmReportView eq "member"> 
								tm.ttlrow, 
								tm.row, 
							</cfif>
							tm.[Last Name], tm.[First Name], tm.MemberNumber, tm.[Company], 
							tm.[Allocated Amount],
							<cfif ListFindNoCase("memdetail",local.frmReportView)> 
								tm.GLAccountID, tm.[Account Name], tm.[Account Code], tm.GLOrder,
							</cfif>
							m.*
						into ###local.tempTableName#
						from ###local.tempTableName#_2 AS tm
						inner join ##tmpMembersFS AS m on m.memberID = tm.memberID;
		
						#makeTempTableAcceptNULLs(local.tempTableName)#
						
						<cfif local.frmReportView eq "member">
							SELECT *, ROW_NUMBER() OVER(ORDER BY ttlrow, row) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportView eq "memdetail">
							<cfif local.frmReportOrder eq "group">
								SELECT *, ROW_NUMBER() OVER(ORDER BY [Extended MemberNumber], glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName#;
							<cfelseif local.frmReportOrder eq "amountasc">
								SELECT *, ROW_NUMBER() OVER(ORDER BY [Allocated Amount], [Extended MemberNumber], glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName#;
							<cfelseif local.frmReportOrder eq "amountdesc">
								SELECT *, ROW_NUMBER() OVER(ORDER BY [Allocated Amount] DESC, [Extended MemberNumber], glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName#;
							</cfif>
						</cfif>

					<cfelseif listFindNoCase("firm,firmdetail",local.frmReportView)>
						select 
							<cfif ListFindNoCase("firm",local.frmReportView)> 
								1 as ttlrow, 
								ROW_NUMBER() OVER (
									order by
									<cfif local.frmReportOrder eq "group">
										case when len(company) > 0 then 1 else 0 end desc, company
									<cfelseif local.frmReportOrder eq "amountasc">
										case when len(company) > 0 then 1 else 0 end desc, sum(allocatedAmount), company
									<cfelseif local.frmReportOrder eq "amountdesc">
										case when len(company) > 0 then 1 else 0 end desc, sum(allocatedAmount) desc, company
									</cfif>
								) as row, 
							</cfif>
							company as [Company], sum(allocatedAmount) as [Allocated Amount]
							<cfif ListFindNoCase("firmdetail",local.frmReportView)> 
								, GLAccountID, AccountName as [Account Name], AccountCode as [Account Code], GLOrder
							</cfif>
						into ###local.tempTableName#
						from (
							select case when len(m.company) = 0 then '[No company listed]' else m.company end as company, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as allocatedAmount
								<cfif ListFindNoCase("firmdetail",local.frmReportView)> 
									, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
								</cfif>
							from dbo.cache_tr_allocations as alloc
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where alloc.orgID = @orgID
								union all
							select case when len(m.company) = 0 then '[No company listed]' else m.company end as company, 
								case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as allocatedAmount
								<cfif ListFindNoCase("firmdetail",local.frmReportView)> 
									, gl.GLAccountID, gl.AccountName, gl.AccountCode, gl.GLOrder
								</cfif>
							from dbo.tr_transactions as VOT 
							inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
							inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
								and b.depositDate between @startdate and @enddate
							inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
							inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
							<cfif local.frmLinkAllocType eq "LinkAllocCash">
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
							<cfelse>
								inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
							</cfif>
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
							inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
							<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
							where VOT.ownedByOrgID = @orgid
							and VOT.typeID = 8
						) as tmp
						group by company
							<cfif ListFindNoCase("firmdetail",local.frmReportView)> 
								, GLAccountID, AccountName, AccountCode, GLOrder
							</cfif>;

						<cfif local.frmReportView eq "firm">
							SELECT Company, [Allocated Amount], ROW_NUMBER() OVER(ORDER BY ttlrow, row) as MCROWID
							INTO ###local.tempTableName#Final
							FROM ###local.tempTableName#;
						<cfelseif local.frmReportView eq "firmdetail">
							<cfif local.frmReportOrder eq "group">
								SELECT company as [Company], [Allocated Amount], [Account Name], [Account Code],
									ROW_NUMBER() OVER(ORDER BY case when company = '[No company listed]' then 1 else 2 end desc, company, glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName#;
							<cfelseif local.frmReportOrder eq "amountasc">
								SELECT rpt.company as [Company], rpt.[Allocated Amount], rpt.[Account Name], rpt.[Account Code], 
									ROW_NUMBER() OVER(ORDER BY case when rpt.company = '[No company listed]' then 1 else 2 end desc, rpt.[Allocated Amount], rpt.company, rpt.glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName# as rpt;
							<cfelseif local.frmReportOrder eq "amountdesc">
								SELECT rpt.company as [Company], rpt.[Allocated Amount], rpt.[Account Name], rpt.[Account Code], 
									ROW_NUMBER() OVER(ORDER BY case when rpt.company = '[No company listed]' then 1 else 2 end desc, rpt.[Allocated Amount] desc, rpt.company, rpt.glOrder) as MCROWID
								INTO ###local.tempTableName#Final
								FROM ###local.tempTableName# as rpt;
							</cfif>
						</cfif>

					<cfelseif local.frmReportView eq "raw">
						CREATE TABLE ###local.tempTableName#_2 (autoID int IDENTITY(1,1), [row] int, glOrder varchar(400), [Revenue Account] varchar(max),
							[Revenue Account Code] varchar(200), alloceeTransactionID int, alloceeTypeID int, rootSaleTransactionID int,
							rootSaleAdjTransactionID int, [Sale Detail] varchar(500), [Cash Account] varchar(max), [Cash Account Code] varchar(200), 
							[Batch Deposit Date] datetime, [Batch Name] varchar(400), [Payment Detail] varchar(500), [Payment Batch Deposit Date] datetime, 
							[Payment Profile] varchar(100), paymentRecordedByMemberID int, [Allocated Amount] decimal(20,2), [Last Name] varchar(75), 
							[First Name] varchar(75), MemberNumber varchar(50), Company varchar(200), memberID int, 
							INDEX IX_rownum NONCLUSTERED (autoID,glOrder,[Batch Deposit Date],[memberID]),
							INDEX IX_rpt_2 NONCLUSTERED (alloceeTypeID,alloceeTransactionID));

						INSERT INTO ###local.tempTableName#_2 (glOrder, [Revenue Account], [Revenue Account Code], alloceeTransactionID,
							alloceeTypeID, [Sale Detail], [Cash Account], [Cash Account Code], [Batch Deposit Date], [Batch Name],
							[Payment Detail], [Payment Batch Deposit Date], [Payment Profile], paymentRecordedByMemberID, [Allocated Amount], memberID)
						select gl.glOrder, gl.accountName as [Revenue Account], gl.accountCode as [Revenue Account Code], 
							alloc.transactionID_rev as alloceeTransactionID, alloc.typeID_rev as alloceeTypeID, 
							coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as [Sale Detail],
							glPay.accountName as [Cash Account], glPay.accountCode as [Cash Account Code],  
							CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as [Batch Deposit Date], b.batchName as [Batch Name], 
							payT.detail as [Payment Detail], CAST(FLOOR(CAST(payB.depositDate AS FLOAT)) AS DATETIME) as [Payment Batch Deposit Date], 
							payMP.profileName as [Payment Profile], payT.recordedByMemberID,
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc else alloc.amount_alloc*-1 end as [Allocated Amount],
							m.memberID
						from dbo.cache_tr_allocations as alloc
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = alloc.transactionID_alloc
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
						left outer join dbo.tr_relationships as ditR 
							inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
							on ditR.orgID = @orgID and ditR.typeID = 14 and ditR.transactionID = alloc.transactionID_rev
						<cfif local.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						inner join dbo.tr_transactions as payT on payT.ownedByOrgID = @orgID and payT.transactionID = alloc.transactionID_cash
						inner join @tblAllGLs as glPay on glPay.glAccountID = payT.debitGLAccountID and glPay.AccountTypeID = 1
						inner join dbo.tr_batchTransactions as payBT on payBT.orgID = @orgID and payBT.transactionID = payT.transactionID
						inner join dbo.tr_batches as payB on payB.orgID = @orgID and payB.batchID = payBT.batchID
						inner join dbo.tr_transactionPayments as tpPay on tpPay.orgID = @orgID and tpPay.transactionID = payT.transactionID
						inner join dbo.mp_profiles as payMP on payMP.profileID = tpPay.profileID
						<cfif len(local.strSQLPrep.joinSQLNoMemberdata)>#PreserveSingleQuotes(local.strSQLPrep.joinSQLNoMemberData)#</cfif>
						where alloc.orgID = @orgID;

						INSERT INTO ###local.tempTableName#_2 (glOrder, [Revenue Account], [Revenue Account Code], alloceeTransactionID,
							alloceeTypeID, [Sale Detail], [Cash Account], [Cash Account Code], [Batch Deposit Date], [Batch Name],
							[Payment Detail], [Payment Batch Deposit Date], [Payment Profile], paymentRecordedByMemberID, [Allocated Amount], memberID)
						select gl.glOrder, gl.accountName as [Revenue Account], gl.accountCode as [Revenue Account Code], 
							alloc.transactionID_rev as alloceeTransactionID, alloc.typeID_rev as alloceeTypeID, 
							coalesce(saleAdjTaxT.detail,saleAdjTaxDitT.detail) as [Sale Detail],
							glPay.accountName as [Cash Account], glPay.accountCode as [Cash Account Code],  
							CAST(FLOOR(CAST(b.depositDate AS FLOAT)) AS DATETIME) as [Batch Deposit Date], b.batchName as [Batch Name], 
							payT.detail as [Payment Detail], CAST(FLOOR(CAST(payB.depositDate AS FLOAT)) AS DATETIME) as [Payment Batch Deposit Date], 
							payMP.profileName as [Payment Profile], payT.recordedByMemberID,
							case when alloc.creditGLAccountID_alloc = @ARGLAccountID then alloc.amount_alloc*-1 else alloc.amount_alloc end as [Allocated Amount],
							m.memberID
						from dbo.tr_transactions as VOT 
						inner join dbo.tr_batchTransactions as bt on bt.orgID = @orgID and bt.transactionID = VOT.transactionID
						inner join dbo.tr_batches as b on b.orgID = @orgID and b.batchID = bt.batchID and b.statusID = 4
							and b.depositDate between @startdate and @enddate
						inner join dbo.tr_relationships as VOR on VOR.orgID = @orgID and VOR.transactionID = VOT.transactionID and VOR.typeID = 8
						inner join dbo.cache_tr_allocations as alloc on alloc.orgID = @orgID and alloc.transactionID_alloc = VOR.appliedToTransactionID
						inner join dbo.tr_transactions as saleAdjTaxDitT on saleAdjTaxDitT.ownedByOrgID = @orgID and saleAdjTaxDitT.transactionID = alloc.transactionID_rev
						left outer join dbo.tr_relationships as ditR 
							inner join dbo.tr_transactions as saleAdjTaxT on saleAdjTaxT.transactionID = ditR.appliedToTransactionID
							on ditR.orgID = @orgID and ditR.typeID = 14 and ditR.transactionID = alloc.transactionID_rev
						<cfif local.frmLinkAllocType eq "LinkAllocCash">
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_alloc
						<cfelse>
							inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberid = alloc.assignedToMemberID_rev
						</cfif>
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = m2.activeMemberID and m.isProtected = 0
						inner join @tblAllGLs as gl on gl.glAccountID = case when alloc.typeID_rev = 10 then alloc.debitGLAccountID_rev else alloc.creditGLAccountID_rev end
						inner join dbo.tr_transactions as payT on payT.ownedByOrgID = @orgID and payT.transactionID = alloc.transactionID_cash
						inner join @tblAllGLs as glPay on glPay.glAccountID = payT.debitGLAccountID and glPay.accountTypeID = 1
						inner join dbo.tr_batchTransactions as payBT on payBT.orgID = @orgID and payBT.transactionID = payT.transactionID
						inner join dbo.tr_batches as payB on payB.orgID = @orgID and payB.batchID = payBT.batchID
						inner join dbo.tr_transactionPayments as tpPay on tpPay.orgID = @orgID and tpPay.transactionID = payT.transactionID
						inner join dbo.mp_profiles as payMP on payMP.profileID = tpPay.profileID
						<cfif len(local.strSQLPrep.JOINSQLNOMEMBERDATA)>#PreserveSingleQuotes(local.strSQLPrep.JOINSQLNOMEMBERDATA)#</cfif>
						where VOT.ownedByOrgID = @orgid
						and VOT.typeID = 8

						update tmp2
						set tmp2.[row] = tmpcalc.rowNum,
							tmp2.[Last Name] = tmpcalc.lastname,
							tmp2.[First Name] = tmpcalc.firstname,
							tmp2.[MemberNumber] = tmpcalc.membernumber,
							tmp2.[Company] = tmpcalc.company
						from ###local.tempTableName#_2 as tmp2
						inner join (
							select t2.autoID, m.lastname, m.firstname, m.memberNumber, m.company,
								ROW_NUMBER() OVER (ORDER BY t2.glOrder, t2.[Batch Deposit Date], m.lastName, m.firstName, m.memberNumber) as rowNum
							from ###local.tempTableName#_2 as t2
							inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = t2.memberID
						) as tmpcalc on tmpcalc.autoID = tmp2.autoID;

						<!--- allocations could be made to sales, adj, or deferred. sub data needs rollup to sales, invoice data needs rollup to sale/adj. --->
						-- sales are already located
						update ###local.tempTableName#_2 set rootSaleTransactionID = alloceeTransactionID, rootSaleAdjTransactionID = alloceeTransactionID where alloceeTypeID = 1;

						-- adj update rootSaleAdjTransactionID
						update ###local.tempTableName#_2 set rootSaleAdjTransactionID = alloceeTransactionID where alloceeTypeID = 3;

						-- adj update rootSaleTransactionID
						update tm
						set tm.rootSaleTransactionID = r.appliedToTransactionID
						from ###local.tempTableName#_2 as tm
						inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = 1 and r.transactionID = tm.alloceeTransactionID
						where tm.alloceeTypeID = 3;
												
						-- dit update rootSaleAdjTransactionID
						update tm
						set tm.rootSaleAdjTransactionID = r.appliedToTransactionID
						from ###local.tempTableName#_2 as tm
						inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = 14 and r.transactionID = tm.alloceeTransactionID
						where tm.alloceeTypeID = 10;

						-- dit update rootSaleTransactionID when deferring sale
						update tm
						set tm.rootSaleTransactionID = r.appliedToTransactionID
						from ###local.tempTableName#_2 as tm
						inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = 14 and r.transactionID = tm.alloceeTransactionID
						inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
						where tm.alloceeTypeID = 10
						and t.typeID = 1;

						-- dit update rootSaleTransactionID when deferring adj
						update tm
						set tm.rootSaleTransactionID = r2.appliedToTransactionID
						from ###local.tempTableName#_2 as tm
						inner join dbo.tr_relationships as r on r.orgID = @orgID and r.typeID = 14 and r.transactionID = tm.alloceeTransactionID
						inner join dbo.tr_transactions as t on t.transactionID = r.appliedToTransactionID
						inner join dbo.tr_relationships as r2 on r2.orgID = @orgID and r2.typeID = 1 and r2.transactionID = t.transactionID
						where tm.alloceeTypeID = 10
						and t.typeID = 3;

						CREATE NONCLUSTERED INDEX IX_memberid ON ###local.tempTableName#_2 (memberID);

						-- get members fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
							@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.fieldSetIDList#">,
							@existingFields='m_lastname,m_firstname,m_membernumber,m_company',
							@ovNameFormat=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.strSQLPrep.ovNameFormat#">,
							@ovMaskEmails=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#local.strSQLPrep.ovMaskEmails#">,
							@membersTableName='###local.tempTableName#_2', @membersResultTableName='##tmpMembersFS', 
							@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select ROW_NUMBER() OVER(ORDER BY tm.row) as MCROWID,
							tm.[Revenue Account], tm.[Revenue Account Code], tm.[Sale Detail] as [Revenue Detail], 
							i.fullInvoiceNumber as [Invoice Number], 
							i.dateBilled as [Invoice Date Billed], 
							i.dateDue as [Invoice Date Due], 
							tm.[Cash Account], tm.[Cash Account Code],  
							tm.[Batch Deposit Date], tm.[Batch Name], 
							tm.[Payment Detail] as [Cash Detail], 
							tm.[Payment Batch Deposit Date], tm.[Payment Profile], tm.[Allocated Amount], 
							<cfif local.frmIncSub is 1>
								mSub2.membernumber as [Sub_MemberNumber],
								sub.subscriptionName as [Sub_Subscription],
								subssr.rateName as [Sub_Rate],
								subss.subStartDate as [Sub_Start Date],
								dateadd(ms,-(datepart(ms,subss.subEndDate)),subss.subEndDate) as [Sub_End Date],
							</cfif>
							(
								select concat(m2.lastname,', ',m2.firstname,' (',m2.membernumber,')')
								from dbo.tr_invoiceStatusHistory as ish
								inner join dbo.ams_members as m on m.orgID in (@orgID,1) and m.memberid = ish.enteredByMemberID
								inner join dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
								where ish.invoiceID = i.invoiceID
								and ish.statusID = 1
								and ish.oldStatusID is NULL
							) as [Invoice Created By],
							concat(mPayRec2.lastname,', ',mPayRec2.firstname,' (',mPayRec2.membernumber,')') as [Payment Recorded By], 
							tm.[Last Name], tm.[First Name], tm.[MemberNumber], tm.[Company], m.*
						INTO ###local.tempTableName#Final
						from ###local.tempTableName#_2 as tm 
						inner join ##tmpMembersFS as m on m.memberID = tm.memberID
						inner join dbo.tr_invoiceTransactions as it on it.orgID = @orgID and it.transactionID = tm.rootSaleTransactionID
						inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = it.invoiceID
						inner join dbo.ams_members as mPayRec on mPayRec.orgID in (@orgID,1) and mPayRec.memberID = tm.paymentRecordedByMemberID
						inner join dbo.ams_members as mPayRec2 on mPayRec2.orgID = mPayRec.orgID and mPayRec2.memberID = mPayRec.activeMemberID
						<cfif local.frmIncSub is 1>
							left outer join dbo.tr_applications as substa 
								inner join dbo.sub_subscribers as subss on subss.subscriberID = substa.itemID
								inner join dbo.ams_members mSub on mSub.memberID = subss.memberID
								inner join dbo.ams_members mSub2 on mSub2.memberID = mSub.activeMemberID
								inner join dbo.sub_subscriptions as sub on sub.subscriptionID = subss.subscriptionID
								left outer join dbo.sub_rateFrequencies as subsrf 
									inner join dbo.sub_rates as subssr on subssr.rateID = subsrf.rateID
									on subsrf.RFID = subss.RFID
								on substa.orgID = @orgID and substa.transactionID = tm.rootSaleTransactionID and substa.applicationTypeID = 17 and substa.itemType = 'Dues'
						</cfif>;
					</cfif>

					#generateFinalBCPTable(tblName="###local.tempTableName#Final", dropFields="MCROWID,ttlrow,row,memberid,GLAccountID,GLOrder")#

					<cfif len(local.strSQLPrep.ruleSQL)>
						IF OBJECT_ID('tempdb..##tmpVGRMembers') IS NOT NULL
							DROP TABLE ##tmpVGRMembers;
					</cfif>
					IF OBJECT_ID('tempdb..###local.tempTableName#') IS NOT NULL
						DROP TABLE ###local.tempTableName#;
					IF OBJECT_ID('tempdb..###local.tempTableName#_2') IS NOT NULL
						DROP TABLE ###local.tempTableName#_2;
					IF OBJECT_ID('tempdb..###local.tempTableName#Final') IS NOT NULL
						DROP TABLE ###local.tempTableName#Final;
					IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
						DROP TABLE ##tmpMembersFS;	

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>	

			<cfscript>
			local.arrInitialReportSort = arrayNew(1);
			if (local.frmReportView eq "RevenueGL") {
				if (local.frmReportOrder eq "group") {
					local.strTemp = { field='Account', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountasc") {
					local.strTemp = { field='Allocated Amount', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Account', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountdesc") {
					local.strTemp = { field='Allocated Amount', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Account', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				}
			} else if (local.frmReportView eq "SaleDesc") {
				if (local.frmReportOrder eq "group") {
					local.strTemp = { field='Sale Detail', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountasc") {
					local.strTemp = { field='Allocated Amount', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Sale Detail', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountdesc") {
					local.strTemp = { field='Allocated Amount', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Sale Detail', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				}
			} else if (local.frmReportView eq "batchDate") {
				if (local.frmReportOrder eq "group") {
					local.strTemp = { field='Batch Deposit Date', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountasc") {
					local.strTemp = { field='Allocated Amount', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Batch Deposit Date', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountdesc") {
					local.strTemp = { field='Allocated Amount', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Batch Deposit Date', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				}
			} else if (listFindNoCase("member,memdetail",local.frmReportView)) {
				if (local.frmReportOrder eq "group") {
					local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountasc") {
					local.strTemp = { field='Allocated Amount', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountdesc") {
					local.strTemp = { field='Allocated Amount', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				}
			} else if (listFindNoCase("firm,firmdetail",local.frmReportView)) {
				if (local.frmReportOrder eq "group") {
					local.strTemp = { field='Company', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountasc") {
					local.strTemp = { field='Allocated Amount', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Company', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				} else if (local.frmReportOrder eq "amountdesc") {
					local.strTemp = { field='Allocated Amount', dir='desc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
					local.strTemp = { field='Company', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				}
			} else if (local.frmReportView eq "raw") {
				local.strTemp = { field='Last Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='First Name', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='MemberNumber', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
				local.strTemp = { field='Revenue Account', dir='asc' }; arrayAppend(local.arrInitialReportSort,local.strTemp);
			}
			local.strReportQry = { qryReportFields=local.qryData, strQryResult=local.qryDataResult };
			local.strReturn.data = getCurrentCSVSettings(strReportQry=local.strReportQry, arrInitialReportSort=local.arrInitialReportSort, otherXML=arguments.qryReportInfo.otherXML);
			</cfscript>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Field names in each report must be unique", cfcatch.detail)>
				<cfset local.strReturn.errMsg = cfcatch.detail.mid(cfcatch.detail.find('Field names in each report must be unique'))>
			<cfelse>
				<cfset application.objError.sendError(cfcatch=cfcatch)>
			</cfif>
			<cfset local.strReturn.success = false>
			<cfset local.strReturn.data = "">
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="saveReportExtra" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.otherXML = XMLParse(arguments.event.getValue('qryReportInfo').otherXML);

		local.strFields = structNew();
		local.strFields.frmdrfrom = { label="Batch Deposit Start Date", value=arguments.event.getValue('frmDRFrom','') };
		local.strFields.frmdrto = { label="Batch Deposit End Date", value=arguments.event.getValue('frmDRTo','') };
		local.strFields.frmreportview = { label="Report View", value=arguments.event.getValue('frmReportView','RevenueGL') };
		local.strFields.frmreportorder = { label="Report Order", value=arguments.event.getValue('frmReportOrder','group') };
		local.strFields.frmlinkalloctype = { label="Link Allocation Type", value=arguments.event.getValue('frmLinkAllocType','LinkAllocCash') };
		local.strFields.frmincsub = { label="Include Subscription Data", value=arguments.event.getValue('frmIncSub',0) };
		local.strFields.gllist = { label="GL Account List", value=XMLSearch(local.otherXML,'string(/report/extra/gllist/text())') };

		reportSaveReportExtra(qryReportInfo=arguments.event.getValue("qryReportInfo"), strFields=local.strFields, event=arguments.event);
		return returnAppStruct('','echo');
		</cfscript>
	</cffunction>

</cfcomponent>