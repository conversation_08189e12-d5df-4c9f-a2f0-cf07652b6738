use membercentral;
GO

SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

declare @siteID int = dbo.fn_getSiteIdFromSitecode('AS'); 
declare @fileShareIDs varchar(100) = '567,402,410,411,412,413'

declare @orgID int = dbo.fn_getOrgIDFromSiteID(@siteID);


declare @fileshareInfo TABLE (fileshareID int PRIMARY KEY, applicationInstanceName varchar(200), rootSectionID int)

insert into @fileshareInfo (fileshareID, applicationInstanceName,rootSectionID)
select fileshareID, applicationInstanceName, fs.rootSectionID
from dbo.fn_intListToTableInline(@fileShareIDs,',') IDs
inner join fs_fileShare fs 
    on fs.fileShareID = IDs.listitem
inner join cms_applicationInstances ai 
    on ai.siteID = @siteID 
    and fs.applicationInstanceID = ai.applicationInstanceID


select fs.fileshareID, fs.applicationInstanceName, activeM.memberNumber, activeM.firstName, activeM.lastName, activeM.company, dv.[filename], dv.dateCreated, 
    action = case ROW_NUMBER() OVER (PARTITION BY dv.documentLanguageID ORDER BY dv.documentVersionID)
        when 1 then 'Upload'
        else 'REPLACE'
    end
from @fileshareInfo fs 
inner join cms_documents d 
    on d.siteID = @siteID
    and d.sectionID = fs.rootSectionID
inner join cms_documentLanguages dl 
    on d.documentId = dl.documentID
inner join cms_documentVersions dv 
    on dv.documentLanguageID = dl.documentLanguageID
inner join ams_members m 
    on m.orgID in (@orgID, 1)
    and m.memberID = dv.contributorMemberID
inner join ams_members activeM 
    on activeM.orgID in (@orgID, 1)
    and m.activememberID = activeM.memberID



select fs.fileshareID, fs.applicationInstanceName, activeM.memberNumber, activeM.firstName, activeM.lastName, activeM.company, dv.[filename], dh.dateentered, 
    action = 'Download'
from @fileshareInfo fs 
inner join cms_documents d 
    on d.siteID = @siteID
    and d.sectionID = fs.rootSectionID
inner join cms_documentLanguages dl 
    on d.documentId = dl.documentID
inner join cms_documentVersions dv 
    on dv.documentLanguageID = dl.documentLanguageID
inner join platformStatsMC.dbo.statsDocumentHits dh
    on dh.siteID = @siteID 
    and dh.documentVersionID = dv.documentVersionID
inner join platformStatsMC.dbo.statsSessions ss
    on ss.siteID = @siteID 
    and dh.sessionid = ss.sessionID
inner join ams_members m 
    on m.orgID in (@orgID, 1)
    and m.memberID = ss.memberID
inner join ams_members activeM 
    on activeM.orgID in (@orgID, 1)
    and m.activememberID = activeM.memberID

SET TRANSACTION ISOLATION LEVEL READ COMMITTED;

GO