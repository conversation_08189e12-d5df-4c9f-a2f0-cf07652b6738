<cfsavecontent variable="local.massEmailsJS">
	<cfoutput>
	#application.objWebEditor.showEditorHeadScripts()#
	<script type="text/javascript">
		var #toScript(arguments.mergeCodeInstructionsLink,"mc_mergecodeslink")#
		var #toScript(local.sendMassEmailsLink,"mc_link_sendemails")#
		var #toScript(arguments.siteID,"mc_siteid")#
		var #toScript(application.regEx.email, "mc_emailregex")#
		var #toScript(arguments.siteCode,"mc_sitecode")#
		var #toScript(arguments.resourceType,"mc_resourceType")#
		var #toScript(arguments.recipientType,"mc_recipientType")#
		var #toScript(local.qryEmailTemplates.recordcount,"mc_emailtemplatescount")#
		<cfif local.qryOrgOptOutLists.modeName eq 'GlobalOptOut'>
			var #toScript(local.qryOrgOptOutLists.consentListID,"mc_consentListIDs")#
		<cfelse>
			var mc_consentListIDs = '';
		</cfif>
		var itemID = 0;
		let recipientsListTable;
		let availableConsentListsTable;
		let massEmailOptOutsTable;

		/* Steps Wizard */
		function onInitMassEmailsWizard() {
			mca_setupDateTimePickerField('emailDateScheduled','#DateTimeFormat(now(),'m/d/yyyy h:nn tt')#','',30);
			mca_setupSelect2($('##divEmailTemplateSel'));
			mca_setupCalendarIcons('frmCompose');
		}
		function validateMassEmailStep(event, currentIndex, newIndex) {
			let currentStep = currentIndex + 1;
			let arrReq = [];
			
			/* Always allow previous action even if the current form is not valid */
			if (currentIndex > newIndex) return true;

			<cfif local.qryEmailTemplates.recordcount OR arrayLen(arguments.arrRecipientModes)>
				<cfif local.showOptOutStep>
					let chooseTemplateStep = 1, composeMsgStep = 2, addOptOutStep = 3, previewMsgStep = 4, sendMailsStep = 5;
				<cfelse>
					let chooseTemplateStep = 1, composeMsgStep = 2, previewMsgStep = 3, sendMailsStep = 4;
				</cfif>

				if(currentStep == chooseTemplateStep) {
					mca_hideAlert('chooseTemplateStepErr');

					if($('input[name="recipientMode"]').length && !$('input[name="recipientMode"]').is(':checked')) arrReq.push('Select the email recipient mode.');
					if (mc_emailtemplatescount > 0) {
						if (!$('input[name="buildEmail"]').is(':checked') || ($('input[name="buildEmail"]:checked').val() == 'existing' && $('##fEmailTemplateID').val() == '')) {
							arrReq.push('Select a template.');
						}
					}

					if(arrReq.length) {
						mca_showAlert('chooseTemplateStepErr',arrReq.join('<br/>'))
						return false;
					}
				}
			<cfelse>
				<cfif local.showOptOutStep>
					let composeMsgStep = 1, addOptOutStep = 2, previewMsgStep = 3, sendMailsStep = 4;
				<cfelse>
					let composeMsgStep = 1, previewMsgStep = 2, sendMailsStep = 3;
				</cfif>
			</cfif>

			if(currentStep == composeMsgStep) {
				mca_hideAlert('composeMsgStepErr');
				var emailRegEx = new RegExp(mc_emailregex,"i");

				if($('##emailTagType').length && $('##emailTagType').val() == 0) arrReq.push('Select an e-mail tag type.');
				if($('##emailFromName').val().trim().length == 0) arrReq.push('Enter the e-mail from name.');
				else if ($('##emailFromName').val().trim().length > 0 && (emailRegEx.test($('##emailFromName').val()))) arrReq.push('E-mail from name cannot contain an email address.');
				if($('##emailReplyTo').val().trim().length == 0) arrReq.push('Enter the e-mail reply-to address.');
				else if ($('##emailReplyTo').val().trim().length > 0 && !(emailRegEx.test($('##emailReplyTo').val()))) arrReq.push('Enter a valid e-mail reply-to address.');
				if($('##emailSubject').val().trim().length == 0) arrReq.push('Enter the e-mail subject.');
				if($.trim(CKEDITOR.instances['templateContent'].getData()).length == 0) arrReq.push('Enter the e-mail content.');

				if(arrReq.length) {
					mca_showAlert('composeMsgStepErr',arrReq.join('<br/>'))
					return false;
				}
			} else if (currentStep == previewMsgStep) {
				if(recipientsListTable.page.info().recordsTotal == 0) {
					mca_showAlert('previewMsgStepErr','We can\'t continue without recipients.')
					return false;
				}
			}

			return true;
		}
		function onLoadMassEmailStep(event, currentIndex, priorIndex) {
			var currentStep = currentIndex + 1;

			<cfif local.qryEmailTemplates.recordcount OR arrayLen(arguments.arrRecipientModes)>
				<cfif local.showOptOutStep>
					let chooseTemplateStep = 1, composeMsgStep = 2, addOptOutStep = 3, previewMsgStep = 4, sendMailsStep = 5;
				<cfelse>
					let chooseTemplateStep = 1, composeMsgStep = 2, previewMsgStep = 3, sendMailsStep = 4;
				</cfif>
			<cfelse>
				<cfif local.showOptOutStep>
					let composeMsgStep = 1, addOptOutStep = 2, previewMsgStep = 3, sendMailsStep = 4;
				<cfelse>
					let composeMsgStep = 1, previewMsgStep = 2, sendMailsStep = 3;
				</cfif>
			</cfif>
			
			if(currentStep == previewMsgStep) {
				$('##divTestEmail').addClass('d-none');
				$('##divEmailDisp, ##spFrom, ##spSubject, ##spEmailAttachment').html('');
				$('##previewEmailAttachRow').hide();
				$('##spTestBtn').hide();

				if($.fn.DataTable.isDataTable('##recipientsListTable')) {
					recipientsListTable.draw();
				} else {
					initRecipientsTable();
				}

			} else if (currentStep == sendMailsStep) {
				if (mc_emailtemplatescount > 0) {
					if ($('##fEmailTemplateID').length && $('##fEmailTemplateID').val() != '') {
						$('##selTemplateOption').html($('##fEmailTemplateID option:selected').text());
						$('##selTemplateRow').removeClass('d-none');
					}
				}
			} 
			<cfif local.showOptOutStep>
				else if(currentStep == addOptOutStep){
					<cfif local.qryOrgOptOutLists.modeName eq 'GlobalOptOut'>
						mc_consentListIDs = #local.qryOrgOptOutLists.consentListID#;
					<cfelse>
						mc_consentListIDs='';
					</cfif>
					initMassEmailOptPreferences();
					if($.fn.DataTable.isDataTable('##availableConsentListsTable')) {
						availableConsentListsTable.draw();
					} else {
						initAvailableConsentListsTable();
					}
					if($.fn.DataTable.isDataTable('##massEmailOptOutsTable')) {
						massEmailOptOutsTable.draw();
					} else {
						initmassEmailOptOutsTable();
					}
				}
			</cfif>

		}
		
		function massEmailsGotoNextStep() {
			$("##massEmailsWizard").steps('next');
		}
		function massEmailsGotoPrevStep() {
			$("##massEmailsWizard").steps('previous');
		}
		function hideWizardBtns() {
			$('##massEmailsWizard .actions').addClass('border-top-0');
			$('##massEmailsWizard ul[aria-label=Pagination] li a[href="##previous"]').addClass('invisible ');
			$('##massEmailsWizard ul[aria-label=Pagination] li a[href="##finish"]').html('Sending...<i class="fa-solid fa-spinner fa-spin"></i>').addClass('btn disabled');
		}
		function showWizardBtns() {
			$('##massEmailsWizard .actions').removeClass('border-top-0');
			$('##massEmailsWizard ul[aria-label=Pagination] li a[href="##previous"]').removeClass('invisible ');
			$('##massEmailsWizard ul[aria-label=Pagination] li a[href="##finish"]').html('Send E-mails').removeClass('btn disabled');
		}

		/* Step 1: Choose Template / Recipient Mode / Message Type */
		function showEmailTemplateBuildOptions(val) {
			mca_hideAlert('chooseTemplateStepErr');
			
			if (val == 'new') {
				if (mc_emailtemplatescount > 0) {
					$("##fEmailTemplateID").val(null).trigger('change');
					$('##divEmailTemplateSel').addClass('d-none');
				}
				$('##emailFromName').val('#replace(local.siteInfo.orgName, "'", "\'", "ALL")#');
				massEmailsGotoNextStep();
			} else {
				$('##divEmailTemplateSel').removeClass('d-none');
			}
		}

		/* Step 2: Compose Message */
		function loadEmailTemplateContent() {			
			var result = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					CKEDITOR.instances['templateContent'].setData(r.emailcontent);
					$('##emailFromName').val(r.emailfromname);
					$('##emailSubject').val(r.subjectline);
					$('##emailReplyTo').val(r.emailfrom);
				} else {
					alert('An error occurred while loading the template.');
				}
			};

			try { CKEDITOR.instances['templateContent'].setData(''); } catch(e) {};
			$('##emailSubject, ##emailReplyTo, ##emailFromName').val('');
			var emailTemplateID = $('##fEmailTemplateID').val() || '';
			if (emailTemplateID > 0) {
				massEmailsGotoNextStep();
				var objParams = { emailTemplateID:emailTemplateID };
				TS_AJX('MASSEMAIL','getEmailTemplateContent',objParams,result,result,10000,result);
			}
		}		
		function showEmailComposeMessageForm() {
			$('##mergeCodeContainerContent').html('');
			$('##mergeCodeContainer').addClass('d-none');
			$('##emailComposeFldContainer').removeClass('d-none');;
		}
		function showEmailMergeCodeInstruction() {
			$('##emailComposeFldContainer').addClass('d-none');
			$('##mergeCodeContainer').removeClass('d-none');
			$('##mergeCodeContainerContent').html('<i class="fa-light fa-circle-notch fa-spin fa-2x"></i> Loading the merge codes...').load(mc_mergecodeslink);
		}
		/* step 3 : Choose Opt-Out List */
		function initAvailableConsentListsTable() {
			let domString = "<'row'<'col-sm-12 col-md-5'<'row'<'col-auto'f><'col-auto'l><'col-sm-12 col-md pl-md-0'i>>><'col-sm-12 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
				availableConsentListsTable = $('##availableConsentListsTable').DataTable({
					"processing": false,
					"serverSide": false,
					"pageLength": 10,
					"lengthMenu": [ 10, 25 ],
					"dom": domString,
					"info": false,
					"language" : {
						"lengthMenu": "_MENU_"
					},
					"autoWidth": false,
					"searching": true,
				});
		}
		function initmassEmailOptOutsTable() {
			let domString = "<'row'<'col-sm-12 col-md-5'<'row'<'col-auto'f><'col-auto'l><'col-sm-12 col-md pl-md-0'i>>><'col-sm-12 col-md-7'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>";
			massEmailOptOutsTable = $('##massEmailOptOutsTable').DataTable({
					"processing": false,
					"serverSide": false,
					"paging": false,
					"info": false,
					"language" : {
						"lengthMenu": "_MENU_"
					},
					"autoWidth": false,
					"searching": false,
				});
		}
		function initMassEmailOptPreferences() {
			autoSelectConsentListAsPrimary();
			$("tbody.tbodyAvailableConsentLists tr.trConsentList").each(function(){
				var clID = $(this).data('consentlistid');
				$('tbody.tbodyAvailableConsentLists tr.trConsentList'+clID + ' .addToMassEmailConsentListButton').prop("disabled",false);	
			});
		}
		function autoSelectConsentListAsPrimary(){
			let allSelectedOptoutLists = $("input[type=radio][class='emailPrefOpt']");
			let primaryOptoutList = $("input[type=radio][class='emailPrefOpt']:checked");
			
			if(allSelectedOptoutLists.length == 1) {
				$(".emailPrefOpt").prop('checked',true).attr("disabled", true);
			} else if (allSelectedOptoutLists.length > 1 && !primaryOptoutList.length) {
				$(allSelectedOptoutLists).first().prop('checked',true);
			}
		}
		function addMassEmailConsentLists(listMode){
			$('.addmore').addClass('d-none');
			$('.availableList').removeClass('d-none');
			$("tbody.tbodyMassEmailConsentLists tr.trConsentList").each(function(){
				var clID = $(this).data('consentlistid');
				$('tbody.tbodyAvailableConsentLists tr.trConsentList'+clID + ' .addToMassEmailConsentListButton').prop("disabled",true);	
			});
			$('.gridContainerLoading,.gridContainer').toggleClass('d-none');
		}
		function addToMassEmailConsentList(clID){
			let arrMassEmailConsentLists = [];
			arrMassEmailConsentLists.push({
				consentlistid:$('.trConsentList'+clID).data('consentlistid'),
				consentlisttypename:$('.trConsentList'+clID).data('listtypename'),
				consentlistname:$('.trConsentList'+clID).data('listname'),
				membercount:$('.trConsentList'+clID).data('membercount')
			});
			let template = Handlebars.compile($('##sf_MassEmailConsentList_rows').html());
			$('tbody.tbodyMassEmailConsentLists').append(template({arrMassEmailConsentLists:arrMassEmailConsentLists}));
			$('tbody.tbodyAvailableConsentLists tr.trConsentList'+clID + ' .addToMassEmailConsentListButton').prop("disabled",true);
			// Update the mc_consentListIDs variable with the new clID
			if (mc_consentListIDs) {
				mc_consentListIDs += ',' + clID;
			} else {
				mc_consentListIDs = clID;
			}
			
			resetAvailableConsentListsGridInfo();
		}
		function resetAvailableConsentListsGridInfo(){
			var rowCount = $('tbody.tbodyAvailableConsentLists tr.trConsentList:not(.d-none)').length;
			$('tbody.tbodyAvailableConsentLists_noRecords').toggleClass('d-none', rowCount != 0);
		}
		function removeMassEmailConsentList(clID){
			var issaved =$('tbody.tbodyMassEmailConsentLists tr.trConsentList'+clID).data('issaved');
			$('tbody.tbodyMassEmailConsentLists tr.trConsentList'+clID).remove();
			if( issaved == 0){
				$('tbody.tbodyAvailableConsentLists tr.trConsentList'+clID + ' .addToMassEmailConsentListButton').prop("disabled",false);
				resetAvailableConsentListsGridInfo();
			} 
			autoSelectConsentListAsPrimary();
			// Remove the clID from the mc_consentListIDs variable
			if (mc_consentListIDs) {
				let currentConsentListIDs = mc_consentListIDs.split(',');
				let updatedConsentListIDs = currentConsentListIDs.filter(id => id != clID).join(',');
				mc_consentListIDs = updatedConsentListIDs;
			}
		}
		/* Step 3: Preview Email Step */
		function initRecipientsTable(){
			recipientsListTable = $('##recipientsListTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 5,
				"lengthMenu": [ 5, 10, 50, 100 ],
				"dom": "<'row'<'col-sm-3 col-md-3'l><'col-sm-9 col-md-9'p>>" + "<'row'<'col-sm-12'tr>>" + "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
				"ajax": { 
					"url": "#local.recipientsListLink#",
					"type": "post",
					"data": function(d) {
						$.each( $('##frmCompose input[type="hidden"], ##frmCompose input[name="recipientMode"], ##frmCompose select').serializeArray(),function() {
							d[this.name] = (typeof d[this.name] != "undefined" ? d[this.name] + ',' : '') + this.value || '';
						});
					}
				},
				"autoWidth": false,
				"select": {
					"style": 'single',
					"info": false,
					"selector": 'td'
				},
				"columns": [
					<cfif listFindNoCase("Member History,Notes,Relationships",arguments.resourceType)>
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display')	{
									renderData += data.memberName;
									if (data.memberCompany.length)
										renderData += '<div class="small text-dim">'+data.memberCompany+'</div>';
									if (data.linkMemberID) {
										renderData += '<div class="mt-2"><i class="fa-solid fa-link-simple"></i> ' + data.linkMemberName + '</div>';
										if (data.linkMemberCompany.length)
											renderData += '<div class="small text-dim"><i class="fa-solid fa-link-simple invisible"></i> '+data.linkMemberCompany+'</div>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"className": "align-top",
							"width": "45%"
						},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display')	{
									renderData += data.typeName;
									if (data.categoryName.length)
										renderData += '<div class="small text-dim">'+data.categoryName+'</div>';
								}
								return type === 'display' ? renderData : data;
							},
							"className": "align-top",
							"width": "15%",
							"orderable": false
						},
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display')	{
									if (data.recipientMode != "linked") {
										if (data.memberEmail.length) {
											renderData += data.memberEmail;
										} else {
											renderData += '<div class="text-danger font-italic font-weight-bold">no email on file</div>';
										}
									}
									if (data.linkMemberID && ['both','linked'].indexOf(data.recipientMode) != -1) {
										if (data.linkMemberEmail.length) {
											renderData += '<div class="mt-2"><i class="fa-solid fa-link-simple"></i> ' + data.linkMemberEmail + '</div>';
										} else {
											renderData += '<div class="text-danger font-italic font-weight-bold"><i class="fa-solid fa-link-simple"></i> no email on file</div>';
										}
									}
								}
								return type === 'display' ? renderData : data;
							},
							"className": "align-top",
							"width": "40%",
							"orderable": false
						},
					<cfelse>
						{ "data": null,
							"render": function ( data, type, row, meta ) {
								let renderData = '';
								if (type === 'display')	{
									renderData += data.memberName;
									if (data.memberCompany.length)
										renderData += '<div class="small text-dim">'+data.memberCompany+'</div>';
								}
								return type === 'display' ? renderData : data;
							},
							"className": "align-top",
							"width": "60%"
						},
						{
							"data": null,
							"render": function (data, type,row,json) {
								let renderData = '';
								if (type === 'display') {
									if (data.memberEmail.length) {
										renderData += data.memberEmail;
									} else {
										renderData += '<span class="text-danger font-italic font-weight-bold">no email on file</span>';
									}
								}
								return type === 'display' ? renderData : data;
							},
							"class": "align-top",
							"width": "40%",
							"orderable": false
						}
					</cfif>
				],
				"order": [[0,'asc']],
				"searching": false,
				"initComplete": function( settings, json ) {
					recipientsListTable.on('select', function ( e, dt, type, indexes) {
						getPreviewMessageOnRowSelect(recipientsListTable.row({selected:true}).data().recipientRowID);
					});
				},
			});
		}
		function sendTestEmail() {
			var sendTestResult = function(r) {
				$('##btnTestTemplate').attr('disabled',false);
				if (r.success && r.success.toLowerCase() == 'true') { 
					if(r.outputmessage && r.outputmessage.length > 0) {
						$('##sendTestEmailResult').html(r.outputmessage).show().fadeOut(10000);
						$('##sendTestPrevMsgResult').html('').hide();
					}
					else
						$('##sendTestPrevMsgResult').html('<span class="badge badge-success ml-1"><i class="fa-regular fa-thumbs-up fa-lg"></i> Test e-mail sent successfully.</span>').fadeOut(5000);
				} else {
					$('##sendTestPrevMsgResult').html('<span class="badge badge-danger ml-1">Error sending test e-mail.</span>').fadeOut(5000);
				}
			};

			$('##btnTestTemplate').attr('disabled',true);
			$('##sendTestPrevMsgResult').html('<i class="fa-light fa-circle-notch fa-spin"></i> Sending test e-mail...').show();

			var templateContent = CKEDITOR.instances['templateContent'].getData();

			if ($('input[type="radio"][name="recipientMode"]').length) var recipientMode = $('input[name="recipientMode"]:checked').val();
			else var recipientMode = '';

			var objParams = { itemID:itemID, resourceType:mc_resourceType, recipientType:mc_recipientType, recipientMode:recipientMode, 
								templateContent:templateContent, subjectLine:$('##emailSubject').val(), 
								emailFromName:$('##emailFromName').val(), emailfrom:$('##emailReplyTo').val(), consentListIDs:mc_consentListIDs };
			TS_AJX('MASSEMAIL','sendTestEmailMessage',objParams,sendTestResult,sendTestResult,20000,sendTestResult);
		}
		function getPreviewMessageOnRowSelect(id) {
			var result = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					
					$('##divTestEmail').removeClass('d-none');
					$('##spTestBtn').show();
					$('##spFrom').html($('##emailReplyTo').val() + ' (' + decodeURIComponent(r.emailfrom) + ')');
					
					if(!$('##divEmailDisp')[0].shadowRoot)
						$('##divEmailDisp')[0].attachShadow({ mode: "open" });
					$('##divEmailDisp')[0].shadowRoot.innerHTML = r.templatedisp;

					$('##spSubject').html(decodeURIComponent(r.subjectline));
					
					if (r.mailattach) {
						$('##spEmailAttachment').html(r.mailattach);
						$('##previewEmailAttachRow').show();
					}
					itemID = r.itemid;

					self.location.href = '##divTestEmail';
				} else {
					alert('Unable to generate message preview.');
				}
			};

			$('##spTestBtn').hide();
			$('##sendTestPrevMsgResult').html('').hide();
			$('##spFrom, ##spSubject, ##spEmailAttachment').html('');
			$('##previewEmailAttachRow').hide();
			$('##divEmailDisp').html('<div class="mt-4"><div class="text-center"><div class="spinner-border" role="status"></div><div class="mt-2">Loading Preview...</div></div></div>');

			var templateContent = CKEDITOR.instances['templateContent'].getData();
			
			if ($('input[type="radio"][name="recipientMode"]').length) var recipientMode = $('input[name="recipientMode"]:checked').val();
			else var recipientMode = '';
			
			var objParams = { itemID:id, resourceType:mc_resourceType, recipientType:mc_recipientType, recipientMode:recipientMode, 
				templateContent:templateContent, subjectLine:$('##emailSubject').val(), emailFrom:$('##emailFromName').val() };
			TS_AJX('MASSEMAIL','getPreviewEmailMessage',objParams,result,result,10000,result);
		}

		/* Step 4: Save Template Step */
		function showEmailNewTemplateSaveDetails() {
			mca_hideAlert('saveTemplateStepErr');
			$('##newTemplateDetailsRow').removeClass('d-none');
			changeEmailTemplateCategoryOptions();
		}
		function hideEmailNewTemplateSaveDetails() {
			mca_hideAlert('saveTemplateStepErr');
			$('##templateName').val('');
			$('##selCategory').val('');
			$('##newTemplateDetailsRow').addClass('d-none');
		}
		function changeEmailTemplateCategoryOptions() {
			if($('##selCategory').val() == 0)
				$('div##divNewCategory').removeClass('d-none');
			else {
				$('##newCategoryName').val('');
				$('div##divNewCategory').addClass('d-none');
			}
		}
		function validateAndSendMassEmails() {
			hideWizardBtns();
			var errMsg = '';
			if ($('input[name="saveTemplateOption"]:checked').val() == 1) {
				if ($('##templateName').val().trim().length == 0) errMsg = 'Enter the template name.<br/>';
				if (Number($('##selCategory').val()) == 0 && $('##newCategoryName').val().trim().length == 0) errMsg += 'Enter the new template category name.<br/>';
			}
			if ($('input[name="massEmailScheduling"]:checked').val() == 'later' && $('##emailDateScheduled').val().length == 0) errMsg += 'Enter the scheduled sending date.<br/>';
			
			if (errMsg.length) {
				showWizardBtns();
				mca_showAlert('saveTemplateStepErr',errMsg);
				return false;
			}

			var arrFrmData = $('##frmCompose').serializeArray();
			var fd = {};

			$.each(arrFrmData, function() {
				if (fd[this.name] !== undefined && typeof this.value !== undefined) {
					fd[this.name] = fd[this.name] + ',' + this.value || '';
				} else {
					fd[this.name] = this.value || '';
				}
			});
			fd['templateContent'] = CKEDITOR.instances['templateContent'].getData();

			$('##saveAndSchedMailOpt').addClass('d-none');
			$('##massEmailsWizard ul[aria-label=Pagination] li a[href="##finish"]').addClass('invisible ');

			$("##massEmailSending")
				.html(mca_getLoadingHTML('Please wait while we prepare and send e-mails.'))
				.show()
				.load(mc_link_sendemails, fd, 
					function() { 
						setTimeout(function() {
							top.MCModalUtils.hideModal();
						},5000);
					});
		}
		
		$(function() {
			$("##massEmailsWizard").steps({
				headerTag: "h3",
				bodyTag: "section",
				autoFocus: false,
				titleTemplate: '<span class="number">##index##</span>',
				stepsOrientation: 1,
				transitionEffect: 'fade',
				transitionEffectSpeed: 300,
				onInit: onInitMassEmailsWizard,
				onStepChanging: validateMassEmailStep,
				onStepChanged: onLoadMassEmailStep,
				onFinishing: validateAndSendMassEmails,
				labels: {
					next: "Continue to Next Step",
					finish: 'Send E-mails'
				}
			});
		});
	</script>
	<style type="text/css">
		##recipientsListTable { font-size:0.85rem }
		div.bn { padding:2px; }
		div.bn2 { padding-top: 2px; }
		div.nmsg { padding:4px 10px 0 10px; } 
		.medim { color: ##808080; }
		.bodyText {margin:0 !important;}
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.massEmailsJS#">

<cfoutput>
<form name="frmCompose" id="frmCompose" class="mc_verticalform" spellcheck="false">
	<input type="hidden" name="resourceType" value="#arguments.resourceType#">
	<input type="hidden" name="recipientType" value="#arguments.recipientType#">
	<cfloop collection="#arguments.strFilters#" item="local.thisField">
		<input type="hidden" name="#local.thisField#" id="#local.thisField#" value="#arguments.strFilters[local.thisField]#">
	</cfloop>
	
	<div id="massEmailsWizard" class="h-100">
		<cfset local.step = 1>
		<cfif local.qryEmailTemplates.recordcount OR arrayLen(arguments.arrRecipientModes)>
			<cfset local.thisStepSelectionsList = "">
			<cfif arrayLen(arguments.arrRecipientModes)>
				<cfset local.thisStepSelectionsList = listAppend(local.thisStepSelectionsList,'Recipient Mode', ' \ ')>
			</cfif>
			<cfif local.qryEmailTemplates.recordcount>
				<cfset local.thisStepSelectionsList = listAppend(local.thisStepSelectionsList,'Template', ' \ ')>
			</cfif>
			<h3 class="font-weight-bold">Step #local.step#: Choose #local.thisStepSelectionsList#</h3>
			<section class="mt-3">
				<cfif len(arguments.strResourceTitle.resourceTitleDesc)>
					<p class="card-text">#arguments.strResourceTitle.resourceTitleDesc#</p>
				</cfif>

				<div id="chooseTemplateStepErr" class="alert alert-danger mb-3 d-none"></div>

				<cfif arrayLen(arguments.arrRecipientModes)>
					<cfif arrayLen(arguments.arrRecipientModes)>
						<div id="emailRecipientMode" class="card card-box p-3 mb-3 shadow-none">
							<p class="card-text mb-2">Email Recipients</p>
							<cfloop array="#arguments.arrRecipientModes#" index="local.thisMode">
								<div class="form-check">
									<input type="radio" name="recipientMode" id="recipientMode#local.thisMode.mode#" value="#local.thisMode.mode#" class="form-check-input">
									<label for="recipientMode#local.thisMode.mode#" class="form-check-label">#local.thisMode.desc#</label>
								</div>
							</cfloop>
						</div>
					</cfif>
				</cfif>

				<cfif local.qryEmailTemplates.recordcount>
					<div id="emailBuildOptions" class="card card-box p-3 mb-3 shadow-none">
						<div class="form-input">
							<input type="radio" name="buildEmail" id="buildEmailFromScratch" value="new" class="form-input-control" onclick="showEmailTemplateBuildOptions(this.value)">
							<label for="buildEmailFromScratch" class="form-input-label">Start this email from scratch</label>
						</div>
						<div class="form-input">
							<input type="radio" name="buildEmail" id="buildEmailFromTemplate" value="existing" class="form-input-control" onclick="showEmailTemplateBuildOptions(this.value)">
							<label for="buildEmailFromTemplate" class="form-input-label">Start this email from a template</label>
						</div>
					</div>

					<div id="divEmailTemplateSel" class="d-none">
						<select name="fEmailTemplateID" id="fEmailTemplateID" onchange="loadEmailTemplateContent();" class="form-control" data-toggle="custom-select2" placeholder="Select a template">
							<option value="">Select a template</option>
							<cfoutput query="local.qryEmailTemplates" group="categoryID">
								<optgroup label="#local.qryEmailTemplates.categoryName#">
									<cfoutput>
										<option value="#local.qryEmailTemplates.templateID#">#local.qryEmailTemplates.templateName#</option>
									</cfoutput>
								</optgroup>
							</cfoutput>
						</select>
					</div>
				<cfelse>
					<input type="hidden" name="fEmailTemplateID" id="fEmailTemplateID" value="">
				</cfif>
			</section>

			<cfset local.step++>
		</cfif>

		<h3 class="font-weight-bold">Step #local.step#: Compose Message</h3>
		<section class="mt-3">
			<div id="mergeCodeContainer" class="d-none">
				<div class="mb-3">
					<a href="javascript:showEmailComposeMessageForm();"><i class="fa-solid fa-chevrons-left"></i> Back to E-mail #arguments.strResourceTitle.resourceTitle#</a>
				</div>
				<div id="mergeCodeContainerContent"></div>
			</div>

			<div id="emailComposeFldContainer">
				<div id="composeMsgStepErr" class="alert alert-danger mb-3 d-none"></div>

				<cfif local.step EQ 1 AND len(arguments.strResourceTitle.resourceTitleDesc)>
					<p class="card-text">#arguments.strResourceTitle.resourceTitleDesc#</p>
				</cfif>

				<div class="form-label-group mb-2">
					<div class="input-group">
					<input type="text" name="emailFromName" id="emailFromName" value="" class="form-control">
					<div class="input-group-append">
						<span class="input-group-text"><a href="javascript:showEmailMergeCodeInstruction();"><small><i class="fa-regular fa-code fa-lg"></i> Merge Codes Supported</small></a></span>
					</div>
					<label for="emailFromName">From Name</label>
				</div>
				<div class="form-label-group mb-2">
					<input type="text" name="emailReplyTo" id="emailReplyTo" value="" class="form-control">
					<label for="emailReplyTo">Reply-To Address</label>
				</div>
				<div class="form-label-group mb-2">
					<div class="input-group">
						<input type="text" name="emailSubject" id="emailSubject" value="" class="form-control" autocomplete="off">
						<div class="input-group-append">
							<span class="input-group-text"><a href="javascript:showEmailMergeCodeInstruction();"><small><i class="fa-regular fa-code fa-lg"></i> Merge Codes Supported</small></a></span>
						</div>
						<label for="emailSubject">Subject</label>
					</div>
				</div>
				<div class="form-label-group mb-2">
					<select name="emailTagType" id="emailTagType" class="custom-select">
						<cfif local.qryOrgEmailTags.recordCount gt 1>
							<option value="0"></option>
						</cfif>
						<cfloop query="local.qryOrgEmailTags">
							<option value="#local.qryOrgEmailTags.emailTagTypeID#">#local.qryOrgEmailTags.emailTagType#</option>
						</cfloop>
					</select>
					<label for="emailTagType">Send to Member Emails Tagged as</label>
				</div>

				<h6 class="mt-4">#arguments.strResourceTitle.templateEditorLabel#</h6>
				#application.objWebEditor.embed(objname="templateContent", objValue="", tools="EmbedEmailEditor", contentsCss="")#
				<div class="form-text small text-dim text-right">
					<a href="javascript:showEmailMergeCodeInstruction();">Merge codes</a> supported.
				</div>
			</div>
		</section>
		<cfset local.step++>
		<cfif local.showOptOutStep>
			<h3 class="font-weight-bold">Step #local.step#: Choose Opt-Out List</h3>
			<section class="mt-3">
				<div class="card card-box mt-2">
					<div class="card-header bg-light">
						<div class="card-header--title">
							<div class="font-weight-bold font-size-lg">Opt-Outs</div>
							<div class="small text-dim">Honor recipient opt-out preferences in compliance with laws or regulations governing commercial emails</div>
						</div>
					</div>
					<div class="card-body pt-2">
						<div class="mb-2 font-weight-bold">Choose one or more opt-out lists to comply with laws, regulations, and industry standard practices. To create a new Opt-Out List, go to <a target="_blank" href="#local.manageListLink#&tab=manageLists"> Manage Lists</a>.</div>
						<div class="pl-3">
								<input type="hidden" name="excludeOptOuts" id="excludeOptOuts" value="1">
							<div id="divExcludeOptOutsOption_1" class="divExcludeOptOutsOption">
								<div class="d-flex mb-1">
									<div class="lead mt-2 mb-2 mr-auto">Selected Opt-Out Lists</div>
									<button type="button" class="btn btn-sm btn-secondary addmore" onclick="addMassEmailConsentLists('Opt-Out')">
										<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
										<span class="btn-wrapper--label">Add More</span>
									</button>
								</div>
								<table id="massEmailOptOutsTable" class="table table-sm table-striped table-bordered" style="width:100%">
									<thead>
										<tr>
											<th style="vertical-align:top;" width="60" class="text-center">Action</th>
											<th style="vertical-align:top;">Opt-Out List</th>
											<th style="vertical-align:top;" width="25%">Primary Opt-Out <div class="small">Used for One-Click Opt-Out</div></th>
										</tr>
									</thead>
									<tbody class="tbodyMassEmailConsentLists">
										<cfloop query="local.qryOrgOptOutLists">
											<cfif local.qryOrgOptOutLists.modeName eq "GlobalOptOut">
											<tr>
												<input type="hidden" name="consentListID" value="#local.qryOrgOptOutLists.consentListID#">
												<td>&nbsp; </td>
												<td>
													<span class="listTypeName">#local.qryOrgOptOutLists.consentListTypeName#</span> / <span class="listName">#local.qryOrgOptOutLists.consentListName#</span><br>
													<div class="small text-dim"><span class="memberCount">#local.qryOrgOptOutLists.memberCount#</span> total email(s)</div>
												</td>
												<td>&nbsp; </td>
											</tr>
											</cfif>
										</cfloop>
									</tbody>
								</table>
								<button type="button" class="mt-3 btn btn-sm btn-secondary btn-block addmore" onclick="addMassEmailConsentLists('Opt-Out')">
									<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
									<span class="btn-wrapper--label">Add More Opt-Out Lists</span>
								</button>
								<div class="availableList d-none">
									<div class="d-flex mb-1 mt-3">
										<div class="lead mt-2 mb-2">Choose Opt-Out Lists to Add</div>
									</div>
									<div class="gridContainerLoading">
										<div class="text-center mt-4"><div class="spinner-border" role="status"><span class="sr-only">Loading...</span></div><div class="font-weight-bold mt-2">Loading...</div></div>
									</div>
									<div id="gridContainerAvailableConsentListsTable" class="gridContainer d-none header-fixed">
										<table id="availableConsentListsTable" class="table table-sm table-striped table-bordered" style="width:100%">
											<thead>
												<tr>
													<th width="60" class="text-center">Action</th>
													<th>Opt-Out List</th>
												</tr>
											</thead>
											<tbody class="tbodyAvailableConsentLists">
												<cfloop query="local.qryOrgOptOutLists">
													<cfif local.qryOrgOptOutLists.modeName neq "GlobalOptOut">
														<tr class="trConsentList trConsentList#local.qryOrgOptOutLists.consentListID#" data-consentlistid="#local.qryOrgOptOutLists.consentListID#" data-listtypename="#local.qryOrgOptOutLists.consentListTypeName#" data-listname="#local.qryOrgOptOutLists.consentListName#" data-membercount="#local.qryOrgOptOutLists.memberCount#">
															<td class="text-center">
																<button class="btn btn-xs btn-outline-success p-1 m-1 addToMassEmailConsentListButton" onclick="addToMassEmailConsentList(#local.qryOrgOptOutLists.consentListID#);return false;" title="Add"><i class="fa-solid fa-circle-plus"></i></button>
															</td>
															<td>
																<span class="listTypeName">#local.qryOrgOptOutLists.consentListTypeName#</span> / <span class="listName">#local.qryOrgOptOutLists.consentListName#</span>
																<div class="small text-dim"><span class="memberCount">#local.qryOrgOptOutLists.memberCount#</span> total email(s)</div>
															</td>
														</tr>
													</cfif>
												</cfloop>
											</tbody>
											<tbody class="tbodyAvailableConsentLists_noRecords d-none">
												<tr><td colspan="3" class="p-3">No lists to select.</td></tr>
											</tbody>
										</table>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="card-footer bg-light">
						<div class="small text-dim">
							<strong>ONE-CLICK OPT-OUT:</strong> Some email providers, such as GMail and Yahoo, require support for a one-click opt-out option that allows their users to opt-out without visiting your website. When you choose multiple opt-out lists for a message, we need to know which one to use when we receive these requests.
						</div>
					</div>
				</div>
			</section>
			<cfset local.step++>
		</cfif>
		<h3 class="font-weight-bold">Step #local.step#: Preview Message</h3>
		<section class="mt-3">
			<table id="recipientsListTable" class="table table-sm table-hover table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>Member</th>
						<cfif listFindNoCase("Member History,Notes,Relationships",arguments.resourceType)>
							<cfif arguments.strFilters.keyExists("typeID") AND arguments.strFilters.typeID NEQ 2>
								<th>Category</th>
							<cfelse>
								<th>Relationship</th>
							</cfif>
						</cfif>
						<th>Email</th>
					</tr>
				</thead>
			</table>
			<div id="divPreviewEmailMessage">
				<div id="previewMsgStepErr" class="alert alert-danger mb-3 d-none"></div>

				<div class="d-flex mt-3">
					<div class="col-auto">
						<a href="javascript:massEmailsGotoPrevStep();" class="btn btn-link px-0"><i class="fa-solid fa-pen-to-square"></i> Modify Message</a>
					</div>
					<cfif Len(session.cfcuser.memberdata.email) gt 0>
						<div id="spTestBtn" class="col" style="display:none;">
							<button type="button" name="btnTestTemplate" id="btnTestTemplate" class="btn btn-link mr-2 px-0" onclick="sendTestEmail();">
								<i class="fa-solid fa-paper-plane"></i> Send Test E-mail
							</button>
							<span id="sendTestPrevMsgResult" style="display:none;"></span>
						</div>
					</cfif>
				</div>
				<div id="sendTestEmailResult" class="alert alert-info mt-3" style="display:none;"></div>

				<div id="divTestEmail" class="border d-none" style="border-color:##c0ccda !important;">
					<div class="bg-light px-2 py-2 border-bottom" style="border-color:##c0ccda !important;">
						<div class="d-flex mb-1">
							<span class="col-2 font-weight-bold">From:</span>
							<span id="spFrom"></span>
						</div>
						<div class="d-flex mb-1">
							<span class="col-2 font-weight-bold">Subject:</span>
							<span id="spSubject"></span>
						</div>
						<div id="previewEmailAttachRow" style="display:none;">
							<div class="d-flex mb-1">
								<span class="col-2 font-weight-bold">Attachment:</span>
								<span><i class="fa-regular fa-paperclip"></i></span>
								<span id="spEmailAttachment"></span>
							</div>
						</div>
					</div>
					<div class="p-1">
						<div id="divEmailDisp"></div>
					</div>
				</div>
			</div>
		</section>
		<cfset local.step++>

		<h3 class="font-weight-bold">Step #local.step#: Save Template Options</h3>
		<section class="mt-3">
			<div id="saveAndSchedMailOpt">
				<p class="card-text">Before we e-mail recipients,</span> should we save this message as a template for future use?</p>

				<div id="saveTemplateStepErr" class="mb-2 alert alert-danger d-none"></div>
				<div class="card card-box p-3 mb-3 shadow-none">
					<div class="form-input">
						<input type="radio" name="saveTemplateOption" id="saveTemplateOption_0" value="0" class="form-input-control" onclick="hideEmailNewTemplateSaveDetails();" checked>
						<label for="saveTemplateOption_0" class="form-input-label">No, don't save this message as a template.</label>
					</div>
					<div class="form-input">
						<input type="radio" name="saveTemplateOption" id="saveTemplateOption_1" value="1" class="form-input-control" onclick="showEmailNewTemplateSaveDetails();">
						<label for="saveTemplateOption_1" class="form-input-label">Yes, save this message as a new template.</label>
					</div>
					<div id="selTemplateRow" class="d-none">
						<div class="form-input">
							<input type="radio" name="saveTemplateOption" id="saveTemplateOption_2" value="2" class="form-input-control" onclick="hideEmailNewTemplateSaveDetails();">
							<label for="saveTemplateOption_2" class="form-input-label">Yes, save this message to the <span id="selTemplateOption" class="font-weight-bold"></span> template</label>
						</div>
					</div>

					<div id="newTemplateDetailsRow" class="d-none">
						<div class="form-label-group">
							<input type="text" name="templateName" id="templateName" value="" class="form-control" maxlength="100">
							<label for="templateName">Template Name</label>
						</div>
						<div class="form-label-group">
							<select name="selCategory" id="selCategory" class="custom-select" onchange="changeEmailTemplateCategoryOptions();">
								<cfloop query="local.qryEmailTemplateCategories">
									<option value="#local.qryEmailTemplateCategories.categoryID#">#local.qryEmailTemplateCategories.categoryName#</option>
								</cfloop>
								<option value="0">--Create a new template category--</option>
							</select>
							<label for="selCategory">Template Category</label>
						</div>
						<div class="form-label-group d-none" id="divNewCategory">
							<input type="text" name="newCategoryName" id="newCategoryName" value="" class="form-control" maxlength="100">
							<label for="newCategoryName">New Template Category</label>
						</div>
					</div>
				</div>

				<div class="card card-box p-3 mb-3 shadow-none">
					<p class="card-text">You can send this message now or schedule a future time for us to send it for you.</p>
					<div class="px-3">
						<div class="form-input">
							<input type="radio" name="massEmailScheduling" id="emailSchedSendNow" value="now" class="form-input-control" checked>
							<label for="emailSchedSendNow" class="form-input-label">Send Now</label>
						</div>
						<div class="form-input">
							<input type="radio" name="massEmailScheduling" id="emailSchedSendLater" value="later" class="form-input-control">
							<div class="form-input-label d-inline-block">
								<div class="d-flex flex-row align-items-center">
									<label for="emailSchedSendLater" class="text-nowrap">Schedule this message to be sent after</label>
									<div class="input-group input-group-sm mx-1">						
										<input type="text" name="emailDateScheduled" id="emailDateScheduled" value="" class="form-control form-control-sm dateControl">
										<div class="input-group-append">
											<span class="input-group-text cursor-pointer calendar-button" data-target="emailDateScheduled"><i class="fa-solid fa-calendar"></i></span>
										</div>
									</div>
									<span>Central</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div id="massEmailSending"></div>
		</section>
	</div>
</form>
<script id="sf_MassEmailConsentList_rows" type="text/x-handlebars-template">
	{{##each arrMassEmailConsentLists}}
		<tr class="trConsentList trConsentList{{consentlistid}}" data-consentlistid="{{consentlistid}}" data-issaved="0">
			<input type="hidden" name="consentListID" value="{{consentlistid}}">
			<td class="text-center">
				<a href="##" class="btn btn-xs btn-outline-danger p-1 m-1" onclick="removeMassEmailConsentList({{consentlistid}});return false;" title="Remove"><i class="fa-solid fa-circle-minus"></i></a>
			</td>
			<td>
				<span class="listTypeName">{{consentlisttypename}}</span> / <span class="listName">{{consentlistname}}</span>
				<div class="small text-dim"><span class="memberCount">{{membercount}}</span> total email(s)</div>
			</td>
			<td class="text-center">
				<div class="input-group mb-0">
					<div class="input-group-prepend">
						<div class="input-group-text">
							<input type="radio" name="chkIsPrimary" id="chkIsPrimary_{{consentlistid}}" class="emailPrefOpt" value="{{consentlistid}}" >
						</div>
						<span class="input-group-text"><label for="chkIsPrimary_{{consentlistid}}" class="m-0"><span class="small">Use as Primary Opt-Out List</span></label></span>
					</div>
				</div>
			</td>
		</tr>
	{{/each}}
</script>
</cfoutput>