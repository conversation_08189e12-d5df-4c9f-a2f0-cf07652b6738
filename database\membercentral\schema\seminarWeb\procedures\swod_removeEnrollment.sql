ALTER PROC dbo.swod_removeEnrollment
@enrollmentID int,
@recordedOnSiteID int,
@recordedByMemberID int,
@statsSessionID int,
@AROption char(1)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpEnrollmentTrans') IS NOT NULL 
		DROP TABLE #tmpEnrollmentTrans;

	DECLARE @depoID int, @seminarID int, @seminarName varchar(250), @message varchar(max), @currentUser varchar(126), 
		@EnrollOnOrgID int, @EnrollOnSiteID int, @EnrollOnSiteCode varchar(10), @handlesOwnPayment bit, @applicationTypeID int, 
		@minTID int, @assignedToMemberID int, @invoiceProfileID int, @invoiceID int, @GLAccountID int, 
		@invoiceNumber varchar(19), @adjAmount decimal(18,2), @registrantName VARCHAR(300), @msgjson VARCHAR(MAX),
		@bundleOrderID int, @reverseEnrollmentDepoTIDList varchar(max), @enteredByDepoMemberDataID int, @nowdate datetime, 
		@trashID int;
	DECLARE @tblAdjust TABLE (transactionID int, amountToAdjust decimal(18,2), creditGLAccountID int);
	DECLARE @tblInvoices TABLE (invoiceID int, invoiceProfileID int);

	IF @AROption NOT IN ('A','B','C')
		GOTO on_done;

	select @nowdate = getdate();
	select @applicationTypeID = memberCentral.dbo.fn_getApplicationTypeIDFromName('SemWebCatalog');

	SELECT @seminarID = s.seminarID, @seminarName = s.seminarName, @depoID = u.depoMemberDataID, 
		@EnrollOnSiteCode = mcs.siteCode, @EnrollOnOrgID = mcs.orgID, @EnrollOnSiteID = mcs.siteID, 
		@handlesOwnPayment = e.handlesOwnPayment, @bundleOrderID = e.bundleOrderID
	FROM dbo.tblSeminars AS s 
	INNER JOIN dbo.tblEnrollments AS e ON s.seminarID = e.seminarID 
	INNER JOIN dbo.tblParticipants p ON p.participantID = e.participantID
	INNER JOIN memberCentral.dbo.sites as mcs on mcs.siteCode = p.orgCode 
	INNER JOIN dbo.tblUsers AS u ON u.userID = e.userID
	WHERE e.enrollmentID = @enrollmentID;
	
	SELECT @currentUser = firstname + ' ' + lastname
	FROM membercentral.dbo.ams_members
	where memberID = @recordedByMemberID;

	SELECT @message = 'SeminarWeb On Demand enrollment removed from ' + isnull(@seminarName,'<unknown seminar>') + ' by ' + @currentuser;

	SELECT @registrantName = '[' + ISNULL(m2.firstname,d.firstName) + ' ' + ISNULL(m2.lastname,d.lastname) + ']' + ISNULL(' (' + m2.membernumber + ')','')
	FROM dbo.tblEnrollments AS e
	INNER JOIN dbo.tblUsers AS u ON e.userID = u.userID
	INNER JOIN trialsmith.dbo.depomemberdata AS d ON d.depomemberdataID = u.depoMemberDataID
	LEFT OUTER JOIN membercentral.dbo.ams_members AS m
		INNER JOIN membercentral.dbo.ams_members as m2 on m2.orgID = m.orgID and m2.memberID = m.activeMemberID
	ON m.memberID = e.MCMemberID
	WHERE e.enrollmentID = @enrollmentID;

	EXEC memberCentral.dbo.ams_getTLASITESDepoMemberDataIDByMemberID @memberID=@recordedByMemberID, @siteID=@recordedOnSiteID, 
		@depomemberdataid=@enteredByDepoMemberDataID OUTPUT;

	-- Assocation handles payment 
	IF @handlesOwnPayment = 1 AND @AROption IN ('A','B') BEGIN
		EXEC memberCentral.dbo.ams_getMemberIDByTLASITESDepoMemberDataID @siteCode=@EnrollOnSiteCode, @depoMemberDataID=@depoID, 
			@memberID=@assignedToMemberID OUTPUT;

		-- get all enrollment transactions
		select transactionID, typeID
		into #tmpEnrollmentTrans
		from memberCentral.dbo.fn_sw_enrollmentTransactions(@enrollmentID,'SWOD')
		OPTION(RECOMPILE);

		-- put all open invoices used for enrollment into table since they were already created and can be used for adjustments
		insert into @tblInvoices (invoiceID, invoiceProfileID)
		select distinct i.invoiceID, i.invoiceProfileID
		from #tmpEnrollmentTrans as et
		inner join memberCentral.dbo.tr_invoiceTransactions as it on it.orgID = @EnrollOnOrgID and it.transactionID = et.transactionID
		inner join memberCentral.dbo.tr_invoices as i on i.orgID = @EnrollOnOrgID and i.invoiceID = it.invoiceID and i.statusID = 1;
		
		-- get all enrollment-related sales transactions we need to adjust
		IF @AROption = 'A' 
			INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
			select et.transactionID, tsFull.cache_amountAfterAdjustment, t.creditGLAccountID
			from #tmpEnrollmentTrans as et
			inner join memberCentral.dbo.tr_transactions as t on t.transactionID = et.transactionID
			cross apply memberCentral.dbo.fn_tr_transactionSalesWithDIT(@EnrollOnOrgID,t.transactionID) as tsFull
			where tsFull.cache_amountAfterAdjustment > 0
			and et.typeID = 1
			OPTION(RECOMPILE);
		IF @AROption = 'B' 
			INSERT INTO @tblAdjust (transactionID, amountToAdjust, creditGLAccountID)
			select et.transactionID, tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount, t.creditGLAccountID
			from #tmpEnrollmentTrans as et
			inner join memberCentral.dbo.tr_transactions as t on t.transactionID = et.transactionID
			cross apply memberCentral.dbo.fn_tr_transactionSalesWithDIT(@EnrollOnOrgID,t.transactionID) as tsFull
			where et.typeID = 1
			and tsFull.cache_amountAfterAdjustment-tsFull.cache_activePaymentAllocatedAmount-tsFull.cache_pendingPaymentAllocatedAmount > 0
			OPTION(RECOMPILE);
	END
	
	-- SW handles payment
	IF @handlesOwnPayment = 0 AND @AROption = 'A'
		SELECT @reverseEnrollmentDepoTIDList = COALESCE(@reverseEnrollmentDepoTIDList + ',', '') + cast(dt.TransactionID as varchar(10))
		FROM trialsmith.dbo.depoTransactionsApplications as dta 
		INNER JOIN trialsmith.dbo.depoTransactions as dt on dt.TransactionID = dta.transactionID
		WHERE dta.itemID = @enrollmentID
		AND dta.itemType = 'SWE'
		AND dt.Reversable = 'Y';

	BEGIN TRAN;
		UPDATE dbo.tblEnrollments
		SET isActive = 0
		WHERE enrollmentID = @enrollmentID;

		-- mark the enrollment as fee exempt in specific cases:
		-- if enrollment is tied to a bundle
		-- if assn takes the money and AROption is A
		-- if SW takes the money and AROption is A
		IF @bundleOrderID IS NOT NULL OR @AROption = 'A'
			UPDATE dbo.tblEnrollments
			SET isFeeExempt = 1
			WHERE enrollmentID = @enrollmentID;

		INSERT INTO trialsmith.dbo.customernotes (depomemberdataid, notetypeid, note, dateentered)
		VALUES (@depoID, 1, @message, @nowdate);

		UPDATE r
		SET r.isActive = 0
		FROM formbuilder.dbo.tblResponses AS r
		INNER JOIN dbo.tblSeminarsAndFormResponses AS safr ON safr.responseID = r.responseID
			AND safr.enrollmentID = @enrollmentID;

		-- Assocation handles payment
		IF @handlesOwnPayment = 1 AND @AROption IN ('A','B') BEGIN
			UPDATE memberCentral.dbo.tr_applications
			SET [status] = 'D'
			WHERE itemID = @enrollmentID
			AND itemType = 'SWODRate'
			AND applicationTypeID = @applicationTypeID
			AND [status] <> 'D';

			-- if there are adjustments to make
			IF EXISTS (select transactionID from @tblAdjust) BEGIN
				SELECT @minTID = min(transactionID) from @tblAdjust;
				WHILE @minTID IS NOT NULL BEGIN
					select @invoiceProfileID = null, @invoiceID = null, @adjAmount = null, @GLAccountID = null;

					select @adjAmount = amountToAdjust*-1, @GLAccountID = creditGLAccountID from @tblAdjust where transactionID = @minTID;
					select @invoiceProfileID = invoiceProfileID from memberCentral.dbo.tr_glAccounts where glAccountID = @GLAccountID;
					select top 1 @invoiceID = invoiceID from @tblInvoices where invoiceProfileID = @invoiceProfileID;
					
					-- if necessary, create invoice assigned to enrollment based on invoice profile
					IF @invoiceID is null BEGIN
						EXEC memberCentral.dbo.tr_createInvoice @invoiceProfileID=@invoiceProfileID, @enteredByMemberID=@recordedByMemberID, 
							@assignedToMemberID=@assignedToMemberID, @dateBilled=@nowDate, @dateDue=@nowdate, 
							@invoiceID=@invoiceID OUTPUT, @invoiceNumber=@invoiceNumber OUTPUT;

						INSERT INTO @tblInvoices (invoiceID, invoiceProfileID)
						values (@invoiceID, @invoiceProfileID);
					END	

					EXEC memberCentral.dbo.tr_createTransaction_adjustment @recordedOnSiteID=@recordedOnSiteID, @recordedByMemberID=@recordedByMemberID, 
						@statsSessionID=@statsSessionID, @amount=@adjAmount, @taxAmount=null, @transactionDate=@nowdate,
						@autoAdjustTransactionDate=1, @saleTransactionID=@minTID, @invoiceID=@invoiceID, @byPassTax=0, @byPassAccrual=0,
						@xmlSchedule=null, @transactionID=@trashID OUTPUT;
					
					SELECT @minTID = min(transactionID) from @tblAdjust where transactionID > @minTID;
				END
			END
		END

		-- SW handles payment
		IF @handlesOwnPayment = 0 AND @AROption = 'A' AND @reverseEnrollmentDepoTIDList IS NOT NULL
			EXEC trialsmith.dbo.transactions_reverse @tidList=@reverseEnrollmentDepoTIDList, @enteredByDepoMemberDataID=@enteredByDepoMemberDataID;

		IF @bundleOrderID IS NULL
			EXEC dbo.sw_calculateEnrollmentRevenueAndRegFees @enrollmentID=@enrollmentID, @calcOnly=0, @asOfDate=@nowDate;
		ELSE
			EXEC dbo.sw_calculateBundleOrderRevenueAndRegFees @bundleOrderID=@bundleOrderID, @calcOnly=0, @asOfDate=@nowDate;
	COMMIT TRAN;

	-- process conditions based on seminarweb
	EXEC membercentral.dbo.ams_processSeminarWebConditionsByDepoMemberDataID @depomemberdataID=@depoID;

	SET @msgjson = 'Enrollment has been removed for registrant '+ @registrantName +' on SWOD-' + CAST(@seminarID AS VARCHAR(10)) + '.' + CHAR(13) + CHAR(10)
		+ 'Accounts Receivable Option: ' + 
		CASE WHEN @handlesOwnPayment = 1 THEN
				CASE
					WHEN @AROption = 'A' THEN 'Full Refund of Entire Registration with No Balances Due'
					WHEN @AROption = 'B' THEN 'Retain Previously Applied Payments, Zero Out Remaining Balances Due on Registration'
					WHEN @AROption = 'C' THEN 'Make No Changes to Sales, Invoices and Payments Associated with this Registration'
				END
			ELSE
				CASE
					WHEN @AROption = 'A' THEN 'Full Refund of Entire Registration with No Balances Due'
					WHEN @AROption = 'B' THEN 'Make No Changes to Sales, Invoices and Payments Associated with this Registration'
				END
		END;

	INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
	VALUES ('{ "c":"auditLog", "d": {
		"AUDITCODE":"SW",
		"ORGID":' + CAST(@EnrollOnOrgID AS VARCHAR(10)) + ',
		"SITEID":' + CAST(@EnrollOnSiteID AS VARCHAR(10)) + ',
		"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
		"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
		"MESSAGE":"' + replace(memberCentral.dbo.fn_cleanInvalidXMLChars(@msgjson),'"','\"') + '" } }');

	on_done:
	IF OBJECT_ID('tempdb..#tmpEnrollmentTrans') IS NOT NULL 
		DROP TABLE #tmpEnrollmentTrans; 

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC memberCentral.dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
