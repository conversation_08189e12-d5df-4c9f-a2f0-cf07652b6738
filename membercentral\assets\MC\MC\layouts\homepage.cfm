<cfoutput>
	<cfset local.zoneC1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='C' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['C'],1)>
			<cfset local.zoneC1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['C'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneD1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='D' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['D'],1)>
			<cfset local.zoneD1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['D'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>

	<cfset local.zoneE1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='E' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['E'],1)>
			<cfset local.zoneE1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['E'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneF1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='F' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['F'],1)>
			<cfset local.zoneF1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['F'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<cfset local.zoneG1Content="" />
	<cfif application.objCMS.getZoneItemCount(zone='G' ,event=event)>
		<cfif arrayIsDefined(event.getValue("mc_pageDefinition").pageZones['G'],1)>
			<cfset local.zoneG1Content=trim(REReplace(event.getValue( "mc_pageDefinition").pageZones['G'][1].data, "<\/?p[^>]*>", "", "all"))>
		</cfif>
	</cfif>
	
	<!doctype html>
	<html lang="en">
		<head>
			<cfinclude template="head.cfm">		
		</head>
		
		<cfif event.getValue('mc_pageDefinition.layoutMode','normal') neq "direct">
			<body>
				<cfinclude template="jssalesscript.cfm">
				<div class="wrapper">
					<cfinclude template="header.cfm">
					<cfif application.objCMS.getZoneItemCount(zone='Main' ,event=event)>
						<cfset local.mainContent=event.getValue("mc_pageDefinition").pageZones['Main']>
						<cfif arrayLen(local.mainContent)>
							<span class="zoneMainWrapper hide">
							<cfloop from="1" to="#arrayLen(local.mainContent)#" index="local.thisItem">
								<cfif lcase(trim(local.mainContent[local.thisItem].view)) EQ 'echo' AND len(trim(local.mainContent[local.thisItem].data))>
								#trim(REReplace(local.mainContent[local.thisItem].data, "<\/?p[^>]*>", "", "all"))#
								</cfif>
							</cfloop>
							</span>
							<span class="zoneMainHolder"></span>
						</cfif>
					</cfif>
					
					<span class="zoneMainC1Wrapper hide">#local.zoneC1Content#</span>
					<span class="zoneMainC1Holder"></span>
					
					<span class="zoneMainD1Wrapper hide">#local.zoneD1Content#</span>
					<span class="zoneMainD1Holder"></span>

					<span class="zoneMainE1Wrapper hide">#local.zoneE1Content#</span>
					<span class="zoneMainE1Holder"></span>

					<span class="zoneMainF1Wrapper hide">#local.zoneF1Content#</span>
					<span class="zoneMainF1Holder"></span>

					<span class="zoneG1Wrapper hide">#local.zoneG1Content#</span>
					<span class="zoneG1Holder"></span>
					
					<span class="zoneMainH1Wrapper hide">#local.zoneH1Content#</span>
					<span class="zoneMainH1Holder"></span>
					
					<cfinclude template="footer.cfm">
					<cfinclude template="toolBar.cfm">
				</div>
			</body>
		<cfelse>
			<cfinclude template="jssalesscript.cfm">
			<body style="background:none!important;background:unset!important;padding:20px;" class="innerPage-content">
				#application.objCMS.renderZone(zone='Main',event=event)#
			</body>		
		</cfif>
		<cfinclude template="foot.cfm">
	</html>
</cfoutput>