<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getEmailRecipients" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 5)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.qryActivity = CreateObject('component', 'model.admin.emailBlast.emailBlast').getEmailRecipientsFromFilters(event=arguments.event, gridMode=arguments.event.getValue('gridMode',''));
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryActivity">
			<cfset local.tmpStr = {
				"recipientID": local.qryActivity.recipientID,
				"messageID": local.qryActivity.messageID,
				"memberID": local.qryActivity.memberID,
				"memberName": local.qryActivity.memberName,
				"company": local.qryActivity.company,
				"toEmail": local.qryActivity.toEmail,
				"subject": local.qryActivity.subject,
				"messageType": local.qryActivity.messageType,
				"statusCode": local.qryActivity.statusCode,
				"status": local.qryActivity.status,
				"dateLastUpdated": dateFormat(local.qryActivity.dateLastUpdated,"m/d/yy") & " " & timeFormat(local.qryActivity.dateLastUpdated,"h:mm tt"),
				"canResend": NOT listFindNoCase("I,scheduled", local.qryActivity.statusCode),
				"allowAdminResend": local.qryActivity.allowAdminResend,
				"sg_open": val(local.qryActivity.sg_open),
				"sg_click": val(local.qryActivity.sg_click),
				"DT_RowId": "recipientRow_#local.qryActivity.messageID#|#local.qryActivity.recipientID#"
			}>

			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryActivity.messageCount),
			"recordsFiltered": val(local.qryActivity.messageCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getProfessionalLicenses" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryProLicenses">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID INT = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;
			<cfif len(local.searchValue)>
				DECLARE @searchValue varchar(300) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			select pl.PLID, pl.PLTypeID, pl.PLStatusID, pl.LicenseNumber, pl.ActiveDate, 
				pls.statusName, plt.PLName, plt.orderNum
			from dbo.ams_memberProfessionalLicenses pl
			inner join dbo.ams_memberProfessionalLicenseTypes plt on plt.PLTypeID = pl.PLTypeID
				and plt.orgID = @orgID
			inner join dbo.ams_memberProfessionalLicenseStatuses pls on pls.PLStatusID = pl.PLStatusID
				and pls.orgID = @orgID
			where pl.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('memberID')#">
			<cfif len(local.searchValue)>
				and (plt.PLName LIKE @searchValue OR pl.LicenseNumber LIKE @searchValue)
			</cfif>
			order by plt.orderNum;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryProLicenses">
			<cfset local.arrData.append({
				"PLID": local.qryProLicenses.PLID,
				"PLName": local.qryProLicenses.PLName,
				"LicenseNumber": local.qryProLicenses.LicenseNumber,
				"ActiveDate": DateFormat(local.qryProLicenses.ActiveDate,"m/d/yyyy"),
				"StatusName": local.qryProLicenses.statusName,
				"PLTypeID": local.qryProLicenses.PLTypeID,
				"DT_RowId": "proLicenseRow_#local.qryProLicenses.PLID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryProLicenses.recordCount,
			"recordsFiltered": local.qryProLicenses.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getMemberDocuments" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"docTitle #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"filename #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"dateModified #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderBy')+1]>

		<cfquery datasource="#application.dsn.memberCentral.dsn#" name="local.qryMemberDocuments">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
				DROP TABLE ##tmpDocuments;
			CREATE TABLE ##tmpDocuments (itemID int, docSection varchar(50), documentID int, documentVersionID int, docTitle varchar(520),
				fileName varchar(255), fileExt varchar(20), languageCode varchar(5), dateModified datetime);

			DECLARE @memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('memberid')#">,
				@posStart int, @posStartAndCount int, @totalCount int;

			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			INSERT INTO ##tmpDocuments (itemID, docSection, documentID, documentVersionID, docTitle, fileName, fileExt, languageCode, dateModified)
			select md.memberDocumentID, 'Member Document', d.documentID, dv.documentVersionID, dl.docTitle, dv.fileName, dv.fileExt, l.languageCode, dv.dateModified
			from dbo.ams_memberDocuments as md
			inner join dbo.ams_members as m on m.memberid = md.memberID
			inner join dbo.cms_documents as d ON md.documentID = d.documentID
			inner join dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
			inner join dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
			inner join dbo.cms_languages as l ON l.languageID = dl.languageID
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID and sr.siteResourceStatusID = 1
			where m.activeMemberID = @memberID
			<cfif len(arguments.event.getTrimValue('cs',''))>
				AND d.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('cs')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('ce',''))>
				AND d.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('ce')# 23:59:59.997">
			</cfif>
			<cfif len(arguments.event.getTrimValue('ms',''))>
				AND dl.dateModified >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('ms')#">
			</cfif>
			<cfif len(arguments.event.getTrimValue('me',''))>
				AND dl.dateModified <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('me')# 23:59:59.997">
			</cfif>
			<cfif len(arguments.event.getTrimValue('docTitle',''))>
				AND dl.docTitle LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('docTitle')#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('docDesc',''))>
				AND dl.docDesc LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('docDesc')#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('filename',''))>
				AND dv.fileName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('filename')#%">
			</cfif>;

			<cfif NOT len(arguments.event.getTrimValue('docDesc',''))>
				INSERT INTO ##tmpDocuments (itemID, docSection, documentID, documentVersionID, docTitle, fileName, fileExt, languageCode, dateModified)
				select pm.panelMemberID, 'LRIS Document', d.documentID, dv.documentVersionID, dl.docTitle + ': ' + p.name, dv.fileName, dv.fileExt, 
					l.languageCode, dv.dateModified
				from dbo.ref_panelMembers as pm
				inner join dbo.ref_panels as p on p.panelID = pm.panelID and p.isActive = 1
				inner join dbo.ams_members as m on m.memberid = pm.memberID
				inner join dbo.cms_documents as d ON pm.applicationDocumentID = d.documentID
				inner join dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				inner join dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				inner join dbo.cms_languages as l ON l.languageID = dl.languageID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = d.siteResourceID and sr.siteResourceStatusID = 1
				where m.activeMemberID = @memberID
				<cfif len(arguments.event.getTrimValue('cs',''))>
					AND d.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('cs')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('ce',''))>
					AND d.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('ce')# 23:59:59.997">
				</cfif>
				<cfif len(arguments.event.getTrimValue('ms',''))>
					AND dl.dateModified >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('ms')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('me',''))>
					AND dl.dateModified <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('me')# 23:59:59.997">
				</cfif>
				<cfif len(arguments.event.getTrimValue('filename',''))>
					AND dv.fileName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('filename')#%">
				</cfif>
				<cfif len(arguments.event.getTrimValue('docTitle',''))>
					AND dl.docTitle + ': ' + p.name LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('docTitle')#%">
				</cfif>;

				INSERT INTO ##tmpDocuments (itemID, docSection, documentID, documentVersionID, docTitle, fileName, fileExt, languageCode, dateModified)
				select mdc.columnID, 'Custom Field Document', d.documentID, dv.documentVersionID, mdc.columnName, dv.fileName, dv.fileExt, l.languageCode, dv.dateModified
				from dbo.ams_memberDataColumns as mdc
				inner join dbo.ams_memberDataColumnDataTypes as dt on dt.dataTypeID = mdc.dataTypeID
				inner join dbo.ams_memberDataColumnValues as mdcv on mdcv.columnID = mdc.columnID
				inner join dbo.cms_siteResources as sr on sr.siteResourceID = mdcv.columnValueSiteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.ams_memberData as md on md.valueID = mdcv.valueID
				inner join dbo.ams_members as m on m.memberid = md.memberID
				inner join dbo.cms_documents as d ON sr.siteResourceID = d.siteResourceID
				inner join dbo.cms_documentLanguages as dl ON d.documentID = dl.documentID
				inner join dbo.cms_documentVersions as dv ON dl.documentLanguageID = dv.documentLanguageID and dv.isActive = 1
				inner join dbo.cms_languages as l ON l.languageID = dl.languageID
				where m.activeMemberID = @memberID
				and dt.dataTypeCode = 'DOCUMENTOBJ'
				<cfif len(arguments.event.getTrimValue('cs',''))>
					AND d.dateCreated >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('cs')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('ce',''))>
					AND d.dateCreated <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('ce')# 23:59:59.997">
				</cfif>
				<cfif len(arguments.event.getTrimValue('ms',''))>
					AND dl.dateModified >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getTrimValue('ms')#">
				</cfif>
				<cfif len(arguments.event.getTrimValue('me',''))>
					AND dl.dateModified <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getTrimValue('me')# 23:59:59.997">
				</cfif>
				<cfif len(arguments.event.getTrimValue('filename',''))>
					AND dv.fileName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('filename')#%">
				</cfif>
				<cfif len(arguments.event.getTrimValue('docTitle',''))>
					AND mdc.columnName LIKE <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getTrimValue('docTitle')#%">
				</cfif>;
			</cfif>

			SELECT @totalCount = COUNT(itemID) FROM ##tmpDocuments;

			SELECT itemID, docSection, documentID, documentVersionID, docTitle, fileName, fileExt, languageCode, dateModified, @totalCount AS totalCount
			FROM (
				SELECT itemID, docSection, documentID, documentVersionID, docTitle, fileName, fileExt, languageCode, dateModified,
					ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderBy)#) AS row
				FROM ##tmpDocuments
			) tmp
			WHERE row > @posStart
			AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpDocuments') IS NOT NULL
				DROP TABLE ##tmpDocuments;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.data = []>
		<cfloop query="local.qryMemberDocuments">
			<cfswitch expression="#local.qryMemberDocuments.fileExt#">
				<cfcase value="doc,docx" delimiters=","><cfset local.fileIcon = "fa-file-word"></cfcase>
				<cfcase value="xls,xlsx" delimiters=","><cfset local.fileIcon = "fa-file-excel"></cfcase>
				<cfcase value="ppt,pptx" delimiters=","><cfset local.fileIcon = "fa-file-powerpoint"></cfcase>
				<cfcase value="gif,png,jpg,jpeg" delimiters=","><cfset local.fileIcon = "fa-file-image"></cfcase>
				<cfcase value="html"><cfset local.fileIcon = "fa-file-code"></cfcase>
				<cfcase value="pdf"><cfset local.fileIcon = "fa-file-pdf"></cfcase>
				<cfcase value="txt"><cfset local.fileIcon = "fa-file-lines"></cfcase>
				<cfdefaultcase><cfset local.fileIcon = "fa-file"></cfdefaultcase>
			</cfswitch>
			<cfset local.tmpStr = {
				"itemID": local.qryMemberDocuments.itemID,
				"docSection": local.qryMemberDocuments.docSection,
				"documentID": local.qryMemberDocuments.documentID,
				"documentVersionID": local.qryMemberDocuments.documentVersionID,
				"languageCode": local.qryMemberDocuments.languageCode,
				"docTitle": local.qryMemberDocuments.docTitle,
				"fileName": local.qryMemberDocuments.fileName,
				"icon": local.fileIcon,
				"dateModified": dateTimeFormat(local.qryMemberDocuments.dateModified,"m/d/yyyy h:nn tt")
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>
		
		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryMemberDocuments.totalCount),
			"recordsFiltered": val(local.qryMemberDocuments.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

    <cffunction name="getInvoiceListByMemberID" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',5))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<!--- get the SRID and permissions of InvoiceAdmin. --->
		<cfset local.InvoiceAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='InvoiceAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsInvoiceAdmin = buildRightAssignments(local.InvoiceAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- card on file options --->
		<cfset local.cof0 = false>
		<cfset local.cofList = arguments.event.getTrimValue('cardOnFile','')>
		<cfif len(local.cofList)>
			<cfset local.cod0Loc = listFind(local.cofList,0)>
			<cfif local.cod0Loc>
				<cfset local.cof0 = true>
				<cfset local.cofList = listDeleteAt(local.cofList,local.cod0Loc)>
			</cfif>
		</cfif>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"dateDue #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"invoiceNumber #arguments.event.getValue('orderDir')#")>
		<cfset arrayAppend(local.arrCols,"memberName #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<!--- get invoices associated with: assigned to, made payment against, or has transactions on --->
		<cfquery name="local.qryInvoices" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @memberID int, @totalCount int, @posStart int, @posStartAndCount int, @hasSuffix int, @hasMiddleName int;
			SET @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">;
			SET @memberID = <cfqueryparam value="#arguments.event.getValue('memberid',0)#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			SELECT @hasSuffix=hasSuffix, @hasMiddleName=hasMiddleName 
			FROM dbo.organizations 
			WHERE orgid = @orgID;

			IF OBJECT_ID('tempdb..##tmpInv') IS NOT NULL 
				DROP TABLE ##tmpInv;
			IF OBJECT_ID('tempdb..##tmpInnerTbl') IS NOT NULL 
				DROP TABLE ##tmpInnerTbl;
			IF OBJECT_ID('tempdb..##tmpFinalInvoices') is not null
				DROP TABLE ##tmpFinalInvoices;
			CREATE TABLE ##tmpInv (invoiceID int PRIMARY KEY);
			CREATE TABLE ##tmpinnerTbl (invoiceID int PRIMARY KEY, invoiceNumber varchar(19), dateCreated datetime, dateBilled datetime, dateDue datetime, 
				assignedToMemberID int, memberName varchar(300), company varchar(200), invoiceStatus varchar(10), invoiceProfile varchar(50), hasCard bit);
			CREATE TABLE ##tmpFinalInvoices (invoiceID int PRIMARY KEY, invoiceNumber varchar(19), dateCreated datetime, dateBilled datetime, dateDue datetime, 
				invoiceStatus varchar(10), invoiceProfile varchar(50), assignedToMemberID int, memberName varchar(300), company varchar(200), hasCard bit, 
				invAmt decimal(14,2), invDue decimal(14,2), inPaymentQueue bit, row int);

			insert into ##tmpInv (invoiceID)
			EXEC dbo.tr_getInvoicesAssociatedToMember @memberID=@memberID;

			INSERT INTO ##tmpinnerTbl (invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, assignedToMemberID, memberName, company, 
				invoiceStatus, invoiceProfile, hasCard)
			select i.invoiceID, i.fullInvoiceNumber, i.dateCreated, i.dateBilled, i.dateDue, m2.memberID as assignedToMemberID, 
				m2.lastname 
				+ case when @hasSuffix = 1 then isnull(' ' + nullif(m2.suffix,''),'') else '' end
				+ ', ' + m2.firstname 
				+ case when @hasMiddleName = 1 then isnull(' ' + nullif(m2.middlename,''),'') else '' end 
				+ ' (' + m2.membernumber + ')' as memberName, m2.company, 
				istat.status as invoiceStatus, ip.profileName as invoiceProfile,
				case when i.payProfileID is null then 0 else 1 end as hasCard
			from ##tmpInv as invoices
			inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceID = invoices.invoiceID
			inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileID = i.invoiceProfileID
			inner join dbo.tr_invoiceStatuses as istat on istat.statusID = i.statusID
			inner join dbo.ams_members as m on m.orgID = @orgID and m.memberid = i.assignedToMemberID
			inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activeMemberID
			where i.orgID = @orgID
			<cfif len(arguments.event.getTrimValue('statusID',''))>
				and i.statusID in (<cfqueryparam value="#arguments.event.getValue('statusID')#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			</cfif>
			<cfif len(arguments.event.getTrimValue('invProfile',''))>
				and i.invoiceProfileID in (<cfqueryparam value="#arguments.event.getValue('invProfile')#" cfsqltype="CF_SQL_INTEGER" list="yes">)
			</cfif>
			<cfif val(arguments.event.getValue('invoiceNumber','')) gt 0 and isValid("integer",val(arguments.event.getValue('invoiceNumber')))>
				and i.invoiceNumber = <cfqueryparam value="#val(arguments.event.getValue('invoiceNumber'))#" cfsqltype="CF_SQL_INTEGER">
			<cfelseif val(arguments.event.getValue('invoiceNumber','')) gt 0>
				and i.invoiceNumber = 0
			</cfif>
			<cfif len(arguments.event.getValue('duedateStart',''))>
				and i.dateDue >= <cfqueryparam value="#arguments.event.getValue('duedateStart')#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.event.getValue('duedateEnd',''))>
				and i.dateDue < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('duedateEnd'))#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.event.getValue('billeddateStart',''))>
				and i.dateBilled >= <cfqueryparam value="#arguments.event.getValue('billeddateStart')#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(arguments.event.getValue('billeddateEnd',''))>
				and i.dateBilled < <cfqueryparam value="#dateAdd('d',1,arguments.event.getValue('billeddateEnd'))#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(local.cofList)>
				and (
					<cfif local.cof0>
						i.payProfileID is null
					</cfif>
					<cfif local.cof0 and listLen(local.cofList)>
						or 
					</cfif>
					<cfif listLen(local.cofList)>
						i.MPProfileID in (<cfqueryparam value="#local.cofList#" cfsqltype="CF_SQL_INTEGER" list="yes">)
					</cfif>
					)
			</cfif>;

			INSERT INTO ##tmpFinalInvoices (invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, invoiceStatus, invoiceProfile, assignedToMemberID, 
				memberName, Company, hasCard, invAmt, invDue, inPaymentQueue, row)
			select tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateCreated, tmp2.dateBilled, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
				tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, tmp2.hasCard,
				sum(it.cache_invoiceAmountAfterAdjustment) as InvAmt,
				sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) as InvDue,
				inPaymentQueue = cast(case when api.invoiceID is not null then 1 else 0 end as bit),
				ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row 
			from ##tmpInnerTbl as tmp2
			left outer join dbo.tr_invoiceTransactions as it 
			<cfif len(arguments.event.getTrimValue('trDetail',''))>
				inner join dbo.tr_transactions as t on t.ownedByOrgID = @orgID and t.transactionID = it.transactionID
			</cfif>
			on it.orgID = @orgID and it.invoiceID = tmp2.invoiceID
			left outer join (
				select qpid.invoiceID
				from platformQueue.dbo.queue_payInvoicesDetail as qpid
				inner join platformQueue.dbo.queue_payInvoices as qpi on qpi.itemID = qpid.itemID
				inner join platformQueue.dbo.tblQueueStatuses as qs on qs.queueStatusID = qpi.statusID
				where qs.queueStatus not in ('readyToNotify','grabbedForNotifying','done')
				) api on api.invoiceID = tmp2.invoiceID
			<cfif len(arguments.event.getTrimValue('trDetail',''))>
				where t.detail like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#arguments.event.getTrimValue('trDetail')#%">
			</cfif>
			group by tmp2.invoiceID, tmp2.invoiceNumber, tmp2.dateCreated, tmp2.dateBilled, tmp2.dateDue, tmp2.invoiceStatus, tmp2.invoiceProfile, 
				tmp2.assignedToMemberID, tmp2.memberName, tmp2.company, tmp2.hasCard, api.invoiceID
			having tmp2.invoiceID = tmp2.invoiceid
			<cfif len(arguments.event.getValue('dueAmtStart',''))>
				and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) >= <cfqueryparam value="#rereplace(arguments.event.getValue('dueAmtStart',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
			</cfif>
			<cfif len(arguments.event.getValue('dueAmtEnd',''))>
				and sum(it.cache_invoiceAmountAfterAdjustment-it.cache_activePaymentAllocatedAmount) <= <cfqueryparam value="#rereplace(arguments.event.getValue('dueAmtEnd',''),"[^0-9.]","","ALL")#" cfsqltype="CF_SQL_DECIMAL" scale="2">
			</cfif>;

			SET @totalCount = @@ROWCOUNT;

			SELECT invoiceID, invoiceNumber, dateCreated, dateBilled, dateDue, invoiceStatus, invoiceProfile, assignedToMemberID, 
				memberName, company, hasCard, invAmt, invDue, inPaymentQueue, row, @totalCount as totalCount 
			FROM ##tmpFinalInvoices
			WHERE row > @posStart AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpInv') IS NOT NULL 
				DROP TABLE ##tmpInv;
			IF OBJECT_ID('tempdb..##tmpInnerTbl') IS NOT NULL 
				DROP TABLE ##tmpInnerTbl;
			IF OBJECT_ID('tempdb..##tmpFinalInvoices') is not null
				DROP TABLE ##tmpFinalInvoices;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryInvoices">
			<cfif (listFindNoCase("Closed,Delinquent",local.qryInvoices.invoiceStatus) 
					AND local.qryInvoices.InvDue gt 0 
					AND local.qryInvoices.inPaymentQueue is 0 
					AND local.myRightsTransactionsAdmin.transAllocatePayment is 1)
				OR (local.qryInvoices.InvDue gt 0 
					AND local.qryInvoices.inPaymentQueue is 0 
					AND local.myRightsInvoiceAdmin.invoiceClose is 1 
					AND local.myRightsTransactionsAdmin.transAllocatePayment is 1)>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=arguments.event.getValue('memberid',0), t="Invoice #local.qryInvoices.invoiceNumber#", ta=local.qryInvoices.InvDue, tmid=arguments.event.getValue('memberid',0), ad="v|#local.qryInvoices.invoiceid#")>
			<cfelse>
				<cfset local.addPaymentEncString = "">
			</cfif>

			<cfset local.tmpStr = {
				"invoiceID": local.qryInvoices.invoiceID,
				"invoiceNumber": local.qryInvoices.invoiceNumber,
				"dateDue": dateformat(local.qryInvoices.dateDue,"m/d/yyyy"),
				"dateBilled": dateformat(local.qryInvoices.dateBilled,"m/d/yyyy"),
				"dateCreated": dateformat(local.qryInvoices.dateCreated,"m/d/yyyy"),
				"invoiceStatus": local.qryInvoices.invoiceStatus,
				"inPaymentQueue": local.qryInvoices.inPaymentQueue,
				"invoiceProfile": local.qryInvoices.invoiceProfile,
				"membername": local.qryInvoices.membername,
				"membernameEnc": encodeForHTMLAttribute(local.qryInvoices.membername),
				"company": local.qryInvoices.company,
				"assignedTomemberid": local.qryInvoices.assignedTomemberid,
				"InvAmt": dollarFormat(local.qryInvoices.InvAmt),
				"InvDue": dollarFormat(local.qryInvoices.InvDue),
				"hasCard": local.qryInvoices.hasCard is 1,
				"addPaymentEncString": local.addPaymentEncString,
				"DT_RowId": "invoiceRow_#local.qryInvoices.invoiceID#",
				"DT_RowData": {
					"invoiceNumber": local.qryInvoices.invoiceNumber
				}
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryInvoices.totalCount),
			"recordsFiltered": val(local.qryInvoices.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getTransactionListByMemberID" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));

			local.tran_fType = val(arguments.event.getValue('tran_fType',0));
			local.tran_fDateFrom = arguments.event.getValue('tran_fDateFrom','');
			local.tran_fDateTo = arguments.event.getValue('tran_fDateTo','');
			local.tran_fDetail = arguments.event.getTrimValue('tran_fDetail','');
			local.tran_fAmtFrom = rereplace(arguments.event.getValue('tran_fAmtFrom',''),"[^0-9.]","","ALL");
			local.tran_fAmtTo = rereplace(arguments.event.getValue('tran_fAmtTo',''),"[^0-9.]","","ALL");

			if (len(local.tran_fDateFrom)) local.tran_fDateFrom = DateFormat(local.tran_fDateFrom, "mm/dd/yyyy");
			if (len(local.tran_fDateTo)) local.tran_fDateTo = DateFormat(local.tran_fDateTo, "mm/dd/yyyy") & " 23:59:59.997";
			if (val(local.tran_fAmtTo) gt 0 and val(local.tran_fAmtFrom) gt val(local.tran_fAmtTo)) {
				local.tran_fAmtFrom = '';
				local.tran_fAmtTo = '';
			}
		</cfscript>

		<!--- get the SRID and permissions of TransactionsAdmin. --->
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin', siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"transactionDate #arguments.event.getValue('orderDir')#, transactionID #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<!--- get transactions --->
		<cfquery name="local.qryTransactions" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @totalCount int, @t_Sale int, @t_Alloc int, @t_VO int, @t_DIT int, @t_Install int, @t_Refund int, @t_Tax int, 
				@t_NSF int, @t_Payment int, @t_Adj int, @t_WO int, @tr_SalesTaxTrans int, @orgID int, @posStart int, @posStartAndCount int;
			set @t_Sale = dbo.fn_tr_getTypeID('Sale');
			set @t_Payment = dbo.fn_tr_getTypeID('Payment');
			set @t_Alloc = dbo.fn_tr_getTypeID('Allocation');
			set @t_VO = dbo.fn_tr_getTypeID('VoidOffset');
			set @t_DIT = dbo.fn_tr_getTypeID('Deferred Transfer');
			set @t_Install = dbo.fn_tr_getTypeID('Installment');
			set @t_Refund = dbo.fn_tr_getTypeID('Refund');
			set @t_Tax = dbo.fn_tr_getTypeID('Sales Tax');
			set @t_NSF = dbo.fn_tr_getTypeID('NSF');
			set @t_Adj = dbo.fn_tr_getTypeID('Adjustment');
			set @t_WO = dbo.fn_tr_getTypeID('Write Off');
			set @tr_SalesTaxTrans = dbo.fn_tr_getRelationshipTypeID('SalesTaxTrans');
			set @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;

			IF OBJECT_ID('tempdb..##innerTbl') IS NOT NULL 
				DROP TABLE ##innerTbl;
			IF OBJECT_ID('tempdb..##innerTbl2') IS NOT NULL 
				DROP TABLE ##innerTbl2;
			CREATE TABLE ##innerTbl (transactionID int PRIMARY KEY);
			CREATE TABLE ##innerTbl2 (transactionID int PRIMARY KEY, statusID int, transactionDate datetime, [type] varchar(20), 
				detail varchar(max), debit decimal(18,2), credit decimal(18,2), canApplyPayment tinyint, paymentDueAmount decimal(18,2), 
				canRefund tinyint, canEdit tinyint, canAdjust tinyint, row int);

			INSERT INTO ##innerTbl (transactionID)
			SELECT t.transactionID
			FROM dbo.tr_transactions AS t
			INNER JOIN dbo.ams_members as m on m.memberID = t.assignedToMemberID 
			WHERE t.ownedByOrgID = @orgID
			AND m.activeMemberID = <cfqueryparam value="#arguments.event.getValue('memberid')#" cfsqltype="CF_SQL_INTEGER">
			<cfif local.tran_fType gt 0>
				and t.typeID = <cfqueryparam value="#local.tran_fType#" cfsqltype="CF_SQL_INTEGER">
			<cfelse>
				AND t.typeID not in (@t_Alloc,@t_VO,@t_DIT,@t_Install)
			</cfif>
			AND t.statusID <> 4
			<cfif len(local.tran_fDateFrom)>
				and t.transactionDate >= <cfqueryparam value="#local.tran_fDateFrom#" cfsqltype="CF_SQL_DATE">
			</cfif>
			<cfif len(local.tran_fDateTo)>
				and t.transactionDate <= <cfqueryparam value="#local.tran_fDateTo#" cfsqltype="CF_SQL_TIMESTAMP">
			</cfif>				
			<cfif len(local.tran_fDetail)>
				and t.detail like <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="%#replace(local.tran_fDetail,'_','\_','ALL')#%"> ESCAPE('\')
			</cfif>
			<cfif len(local.tran_fAmtFrom)>
				and t.amount >= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.tran_fAmtFrom#">
			</cfif>
			<cfif len(local.tran_fAmtTo)>
				and t.amount <= <cfqueryparam cfsqltype="CF_SQL_DECIMAL" scale="2" value="#local.tran_fAmtTo#">
			</cfif>;

			INSERT INTO ##innerTbl2 (transactionID, statusID, transactionDate, [type], detail, debit, credit, canApplyPayment, paymentDueAmount, 
				canRefund, canEdit, canAdjust, row)
			select transactionID, statusID, transactionDate, [type], detail, debit, credit, canApplyPayment, paymentDueAmount, canRefund, canEdit, 
				canAdjust, ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row 
			from (
				SELECT tmp.transactionID, at.statusID, at.transactionDate, tt.type,
					case when at.statusID = 3 then '**PENDING** ' + isnull(at.detail,'') 
						 when at.statusID = 2 then '**VOID** ' + isnull(at.detail,'') 
						 else at.detail end as detail,
					DEBIT = case
						when at.statusID = 2 then null
						when at.typeID in (@t_Sale,@t_Refund,@t_Tax,@t_NSF) then null
						when at.typeID = @t_Payment then at.amount
						when at.typeID = @t_Adj and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then null
						when at.typeID = @t_Adj then at.amount
						when at.typeID = @t_WO and glDeb.GLCode = 'DEPOSITS' then null
						when at.typeID = @t_WO then at.amount
						else ''
						end,
					CREDIT = case
						when at.statusID = 2 then null
						when at.typeID in (@t_Sale,@t_Refund,@t_Tax,@t_NSF) then at.amount
						when at.typeID = @t_Payment then null
						when at.typeID = @t_Adj and glDeb.GLCode = 'ACCOUNTSRECEIVABLE' then at.amount
						when at.typeID = @t_Adj then null
						when at.typeID = @t_WO and glDeb.GLCode = 'DEPOSITS' then at.amount
						when at.typeID = @t_WO then null
						else ''
						end,
					canApplyPayment = case 
						when at.typeid = @t_Sale and at.statusID = 1 and (isnull(tax.cache_amountAfterAdjustment,0) + tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0) + tsFull.cache_activePaymentAllocatedAmount) > 0 then 1 
						else 0 
						end,
					paymentDueAmount = case
						when at.typeid = @t_Sale and at.statusID = 1 then (isnull(tax.cache_amountAfterAdjustment,0) + tsFull.cache_amountAfterAdjustment) - (isnull(tax.cache_activePaymentAllocatedAmount,0) + tsFull.cache_activePaymentAllocatedAmount) - (isnull(tax.cache_pendingPaymentAllocatedAmount,0) + tsFull.cache_pendingPaymentAllocatedAmount)
						else 0
						end, 
					canRefund = case
						when at.typeid = @t_Payment and at.statusid = 1 
							and (
								((tp.cache_refundableAmountOfPayment >= tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment > 0))
								OR 
								((tp.cache_refundableAmountOfPayment < tp.cache_refundableAmountOfPayment-tp.cache_allocatedAmountOfPayment) and (tp.cache_refundableAmountOfPayment > 0))
								) then 1
						else 0
						end,
					canEdit = case 
						when at.statusID = 3 and at.typeID = @t_Payment then 1
						else 0
						end,
					canAdjust = case 
						when at.typeID = @t_Sale and at.statusID = 1 and tspf.transactionID is null then 1
						else 0
						end
				FROM ##innerTbl as tmp
				INNER JOIN dbo.tr_transactions AS at on at.ownedByOrgID = @orgID and at.transactionID = tmp.transactionID
				INNER JOIN dbo.tr_types as tt on tt.typeid = at.typeid
				INNER JOIN dbo.tr_GLAccounts as glDeb on glDeb.orgID = @orgID and glDeb.GLAccountID = at.debitGLAccountID 
				INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = at.assignedToMemberID 
				LEFT OUTER JOIN dbo.tr_transactionPayments as tp on tp.orgID = @orgID and tp.transactionID = at.transactionid and at.typeID = @t_Payment and at.statusid = 1 
				LEFT OUTER JOIN dbo.tr_transactionSales AS tspf ON tspf.orgID = @orgID AND tspf.transactionID = at.transactionID AND tspf.paymentFeeTypeID IN (1,2)
				OUTER APPLY (
					select sum(tsTaxFull.cache_amountAfterAdjustment) as cache_amountAfterAdjustment, 
						   sum(tsTaxFull.cache_activePaymentAllocatedAmount) as cache_activePaymentAllocatedAmount, 
						   sum(tsTaxFull.cache_pendingPaymentAllocatedAmount) as cache_pendingPaymentAllocatedAmount
					from dbo.tr_relationships as tr 
					inner join dbo.tr_transactions as tTax on tTax.ownedByOrgID = @orgID and tTax.transactionID = tr.transactionID and tTax.statusID = 1
					cross apply dbo.fn_tr_transactionSalesWithDIT(@orgID,tTax.transactionID) as tsTaxFull
					where tr.orgID = @orgID
					and tr.typeID = @tr_SalesTaxTrans 
					and tr.appliedToTransactionID = at.transactionID
					) as tax
				outer apply dbo.fn_tr_transactionSalesWithDIT(@orgID,at.transactionID) as tsFull
			) as tmp
			<cfif arguments.event.getValue('tran_fAR',0)>
				where tmp.paymentDueAmount > 0
			<cfelseif arguments.event.getValue('tran_fCB',0)>
				where tmp.canRefund = 1
			</cfif>;

			select @totalCount = @@ROWCOUNT;

			DELETE FROM ##innerTbl2
			WHERE NOT (row > @posStart AND row <= @posStartAndCount);

			SELECT transactionID, statusID, transactionDate, [type], detail, debit, credit, canApplyPayment, paymentDueAmount, 
				canRefund, canEdit, canAdjust, row, @totalCount as totalCount, 
				<cfif local.myRightsTransactionsAdmin.transAllocatePayment is 1>
					(select max(canApplyPayment) from ##innerTbl2) as ANYcanApplyPayment,
				<cfelse>
					0 as ANYcanApplyPayment,
				</cfif>
				<cfif local.myRightsTransactionsAdmin.transRefundPayment is 1>
					(select max(canRefund) from ##innerTbl2) as ANYcanRefund, 
				<cfelse>
					0 as ANYcanRefund,
				</cfif>
				(select max(canEdit) from ##innerTbl2) as ANYcanEdit, 
				<cfif local.myRightsTransactionsAdmin.transAdjustSale is 1>
					(select max(canAdjust) from ##innerTbl2) as ANYcanAdjust
				<cfelse>
					0 as ANYcanAdjust
				</cfif>
			FROM ##innerTbl2
			ORDER BY row;

			IF OBJECT_ID('tempdb..##innerTbl') IS NOT NULL 
				DROP TABLE ##innerTbl;
			IF OBJECT_ID('tempdb..##innerTbl2') IS NOT NULL 
				DROP TABLE ##innerTbl2;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryTransactions">
			<cfif local.qryTransactions.canApplyPayment is 1>
				<cfset local.addPaymentEncString = local.objTransAdmin.generatePOForAddPayment(pmid=arguments.event.getValue('memberid',0), t=local.qryTransactions.detail, ta=local.qryTransactions.paymentDueAmount, tmid=arguments.event.getValue('memberid',0), ad="s|#local.qryTransactions.transactionid#")>
			<cfelse>
				<cfset local.addPaymentEncString = "">
			</cfif>
			<cfset local.debit = "">
			<cfif len(local.qryTransactions.debit)>
				<cfset local.debit = dollarFormat(local.qryTransactions.debit)>
			</cfif>

			<cfset local.credit = "">
			<cfif len(local.qryTransactions.credit)>
				<cfset local.credit = dollarFormat(local.qryTransactions.credit)>
			</cfif>

			<cfset local.tmpStr = {
				"transactionID": local.qryTransactions.transactionID,
				"statusID": local.qryTransactions.statusID,
				"transactionDate": dateformat(local.qryTransactions.transactionDate,"m/d/yyyy"),
				"transactionTime": timeformat(local.qryTransactions.transactionDate,"h:mm tt"),
				"type": local.qryTransactions.type,
				"detail": htmlEditFormat(local.qryTransactions.detail),
				"detailEnc": encodeForHTMLAttribute(local.qryTransactions.detail),
				"debit": local.debit,
				"credit": local.credit,
				"showPaymentAction": local.qryTransactions.ANYcanApplyPayment is 1,
				"canApplyPayment": local.qryTransactions.canApplyPayment is 1,
				"showRefundAction": local.qryTransactions.ANYcanRefund is 1,
				"canRefund": local.qryTransactions.canRefund is 1,
				"showEditAction": local.qryTransactions.ANYcanEdit is 1,
				"canEdit": local.qryTransactions.canEdit is 1,
				"showAdjustAction": local.qryTransactions.ANYcanAdjust is 1,
				"canAdjust": local.qryTransactions.canAdjust is 1,
				"addPaymentEncString": local.addPaymentEncString,
				"DT_RowId": "transactionRow_#local.qryTransactions.transactionID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryTransactions.totalCount),
			"recordsFiltered": val(local.qryTransactions.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getFailedPaymentListByMemberID" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objTransAdmin = CreateObject('component','model.admin.transactions.transactionAdmin')>

		<cfscript>
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',5))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
		</cfscript>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"ph.datePaid #arguments.event.getValue('orderDir')#, ph.historyID #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryFailures" datasource="#application.dsn.memberCentral.dsn#" result="local.qryFailuresResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @memberID int, @orgID int, @totalCount int, @posStart int, @posStartAndCount int, @searchValue varchar(300);
			SET	@memberID = <cfqueryparam value="#arguments.event.getValue('memberid',0)#" cfsqltype="CF_SQL_INTEGER">;
			SET	@orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpHistory') IS NOT NULL 
				DROP TABLE ##tmpHistory;
			IF OBJECT_ID('tempdb..##tmpFailed') IS NOT NULL 
				DROP TABLE ##tmpFailed;
			CREATE TABLE ##tmpMembers (memberID int PRIMARY KEY);
			CREATE TABLE ##tmpHistory (historyID int PRIMARY KEY, trType varchar(7));
			CREATE TABLE ##tmpFailed (historyID int PRIMARY KEY, datePaid datetime, trType varchar(7), profileName varchar(100), 
				cofDetail varchar(50), payDescription varchar(800), payAmount decimal(18,2), responseReasonText varchar(800), 
				row int INDEX IX_tmpFailed_row);

			insert into ##tmpMembers (memberID)
			select memberID 
			from dbo.ams_members
			where orgID = @orgID 
			and activeMemberID = @memberID;

			insert into ##tmpHistory (historyID, trType)
			select ph.historyID, 'Payment'
			from ##tmpMembers as tmp
			inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.payerMemberID = tmp.memberID
			where ph.isSuccess = 0
			and ph.paymentType = 'payment'
			order by ph.historyID desc;

			insert into ##tmpHistory (historyID, trType)
			select ph.historyID, 'Refund'
			from ##tmpMembers as tmp
			inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.payerMemberID = tmp.memberID
			where ph.isSuccess = 0
			and ph.paymentType = 'refund'
			order by ph.historyID desc;

			INSERT INTO ##tmpFailed (historyID, datePaid, trType, profileName, cofDetail, payDescription, payAmount, responseReasonText, row)
			select historyID, datePaid, trType, profileName, detail, payDescription, payAmount, responseReasonText, row
			from (
				select ph.historyID, ph.datePaid, tmp.trType, mp.profileName, mpp.detail, 
					ph.paymentInfo.value('(//args/x_description)[1]','varchar(800)') as payDescription,
					ph.paymentInfo.value('(//args/x_amount)[1]','decimal(18,2)') as payAmount,
					ph.gatewayResponse.value('(/response/responsereasontext)[1]','varchar(800)') as responseReasonText,
					ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
				from ##tmpHistory as tmp
				inner join dbo.tr_paymentHistory as ph on ph.orgID = @orgID and ph.historyID = tmp.historyID
				inner join dbo.mp_profiles as mp on mp.profileID = ph.profileID
				left outer join dbo.ams_memberPaymentProfiles as mpp on mpp.payProfileID = ph.memberPaymentProfileID
			) tmp
			<cfif len(local.searchValue)>
				where (
					profileName LIKE @searchValue 
					OR detail LIKE @searchValue 
					OR payDescription LIKE @searchValue 
					OR responseReasonText LIKE @searchValue
				)
			</cfif>;

			set @totalCount = @@ROWCOUNT;

			select historyID, datePaid, trType, profileName, cofDetail, payDescription, payAmount, responseReasonText, @totalCount as totalCount
			from ##tmpFailed
			where row > @posStart AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpHistory') IS NOT NULL 
				DROP TABLE ##tmpHistory;
			IF OBJECT_ID('tempdb..##tmpFailed') IS NOT NULL 
				DROP TABLE ##tmpFailed;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryFailures">
			<cfset local.tmpStr = {
				"historyID": local.qryFailures.historyID,
				"datePaid": dateTimeformat(local.qryFailures.datePaid,"m/d/yyyy h:nn tt"),
				"type": local.qryFailures.trType,
				"payDescription": local.qryFailures.payDescription,
				"profileName": local.qryFailures.profileName,
				"cofDetail": local.qryFailures.cofDetail,
				"responseReasonText": local.qryFailures.responseReasonText,
				"payAmount": dollarFormat(local.qryFailures.payAmount),
				"DT_RowId": "failedPaymentRow_#local.qryFailures.historyID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryFailures.totalCount),
			"recordsFiltered": val(local.qryFailures.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getLoginHistory" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('orderBy',int(val(form['order[0][column]'] ?: 0)));
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',50))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';
			local.gridMode = arguments.event.getValue('gridMode','memberLogins');
		</cfscript>

		<!--- handle ordering --->
		<cfset local.arrCols = arrayNew(1)>
		<cfset arrayAppend(local.arrCols,"ml.dateentered #arguments.event.getValue('orderDir')#")>
		<cfset local.orderby = local.arrcols[arguments.event.getValue('orderby')+1]>

		<cfquery name="local.qryloginHistory" datasource="#application.dsn.platformStatsMC.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @memberID int, @orgID int, @siteID int, @totalCount int, @posStart int, @posStartAndCount int, 
				@searchValue varchar(300), @activeMemberID int;
			SET @memberID = <cfqueryparam value="#arguments.event.getValue('mid',0)#" cfsqltype="CF_SQL_INTEGER">;
			SET @activeMemberID = memberCentral.dbo.fn_getActiveMemberID(@memberID);
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
			SET @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			<cfif len(local.searchValue)>
				SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
			</cfif>
			
			IF OBJECT_ID('tempdb..##tmpLoginHistory') IS NOT NULL
				DROP TABLE ##tmpLoginHistory;
			CREATE TABLE ##tmpLoginHistory (loginID int PRIMARY KEY, dateentered datetime, ipAddress varchar(20), userAgent varchar(500), orgID int, 
				requestMID int, firstname varchar(75), lastname varchar(75), memberNumber varchar(50), company varchar(200), city varchar(100),
				regionCode varchar(10), ISOCountryCode char(2), methodName varchar(50), loginMemberID int, loginMemberName varchar(200), loginMemberOrgID int, row int);

			INSERT INTO ##tmpLoginHistory (loginID, dateentered, ipAddress, userAgent, orgID, requestMID, firstname, lastname, memberNumber, company,
				city, regionCode, ISOCountryCode, methodName, loginMemberID, loginMemberName, loginMemberOrgID, row)
			select loginID, dateentered, ipaddress, useragent, orgID, memberID, firstname, lastname, memberNumber, company,
				city, regionCode, ISOCountryCode, methodName, loginMemberID, loginMemberName, loginMemberOrgID, row
			from (
				select ml.loginID, ml.dateentered, ss.ipaddress, sua.useragent, mReqActive.orgID, mReqActive.memberID, mReqActive.firstname, 
					mReqActive.lastname, mReqActive.memberNumber, m.company, ml.city, ml.regionCode,  ml.ISOCountryCode, vm.methodName,
					mActive.memberID as loginMemberID, mActive.firstName + ' ' + mActive.LastName as loginMemberName, mActive.orgID as loginMemberOrgID,
					ROW_NUMBER() OVER (ORDER BY #local.orderby#) as row
				from dbo.ams_memberLogins as ml
				inner join memberCentral.dbo.ams_members as m on m.memberID = ml.memberID
				inner join memberCentral.dbo.ams_members as mActive on mActive.memberID = m.activeMemberID
				inner join dbo.statsSessions as ss on ss.sessionid = ml.statsSessionID
				inner join dbo.statsUserAgents as sua on sua.useragentID = ss.userAgentID
				<cfif local.gridMode eq "memberLogins">
					<cfif arguments.event.getValue('fLinkedMemberID',0) gt 0>
						inner join memberCentral.dbo.ams_members as mLink on mLink.memberID = ml.memberID and mLink.orgID = @orgID
						inner join memberCentral.dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID
							and mLinkActive.memberID = <cfqueryparam value="#arguments.event.getValue('fLinkedMemberID')#" cfsqltype="CF_SQL_INTEGER">
							and mLinkActive.orgID = @orgID
					<cfelseif arguments.event.getValue('fLinkedGroupID',0) gt 0>
						inner join memberCentral.dbo.ams_members as mLink on mLink.memberID = ml.memberID and mLink.orgID = @orgID
						inner join memberCentral.dbo.ams_members as mLinkActive on mLinkActive.memberID = mLink.activeMemberID and mLinkActive.orgID = @orgID
						inner join memberCentral.dbo.cache_members_groups as mgLinked on mgLinked.memberID = mLinkActive.memberID 
							and mgLinked.groupid = <cfqueryparam value="#arguments.event.getValue('fLinkedGroupID')#" cfsqltype="CF_SQL_INTEGER">
					</cfif>
				</cfif>
				left outer join membercentral.dbo.platform_verificationMethods vm 
    				on vm.verificationMethodID = ml.verificationMethodID
				left outer join memberCentral.dbo.ams_members as mReq 
					inner join memberCentral.dbo.ams_members as mReqActive on mReqActive.memberID = mReq.activeMemberID
					on mReq.memberID = ml.requestByMemberID
				where ml.siteID = @siteID
				<cfif local.gridMode eq "memberRecord">
					and m.activeMemberID = @activeMemberID
				<cfelseif local.gridMode eq "memberLogins">
					<cfif len(arguments.event.getValue('fDateFrom',''))>
						AND ml.dateentered >= <cfqueryparam cfsqltype="cf_sql_date" value="#arguments.event.getValue('fDateFrom')#">
					</cfif>
					<cfif len(arguments.event.getValue('fDateTo',''))>
						AND ml.dateentered <= <cfqueryparam cfsqltype="cf_sql_timestamp" value="#arguments.event.getValue('fDateTo')# 23:59:59.997">
					</cfif>
				</cfif>
			) tmp
			<cfif len(local.searchValue)>
				where (ipAddress LIKE @searchValue 
						OR userAgent LIKE @searchValue 
						OR memberNumber LIKE @searchValue 
						OR firstname LIKE @searchValue
						OR lastname LIKE @searchValue)
			</cfif>;

			set @totalCount = @@ROWCOUNT;

			select loginID, dateentered, ipAddress, userAgent, orgID, requestMID, firstname, lastname, memberNumber, company, city, regionCode,
				ISOCountryCode, methodName, loginMemberID, loginMemberName, loginMemberOrgID, @totalCount as totalCount
			from ##tmpLoginHistory
			where row > @posStart AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpLoginHistory') IS NOT NULL 
			DROP TABLE ##tmpLoginHistory;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryloginHistory">
			<cfset local.tmpStr = {
				"loginid": local.qryloginHistory.loginID,
				"dateentered": dateTimeformat(local.qryloginHistory.dateentered,"m/d/yyyy h:nn tt"),
				"ipaddress": local.qryloginHistory.ipAddress,
				"useragent": local.qryloginHistory.userAgent,
				"orgid" : local.qryloginHistory.orgID,
				"requestmid": val(local.qryloginHistory.requestMID),
				"firstname": local.qryloginHistory.firstname,
				"lastname": local.qryloginHistory.lastname,
				"membernumber": local.qryloginHistory.memberNumber,
				"company": local.qryloginHistory.company,
				"city": local.qryloginHistory.city,
				"regioncode": local.qryloginHistory.regionCode,
				"isocountrycode": local.qryloginHistory.ISOCountryCode,
				"methodname": local.qryloginHistory.methodName,
				"loginmemberid": local.qryloginHistory.loginMemberID,
				"loginmembername": local.qryloginHistory.loginMemberName,
				"loginmemberorgid": local.qryloginHistory.loginMemberOrgID,
				"DT_RowId": "loginHistoryRow_#local.qryloginHistory.loginID#"
			}>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryloginHistory.totalCount),
			"recordsFiltered": val(local.qryloginHistory.totalCount),
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getRecordRelationships" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.searchValue = form['search[value]'] ?: '';

			local.memberID = arguments.event.getValue('memberID');
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRecordRelationships">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				IF OBJECT_ID('tempdb..##tmpRecordRelationships') IS NOT NULL
					DROP TABLE ##tmpRecordRelationships;
				CREATE TABLE ##tmpRecordRelationships (memberRole VARCHAR(10), relationshipID INT, masterMemberNumber VARCHAR(50), masterMemberID INT, 
					masterFirstName VARCHAR(75), masterLastName VARCHAR(75), masterCompany VARCHAR(200), relationshipTypeName VARCHAR(100), childMemberNumber VARCHAR(50), 
					childMemberID INT, childFirstname VARCHAR(75), childLastName VARCHAR(75), childCompany VARCHAR(200), isInvalid INT, row INT);

				DECLARE @orgID int, @memberID int, @totalCount INT, @posStart INT, @posStartPlusCount INT, @searchValue varchar(300);
				SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;
				SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
				SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
				<cfif len(local.searchValue)>
					SET @searchValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="%#local.searchValue#%">;
				</cfif>

				INSERT INTO ##tmpRecordRelationships (memberRole, relationshipID, masterMemberNumber, masterMemberID, masterFirstName, masterLastName, masterCompany, 
					relationshipTypeName, childMemberNumber, childMemberID, childFirstname, childLastName, childCompany, isInvalid, row)
				SELECT memberRole, relationshipID, masterMemberNumber, masterMemberID, masterFirstName, masterLastName, masterCompany, 
					relationshipTypeName, childMemberNumber, childMemberID, childFirstname, childLastName, childCompany, isInvalid, 
					ROW_NUMBER() OVER (ORDER BY  memberrole asc, childLastName asc, childFirstname asc, childCompany asc, masterLastname asc, masterFirstname asc, masterCompany asc, relationshipTypeName asc) AS row
				FROM (
					select
						'child' as memberRole,
						rr.relationshipID,
						masterMember.memberNumber as masterMemberNumber,
						masterMember.memberID as masterMemberID,
						masterMember.firstname as masterFirstName,
						masterMember.lastname as masterLastName,
						masterMember.company as masterCompany,
						rrt.relationshipTypeName,
						m.memberNumber as childMemberNumber,
						m.memberID as childMemberID,
						m.firstname as childFirstname,
						m.lastname as childLastName,
						m.company as childCompany,
						case when (rtrt.masterRecordTypeID <> isnull(masterMember.recordTypeID,0) OR rtrt.childRecordTypeID <> isnull(m.recordTypeID,0)) then 1 else 0 end as isInvalid
					from dbo.ams_members as m
					inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID
						and rr.childMemberID = m.memberID
						and m.memberID = @memberID
						and rr.isActive = 1
					inner join dbo.ams_members as masterMember on rr.masterMemberID = masterMember.memberID
						and masterMember.status <> 'D'
					left outer join dbo.ams_recordTypes as rtActualMaster on rtActualMaster.recordTypeID = masterMember.RecordTypeID
					left outer join dbo.ams_recordTypes as rtActualChild on rtActualChild.recordTypeID = m.RecordTypeID
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
						and rtrt.isActive = 1
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
					
					union
					
					select
						'parent' as memberRole,
						rr.relationshipID,
						m.memberNumber as masterMemberNumber,
						m.memberID as masterMemberID,
						m.firstname as masterFirstName,
						m.lastname as masterLastName,
						m.company as masterCompany,
						rrt.relationshipTypeName,
						childMember.memberNumber as childMemberNumber,
						childMember.memberID as childMemberID,
						childMember.firstname as childFirstname,
						childMember.lastname as childLastName,
						childMember.company as childCompany,
						case when (rtrt.masterRecordTypeID <> isnull(m.recordTypeID,0) OR rtrt.childRecordTypeID <> isnull(childMember.recordTypeID,0)) then 1 else 0 end as isInvalid
					from dbo.ams_members as m
					inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID
						and rr.masterMemberID = m.memberID
						and m.memberID = @memberID
						and rr.isActive = 1
					inner join dbo.ams_members as childMember on rr.childMemberID = childMember.memberID
						and childMember.status <> 'D'
					left outer join dbo.ams_recordTypes as rtActualMaster on rtActualMaster.recordTypeID = m.RecordTypeID
					left outer join dbo.ams_recordTypes as rtActualChild on rtActualChild.recordTypeID = childMember.RecordTypeID
					inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
						and rtrt.isActive = 1
					inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
				) AS tmp
				<cfif len(local.searchValue)>
					WHERE (masterCompany LIKE @searchValue
							OR relationshipTypeName LIKE @searchValue
							OR childFirstname LIKE @searchValue
							OR childLastName LIKE @searchValue)
				</cfif>;

				SET @totalCount = @@ROWCOUNT;

				SELECT memberRole, relationshipID, masterMemberNumber, masterMemberID, masterFirstName, masterLastName, masterCompany, 
					relationshipTypeName, childMemberNumber, childMemberID, childFirstname, childLastName, childCompany, isInvalid, @totalCount AS totalCount
				FROM ##tmpRecordRelationships
				WHERE row > @posStart
				AND row <= @posStartPlusCount
				ORDER BY row;

				IF OBJECT_ID('tempdb..##tmpRecordRelationships') IS NOT NULL
					DROP TABLE ##tmpRecordRelationships;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryRecordRelationships">
			<cfset local.arrData.append({
				"relationshipid": local.qryRecordRelationships.relationshipID,
				"relationshiptypename": local.qryRecordRelationships.relationshipTypeName,
				"mastermemberid": local.qryRecordRelationships.masterMemberID,
				"mastercompany": local.qryRecordRelationships.masterCompany,
				"childmemberid": local.qryRecordRelationships.childMemberID,
				"childfirstname": local.qryRecordRelationships.childFirstname,
				"childlastname": local.qryRecordRelationships.childLastname,
				"isinvalid": local.qryRecordRelationships.isInvalid
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal":  val(local.qryRecordRelationships.totalcount),
			"recordsFiltered":  val(local.qryRecordRelationships.totalcount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getPanels" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			local.referralsObj = CreateObject("component","model.admin.referrals.referrals");

			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.memberID = int(val(arguments.event.getValue('memberid',0)));
			local.isPanelGroupDepend = local.referralsObj.getReferralSettings(arguments.event.getValue('mc_siteInfo.siteID')).isPanelGroupDepend;
		</cfscript>

		<cfquery name="local.qryMemberPanels" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;

			select pm.panelMemberID, pm.panelID, p.name, pm.memberID, pms.statusName as memberStatusName, p.isActive, ps.statusName
			from dbo.ref_panelMembers as pm
			inner join ref_panels as p on p.panelID = pm.panelID
				and p.isActive = 1	
			inner join ref_panelStatus as ps on ps.panelStatusID = p.statusID	
			inner join ref_panelMemberStatus as pms on pms.panelMemberStatusID = pm.statusID
			inner join ams_members as m on m.orgID = @orgID and m.memberid = pm.memberID
			inner join ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
			where m2.memberID = @memberID
			and p.panelParentID is null
			order by p.name;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryMemberSubPanels" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">,
				@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;

			select pm.panelMemberID, pm.panelID, p.name, p.panelParentID, pm.memberID, pms.statusName as memberStatusName, 
				p.isActive, ps.statusName
			from dbo.ref_panelMembers as pm
			inner join ref_panels as p on p.panelID = pm.panelID
				and p.isActive = 1
				and p.panelParentID is not null
			inner join ref_panelStatus as ps on ps.panelStatusID = p.statusID	
			inner join ref_panelMemberStatus as pms on pms.panelMemberStatusID = pm.statusID
				and pms.isActive = 1
			inner join ams_members as m on m.orgID = @orgID and m.memberid = pm.memberID
			inner join ams_members as m2 on m2.orgID = @orgID and m2.memberid = m.activeMemberID
			where m2.memberID = @memberID
			order by p.name;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qryMemberPanels">
			<cfset var panelID = local.qryMemberPanels.panelID>
			<cfset local.panelRotationGroup = local.referralsObj.getPanelRotationGroup(orgID=arguments.event.getValue('mc_siteInfo.orgID'), panelID=panelID, subpanelid=0)>
			<cfset local.tmpStr = {
				"panelid": local.qryMemberPanels.panelID,
				"panelmemberid": local.qryMemberPanels.panelMemberID,
				"memberstatusname": local.qryMemberPanels.memberStatusName,
				"name": htmleditformat(local.qryMemberPanels.name),
				"memberid": local.qryMemberPanels.memberID,
				"statusname": local.qryMemberPanels.statusName,
				"success": local.panelRotationGroup.success,
				"members": local.panelRotationGroup.members,
				"inpanelrotationgrp": local.panelRotationGroup.success and listFindNoCase(local.panelRotationGroup.members,local.memberID),
				"addsubpanelrights": local.qryMemberPanels.statusName neq "Deleted" and not listFindNoCase("Removed,Inactive",local.qryMemberPanels.memberStatusName),
				"deletepanelrights": local.qryMemberPanels.statusName neq "Deleted" and local.qryMemberPanels.memberStatusName neq "Removed",
				"arrsubpanels": [],
				"DT_RowId": "panelRow_#local.qryMemberPanels.panelID#"
			}>
			
			<cfif local.qryMemberPanels.statusName neq "Deleted" and not listFindNoCase("Removed,Inactive",local.qryMemberPanels.memberStatusName)>
				<cfset local.qryThisMemberSubPanels = QueryFilter(local.qryMemberSubPanels,
														function(thisRow) {
															return arguments.thisRow.panelParentID EQ panelID;
														})>

				<cfloop query="local.qryThisMemberSubPanels">
					<cfset local.tmpStr.arrsubpanels.append({
							"panelid": local.qryThisMemberSubPanels.panelID,
							"panelmemberid": local.qryThisMemberSubPanels.panelMemberID,
							"memberstatusname": local.qryThisMemberSubPanels.memberStatusName,
							"name": htmleditformat(local.qryThisMemberSubPanels.name),
							"memberid": local.qryThisMemberSubPanels.memberID,
							"statusname": local.qryThisMemberSubPanels.statusName,
							"deletesubpanelrights": local.qryThisMemberSubPanels.statusName neq "Deleted" and local.qryThisMemberSubPanels.memberStatusName neq "Removed"
						})>
				</cfloop>
			</cfif>
			<cfset arrayAppend(local.arrData, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryMemberPanels.recordCount,
			"recordsFiltered": local.qryMemberPanels.recordCount,
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getLinkedRecords" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
			
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.orgID = arguments.event.getValue('mc_siteInfo.orgID')>
		<cfset local.orgDefaultSiteID = application.objOrgInfo.getOrgDefaultSiteID(orgID=local.orgID)>

		<cfset arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))))>
		<cfset arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))))>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfset local.qryRecordRelationships = CreateObject('component','model.admin.members.members').getRecordRelationshipsByFilters(
			orgID=local.orgID,
			siteID=local.orgDefaultSiteID,
			memberID=arguments.event.getValue('memberID',0),
			fLastName=arguments.event.getValue('fLastName',''),
			fRoleTypeIDList=arguments.event.getValue('fRoleTypeIDList',''),
			fRecordTypeIDList=arguments.event.getValue('fRecordTypeIDList',''),
			posStart=arguments.event.getValue('posStart'),
			count=arguments.event.getValue('count'),
			mode="grid"
		)>

		<cfif local.qryRecordRelationships.recordcount>
			<cfset local.xmlResultFields = local.qryRecordRelationships.mc_outputFieldsXML[1]>
			<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
			<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
			<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
			<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
			<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>

			<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'), includeTags=1)>
			<cfset local.strOrgAddressTypes = structNew()>
			<cfloop query="local.orgAddressTypes">
				<cfif local.orgAddressTypes.isTag is 1>
					<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
				<cfelse>
					<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
				</cfif>
			</cfloop>
			<cfset local.mc_combinedAddresses = structNew()>
			<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
			<cfloop array="#local.tmp#" index="local.thisField">
				<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
				<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
					<cfset local.strKey = "t#local.thisATID#">
				<cfelse>
					<cfset local.strKey = local.thisATID>
				</cfif>
				<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
					<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
				</cfif>
			</cfloop>

			<!--- remove fields from qryOutputFields that are handled manually --->
			<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
				SELECT *
				FROM [local].qryOutputFields
				WHERE fieldcodeSect NOT IN ('mc','m','ma','mat','mp','mpt')
			</cfquery>
		</cfif>

		<cfset local.arrData = []>
		<cfloop query="local.qryRecordRelationships">
			<cfset local.mc_combinedName = local.qryRecordRelationships['Extended MemberNumber'][local.qryRecordRelationships.currentrow]>

			<!--- combine address fields if there are any --->
			<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
			<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
				<cfsavecontent variable="local.thisATFull">
					<cfoutput>
					<cfif left(local.thisATID,1) eq "t">
						<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
						<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
					<cfelse>
						<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
						<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
					</cfif>

					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>#encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#<br/> </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>#encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#<br/> </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>#encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>#encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])# </cfif>
					<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
					<cfif arrayLen(local.tmp2) is 1 and len(local.qryRecordRelationships[local.tmp2[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>, #encodeForHTML(local.qryRecordRelationships[local.tmp2[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])# </cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])> #encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>#encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])# County<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
					<cfif arrayLen(local.tmp) is 1 and len(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])> #encodeForHTML(local.qryRecordRelationships[local.tmp[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#<br/></cfif>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
					<cfloop array="#local.tmp#" index="local.thisPT">
						<cfif len(local.qryRecordRelationships[local.thisPT.xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>
							<div>#local.thisPT.xmlAttributes.FieldLabel#: #encodeForHTML(local.qryRecordRelationships[local.thisPT.xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])#</div>
						</cfif>
					</cfloop>
					</cfoutput>
				</cfsavecontent>
				<cfset local.thisATfull = trim(replace(replace(rereplace(local.thisATFull,'[\r\n\t]','','ALL'),'  ',' ','ALL'),' ,',',','ALL'))>
				<cfif left(local.thisATfull,2) eq ", ">
					<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
				</cfif>
				<cfif len(local.thisATfull)>
					<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
				<cfelse>
					<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
				</cfif>
			</cfloop>

			<!--- get last login date if available --->
			<cfif arrayLen(local.LastLoginDateInFS) is 1>
				<cfif len(local.qryRecordRelationships[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>
					<cfset local.mc_dateLastLogin = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryRecordRelationships[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow],"m/d/yy")#'>
				<cfelse>
					<cfset local.mc_dateLastLogin = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
				</cfif>
			<cfelse>
				<cfset local.mc_dateLastLogin = "">
			</cfif>

			<!--- get recordtype if available --->
			<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryRecordRelationships[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>
				<cfset local.mc_recordType = local.qryRecordRelationships[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow]>
			<cfelse>
				<cfset local.mc_recordType = "">
			</cfif>
			
			<!--- get membertypeid if available --->
			<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryRecordRelationships[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>
				<cfset local.mc_memberType = local.qryRecordRelationships[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow]>
			<cfelse>
				<cfset local.mc_memberType = "">
			</cfif>	
			
			<!--- get status if available --->
			<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryRecordRelationships[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow])>
				<cfset local.mc_memberStatus = local.qryRecordRelationships[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryRecordRelationships.currentrow]>
			<cfelse>
				<cfset local.mc_memberStatus = "">
			</cfif>

			<cfset local.tmpStr = {
				"hasmemberphotothumb": local.qryRecordRelationships.hasMemberPhotoThumb is 1,
				"membernumber": local.qryRecordRelationships.MemberNumber,
				"memberid": local.qryRecordRelationships.memberID,
				"combinedname": local.mc_combinedName,
				"company": local.qryRecordRelationships.Company,
				"combinedaddresses": "",
				"recordfields": "",
				"relationshiplist": local.qryRecordRelationships.roleList,
				"relationshipidlist": local.qryRecordRelationships.relationshipIDList,
				"mc_recordtype": local.mc_recordType,
				"mc_membertype": local.mc_memberType,
				"isinactiveaccount": local.qryRecordRelationships.MCAccountStatus eq "I",
				"mc_memberstatus": local.mc_memberStatus,
				"relationshipclasses": "",
				"DT_RowId": "relRow_#local.qryRecordRelationships.memberID#"
			}>

			<cfif StructCount(local.thisMem_mc_combinedAddresses)>
				<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
					<cfset local.tmpStr.combinedaddresses &= "<div><b>#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#</b><br/>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>">
				</cfloop>
			</cfif>

			<cfsavecontent variable="local.tmpStr.recordfields">
				<cfloop query="local.qryOutputFieldsForLoop">
					<cfset local.currValue = local.qryRecordRelationships[local.qryOutputFieldsForLoop.fieldLabel][local.qryRecordRelationships.currentrow]>
					<cfif len(local.currValue)>
						<cfoutput>
						<div>
							#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
							<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
								#dollarFormat(local.currValue)#
							<cfelse>
								<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
									<cfcase value="DATE">
										#dateFormat(local.currValue,"m/d/yyyy")#
									</cfcase>
									<cfcase value="STRING,DECIMAL2,INTEGER">
										#local.currValue#
									</cfcase>
									<cfcase value="BIT">
										#YesNoFormat(local.currValue)#
									</cfcase>
								</cfswitch>
							</cfif>
						</div>
						</cfoutput>
					</cfif>
				</cfloop>				
			</cfsavecontent>			

			<cfif listLen(local.qryRecordRelationships.class)>
				<cfset local.listCount = 1>
				<cfset local.thisElementCount = 1>
				<cfset local.thisMemberClassList = class>

				<cfsavecontent variable="local.tmpStr.relationshipclasses">
					<cfoutput>
					<cfloop list="#local.qryRecordRelationships.class#" index="local.currentClass" delimiters="#chr(7)#">
						<cftry>
							<cfset local.thisName = listGetAt(local.currentClass,1,"|" )>
							<cfcatch type="any">
								<cfset local.thisName = "">
							</cfcatch>
						</cftry>
						<cftry>
							<cfset local.thisGroupName = listGetAt(local.currentClass,2,"|" )>
							<cfcatch type="any">
								<cfset local.thisGroupName = "">
							</cfcatch>
						</cftry>

						<cfif local.thisElementCount eq 1>
							<b style="font-size:95%;">#replace(local.thisName,'_',' ','ALL')#</b><br/>
						</cfif>
						- #local.thisGroupName#<br/>

						<cftry>
							<cfset local.nextName = listGetAt(listgetAt(local.thisMemberClassList,local.listCount+1,chr(7)),1,"|")>
							<cfcatch type="any">
								<cfset local.nextName = local.thisName>
							</cfcatch>
						</cftry>

						<cfif local.thisName neq local.nextName>
							<br/>
							<cfset local.thisElementCount = 1>
						<cfelse>
							<cfset local.thisElementCount++>
						</cfif>
						<cfset local.listCount++>
					</cfloop>
					</cfoutput>
				</cfsavecontent>
			</cfif>

			<cfset arrayAppend(local.arrData, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryRecordRelationships.mc_totalMatches),
			"recordsFiltered": val(local.qryRecordRelationships.mc_totalMatches),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>	
	</cffunction>

	<cffunction name="getLinkedRecordsWithSelectedData" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
			
		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.posStart = int(val(arguments.event.getValue('start',0)))>
		<cfset local.count = int(val(arguments.event.getValue('length',10)))>

		<cfset local.fMismatchOnly = arguments.event.getValue('fMismatchOnly',0)>
		<cfset local.fFieldType = arguments.event.getValue('fFieldType','')>
		<cfset local.formAttn = arguments.event.getValue('formAttn','')>
		<cfset local.formAddressID = arguments.event.getValue('formAddressID','')>
		<cfset local.formPhoneIDs = arguments.event.getValue('formPhoneIDs','')>
		<cfif find("|",arguments.event.getTrimValue('fFieldType',''))>
			<cfset local.fieldType = listFirst(arguments.event.getTrimValue('fFieldType',''),"|")>
			<cfset local.fieldTypeID = listLast(arguments.event.getTrimValue('fFieldType',''),"|")>
		<cfelse>
			<cfset local.fieldType = arguments.event.getTrimValue('fFieldType','')>
			<cfset local.fieldTypeID = 0>
		</cfif>
		<cfset local.memberID = arguments.event.getValue('memberID',0)>
		<cfset local.fLastName = arguments.event.getValue('fLastNameCopy','')>
		<cfset local.fRoleTypeIDList = arguments.event.getValue('fRoleTypeIDListCopy','')>
		<cfset local.fRecordTypeIDList = arguments.event.getValue('fRecordTypeIDListCopy','')>

		<cfif local.fMismatchOnly EQ 1 and listfindnocase("addressType,company,websiteType",local.fieldType)>
			<cfswitch expression="#local.fieldType#">
				<cfcase value="addressType">
					<!--- get address/phone data for master member id for this addressTypeID --->
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMasterAddress">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT ma.addressID, ma.address1, 
							case when mat.hasAddress2 = 1 then ma.address2 else '' end as address2,
							ma.city, ma.stateCode, ma.postalCode, ma.countryName as country
						FROM dbo.ams_memberAddresses AS ma
						INNER JOIN dbo.ams_memberAddressTypes AS mat ON mat.addressTypeID = ma.addressTypeID
								AND mat.orgID = @orgID
								AND mat.addressTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fieldTypeID#">
						WHERE ma.orgID = @orgID
						AND ma.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryMasterPhones">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT mp.phone
						FROM dbo.ams_memberPhones AS mp 
						INNER JOIN dbo.ams_memberPhoneTypes as mpt on mpt.orgID = @orgID
							AND mpt.phoneTypeID = mp.phoneTypeID
						WHERE mp.orgID = @orgID
						AND mp.memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">
						AND mp.addressID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryMasterAddress.addressID#"> 
						AND LEN(mp.phone) > 0;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
				</cfcase>
				<cfcase value="company">
					<!--- get companyname for master member --->
					<cfquery name="local.qryMasterCompany" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
						SELECT company
						FROM dbo.ams_members
						WHERE memberID = <cfqueryparam value="#local.memberID#" cfsqltype="CF_SQL_INTEGER">
						AND orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
				</cfcase>
				<cfcase value="websiteType">
					<!--- get website data for master member id for this websiteTypeID --->
					<cfquery name="local.qryMasterWebsites" datasource="#application.dsn.membercentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @orgID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SELECT mw.websiteTypeID, mw.websiteID, mw.website, mwt.websiteType, mwt.websiteTypeDesc
						FROM dbo.ams_memberWebsites as mw
						INNER JOIN dbo.ams_memberWebsiteTypes as mwt on mwt.orgID = @orgID
							AND mwt.websiteTypeID = mw.websiteTypeID
						WHERE mw.orgID = @orgID
						AND mw.memberID = <cfqueryparam value="#local.memberID#" cfsqltype="CF_SQL_INTEGER">
						AND	mw.websiteTypeID = <cfqueryparam value="#local.fieldTypeID#" cfsqltype="CF_SQL_INTEGER">
						ORDER BY mwt.websiteTypeOrder;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryWebsiteType">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
	
						SELECT mwt.websiteType
						FROM dbo.ams_memberWebsiteTypes mwt 
						where mwt.websiteTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.fieldTypeID)#">
						and mwt.orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
	
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
				</cfcase>
			</cfswitch>
		</cfif>
			
		<cfquery name="local.qryRecordRelationships" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @memberID int, @totalMatches int, @posStart int, @posStartAndCount int, @fLastName varchar(75);

			SET @orgID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.orgID')#" cfsqltype="CF_SQL_INTEGER">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.posStart#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.count#">;
			SET @memberID = <cfqueryparam value="#local.memberID#" cfsqltype="CF_SQL_INTEGER">;

			<cfif local.fLastName NEQ ''>
				SET @fLastName = replace(<cfqueryparam value="#local.fLastName#" cfsqltype="CF_SQL_VARCHAR">,'_','\_');
			</cfif>

			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpMembersResults') IS NOT NULL
				DROP TABLE ##tmpMembersResults;

			select 'child' as memberRole, STRING_AGG(rr.relationshipID,',') as relationshipIDList,
				masterMember.memberID as memberID
			into ##tmpMembers
			from dbo.ams_members as childMember
			inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID
				and rr.childMemberID = childMember.memberID
				and rr.isActive = 1
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			<cfif len(local.fRoleTypeIDList)>
				and rrt.relationshipTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.fRoleTypeIDList#">)
			</cfif>
			inner join dbo.ams_members as mm on rr.masterMemberID = mm.memberID
				and mm.status <> 'D'
			inner join dbo.ams_members as masterMember on mm.activeMemberID = masterMember.memberID
				and masterMember.status <> 'D'
			where childMember.memberID = @memberID
			group by masterMember.memberID
				union
			select 'parent' as memberRole, STRING_AGG(rr.relationshipID,',') as relationshipIDList,
				childMember.memberID as memberid
			from dbo.ams_members as masterMember
			inner join dbo.ams_recordRelationships as rr on rr.orgID = @orgID
				and rr.masterMemberID = masterMember.memberID
				and rr.isActive = 1
			inner join dbo.ams_recordTypesRelationshipTypes as rtrt on rtrt.recordTypeRelationshipTypeID = rr.recordTypeRelationshipTypeID
			inner join dbo.ams_recordRelationshipTypes as rrt on rrt.relationshipTypeID = rtrt.relationshipTypeID
			<cfif len(local.fRoleTypeIDList)>
				and rrt.relationshipTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.fRoleTypeIDList#">)
			</cfif>
			inner join dbo.ams_members as cm on rr.childMemberID = cm.memberID
				and cm.status <> 'D'
			inner join dbo.ams_members as childMember on cm.activeMemberID = childMember.memberID
				and childMember.status <> 'D'
			where masterMember.memberID = @memberID
			group by childMember.memberID;

			select m.memberID, tmp.relationshipIDList, m.MemberNumber, m.FirstName, m.LastName, m.company, 
				m.hasMemberPhotoThumb
			<cfif listfindnocase("addressType,company,websiteType",local.fieldType)>
				<cfswitch expression="#local.fieldType#">
					<cfcase value="addressType">
						, tmp1.addressID, tmp1.addressType, tmp1.attn,
						tmp1.address1, tmp1.address2, tmp1.address3,
						tmp1.city, tmp1.postalCode,
						tmp1.county, tmp1.country, tmp1.stateCode
						<cfif local.formPhoneIDs NEQ "">
							<cfloop list="#local.formPhoneIDs#" index="idx">
								, mp#idx#.phone as phone#idx#, mpt#idx#.phonetype as phonetype#idx#
							</cfloop>
						</cfif>
					</cfcase>
					<cfcase value="websiteType">
						, mw.website, mwt.websiteType
					</cfcase>
				</cfswitch>
			</cfif>
			, ROW_NUMBER() OVER (order by tmp.memberRole, m.LastName, m.FirstName, m.Company) as mc_row
			into ##tmpMembersResults
			from ##tmpMembers as tmp
			inner join dbo.ams_members as m on m.memberID = tmp.memberID
				<cfif local.fLastName NEQ ''>
					AND m.lastname LIKE '%'+@fLastName+'%' 
				</cfif>
			<cfif len(local.fRecordTypeIDList)>
				inner join dbo.ams_recordTypes rt on rt.recordTypeID = m.recordTypeID
					and m.recordTypeID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#local.fRecordTypeIDList#">)
			</cfif>
			<cfif listfindnocase("addressType,company,websiteType",local.fieldType)>
				<cfswitch expression="#local.fieldType#">
					<cfcase value="addressType">
						LEFT JOIN (
							SELECT ma.addressID, ma.memberID, mat.addressType,
								case when mat.hasAttn = 1 then ma.attn else '' end as attn,
								ma.address1,
								case when mat.hasAddress2 = 1 then ma.address2 else '' end as address2,
								case when mat.hasAddress3 = 1 then ma.address3 else '' end as address3,
								ma.city, ma.postalCode,
								case when mat.hasCounty = 1 then ma.county else '' end as county,
								ma.countryName as country,
								ma.stateCode
							FROM dbo.ams_memberAddresses AS ma
							INNER JOIN dbo.ams_memberAddressTypes AS mat ON mat.addressTypeID = ma.addressTypeID
								AND mat.orgID = @orgID
								AND ma.orgID= @orgid
								AND mat.addressTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.fieldTypeID#">
						) tmp1 ON tmp1.memberID = tmp.memberID
						<cfif local.formPhoneIDs NEQ "">
							<cfloop list="#local.formPhoneIDs#" index="idx">
								LEFT OUTER JOIN dbo.ams_memberPhones AS mp#idx#
									INNER JOIN dbo.ams_memberPhoneTypes as mpt#idx# ON mpt#idx#.orgID = @orgID
										AND mpt#idx#.phoneTypeID = mp#idx#.phoneTypeID
									ON mp#idx#.orgID = @orgID
									AND mp#idx#.memberID = tmp1.memberID
									AND mp#idx#.phoneTypeID = #idx# 
									AND mp#idx#.addressID = tmp1.addressID
									AND mpt#idx#.phoneTypeID = #idx#
									<cfif local.fMismatchOnly EQ 1>
										AND mp#idx#.phone NOT IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#valuelist(local.qryMasterPhones.phone)#">)
									</cfif>
							</cfloop>
						</cfif>
						WHERE 1=1
						<cfif local.formAddressID NEQ "" AND local.fMismatchOnly EQ 1>
							AND (
								tmp1.address1 NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.address1#">
								OR tmp1.address2 NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.address2#">
								OR tmp1.city NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.city#">
								OR tmp1.statecode NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.stateCode#">
								OR tmp1.postalCode NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.postalcode#">
								OR tmp1.country NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterAddress.country#">
							)
						</cfif>
						<cfif local.formPhoneIDs NEQ "" AND local.fMismatchOnly EQ 1>
							<cfloop list="#local.formPhoneIDs#" index="idx">
								AND mp#idx#.phone NOT IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#valuelist(local.qryMasterPhones.phone)#">)		
							</cfloop>
						</cfif>
					</cfcase>
					<cfcase value="company">
						WHERE 1=1 
						<cfif local.fMismatchOnly EQ 1>
							AND m.company NOT LIKE  <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterCompany.company#">
						</cfif>
					</cfcase>
					<cfcase value="websiteType">
						INNER JOIN dbo.ams_memberWebsites as mw ON mw.memberID = m.memberID
							AND mw.orgID = @orgID
							AND	mw.websiteTypeID = <cfqueryparam value="#local.fieldTypeID#" cfsqltype="CF_SQL_INTEGER">
						INNER JOIN dbo.ams_memberWebsiteTypes as mwt on mwt.orgID = @orgID
							AND mwt.websiteTypeID = mw.websiteTypeID
						WHERE 1=1
						<cfif local.fMismatchOnly EQ 1>
							AND mw.website NOT like <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryMasterWebsites.website#">
						</cfif>
					</cfcase>
				</cfswitch>
			</cfif>
			
			select @totalMatches = count(*) from ##tmpMembersResults;

			select *, @totalMatches as mc_totalMatches
			from ##tmpMembersResults
			where mc_row > @posStart
			and mc_row <= @posStartAndCount
			order by mc_row;
			
			IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL
				DROP TABLE ##tmpMembers;
			IF OBJECT_ID('tempdb..##tmpMembersResults') IS NOT NULL
				DROP TABLE ##tmpMembersResults;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfset local.arrData = []>
		<cfloop query="local.qryRecordRelationships">
			<cfsavecontent variable="local.dataToBeDisplayed">
				<cfoutput>
				<cfif listfindnocase("addressType,company,websiteType",local.fieldType)>
					<cfswitch expression="#local.fieldType#">
						<cfcase value="addressType">
							<cfif  local.formAttn NEQ "">
								Attention: <cfif len(local.qryRecordRelationships.attn)>#local.qryRecordRelationships.attn#<cfelse><span class="text-muted"><i>not specified</i></span></cfif>
								<br/>
							</cfif>
							<cfif local.formAddressID NEQ "">
								<cfif len(local.qryRecordRelationships.addressType)>#local.qryRecordRelationships.addressType#:<br/></cfif>
								<cfif len(local.qryRecordRelationships.address1)>#local.qryRecordRelationships.address1#<br/></cfif>
								<cfif len(local.qryRecordRelationships.address2)>#local.qryRecordRelationships.address2#<br/></cfif>
								<cfif len(local.qryRecordRelationships.address3)>#local.qryRecordRelationships.address3#<br/></cfif>
								<cfif len(local.qryRecordRelationships.city)>#local.qryRecordRelationships.city#</cfif> 
								<cfif len(local.qryRecordRelationships.stateCode)>#local.qryRecordRelationships.stateCode#</cfif>
								<cfif len(local.qryRecordRelationships.postalcode)>#local.qryRecordRelationships.postalcode#</cfif>
								<cfif len(local.qryRecordRelationships.county)>
									<br/>#local.qryRecordRelationships.county#
								</cfif>
								<cfif len(local.qryRecordRelationships.country)>
									<br/>#local.qryRecordRelationships.country#
								</cfif>
								<br/>
							</cfif>
							<cfif local.formPhoneIDs NEQ "">
								<cfloop list="#local.formPhoneIDs#" index="idx">
									<cfif len(evaluate("local.qryRecordRelationships.phonetype#idx#"))>
										#evaluate("local.qryRecordRelationships.phonetype#idx#")#: #evaluate("local.qryRecordRelationships.phone#idx#")# <br/>
									</cfif>
								</cfloop>
							</cfif>	
						</cfcase>
						<cfcase value="company">
							Company: <cfif len(local.qryRecordRelationships.company)>#local.qryRecordRelationships.company#<cfelse><span class="text-muted"><i>not specified</i></span></cfif>
						</cfcase>
						<cfcase value="websiteType">
							#local.qryRecordRelationships.websiteType#: <cfif len(local.qryRecordRelationships.website)>#local.qryRecordRelationships.website#<cfelse><span class="text-muted"><i>not specified</i></span></cfif>
						</cfcase>
					</cfswitch>
				</cfif>
				</cfoutput>
			</cfsavecontent>
			
			<cfset local.arrData.append({
				"memberid": local.qryRecordRelationships.memberID,
				"relationshipidlist": local.qryRecordRelationships.relationshipIDList,
				"firstname": local.qryRecordRelationships.FirstName,
				"lastname": local.qryRecordRelationships.LastName,
				"membernumber": local.qryRecordRelationships.MemberNumber,
				"hasmemberphotothumb": local.qryRecordRelationships.hasMemberPhotoThumb is 1,
				"datatobedisplayed": local.dataToBeDisplayed,
				"DT_RowId": "relCopyDataRow_#local.qryRecordRelationships.memberID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal": val(local.qryRecordRelationships.mc_totalMatches),
			"recordsFiltered": val(local.qryRecordRelationships.mc_totalMatches),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>	
	</cffunction>

	<cffunction name="getMFAMethods" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
			local.memberID = arguments.event.getValue('memberID');
			local.objLogin = CreateObject("component","model.login.login");
			local.qryLoginPolicyMethod = local.objLogin.getQualifiedLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.memberID);
			local.arrConfiguredMethods = local.objLogin.getConfiguredLoginPolicyMethod(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.memberID);
			var arrConfiguredMethodsWithOutEmail = local.arrConfiguredMethods.filter((MFAMethod) => arguments.MFAMethod NEQ 'Email');
			local.qryMemberLoginPolicyMethods = local.qryLoginPolicyMethod.filter((row) => arrConfiguredMethodsWithOutEmail.contains(row.methodCode));
			
			local.MFAPhoneNumber = "";
			if (local.arrConfiguredMethods.containsNoCase('MFASMS'))
				local.MFAPhoneNumber = local.objLogin.getMFAPhoneNumber(siteID=arguments.event.getValue('mc_siteinfo.siteID'), memberID=local.memberID);
		</cfscript>

		<cfset local.data = []>
		<cfloop query="local.qryMemberLoginPolicyMethods">
			<cfset local.tmpStr = {
				"policyMethodID": local.qryMemberLoginPolicyMethods.policyMethodID,
				"methodName": "",
				"MFAPhoneNumberMasked": "",
				"memberID": local.memberID,			
				"DT_RowId": "mfs_field_#local.qryMemberLoginPolicyMethods.policyMethodID#"
			}>
			<cfswitch expression="#local.qryMemberLoginPolicyMethods.methodCode#">
				<cfcase value="MFATOTP">
					<cfset local.tmpStr.methodName = "Authenticator">
				</cfcase>
				<cfcase value="MFASMS">
					<cfset local.tmpStr.methodName = "PHONE">
					<cfset local.tmpStr.MFAPhoneNumberMasked = '#left(local.MFAPhoneNumber,5)##repeatString("*",len(local.MFAPhoneNumber) - 7)##right(local.MFAPhoneNumber,2)#'>
				</cfcase>
				<cfcase value="Email">
					<cfset local.tmpStr.methodName = "Email">
				</cfcase>
			</cfswitch>
			<cfset arrayAppend(local.data, local.tmpStr)>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryMemberLoginPolicyMethods.recordCount,
			"recordsFiltered": local.qryMemberLoginPolicyMethods.recordCount,
			"data": local.data
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

</cfcomponent>