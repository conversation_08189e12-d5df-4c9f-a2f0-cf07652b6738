<cfcomponent output="no">
	<cffunction name="iframe" access="public" returntype="struct" hint="Displays a page in an iFrame.">
		<cfargument name="url" type="string" required="true" default="">
		<cfargument name="width" type="string" required="false" default="100%">
		<cfargument name="height" type="numeric" required="false" default="300">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">		

		<cfif isNumeric(arguments.width)>
			<cfset local.width = arguments.width & "px">
		<cfelse>
			<cfset local.width = arguments.width/>
		</cfif>

		<cfif len(arguments.url)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
					<iframe id="mc-iframe" src="#arguments.url#" height="#arguments.height#px" width="#local.width#"></iframe>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="upcomingEvents" access="public" returntype="struct" hint="Displays upcoming events.">
		<cfargument name="pageName" type="string" required="false" default="" hint="leave blank for all calendars">
		<cfargument name="includeRegistered" type="boolean" required="false" default="1">
		<cfargument name="includeNotRegistered" type="boolean" required="false" default="1">
		<cfargument name="includeSemWeb" type="boolean" required="false" default="0" hint="0 or 1. Only applies when including a pageName.">
		<cfargument name="includeHiddenEvents" type="boolean" required="false" default="0" hint="0 or 1">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="category" type="string" required="false" default="" hint="Pipe-delimited list of category names. Leave blank for all categories.">
		<cfargument name="noresultstext" type="string" required="false" default="There are no events.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["events"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.objEventReg = CreateObject("component","model.events.eventRegV2");
		local.tblName = "####tmpSWCF#replace(createUUID(),'-','','ALL')#";
		if (arguments.includeSemWeb)
			local.objSWCredits = CreateObject("component","model.seminarweb.SWCredits");
		arguments.maxRows = max(arguments.maxRows,1);

		if (len(arguments.pageName))
			local.qryAppInstance = application.objSiteResource.getApplicationInstanceFromPageName(pageName=arguments.pageName, siteID=local.commonTagVars.siteID);

		if (NOT isDefined("local.qryAppInstance") OR val(local.qryAppInstance.applicationInstanceID) is 0)
			local.applicationInstanceID = 0;
		else
			local.applicationInstanceID = local.qryAppInstance.applicationInstanceID;

		local.orgSiteFolder = lCase("#local.commonTagVars.orgcode#/#local.commonTagVars.sitecode#");
		</cfscript>

		<cfstoredproc procedure="cms_processMergeCode_UpcomingEvents" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.groupPrintID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.category#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.applicationInstanceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeRegistered#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeNotRegistered#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.tblName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeSemWeb#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeHiddenEvents#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>

		<cfset local.qryCalendarEventsPossibleCredits = QueryNew('')>
		<cfset local.qryGetCalendarEvents = local.qryData.filter(function(row) { return arguments.row.isSWL is 0; })>
		<cfif local.qryGetCalendarEvents.recordCount>
			<cfset local.eventIDList = valueList(local.qryGetCalendarEvents.eventID)>
			<cfquery name="local.qryCalendarEventsPossibleCredits" datasource="#application.dsn.membercentral.dsn#">
				select co.eventID, ca.authorityID, ca.authorityName, ca.authoritycode, co.ApprovalNum, cs.status,
					co.offeredStartDate, co.offeredEndDate, co.completeByDate, cot.creditValue, 
					isnull(ecast.ovTypeName,cat.typeName) as creditType
				from dbo.crd_offerings as co
				inner join dbo.crd_statuses as cs on cs.statusID = co.statusID
				inner join dbo.crd_authoritySponsors as cas on cas.ASID = co.ASID
				inner join dbo.crd_authorities as ca on ca.authorityID = cas.authorityID
				inner join dbo.crd_offeringTypes as cot on cot.offeringID = co.offeringID
				inner join dbo.crd_authoritySponsorTypes as ecast on ecast.ASTID = cot.ASTID
				inner join dbo.crd_authorityTypes as cat on cat.typeID = ecast.typeID
				where co.eventID in (<cfqueryparam value="#local.eventIDList#" cfsqltype="CF_SQL_INTEGER" list="true">)
				order by co.eventID, ca.authorityName, ca.authorityID, creditType
			</cfquery>
		</cfif>

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["id"] = local.qryData.eventID>
			<cfset local.thisrow["title"] = encodeForHTML(local.qryData.eventTitle)>
			<cfset local.thisrow["subtitle"] = encodeForHTML(local.qryData.eventSubTitle)>
			<cfset local.thisrow["allDayEvent"] = local.qryData.isAllDayEvent>
			<cfset local.thisrow["categories"] = arrayNew(1)>
			<cfset local.categoryNameArray = listToArray(local.qryData.categoryNameList,"|")/>
			<cfset local.categoryColorArray = listToArray(local.qryData.categoryColorList,"|")/>
			<cfif ArrayLen(local.categoryNameArray)>
				<cfloop array="#local.categoryNameArray#" item="local.category" index="local.i">
					<cfset local.tempStruct = structNew()>
					<cfset local.tempStruct["name"] = local.category>
					<cfset local.tempStruct["color"] = local.categoryColorArray[local.i]>
					<cfset arrayAppend(local.thisrow["categories"], local.tempStruct)>
				</cfloop>
			</cfif>
			<cfset local.thisrow["startDate"] = dateformat(local.qryData.displayStartTime,"m/d/yyyy") & " " & timeformat(local.qryData.displayStartTime, "h:mm tt")>
			<cfset local.thisrow["endDate"] = dateformat(local.qryData.displayEndTime,"m/d/yyyy") & " " & timeformat(local.qryData.displayEndTime, "h:mm tt")>
			<cfset local.thisrow["tzID"] = local.qryData.displayTimeZoneID>
			<cfset local.thisrow["tzCode"] = local.qryData.displayTimeZoneCode>
			<cfset local.thisrow["tzAbbr"] = local.qryData.displayTimeZoneAbbr>
			<cfset local.thisrow["startDateISO"] = local.objTZ.convertTimeZone(dateToConvert=local.qryData.displayStartTime, fromTimeZone=local.qryData.displayTimeZoneCode, toTimeZone="UTC")>
			<cfset local.thisrow["endDateISO"] = local.objTZ.convertTimeZone(dateToConvert=local.qryData.displayEndTime, fromTimeZone=local.qryData.displayTimeZoneCode, toTimeZone="UTC")>
			<cfif not val(local.qryData.isSWL)>
				<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?#application.objApplications.getAppBaseLink(applicationInstanceID=local.qryData.applicationInstanceID, siteID=local.commonTagVars.siteID)#&evAction=showDetail&eid=#local.qryData.eventID#">
			<cfelse>
				<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=semwebCatalog&panel=showLive&seminarid=#local.qryData.eventID#">
			</cfif>

			<cfset local.thisrow["evDesc"] = local.qryData.eventContent>
			<cfset local.thisrow["locTitle"] = HTMLEditFormat(local.qryData.locationContentTitle)>
			<cfset local.thisrow["locContent"] = local.qryData.locationContent>
			<cfset local.thisrow["oneDayEvent"] = 0>
			<cfif DateCompare(local.qryData.displayEndTime,local.qryData.displayStartTime,"d") eq 0>
				<cfset local.thisrow["oneDayEvent"] = 1>
			</cfif>

			<cfset local.thisrow["rates"] = arrayNew(1)>
			<cfif local.qryData.registrationID gt 0>
				<cfset local.qryRates = local.objEventReg.getRegRates(regid=local.qryData.registrationID,mid=local.commonTagVars.memberid,showall=true)>
				<cfloop query="local.qryRates">
					<cfset local.tempStruct = structNew()>
					<cfset local.tempStruct["name"] = local.qryRates.rateName>
					<cfset local.tempStruct["amt"] = local.qryRates.rate>
					<cfset arrayAppend(local.thisrow["rates"], local.tempStruct)>
				</cfloop>
			</cfif>
			
			<cfloop list="#local.qryData.swcfList#" index="local.swcfItem">
				<cfif structKeyExists(local.qryData,local.swcfItem)>
					<cfset local.thisrow[local.swcfItem] = local.qryData[local.swcfItem][currentRow]>
				</cfif>
			</cfloop>

			<cfset local.thisrow["featuredimages"] = structNew()>
			<cfset local.eventFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#'>
			<cfif val(local.qryData.calendarFeatureImageConfigID) and len(local.qryData.featureImageFileExt) and fileExists(local.eventFeaturedImageOriginalPath)>
				<cfset local.eventFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
				<cfset local.eventFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
				
				<cfset local.thisrow["featuredimages"]['fullsize'] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize']['url'] = "#local.eventFeaturedImageURL##local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				<cfset local.thisrow["featuredimages"]['fullsize']['width'] = local.qryData.featureImageOriginalWidth>
				<cfset local.thisrow["featuredimages"]['fullsize']['height'] = local.qryData.featureImageOriginalHeight>						
				<cfset local.thisrow["featuredimages"]['fullsize']['filename'] = "#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				
				<cfloop list="#local.qryData.imageConfigs#" index="local.thisImageConfig" delimiters="^~~~^">
					<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
					<cfset local.currentSizeID = local.imageConfigDataArray[1]>
					<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
					<cfset local.currentWidth = local.imageConfigDataArray[3]>
					<cfset local.currentHeight = local.imageConfigDataArray[4]>
					<cfset local.currentExt = local.imageConfigDataArray[5]>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#'] = structNew()>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['url'] = "#local.eventFeaturedImageThumbnailURL##local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['width'] = local.currentWidth>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['height'] = local.currentHeight>							
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['filename'] = "#local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">					
				</cfloop>
			</cfif>

			<cfset local.thisRow["credits"] = []>
			<cfif local.qryData.isSWL>
				<cfset local.qryAuthorities = local.objSWCredits.getCreditsGridBySeminar(seminarID=local.thisrow.id, siteCode=local.commonTagVars.sitecode).filter(function(row) { return arguments.row.wddxCreditsAvailable <> ''; }, true)>
				<cfloop query="local.qryAuthorities">
					<cfset local.qryCredits = local.objSWCredits.getCreditsFromWDDX(local.qryAuthorities.wddxCreditTypes,local.qryAuthorities.wddxCreditsAvailable,true)>
					<cfif local.qryCredits.recordcount>
						<cfset local.authorityStruct = { 
							"authorityname":local.qryAuthorities.authorityName, 
							"authoritycode":local.qryAuthorities.authorityCode, 
							"approvalnum":local.qryAuthorities.courseApproval, 
							"credittypes":[],
							"creditstatus":local.qryAuthorities.status, 
							"sponsorname":local.qryAuthorities.sponsorName,
							"datecreditstart":dateformat(local.qryAuthorities.creditOfferedStartDate,"m/d/yyyy"),
							"datecreditend":dateformat(local.qryAuthorities.creditOfferedEndDate,"m/d/yyyy"),
							"datecompleteby":dateformat(local.qryAuthorities.creditCompleteByDate,"m/d/yyyy")
						}>
						<cfloop query="local.qryCredits">
							<cfset local.authorityStruct.credittypes.append({ "type":local.qryCredits.displayName, "amount":local.qryCredits.numCredits })>
						</cfloop>
						<cfset arrayAppend(local.thisRow["credits"], local.authorityStruct)>
					</cfif>
				</cfloop>
			<cfelse>
				<cfset var eventIDForCreditLookup = local.thisrow["id"]>
				<cfset local.qryGetEventCredits = local.qryCalendarEventsPossibleCredits.filter(function(row) { return arguments.row.eventID is eventIDForCreditLookup }, true)>
				<cfoutput query="local.qryGetEventCredits" group="authorityID">
					<cfset local.authorityStruct = { 
						"authorityname":local.qryGetEventCredits.authorityName, 
						"authoritycode":local.qryGetEventCredits.authorityCode, 
						"approvalnum":local.qryGetEventCredits.approvalNum, 
						"credittypes":[],
						"creditstatus":local.qryGetEventCredits.status, 
						"datecreditstart":dateformat(local.qryGetEventCredits.offeredStartDate,"m/d/yyyy"),
						"datecreditend":dateformat(local.qryGetEventCredits.offeredEndDate,"m/d/yyyy"),
						"datecompleteby":dateformat(local.qryGetEventCredits.completeByDate,"m/d/yyyy")
					}>
					<cfoutput>
						<cfset local.authorityStruct.credittypes.append({ "type":local.qryGetEventCredits.creditType, "amount":local.qryGetEventCredits.creditValue })>
					</cfoutput>
					<cfset arrayAppend(local.thisRow["credits"], local.authorityStruct)>
				</cfoutput>
			</cfif>

			<cfset arrayAppend(local.dataStruct["events"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-eventList">
					<cfloop array="#local.dataStruct.events#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.title#</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-eventList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="myPastEvents" access="public" returntype="struct" hint="Displays upcoming events.">
		<cfargument name="pageName" type="string" required="false" default="" hint="leave blank for all calendars">
		<cfargument name="includeAttended" type="boolean" required="false" default="1">
		<cfargument name="includeNotAttended" type="boolean" required="false" default="1">
		<cfargument name="includeHiddenEvents" type="boolean" required="false" default="0" hint="0 or 1">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="category" type="string" required="false" default="" hint="Pipe-delimited list of category names. Leave blank for all categories.">
		<cfargument name="noresultstext" type="string" required="false" default="There were no past registrations.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["events"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.objEventReg = CreateObject("component","model.events.eventRegV2");
		local.tblName = "####tmpSWCF#replace(createUUID(),'-','','ALL')#";

		arguments.maxRows = max(arguments.maxRows,1);

		if (len(arguments.pageName))
			local.qryAppInstance = application.objSiteResource.getApplicationInstanceFromPageName(pageName=arguments.pageName, siteID=local.commonTagVars.siteID);

		if (NOT isDefined("local.qryAppInstance") OR val(local.qryAppInstance.applicationInstanceID) is 0)
			local.applicationInstanceID = 0;
		else
			local.applicationInstanceID = local.qryAppInstance.applicationInstanceID;

		local.orgSiteFolder = lCase("#local.commonTagVars.orgcode#/#local.commonTagVars.sitecode#");
		</cfscript>

		<cfstoredproc procedure="cms_processMergeCode_myPastEvents" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.groupPrintID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.category#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.applicationInstanceID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeAttended#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeNotAttended#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.tblName#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.includeHiddenEvents#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>	

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["id"] = local.qryData.eventID>
			<cfset local.thisrow["title"] = encodeForHTML(local.qryData.eventTitle)>
			<cfset local.thisrow["subtitle"] = encodeForHTML(local.qryData.eventSubTitle)>
			<cfset local.thisrow["allDayEvent"] = local.qryData.isAllDayEvent>
			<cfset local.thisrow["categories"] = arrayNew(1)>
			<cfset local.categoryNameArray = listToArray(local.qryData.categoryNameList,"|")/>
			<cfset local.categoryColorArray = listToArray(local.qryData.categoryColorList,"|")/>
			<cfif ArrayLen(local.categoryNameArray)>
				<cfloop array="#local.categoryNameArray#" item="local.category" index="local.i">
					<cfset local.tempStruct = structNew()>
					<cfset local.tempStruct["name"] = local.category>
					<cfset local.tempStruct["color"] = local.categoryColorArray[local.i]>
					<cfset arrayAppend(local.thisrow["categories"], local.tempStruct)>
				</cfloop>
			</cfif>
			<cfset local.thisrow["startDate"] = dateformat(local.qryData.displayStartTime,"m/d/yyyy") & " " & timeformat(local.qryData.displayStartTime, "h:mm tt")>
			<cfset local.thisrow["endDate"] = dateformat(local.qryData.displayEndTime,"m/d/yyyy") & " " & timeformat(local.qryData.displayEndTime, "h:mm tt")>
			<cfset local.thisrow["tzID"] = local.qryData.displayTimeZoneID>
			<cfset local.thisrow["tzCode"] = local.qryData.displayTimeZoneCode>
			<cfset local.thisrow["tzAbbr"] = local.qryData.displayTimeZoneAbbr>
			<cfset local.thisrow["startDateISO"] = local.objTZ.convertTimeZone(dateToConvert=local.qryData.displayStartTime, fromTimeZone=local.qryData.displayTimeZoneCode, toTimeZone="UTC")>
			<cfset local.thisrow["endDateISO"] = local.objTZ.convertTimeZone(dateToConvert=local.qryData.displayEndTime, fromTimeZone=local.qryData.displayTimeZoneCode, toTimeZone="UTC")>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?#application.objApplications.getAppBaseLink(applicationInstanceID=local.qryData.applicationInstanceID, siteID=local.commonTagVars.siteID)#&evAction=showDetail&eid=#local.qryData.eventID#">

			<cfset local.thisrow["evDesc"] = local.qryData.eventContent>
			<cfset local.thisrow["locTitle"] = HTMLEditFormat(local.qryData.locationContentTitle)>
			<cfset local.thisrow["locContent"] = local.qryData.locationContent>
			<cfset local.thisrow["oneDayEvent"] = 0>
			<cfif DateCompare(local.qryData.displayEndTime,local.qryData.displayStartTime,"d") eq 0>
				<cfset local.thisrow["oneDayEvent"] = 1>
			</cfif>

			<cfset local.thisrow["rates"] = arrayNew(1)>
			<cfif local.qryData.registrationID gt 0>
				<cfset local.qryRates = local.objEventReg.getRegRates(regid=local.qryData.registrationID,mid=local.commonTagVars.memberid,showall=true)>
				<cfloop query="local.qryRates">
					<cfset local.tempStruct = structNew()>
					<cfset local.tempStruct["name"] = local.qryRates.rateName>
					<cfset local.tempStruct["amt"] = local.qryRates.rate>
					<cfset arrayAppend(local.thisrow["rates"], local.tempStruct)>
				</cfloop>
			</cfif>
			
			<cfloop list="#local.qryData.swcfList#" index="local.swcfItem">
				<cfif structKeyExists(local.qryData,local.swcfItem)>
					<cfset local.thisrow[local.swcfItem] = local.qryData[local.swcfItem][currentRow]>
				</cfif>
			</cfloop>

			<cfset local.thisrow["featuredimages"] = structNew()>
			<cfset local.eventFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#'>
			<cfif val(local.qryData.calendarFeatureImageConfigID) and len(local.qryData.featureImageFileExt) and fileExists(local.eventFeaturedImageOriginalPath)>
				<cfset local.eventFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
				<cfset local.eventFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
				
				<cfset local.thisrow["featuredimages"]['fullsize'] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize']['url'] = "#local.eventFeaturedImageURL##local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				<cfset local.thisrow["featuredimages"]['fullsize']['width'] = local.qryData.featureImageOriginalWidth>
				<cfset local.thisrow["featuredimages"]['fullsize']['height'] = local.qryData.featureImageOriginalHeight>						
				<cfset local.thisrow["featuredimages"]['fullsize']['filename'] = "#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				
				<cfloop list="#local.qryData.imageConfigs#" index="local.thisImageConfig" delimiters="^~~~^">
					<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
					<cfset local.currentSizeID = local.imageConfigDataArray[1]>
					<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
					<cfset local.currentWidth = local.imageConfigDataArray[3]>
					<cfset local.currentHeight = local.imageConfigDataArray[4]>
					<cfset local.currentExt = local.imageConfigDataArray[5]>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#'] = structNew()>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['url'] = "#local.eventFeaturedImageThumbnailURL##local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['width'] = local.currentWidth>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['height'] = local.currentHeight>							
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['filename'] = "#local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">					
				</cfloop>
			</cfif>

			<cfset arrayAppend(local.dataStruct["events"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-eventList">
					<cfloop array="#local.dataStruct.events#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.title#</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-eventList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="mySubscriptions" access="public" returntype="struct" hint="Displays member subscriptions.">
		<cfargument name="typeList" type="string" required="false" default="" hint="Pipe-delimited list of Subscription Types' UIDs. Omit or leave blank for all types.">
		<cfargument name="subList" type="string" required="false" default="" hint="Pipe-delimited list of Subscriptions' UIDs. Omit or leave blank for all subs.">
		<cfargument name="rateList" type="string" required="false" default="" hint="Pipe-delimited list of Rates UIDs. Omit or leave blank for all rates.">
		<cfargument name="subStatusList" type="string" required="false" default="A|O" hint="Pipe-delimited list of statuses. Omit or leave blank to limit to Active and Billed.">
		<cfargument name="payStatusList" type="string" required="false" default="" hint="Activation status">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="dataView" type="string" required="false" default="list">
		<cfargument name="noresultstext" type="string" required="false" default="There are no subscriptions.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = arguments.format>
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = structNew()>
		<cfset local.dataStruct["subs"] = arrayNew(1)>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>
			
		<cfset arguments.maxRows = max(arguments.maxRows,1)>

		<cfif NOT application.objCommon.validateUIDList(inputString=arguments.typeList) OR
			NOT application.objCommon.validateUIDList(inputString=arguments.subList) OR
			NOT application.objCommon.validateUIDList(inputString=arguments.rateList)>
			<cfset local.qryData = queryNew('')>
		<cfelse>
			<cfstoredproc procedure="cms_processMergeTag_mySubscriptions" datasource="#application.dsn.membercentral.dsn#">			
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.typeList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.subList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.rateList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.subStatusList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.payStatusList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
				<cfprocresult name="local.qryData" resultset="1">
			</cfstoredproc>
		</cfif>

 		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["typeName"] = HTMLEditFormat(local.qryData.typeName)>
			<cfset local.thisrow["subId"] = local.qryData.subscriptionID>
			<cfset local.thisrow["subcriberId"] = local.qryData.subscriberID>
			<cfset local.thisrow["subName"] = HTMLEditFormat(local.qryData.subscriptionName)>
			<cfset local.thisrow["rateName"] = HTMLEditFormat(local.qryData.rateName)>
			<cfset local.thisrow["freqName"] = HTMLEditFormat(local.qryData.frequencyName)>
			<cfset local.thisrow["startDate"] = dateFormat(local.qryData.subStartDate,"m/d/yyyy")>
			<cfset local.thisrow["endDate"] = dateFormat(local.qryData.subEndDate,"m/d/yyyy")>
			<cfset local.thisrow["graceEndDate"] = dateFormat(local.qryData.graceEndDate,"m/d/yyyy")>
			<cfset local.thisrow["billedAmt"] = dollarformat(local.qryData.billedAmt)>
			<cfset local.thisrow["dueAmt"] = dollarformat(local.qryData.dueAmt)>
			<cfset local.thisrow["subStatusName"] = local.qryData.statusName>
			<cfset local.thisrow["payStatusName"] = local.qryData.paymentStatus>
			<cfset local.thisrow["renewLink"] = "">
			<cfset local.thisrow["subNameRenewLink"] = "<span title='Subscription dates: #local.thisrow['startDate']# - #local.thisrow['endDate']#.'>" & HTMLEditFormat(local.qryData.subscriptionName) & "</span>">
			<cfif len(trim(local.qryData.directLinkCode))>
				<cfset local.thisrow["renewLink"] = "#local.commonTagVars.baseUrl#/renewsub/#local.qryData.directLinkCode#">
				<cfset local.thisrow["subNameRenewLink"] = "<a href='#local.thisrow["renewLink"]#' title='Subscription dates: #local.thisrow['startDate']# - #local.thisrow['endDate']#. Click link to renew.' target='_blank'>" & HTMLEditFormat(local.qryData.subscriptionName) & "</a>">
			</cfif>
			<cfset arrayAppend(local.dataStruct["subs"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfif arguments.dataView eq "table">
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<table class="mc-mergeTagTable mc-subTable">
					<tr>
						<th>Subscription Name</th>
						<th>Start</th>
						<th>End</th>
						<th>Billed</th>
						<th>Due</th>
						<th>Status</th>
						<th>Renew Link</th>					
					</tr>
					<cfloop array="#local.dataStruct.subs#" index="local.thisRow">
						<tr class="mc-mergeTagRow">
							<td>#local.thisRow.subName# / #local.thisRow.rateName# (#local.thisRow.freqName#)</td>
							<td>#local.thisRow.startDate#</td>
							<td>#local.thisRow.endDate#</td>
							<td align="right">#local.thisRow.billedAmt#</td>
							<td align="right">#local.thisRow.dueAmt#</td>
							<td>#local.thisRow.subStatusName#</td>
							<cfif len(trim(local.thisrow.renewLink))>
								<td><a href="#local.thisrow.renewLink#" target="_blank">Renew</a></td>
							<cfelse>
								<td>&nbsp;</td>
							</cfif>						
						</tr>						
					</cfloop>
					</table>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<ul class="mc-mergeTagList mc-subList">
						<cfloop array="#local.dataStruct.subs#" index="local.thisRow">
							<li class="mc-mergeTagListItem">#local.thisRow.subNameRenewLink# / #local.thisRow.rateName# (#local.thisRow.freqName#)</li>
						</cfloop>
					</ul>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-subList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>	

	<cffunction name="myAnnouncements" access="public" returntype="struct" output="false" hint="Displays a member's announcements.">
		<cfargument name="pageName" type="string" required="false" default="" hint="leave blank for all announcement centers">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no announcements.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["announcements"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		arguments.maxRows = max(arguments.maxRows,1);
		
		if (len(arguments.pageName))
			local.qryAppInstance = application.objSiteResource.getApplicationInstanceFromPageName(pageName=arguments.pageName, siteID=local.commonTagVars.siteID);

		if (NOT isDefined("local.qryAppInstance") OR val(local.qryAppInstance.applicationInstanceID) is 0)
			local.applicationInstanceID = 0;
		else
			local.applicationInstanceID = local.qryAppInstance.applicationInstanceID;
		</cfscript>

		<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Announcement", functionName="View")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @siteID int, @languageID int, @memberID int, @functionID int, @groupPrintID int, @applicationInstanceID int, @maxrows int, @now datetime = getdate();

				set @siteID = <cfqueryparam value="#local.commonTagVars.siteID#" cfsqltype="CF_SQL_INTEGER">;
				set @memberID = <cfqueryparam value="#local.commonTagVars.memberid#" cfsqltype="CF_SQL_INTEGER">;
				set @functionID = <cfqueryparam value="#local.rfid#" cfsqltype="CF_SQL_INTEGER">;
				set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;
				set @groupPrintID = <cfqueryparam value="#application.objMember.getMemberGroupPrintID(memberID=local.commonTagVars.memberid)#" cfsqltype="CF_SQL_INTEGER">;
				set @applicationInstanceID = <cfqueryparam value="#local.applicationInstanceID#" cfsqltype="CF_SQL_INTEGER">;
				select @languageID = defaultLanguageID from dbo.sites where siteID = @siteID;

				IF OBJECT_ID('tempdb..##tmpNotices') IS NOT NULL
					DROP TABLE ##tmpNotices;
				CREATE TABLE ##tmpNotices (noticeID int, contentTitle varchar(400), startdate datetime);

				INSERT INTO ##tmpNotices (noticeID, contentTitle, startdate)
				select min(n.noticeID) as noticeID, noticeContent.contentTitle, min(n.startdate) as startdate
				from dbo.an_notices as n
				inner join dbo.cms_content as content 
					on n.siteID = @siteID
					and content.siteID = @siteID 
					and content.contentID = n.noticeContentID
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and content.siteResourceID = sr.siteResourceID and sr.siteResourceStatusID = 1
				inner join dbo.an_centers c 
					on n.centerID = c.centerID
				inner join dbo.cms_applicationInstances ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID 
				and ai.applicationInstanceID = case when @applicationInstanceID > 0 then @applicationInstanceID else ai.applicationInstanceID end
				inner join dbo.cms_siteResources as sr2 on sr2.siteID = @siteID and n.siteResourceID = sr2.siteResourceID and sr2.siteResourceStatusID = 1
				inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteResourceID = sr2.siteResourceID
					and srfrp.functionID = @functionID and srfrp.siteID = @siteID
				inner join dbo.cache_perms_groupPrintsrightPrints as gprp on gprp.rightPrintID = srfrp.rightPrintID
					and gprp.groupPrintID = @groupPrintID and gprp.siteID = @siteID
				inner join dbo.cms_contentLanguages as noticeContent on noticeContent.contentID = n.noticeContentID 
					and noticeContent.languageID = @languageID
				where @now between n.startdate and n.enddate
				group by noticeContent.contentTitle;

				SELECT top (@maxrows) n2.noticeID, dbo.fn_getResourcePagePlacementXML(@siteID, ai2.siteResourceID) as placementXML, 
					tmp.contentTitle, tmp.startdate, ai2.applicationInstanceID, noticeContent.isHTML, noticeContent.rawContent
				FROM dbo.an_notices as n2
				cross apply dbo.fn_getContent(n2.noticeContentID,@languageID) as noticeContent
				inner join dbo.an_centers as c2 on n2.centerID = c2.centerID
				inner join dbo.cms_applicationInstances as ai2 on ai2.siteID = @siteID and ai2.applicationInstanceID = c2.applicationInstanceID
				and ai2.applicationInstanceID = case when @applicationInstanceID > 0 then @applicationInstanceID else ai2.applicationInstanceID end
				inner join ##tmpNotices as tmp on n2.noticeID = tmp.noticeID
				ORDER BY tmp.startdate DESC;

				IF OBJECT_ID('tempdb..##tmpNotices') IS NOT NULL
					DROP TABLE ##tmpNotices;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["title"] = local.qryData.contentTitle>
			<cfset local.placementXML = xmlParse(local.qryData.placementXML)>
			<cfset local.thisrow['isHTML'] =local.qryData.isHTML>
			<cfset local.thisrow['pageContent'] =local.qryData.rawContent>
			<cftry>
				<cfset local.thisrow["pageTitle"] = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageTitle>
				<cfcatch type="any">
					<cfset local.thisrow["pageTitle"] = "Untitled Page">
				</cfcatch>	
			</cftry>
			<cftry>
				<cfset local.thisrow["pageName"] = local.placementXML.placements.XmlChildren[1].XmlAttributes.pageName>
				<cfcatch type="any">
					<cfset local.thisrow["pageName"] = "UnnamedPage">
				</cfcatch>	
			</cftry>
			<cftry>
				<cfif len(local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName)>
					<cfset local.thisrow["communityPageName"] = local.placementXML.placements.XmlChildren[1].XmlAttributes.communityPageName>
					<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=#local.thisrow["communityPageName"]#&commpg=#local.thisrow["pageName"]#">
				<cfelse>
					<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?#application.objApplications.getAppBaseLink(applicationInstanceID=local.qryData.applicationInstanceID, siteID=local.commonTagVars.siteid)#">
				</cfif>
				<cfcatch type="any">
					<cfset local.thisrow["pageName"] = "">
					<cfset local.thisrow["pageLink"] = "##">
				</cfcatch>	
			</cftry>
			<cfset arrayAppend(local.dataStruct["announcements"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>			
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-announcementList">
					<cfloop array="#local.dataStruct.announcements#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.title#</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-announcementList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="myContributions" access="public" returntype="struct" hint="Displays member contributions.">
		<cfargument name="programList" type="string" required="false" default="" hint="Pipe-delimited list of Contribution Program UIDs. Omit or leave blank for all programs.">
		<cfargument name="campaignList" type="string" required="false" default="" hint="Pipe-delimited list of Campaign Names. Omit or leave blank for all campaigns.">
		<cfargument name="statusList" type="string" required="false" default="" hint="Pipe-delimited list of statuses. Omit or leave blank to limit to all statuses.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no contributions.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = arguments.format>
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = structNew()>
		<cfset local.dataStruct["contributions"] = arrayNew(1)>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>

			
		<cfset arguments.maxRows = max(arguments.maxRows,1)>

		<cfif NOT application.objCommon.validateUIDList(inputString=arguments.programList)>
			<cfset local.qryData = queryNew('')>
		<cfelse>
			<cfstoredproc procedure="cms_processMergeCode_myContributions" datasource="#application.dsn.membercentral.dsn#">			
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.campaignList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.statusList#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
				<cfprocresult name="local.qryData" resultset="1">
			</cfstoredproc>
		</cfif>

 		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["programName"] = HTMLEditFormat(local.qryData.programName)>
			<cfset local.thisrow["campaignName"] = HTMLEditFormat(local.qryData.campaignName)>
			<cfset local.thisrow["rateName"] = HTMLEditFormat(local.qryData.rateName)>
			<cfset local.thisrow["frequency"] = HTMLEditFormat(local.qryData.frequency)>
			<cfset local.thisrow["statusName"] = HTMLEditFormat(local.qryData.statusName)>
			<cfset local.thisrow["startDate"] = dateFormat(local.qryData.startDate,"m/d/yyyy")>
			<cfset local.thisrow["endDate"] = dateFormat(local.qryData.endDate,"m/d/yyyy")>
			<cfset local.thisrow["firstPaymentDate"] = dateFormat(local.qryData.firstPaymentDate,"m/d/yyyy")>
			<cfset local.thisrow["firstInstallment"] = dollarformat(local.qryData.totalPledgeFirst)>
			<cfif local.qryData.isPerpetual>
				<cfset local.thisrow["pledgedValue"] = "Perpetual">
				<cfset local.thisrow["amountLeft"] = "Perpetual">
			<cfelse>
				<cfset local.thisrow["pledgedValue"] = dollarformat(local.qryData.pledgedValue)>
				<cfset local.thisrow["amountLeft"] = dollarformat(local.qryData.amountLeft)>
			</cfif>
			<cfset local.thisrow["amountPaid"] = dollarformat(local.qryData.totalPaid)>
			<cfset local.thisrow["recurringInstallment"] = dollarformat(local.qryData.totalPledgeRecurring)>
			<cfset arrayAppend(local.dataStruct["contributions"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-contributionList">
					<cfloop array="#local.dataStruct.contributions#" index="local.thisRow">
						<li class="mc-mergeTagListItem">#local.thisRow.programName# (#local.thisRow.frequency#)<cfif len(local.thisRow.campaignName)> / #local.thisRow.campaignName#</cfif><cfif len(local.thisRow.rateName)> / #local.thisRow.rateName#</cfif> / #local.thisRow.statusName#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-contributionList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="overdueInvoices" access="public" returntype="struct" output="false" hint="Displays a member's past due invoices.">
		<cfargument name="invoiceProfile" type="string" required="false" default="" hint="Pipe-delimited list of Invoice Profile Names. Omit or leave blank for all invoice profiles.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no overdue invoices.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";
	
		local.dataStruct = structNew();
		local.dataStruct["invoices"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfstoredproc procedure="cms_processMergeCode_overdueInvoices" datasource="#application.dsn.membercentral.dsn#">			
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.orgID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invoiceProfile#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["invoiceID"] = local.qryData.invoiceID>
			<cfset local.thisrow["dateDue"] = dateformat(local.qryData.dateDue,"m/d/yyyy")>
			<cfset local.thisrow["invoiceNumber"] = local.qryData.invoiceNumber>
			<cfset local.thisrow["amountDue"] = local.qryData.invDue>
			<cfset local.thisrow["profileName"] = local.qryData.profileName>

			<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryData.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryData.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=invoices&va=show&item=#local.stInvEnc#">

			<cfset arrayAppend(local.dataStruct["invoices"], local.thisrow)>
		</cfloop>		

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-invoiceList">
					<cfloop array="#local.dataStruct.invoices#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.invoiceNumber#</a> #dollarformat(local.thisRow.amountDue)# Due #local.thisRow.dateDue#<br/>
						#local.thisRow.profileName#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-invoiceList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="upcomingInvoices" access="public" returntype="struct" output="false" hint="Displays a member's invoices due in the future.">
		<cfargument name="invoiceProfile" type="string" required="false" default="" hint="Pipe-delimited list of Invoice Profile Names. Omit or leave blank for all invoice profiles.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no scheduled invoices.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";
	
		local.dataStruct = structNew();
		local.dataStruct["invoices"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfstoredproc procedure="cms_processMergeCode_upcomingInvoices" datasource="#application.dsn.membercentral.dsn#">			
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.orgID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.invoiceProfile#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["invoiceID"] = local.qryData.invoiceID>
			<cfset local.thisrow["dateDue"] = dateformat(local.qryData.dateDue,"m/d/yyyy")>
			<cfset local.thisrow["invoiceNumber"] = local.qryData.invoiceNumber>
			<cfset local.thisrow["amountDue"] = local.qryData.invDue>
			<cfset local.thisrow["profileName"] = local.qryData.profileName>

			<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryData.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryData.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=invoices&va=show&item=#local.stInvEnc#">

			<cfset arrayAppend(local.dataStruct["invoices"], local.thisrow)>
		</cfloop>		

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-invoiceList">
					<cfloop array="#local.dataStruct.invoices#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.invoiceNumber#</a> #dollarformat(local.thisRow.amountDue)# Due #local.thisRow.dateDue#<br/>
						#local.thisRow.profileName#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-invoiceList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="mySearchHistory" access="public" returntype="struct" output="false" hint="Displays a member's previous searches.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no searches.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["searches"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);


		arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfif local.commonTagVars.memberid gt 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>

			<cfif local.depoMemberDataID gt 0>
				<cfquery datasource="#application.dsn.TLASites_search.dsn#" name="local.qrySearch">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


					declare @maxrows int;
					set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;

					select top (@maxrows) searchID, dateEntered, bucketIDOrigin, searchVerbose
					from dbo.tblSearchHistory as sh
					inner join dbo.tblSearchBuckets as sb on sb.bucketID = sh.bucketIDorigin
					where sh.depoMemberDataID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.depoMemberDataID#">
					and sb.siteID = <cfqueryparam value="#local.commonTagVars.siteID#" cfsqltype="CF_SQL_INTEGER">
					order by sh.dateEntered DESC;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			<cfelse>
				<cfset local.qrySearch = queryNew("searchID","integer")>
			</cfif>
		<cfelse>
			<cfset local.qrySearch = queryNew("searchID","integer")>
		</cfif>

		<cfloop query="local.qrySearch">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["searchID"] = local.qrySearch.searchID>
			<cfset local.thisrow["dateEntered"] = dateformat(local.qrySearch.dateEntered,"m/d/yyyy")>
			<cfset local.thisrow["bucketIDOrigin"] = local.qrySearch.bucketIDOrigin>
			<cfset local.thisrow["searchVerbose"] = application.objSearchTranslate.printVerboseString(local.qrySearch.searchVerbose,'	','<br/>')>
			<cfset local.thisrow["searchVerbose"] = ReplaceNoCase(local.thisrow["searchVerbose"],"Section:","<span class='searchTitle'>Section:</span>","ALL")>
			<cfset local.thisrow["searchVerbose"] = ReplaceNoCase(local.thisrow["searchVerbose"],"Contains:",'<span class="searchTitle">Contains:</span>',"ALL")>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=search&bid=#local.qrySearch.bucketIDOrigin#&s_a=doSearch&sid=#local.qrySearch.searchID#">
			<cfset arrayAppend(local.dataStruct["searches"], local.thisrow)>
		</cfloop>		

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qrySearch.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-searchList">
					<cfloop array="#local.dataStruct.searches#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisrow.searchVerbose#</a> #local.thisrow.dateEntered#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-searchList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="recentFileShareUploads" access="public" returntype="struct" output="false" hint="Displays recently uploaded files.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no recent files.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["files"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

		arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfset local.rfid = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="UserCreatedContent", functionName="View")>
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryFiles">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @siteID int, @memberID int, @maxrows int, @functionID int;

				set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.commonTagVars.siteID#">;
				set @memberID = <cfqueryparam value="#local.commonTagVars.memberid#" cfsqltype="CF_SQL_INTEGER">;
				set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;
				set @functionID = <cfqueryparam value="#local.rfid#" cfsqltype="CF_SQL_INTEGER">;
		
				IF OBJECT_ID('tempdb..##fsSections') IS NOT NULL 
					DROP TABLE ##fsSections;
				CREATE TABLE ##fsSections (fsID int, fsName varchar(max), applicationInstanceID int, sectionID int INDEX IX_fsSections_sectionID, 
					sectionName varchar(100), parentSectionID int, siteResourceID int);

				insert into ##fsSections (fsID, fsName, applicationInstanceID, sectionID, sectionName, parentSectionID, siteResourceID)
				select distinct fs.fileshareid, ai.applicationInstanceName, ai.applicationInstanceID, childSections.sectionID, childSections.sectionName, 
					childSections.parentSectionID, childSections.siteResourceID
				from dbo.fs_fileshare as fs
				inner join dbo.cms_applicationInstances as ai on fs.applicationInstanceID = ai.applicationInstanceID
					and ai.siteID = @siteID
				inner join dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteResourceID = ai.siteResourceID
					and srfrp.functionID = @functionID and srfrp.siteID = @siteID
				inner join dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.rightPrintID = srfrp.rightPrintID and gprp.siteID = @siteID
				inner join dbo.ams_members as m on m.groupPrintID = gprp.groupPrintID
					and m.memberID = @memberID
				inner join dbo.cache_cms_recursivePageSections as rps on rps.sectionID = fs.rootSectionID
				inner join dbo.cms_pageSections as childSections on childSections.sectionID = rps.startSectionID;
		
				select top (@maxrows) sec.applicationInstanceID, sec.fsName, l.doctitle, d.documentID, v.dateModified
				from ##fsSections as sec
				inner join dbo.cms_documents d on d.siteID = @siteID and d.sectionID = sec.sectionID
				INNER JOIN dbo.cms_documentLanguages as l on d.documentID = l.documentID
				INNER JOIN dbo.cms_documentVersions as v on l.documentLanguageID = v.documentLanguageID AND v.isActive = 1
				INNER JOIN dbo.cms_siteResources as docSR ON docSR.siteID = @siteID and docSR.siteResourceStatusID = 1 and d.siteResourceID = docSR.siteResourceID
				order by v.documentVersionID desc;

				IF OBJECT_ID('tempdb..##fsSections') IS NOT NULL 
					DROP TABLE ##fsSections;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfloop query="local.qryFiles">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["dateModified"] = dateformat(local.qryFiles.dateModified,"m/d/yyyy")>
			<cfset local.thisrow["fileShare"] = local.qryFiles.fsName>
			<cfset local.thisrow["docTitle"] = local.qryFiles.doctitle>
			<cfset local.thisrow["documentID"] = local.qryFiles.documentID>
			<cfset local.appLink = CreateObject("component","model.appLoader").getAppBaseLink(applicationInstanceID=local.qryFiles.applicationInstanceID)>
			<cfset local.thisrow["fileShareLink"] = "#local.commonTagVars.baseUrl#?#local.appLink#">
			<cfset local.thisrow["documentLink"] = "#local.commonTagVars.baseUrl#?#local.appLink#&fsAction=viewDocument&fsDocumentID=#local.qryFiles.documentID#">
			<cfset arrayAppend(local.dataStruct["files"], local.thisrow)>
		</cfloop>		

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryFiles.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-uploadList">
					<cfloop array="#local.dataStruct.files#" index="local.thisRow">
						<li class="mc-mergeTagListItem">
							<a href="#local.thisRow.documentLink#">#local.thisRow.docTitle#</a><br/>
							Fileshare: <a href="#local.thisRow.fileShareLink#">#local.thisRow.fileShare#</a> (Updated #local.thisRow.dateModified#)
						</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-uploadList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="myRecentFileshareDownloads" access="public" returntype="struct" output="false" hint="Displays a member's recently downloaded files.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no recent downloads.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
		var local = structNew();
		local.data = structNew();
		local.data["format"] = arguments.format;
		local.data["jsonvariable"] = arguments.jsonvariable;
		local.data["dataString"] = "";

		local.dataStruct = structNew();
		local.dataStruct["downloads"] = arrayNew(1);
		local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);


		arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfif local.commonTagVars.memberid gt 0>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryDownloads">
				set nocount on;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @maxrows int, @siteID int, @memberID int;
				declare @recentDocuments TABLE (documentLanguageID int, documentVersionID int, maxDocHitID int);

				set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;
				set @siteID = <cfqueryparam value="#local.commonTagVars.siteID#" cfsqltype="CF_SQL_INTEGER">;
				set @memberID = <cfqueryparam value="#local.commonTagVars.memberid#" cfsqltype="CF_SQL_INTEGER">;

				insert into @recentDocuments (documentLanguageID, maxDocHitID)
				select top (@maxrows) l.documentLanguageID, max(dh.docHitID) as docHitID
				from platformstatsMC.dbo.statsDocumentHits as dh
				inner join platformstatsMC.dbo.statsSessions as ss on ss.sessionid = dh.sessionid
					and ss.siteid = @siteID
					and dh.siteID = @siteID
					and ss.memberid = @memberID
				INNER JOIN dbo.cms_documentVersions as v on v.documentVersionID = dh.documentVersionID
				INNER JOIN dbo.cms_documentLanguages as l on l.documentLanguageID = v.documentLanguageID
				group by l.documentLanguageID
				order by docHitID desc
		
				select l.documentID, l.docTitle, l.docdesc
				from @recentDocuments as rd
				inner join dbo.cms_documentLanguages as l on rd.documentLanguageID = l.documentLanguageID
				order by rd.maxDocHitID desc

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		<cfelse>
			<cfset local.qryDownloads = queryNew("documentID","integer")>
		</cfif>

		<cfloop query="local.qryDownloads">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["documentID"] = local.qryDownloads.documentID>
			<cfset local.thisrow["docTitle"] = local.qryDownloads.docTitle>
			<cfset local.thisrow["docdesc"] = local.qryDownloads.docdesc>
			<cfset local.thisrow["downloadLink"] = "#local.commonTagVars.baseUrl#docDownload/#local.qryDownloads.documentID#">
			<cfset arrayAppend(local.dataStruct["downloads"], local.thisrow)>
		</cfloop>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryDownloads.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-downloadList">
					<cfloop array="#local.dataStruct.downloads#" index="local.thisRow">
						<li class="mc-mergeTagListItem">
							<a href="#local.thisRow.downloadLink#" target="_blank">#local.thisRow.docTitle#</a><br/>
							#local.thisRow.docDesc#
						</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-downloadList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="recentBlogEntries" access="public" returntype="struct" output="false" hint="Displays entries associated with blogs ordered by creation date." defaultJSONVariableName="blogEntries">
		<cfargument name="pageName" type="string" required="true" hint="pagename of the blog instance from which to pull entries">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="category" type="string" required="false" default="" hint="Pipe-delimited list of category codes. Leave blank for all categories.">
		<cfargument name="includeSummary" type="boolean" required="false" default="false" hint="Include blog summary, if defined">
		<cfargument name="entriesToExclude" type="string" required="false" default="" hint="Comma-delimited list of blog entry ids to exclude.">
		<cfargument name="noresultstext" type="string" required="false" default="There are no recent blog entries.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="sortmethod" type="string" required="false" default="" hint="sort the result based on passed attribute. Values can be publishDate, creationDate, articleDate, blogTitle & blogEntryID">
		<cfargument name="order" type="string" required="false" default="" hint="sort order for the sortmethod passed. Values can be desc & asc">

		<cfscript>
			var local = structNew();
			local.data =  structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["blogEntries"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
			local.tblName = "####tmpPTCF#replace(createUUID(),'-','','ALL')#";


			if (arguments.format eq "json") local.includeSummary = true;
			else local.includeSummary = arguments.includeSummary;

			arguments.maxRows = max(arguments.maxRows,1);

			if (len(arguments.pageName))
				local.qryAppInstance = application.objSiteResource.getApplicationInstanceFromPageName(pageName=arguments.pageName, siteID=local.commonTagVars.siteID);

			if (NOT structKeyExists(local,"qryAppInstance") OR val(local.qryAppInstance.applicationInstanceID) is 0)
				local.applicationInstanceID = 0;
			else {
				local.applicationInstanceID = local.qryAppInstance.applicationInstanceID;
				local.baseLink = application.objApplications.getAppBaseLink(applicationinstanceID=local.applicationInstanceID, siteID=local.commonTagVars.siteID);
			}
		</cfscript>
		<cfif local.applicationInstanceID gt 0>
			<cfstoredproc procedure="cms_processMergeCode_RecentBlogEntries" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.category#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.applicationInstanceID#">
				<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#local.includeSummary#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.entriesToExclude#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.tblName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.sortmethod#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.order#">
				<cfprocresult name="local.qryData" resultset="1">
			</cfstoredproc>

			<cfloop query="local.qryData">
				<cfset local.thisrow = structNew()>
				<cfset local.thisrow["blogEntryID"] = local.qryData.blogEntryID>
				<cfset local.thisrow["blogTitle"] = local.qryData.blogTitle>
				<cfset local.thisrow["mainContent"] = application.objCommon.getCleanContentData(local.qryData.mainContent)>

				<cfset local.thisrow["blogCategories"] = structNew("ordered")>
				<cfloop list="#local.qryData.categories#" index="local.thisCategoryTree" delimiters="^~~~^">					
					<cfset local.thisCategoryTreeData = listToArray(local.thisCategoryTree,"||",false,true)>
					<cfset local.currentCategoryTreeName = local.thisCategoryTreeData[1]>
					<cfset local.currentCategoriesInfo = local.thisCategoryTreeData[2]>
					<cfset local.currentCategoriesInfo = ListSort(local.currentCategoriesInfo,"TextNoCase","asc","|")>

					<cfset local.thisCategoryArray = arrayNew(1)>
					<cfloop list="#local.currentCategoriesInfo#" index="local.thisCategoryInfo" delimiters="|">
						<cfset local.thisCategoryInfoArray = listToArray(local.thisCategoryInfo,"***")>
						<cfset local.thisCategory = structNew()>
						<cfset local.thisCategory['categoryname'] = local.thisCategoryInfoArray[2]>
						<cfset local.thisCategory['categorypath'] = local.thisCategoryInfoArray[3]>
						<cfset local.thisCategory['categoryid'] = local.thisCategoryInfoArray[4]>
						<cfset arrayAppend(local.thisCategoryArray, local.thisCategory)>					
					</cfloop>
					<cfset StructInsert(local.thisrow["blogCategories"],"#local.currentCategoryTreeName#",local.thisCategoryArray)>
				</cfloop>

				<cfif local.includeSummary>
					<cfset local.thisrow["summaryContent"] = application.objCommon.getCleanContentData(trim(local.qryData.summaryContent))>
				</cfif>

				<cfset local.thisrow["blogAuthors"] = ArrayNew(1)>
				<cfif local.qryData.blogAuthorSupport neq "D" and len(local.qryData.authors)>
					<cfset local.numOfAuthors = listLen(local.qryData.authors,'^~~~^')>
					<cfset local.thisNum = 1>
					<cfset local.thisrow["blogAuthor"] = "">
					<cfloop list="#local.qryData.authors#" index="local.thisAuthor" delimiters="^~~~^">
						<cfset local.thisAuthorDetails = structNew()>
						<cfset local.thisrow["blogAuthor"] = local.thisrow["blogAuthor"] & local.thisAuthor>
						<cfset local.thisAuthorDetails["author"] = local.thisAuthor>
						<cfset local.thisAuthorDetails["memberID"] = listGetAt(local.qryData.memberIDs,local.thisNum,"^~~~^")>
						<cfset local.thisAuthorDetails["blogAuthorLink"] = "#local.commonTagVars.baseUrl#?#local.baseLink#&fAuthor=#listGetAt(local.qryData.memberIDs,local.thisNum,"^~~~^")#">
						<cfif local.numOfAuthors gt 1 and local.thisNum lt local.numOfAuthors>
							<cfif local.thisNum + 1 eq local.numOfAuthors>
								<cfset local.thisrow["blogAuthor"] = "#local.thisrow["blogAuthor"]# & ">
							<cfelse>
								<cfset local.thisrow["blogAuthor"] = "#local.thisrow["blogAuthor"]#, ">
							</cfif>
						</cfif>
						<cfset arrayAppend(local.thisrow["blogAuthors"], local.thisAuthorDetails)>
						<cfset local.thisNum = local.thisNum + 1>
					</cfloop>
				</cfif>
				
				<cfset local.thisrow["dateCreated"] = trim(dateformat(local.qryData.dateCreated,"m/d/yyyy") & " " & timeformat(local.qryData.dateCreated, "h:mm tt"))>
				<cfset local.thisrow["articleDate"] = trim(dateformat(local.qryData.articleDate,"m/d/yyyy") & " " & timeformat(local.qryData.articleDate, "h:mm tt"))>
				<cfset local.thisrow["publishDate"] = trim(dateformat(local.qryData.publishDate,"m/d/yyyy") & " " & timeformat(local.qryData.publishDate, "h:mm tt"))>								
				<cfif local.qryData.enableFeaturedImage is 1>
					<cfset local.orgSiteFolder = lCase("#local.qryData.orgcode#/#local.qryData.sitecode#")>
					<cfset local.blogFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#'>
					<cfif len(local.qryData.featureImageFileExt) and fileExists(local.blogFeaturedImageOriginalPath)>
						<cfset local.blogFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
						<cfset local.blogFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
						<cfset local.thisrow["featuredimages"] = structNew()>
						<cfset local.thisrow["featuredimages"]['fullsize'] = structNew()>
						<cfset local.thisrow["featuredimages"]['fullsize']['url'] = "#local.blogFeaturedImageURL##local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
						<cfset local.thisrow["featuredimages"]['fullsize']['width'] = local.qryData.featureImageOriginalWidth>
						<cfset local.thisrow["featuredimages"]['fullsize']['height'] = local.qryData.featureImageOriginalHeight>						
						<cfset local.thisrow["featuredimages"]['fullsize']['filename'] = "#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
						
						<cfloop list="#local.qryData.imageConfigs#" index="local.thisImageConfig" delimiters="^~~~^">
							<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
							<cfset local.currentSizeID = local.imageConfigDataArray[1]>
							<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
							<cfset local.currentWidth = local.imageConfigDataArray[3]>
							<cfset local.currentHeight = local.imageConfigDataArray[4]>
							<cfset local.currentExt = local.imageConfigDataArray[5]>
							<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#'] = structNew()>
							<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['url'] = "#local.blogFeaturedImageThumbnailURL##local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">
							<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['width'] = local.currentWidth>
							<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['height'] = local.currentHeight>							
							<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['filename'] = "#local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">					
						</cfloop>
					<cfelse>
						<cfset local.thisrow["featuredimages"] = structNew()>
					</cfif>
				</cfif>
				<cfset local.thisrow["entryLink"] = "#local.commonTagVars.baseUrl#?#local.baseLink#&blAction=showEntry&blogEntry=#local.qryData.blogEntryID#">
				<cfset local.thisrow["isSticky"] = local.qryData.isSticky>

				<cfloop list="#local.qryData.ptcfList#" index="local.ptcfItem">
					<cfif structKeyExists(local.qryData,local.ptcfItem)>
						<cfset local.thisrow[local.ptcfItem] = local.qryData[local.ptcfItem][currentRow]>
					</cfif>
				</cfloop>

				<cfset local.thisrow["isEvenRow"] = "false">
				<cfset local.thisrow["isOddRow"] = "true">
				<cfif not (local.qryData.currentRow MOD 2)>
					<cfset local.thisrow["isEvenRow"] = "true">
					<cfset local.thisrow["isOddRow"] = "false">
				</cfif>

				<cfset local.thisrow["documents"] = structNew()>
				<cfset local.idx = 0>
				<cfloop list="#local.qryData.documents#" index="local.thisDocuments" delimiters="^~~~^">
					<cfset local.docsDataArray = listToArray(local.thisDocuments,"|")>
					<cfset local.documentID = local.docsDataArray[1]>
					<cfset local.fileName = local.docsDataArray[2]>
					<cfset local.entryDocumentID = local.docsDataArray[3]>
					<cfset local.docTitle = local.docsDataArray[4]>

					<cfset local.thisrow["documents"][local.idx] = structNew()>
					<cfset local.thisrow["documents"][local.idx]['url'] = "/docDownload/#local.documentID#">
					<cfset local.thisrow["documents"][local.idx]['filename'] = local.fileName>	
					<cfset local.thisrow["documents"][local.idx]['title'] = local.docTitle>	
					<cfset local.idx = local.idx + 1>				
				</cfloop>
				<cfset arrayAppend(local.dataStruct["blogEntries"], local.thisrow)>
			</cfloop>

			<cfif arguments.format eq "json">
				<cfset local.data["dataStruct"] = local.dataStruct>
			<cfelseif local.qryData.recordCount>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<ul class="mc-mergeTagList mc-blogList">
						<cfloop array="#local.dataStruct.blogEntries#" index="local.thisRow">
							<li class="mc-mergeTagListItem">
								<div class="mc-mergeTagListItemTitle"><a href="#local.thisRow.entryLink#">#local.thisRow.blogTitle#</a></div>
								<cfif local.includeSummary and len(local.thisRow.summaryContent)>
									<div class="mc-mergeTagListItemBody">
										#local.thisRow.summaryContent#
										<div class="mc-blogReadMore">
											<a href="#local.thisRow.entryLink#">Read More</a>
										</div>
									</div>
								</cfif>
							</li>
						</cfloop>
					</ul>
					</cfoutput>
				</cfsavecontent>
			<cfelseif len(arguments.noresultstext)>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-blogList mc-noDataMessageContainer">
						<span class="mc-noDataMessage">#arguments.noresultstext#</span>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>
		<cfreturn local.data>
	</cffunction>

	<cffunction name="recentPublicationIssues" access="public" returntype="struct" output="false" hint="Displays publication issues ordered by issue date.">
		<cfargument name="pageName" type="string" required="true" hint="pagename of the publication instance from which to pull entries">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="noresultstext" type="string" required="false" default="There are no recent issues.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["volumes"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

			arguments.maxRows = max(arguments.maxRows,1);

			if (len(arguments.pageName))
				local.qryAppInstance = application.objSiteResource.getApplicationInstanceFromPageName(pageName=arguments.pageName, siteID=local.commonTagVars.siteID);

			if (NOT structKeyExists(local,"qryAppInstance") OR val(local.qryAppInstance.applicationInstanceID) is 0)
				local.applicationInstanceID = 0;
			else {
				local.applicationInstanceID = local.qryAppInstance.applicationInstanceID;
				local.baseLink = application.objApplications.getAppBaseLink(applicationinstanceID=local.applicationInstanceID, siteID=local.commonTagVars.siteID);
			}
		</cfscript>

		<cfif local.applicationInstanceID gt 0>
			<cfstoredproc procedure="cms_processMergeCode_recentPublicationIssues" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.applicationInstanceID#">
				<cfprocresult name="local.qryData" resultset="1">
			</cfstoredproc>

			<cfset local.dataStruct["publicationName"] = local.qryData.publicationName>
			<cfset local.dataStruct["siteName"] = local.qryData.siteName>
			<cfif local.qryData.supportsOnlineEditions is 1 or local.qryData.supportsPDFEditions is 1>
				<cfset local.dataStruct["publicationurl"] = "#local.commonTagVars.baseUrl#?#local.baseLink#">
			</cfif>

			<cfset local.dataStruct["templatecustomfields"] = structNew("ordered")>

			<cfset local.thisPublicationTemplatesArray = listToArray(local.qryData.templatecustomfields,"^~~~^",true,true)>
			<cfloop array="#local.thisPublicationTemplatesArray#" index="local.thisPublicationTemplateInfo">
				<cfif len(local.thisPublicationTemplateInfo)>
					<cfset local.thisPublicationTemplateData = listToArray(local.thisPublicationTemplateInfo,"^||^",true,true)>
					<cfset local.currentPublicationTemplateName = local.thisPublicationTemplateData[1]>
					<cfset local.currentPublicationTemplateFieldsArray = listToArray(local.thisPublicationTemplateData[2],"^---^",true,true)>

					<cfset local.thisTemplateFieldsArray = arrayNew(1)>
					<cfloop array="#local.currentPublicationTemplateFieldsArray#" index="local.thisFieldInfo">
						<cfset local.thisFieldInfoArray = listToArray(local.thisFieldInfo,"^***^",true,true)>
						<cfset local.thisField = structNew()>
						<cfset local.thisField['fieldtext'] = local.thisFieldInfoArray[1]>
						<cfset local.thisField['fieldvalue'] = local.thisFieldInfoArray[2]>
						<cfset arrayAppend(local.thisTemplateFieldsArray, local.thisField)>
					</cfloop>
					<cfset StructInsert(local.dataStruct["templatecustomfields"],"#local.currentPublicationTemplateName#",local.thisTemplateFieldsArray)>
				</cfif>
			</cfloop>

			<cfoutput query="local.qryData" group="volumeID">
				<cfset local.thisrow = structNew()>
				<cfset local.thisrow["volumename"] = local.qryData.volumeName>

				<cfset local.thisIssueArray = arrayNew(1)>
				<cfoutput>
					<cfset local.thisIssue = structNew()>

					<cfset local.thisIssueID = val(local.qryData.issueID)>
					<cfset local.thisIssue['issuetitle'] = local.qryData.issueTitle>
					<cfset local.thisIssue['issuedate'] = local.qryData.issueDate>
					<cfset local.pdfEditionDocumentID = local.qryData.pdfEditionDocumentID>
					<cfset local.pdfFileName = local.qryData.pdfFileName>

					<cfset local.issueFeaturedImageID = local.qryData.featureImageID>
					<cfset local.issueFeaturedImageFileExt = local.qryData.featureImageFileExtension>
					<cfset local.issueFeaturedImageOriginalWidth = local.qryData.featureImageOriginalWidth>
					<cfset local.issueFeaturedImageOriginalHeight = local.qryData.featureImageOriginalHeight>

					<cfset local.issuePdfFeaturedImageID = local.qryData.pdfFeatureImageID>
					<cfset local.issuePdfFeaturedImageFileExt = local.qryData.pdfFeatureImageFileExtension>
					<cfset local.issuePdfFeaturedImageOriginalWidth = local.qryData.pdfFeatureImageOriginalWidth>
					<cfset local.issuePdfFeaturedImageOriginalHeight = local.qryData.pdfFeatureImageOriginalHeight>

					<cfif arguments.format eq "html">
						<cfset local.thisIssue['issuetype'] = local.qryData.issueType>
						<cfset local.thisIssue['htmlcontentid'] = local.qryData.htmlContentID>
						<cfset local.thisIssue['itemcount'] = local.qryData.issueItemsCount>
					</cfif>

					<cfif local.qryData.supportsOnlineEditions is 1 or local.qryData.supportsPDFEditions is 1>
						<cfset local.thisIssue["issueurl"] = "#local.commonTagVars.baseUrl#?#local.baseLink#&pubAction=viewIssue&pubIssueID=#local.thisIssueID#">
					</cfif>
					<cfif local.qryData.supportsPDFEditions is 1 and local.pdfEditionDocumentID gt 0>
						<cfset local.thisIssue["issuepdf"] = structNew()>
						<cfset local.thisIssue["issuepdf"]["filename"] = local.pdfFileName>
						<cfset local.thisIssue["issuepdf"]["url"] = "#local.commonTagVars.baseUrl#?#local.baseLink#&pubAction=downloadIssue&pubIssueID=#local.thisIssueID#">
					</cfif>

					<!--- issue featured image --->
					<cfset local.thisIssue["issuefeaturedimages"] = structNew()>
					<cfif local.issueFeaturedImageID gt 0>
						<cfset local.orgSiteFolder = lCase("#local.qryData.orgcode#/#local.qryData.sitecode#")>
						<cfset local.issueFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.issueFeaturedImageID#.#local.issueFeaturedImageFileExt#'>
						<cfif len(local.issueFeaturedImageFileExt) and fileExists(local.issueFeaturedImageOriginalPath)>
							<cfset local.issueFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
							<cfset local.issueFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
							<cfset local.thisIssue["issuefeaturedimages"] = structNew()>
							<cfset local.thisIssue["issuefeaturedimages"]['fullsize'] = structNew()>
							<cfset local.thisIssue["issuefeaturedimages"]['fullsize']['url'] = "#local.issueFeaturedImageURL##local.issueFeaturedImageID#.#local.issueFeaturedImageFileExt#">
							<cfset local.thisIssue["issuefeaturedimages"]['fullsize']['width'] = val(local.issueFeaturedImageOriginalWidth)>
							<cfset local.thisIssue["issuefeaturedimages"]['fullsize']['height'] = val(local.issueFeaturedImageOriginalHeight)>
							<cfset local.thisIssue["issuefeaturedimages"]['fullsize']['filename'] = "#local.issueFeaturedImageID#.#local.issueFeaturedImageFileExt#">
							
							<cfloop list="#local.qryData.issueImageConfigString#" index="local.thisImageConfig" delimiters="^~~~^">
								<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
								<cfset local.currentSizeID = local.imageConfigDataArray[1]>
								<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
								<cfset local.currentWidth = local.imageConfigDataArray[3]>
								<cfset local.currentHeight = local.imageConfigDataArray[4]>
								<cfset local.currentExt = local.imageConfigDataArray[5]>
								<cfset local.thisIssue["issuefeaturedimages"]['#local.currentSizeCode#'] = structNew()>
								<cfset local.thisIssue["issuefeaturedimages"]['#local.currentSizeCode#']['url'] = "#local.issueFeaturedImageThumbnailURL##local.issueFeaturedImageID#-#local.currentSizeID#.#local.currentExt#">
								<cfset local.thisIssue["issuefeaturedimages"]['#local.currentSizeCode#']['width'] = val(local.currentWidth)>
								<cfset local.thisIssue["issuefeaturedimages"]['#local.currentSizeCode#']['height'] = val(local.currentHeight)>
								<cfset local.thisIssue["issuefeaturedimages"]['#local.currentSizeCode#']['filename'] = "#local.issueFeaturedImageID#-#local.currentSizeID#.#local.currentExt#">					
							</cfloop>
						</cfif>
					</cfif>

					<!--- issue pdf featured image --->
					<cfset local.thisIssue["issuepdffeaturedimages"] = structNew()>
					<cfif local.issuePdfFeaturedImageID gt 0>
						<cfset local.orgSiteFolder = lCase("#local.qryData.orgcode#/#local.qryData.sitecode#")>
						<cfset local.issuePdfFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.issuePdfFeaturedImageID#.#local.issuePdfFeaturedImageFileExt#'>
						<cfif len(local.issuePdfFeaturedImageFileExt) and fileExists(local.issuePdfFeaturedImageOriginalPath)>
							<cfset local.issuePdfFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
							<cfset local.issuePdfFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
							<cfset local.thisIssue["issuepdffeaturedimages"] = structNew()>
							<cfset local.thisIssue["issuepdffeaturedimages"]['fullsize'] = structNew()>
							<cfset local.thisIssue["issuepdffeaturedimages"]['fullsize']['url'] = "#local.issuePdfFeaturedImageURL##local.issuePdfFeaturedImageID#.#local.issuePdfFeaturedImageFileExt#">
							<cfset local.thisIssue["issuepdffeaturedimages"]['fullsize']['width'] = val(local.issuePdfFeaturedImageOriginalWidth)>
							<cfset local.thisIssue["issuepdffeaturedimages"]['fullsize']['height'] = val(local.issuePdfFeaturedImageOriginalHeight)>
							<cfset local.thisIssue["issuepdffeaturedimages"]['fullsize']['filename'] = "#local.issuePdfFeaturedImageID#.#local.issuePdfFeaturedImageFileExt#">
							
							<cfloop list="#local.qryData.issuePdfImageConfigString#" index="local.thisImageConfig" delimiters="^~~~^">
								<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
								<cfset local.currentSizeID = local.imageConfigDataArray[1]>
								<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
								<cfset local.currentWidth = local.imageConfigDataArray[3]>
								<cfset local.currentHeight = local.imageConfigDataArray[4]>
								<cfset local.currentExt = local.imageConfigDataArray[5]>
								<cfset local.thisIssue["issuepdffeaturedimages"]['#local.currentSizeCode#'] = structNew()>
								<cfset local.thisIssue["issuepdffeaturedimages"]['#local.currentSizeCode#']['url'] = "#local.issuePdfFeaturedImageThumbnailURL##local.issuePdfFeaturedImageID#-#local.currentSizeID#.#local.currentExt#">
								<cfset local.thisIssue["issuepdffeaturedimages"]['#local.currentSizeCode#']['width'] = val(local.currentWidth)>
								<cfset local.thisIssue["issuepdffeaturedimages"]['#local.currentSizeCode#']['height'] = val(local.currentHeight)>
								<cfset local.thisIssue["issuepdffeaturedimages"]['#local.currentSizeCode#']['filename'] = "#local.issuePdfFeaturedImageID#-#local.currentSizeID#.#local.currentExt#">
							</cfloop>
						</cfif>
					</cfif>

					<cfset arrayAppend(local.thisIssueArray, local.thisIssue)>
				</cfoutput>
				<cfset local.thisrow["issues"] = local.thisIssueArray>

				<cfset arrayAppend(local.dataStruct["volumes"], local.thisrow)>
			</cfoutput>

			<cfif arguments.format eq "json">
				<cfset local.data["dataStruct"] = local.dataStruct>
			<cfelseif local.qryData.recordCount>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<ul class="mc-mergeTagList mc-publicationIssueList">
						<cfloop array="#local.dataStruct.volumes#" index="local.thisVolume">
							<li class="mc-mergeTagListItem">
								#local.thisVolume.volumename#
								<ul>
									<cfloop array="#local.thisVolume.issues#" index="local.thisIssue">
										<cfset local.canViewIssue = ( (local.thisIssue.issuetype eq 'P' and local.thisIssue.itemcount gt 0) 
												OR (local.thisIssue.issuetype eq 'I' and val(local.thisIssue.htmlcontentid) gt 0) )
											AND (local.qryData.supportsEmailEditions or local.qryData.supportsOnlineEditions)>
										<cfset local.canDownloadIssue = StructKeyExists(local.thisIssue,"issuepdf")>

										<cfif local.canViewIssue OR local.canDownloadIssue>
											<li>
												<div class="mc-mergeTagListItemTitle">
													<cfif local.canViewIssue>
														<a href="#local.thisIssue.issueurl#">#local.thisIssue.issuetitle#</a>
													<cfelseif local.canDownloadIssue>
														<a href="#local.thisIssue.issuepdf.url#">#local.thisIssue.issuetitle#</a>
													</cfif>
												</div>
											</li>
										</cfif>
									</cfloop>
								</ul>
							</li>
						</cfloop>
					</ul>
					</cfoutput>
				</cfsavecontent>
			<cfelseif len(arguments.noresultstext)>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-publicationIssueList mc-noDataMessageContainer">
						<span class="mc-noDataMessage">#arguments.noresultstext#</span>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="myECommunities" returntype="struct" access="public" output="false" hint="Displays a list of a member's eCommunities.">
		<cfargument name="noresultstext" type="string" required="false" default="You are not currently a member of any eCommunities." hint="Message shown when no items available for display">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
	
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["eCommunities"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
	

			local.profileID = application.objMember.getMemberProfileID(memberID=local.commonTagVars.memberID,siteID=local.commonTagVars.siteID,orgID=local.commonTagVars.orgID);

		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT DISTINCT ai.applicationInstanceID, ai.siteID, 
				c.communityID, c.defaultCommunityPageName, c.communityName, c.communityDescription, c.rootSectionID, c.lyrisListName, c.summaryHTML, c.emailList, c.communityCenterAppInstanceID,
				s.siteName,	
				oi.organizationName as orgName, oi.website as orgURL,
				isCommunityMember = case when gprp.rightPrintID is null then cast(0 as bit) else cast(1 as bit) end,
				isMemberOrgCommunity = case when s.siteID is null then cast(0 as bit) else cast(1 as bit) end
			FROM dbo.cms_applicationInstances ai
			INNER JOIN dbo.comm_communities AS c ON c.applicationInstanceID = ai.applicationInstanceID
			INNER JOIN dbo.cms_siteResources AS sr ON ai.siteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_siteResourceStatuses AS srs ON sr.siteResourceStatusID = srs.siteResourceStatusID 
				AND srs.siteResourceStatusDesc = 'Active'
			INNER JOIN dbo.sites AS s ON s.siteID = sr.siteID
			INNER JOIN dbo.organizations AS o ON s.orgID = o.orgID
			INNER JOIN dbo.orgIdentities as oi on oi.orgID = o.orgID and oi.orgIdentityID = o.defaultOrgIdentityID
			INNER JOIN dbo.ams_members AS activeMembers ON o.orgID = activeMembers.orgID
				AND activeMembers.status = 'A' 
				AND activeMembers.memberID = activeMembers.activeMemberID
			INNER JOIN dbo.ams_members AS allmembers ON allmembers.activememberID = activeMembers.memberID
			INNER JOIN dbo.ams_memberNetworkProfiles AS mnp ON allmembers.memberID = mnp.memberID
			INNER JOIN dbo.ams_networkProfiles AS np ON np.profileID = mnp.profileID
				AND np.profileID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.profileID#">
			inner JOIN dbo.cache_perms_siteResourceFunctionRightPrints AS srfrp ON sr.siteResourceID = srfrp.siteResourceID and srfrp.siteID = #local.commonTagVars.siteID#
			INNER JOIN dbo.cms_siteResourceFunctions AS srf ON srfrp.functionID = srf.functionID 
				AND srf.functionName = 'view'
			INNER JOIN dbo.cache_perms_groupPrintsRightPrints AS gprp ON srfrp.rightPrintID = gprp.rightPrintID and srfrp.siteID = #local.commonTagVars.siteID#
				AND gprp.groupPrintID = activeMembers.groupPrintID
			WHERE gprp.rightPrintID is not null 
			AND sr.siteID = #local.commonTagVars.siteID#
			ORDER BY c.communityName;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["communityID"] = local.qryData.communityID>
			<cfset local.thisrow["communityName"] = local.qryData.communityName>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?#application.objApplications.getAppBaseLink(local.qryData.applicationInstanceID,local.commonTagVars.siteid)#">
			<cfset arrayAppend(local.dataStruct["eCommunities"], local.thisrow)>
		</cfloop>
		
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-communityList">
					<cfloop array="#local.dataStruct.eCommunities#" index="local.thisRow">
						<li class="mc-mergeTagListItem">#local.thisRow.communityName# <a href="#local.thisRow.pageLink#">(View)</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-communityList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="latestStoreProducts" returntype="struct" access="public" output="false" hint="Displays a list of recently added store products.">
		<cfargument name="noresultstext" type="string" required="no" default="There are no recent store products to display.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="category" type="string" required="false" default="" hint="Pipe-delimited list of category names. Leave blank for all categories.">
		
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["storeProducts"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
	
	
			arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>
	
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @maxrows int, @siteID int, @storeID int;
			set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;
			SET @siteID = <cfqueryparam value="#local.commonTagVars.siteID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT @storeID = storeID
			FROM dbo.store
			WHERE siteID = @siteID;

			SELECT top (@maxrows) p.ItemID, cl.contentTitle
			FROM dbo.store_Products p
			inner join dbo.cms_contentLanguages as cl on cl.contentID = p.productContentID and cl.languageID = 1
			<cfif len(arguments.category)>
				inner join dbo.store_ProductCategoryLinks as pcl on p.ItemID = pcl.ItemID
				inner join dbo.store_Categories as sc on sc.storeID = @storeID
					and pcl.CategoryID = sc.CategoryID
				inner join dbo.fn_VarCharListToTable(<cfqueryparam value="#arguments.category#" cfsqltype="CF_SQL_VARCHAR">,'|') as li on li.listItem = sc.CategoryName
			</cfif>
			WHERE p.storeID = @storeID
			AND p.status <> 'D'
			ORDER BY p.ItemID desc;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["itemID"] = local.qryData.ItemID>
			<cfset local.thisrow["contentTitle"] = local.qryData.contentTitle>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=store&sa=ViewDetails&ItemID=#local.qryData.itemID#&cat=0">
			<cfset arrayAppend(local.dataStruct["storeProducts"], local.thisrow)>
		</cfloop>
		
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-storeProductList">
					<cfloop array="#local.dataStruct.storeProducts#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#" target="_blank">#local.thisRow.contentTitle#</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-storeProductList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="latestSWODSeminars" returntype="struct" access="public" output="false" hint="Displays a list of SWOD Seminars.">
		<cfargument name="featuredOnly" type="boolean" required="false" default="0" hint="list only featured programs">
		<cfargument name="noresultstext" type="string" required="false" default="There are no SWOD seminars to display.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["SWODSeminars"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
			local.objSWCredits = CreateObject("component","model.seminarweb.SWCredits");
	
			arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfset local.depoMemberDataID = 0/>

		<cfif local.commonTagVars.memberid gt 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>

			<cfif local.depoMemberDataID LT 0>
				<cfset local.depoMemberDataID = 0/>
			</cfif>
		</cfif>

		<cfstoredproc procedure="swod_getSeminarsForCatalogByName" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.commonTagVars.siteCode#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxrows#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.depoMemberDataID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.featuredOnly#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>

		<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="cms_getPlatformFeaturedImagesSetup">
			<cfprocresult name="local.qryPlatformFeaturedImageSetup" resultset="1">
		</cfstoredproc>

		<cfset local.platformFtdImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##lCase(local.qryPlatformFeaturedImageSetup.orgcode)#/#lCase(local.qryPlatformFeaturedImageSetup.sitecode)#/featuredimages/">

		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["contentID"] = local.qryData.contentID>
			<cfset local.thisrow["contentName"] = local.qryData.contentName>
			<cfset local.thisrow["contentSubTitle"] = local.qryData.contentSubTitle>		
			<cfset local.thisrow["format"] = local.qryData.format>
			<cfset local.thisrow["isRegistered"] = local.qryData.isRegistered>
			<cfset local.thisrow["description"] = local.qryData.description>
			<cfset local.thisrow["publisherOrgCode"] = local.qryData.publisherOrgCode>
			<cfset local.thisrow["dateOrigPublished"] = dateformat(local.qryData.dateOrigPublished,"m/d/yyyy") & " " & timeformat(local.qryData.dateOrigPublished, "h:mm tt")>
			<cfset local.thisrow["offerCertificate"] = local.qryData.offerCertificate>
			
			<cfset local.orgSiteFolder = lCase("#local.qryData.featureImageOrgcode#/#local.qryData.featureImageSiteCode#")>
			<cfset local.swodFeaturedImageOriginalPath = '#application.paths.RAIDUserAssetRoot.path##local.orgSiteFolder#/featuredimages/originals/#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#'>

			<!--- Program Image / Site Default Image --->
			<cfif len(local.qryData.featureImageFileExt) and fileExists(local.swodFeaturedImageOriginalPath)>
				<cfset local.swodFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/originals/'>
				<cfset local.swodFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.orgSiteFolder#/featuredimages/thumbnails/'>
				<cfset local.thisrow["featuredimages"] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize'] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize']['url'] = "#local.swodFeaturedImageURL##local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				<cfset local.thisrow["featuredimages"]['fullsize']['width'] = local.qryData.featureImageOriginalWidth>
				<cfset local.thisrow["featuredimages"]['fullsize']['height'] = local.qryData.featureImageOriginalHeight>
				<cfset local.thisrow["featuredimages"]['fullsize']['filename'] = "#local.qryData.featureImageID#.#local.qryData.featureImageFileExt#">
				
				<cfloop list="#local.qryData.imageConfigs#" index="local.thisImageConfig" delimiters="^~~~^">
					<cfset local.imageConfigDataArray = listToArray(local.thisImageConfig,"|")>
					<cfset local.currentSizeID = local.imageConfigDataArray[1]>
					<cfset local.currentSizeCode = local.imageConfigDataArray[2]>
					<cfset local.currentWidth = local.imageConfigDataArray[3]>
					<cfset local.currentHeight = local.imageConfigDataArray[4]>
					<cfset local.currentExt = local.imageConfigDataArray[5]>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#'] = structNew()>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['url'] = "#local.swodFeaturedImageThumbnailURL##local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['width'] = local.currentWidth>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['height'] = local.currentHeight>
					<cfset local.thisrow["featuredimages"]['#local.currentSizeCode#']['filename'] = "#local.qryData.featureImageID#-#local.currentSizeID#.#local.currentExt#">
				</cfloop>

			<!--- Platform Default Image --->
			<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdImgFullRootPath#originals/#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#")>
				<cfset local.ftdImgOrgSiteFolder = lCase("#local.qryPlatformFeaturedImageSetup.orgcode#/#local.qryPlatformFeaturedImageSetup.siteCode#")>
				<cfset local.swodFeaturedImageURL = '#local.commonTagVars.baseUrl#userassets/#local.ftdImgOrgSiteFolder#/featuredimages/originals/'>
				<cfset local.swodFeaturedImageThumbnailURL = '#local.commonTagVars.baseUrl#userassets/#local.ftdImgOrgSiteFolder#/featuredimages/thumbnails/'>
				<cfset local.thisrow["featuredimages"] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize'] = structNew()>
				<cfset local.thisrow["featuredimages"]['fullsize']['url'] = "#local.swodFeaturedImageURL##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#">
				<cfset local.thisrow["featuredimages"]['fullsize']['width'] = local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageWidth>
				<cfset local.thisrow["featuredimages"]['fullsize']['height'] = local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageHeight>
				<cfset local.thisrow["featuredimages"]['fullsize']['filename'] = "#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#">

				<cfset local.thisrow["featuredimages"]['#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeCode#'] = {
					"url":"#local.swodFeaturedImageThumbnailURL##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#",
					"width":local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageWidth,
					"height":local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageHeight,
					"filename":"#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#"
				}>
				
				<cfset local.thisrow["featuredimages"]['#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeCode#'] = {
					"url":"#local.swodFeaturedImageThumbnailURL##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#",
					"width":local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageWidth,
					"height":local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageHeight,
					"filename":"#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#"
				}>

				<cfset local.thisrow["featuredimages"]['#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeCode#'] = {
					"url":"#local.swodFeaturedImageThumbnailURL##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#",
					"width":local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageWidth,
					"height":local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageHeight,
					"filename":"#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#"
				}>

				<cfset local.thisrow["featuredimages"]['#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeCode#'] = {
					"url":"#local.swodFeaturedImageThumbnailURL##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#",
					"width":local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageWidth,
					"height":local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageHeight,
					"filename":"#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageFileExtension#"
				}>
			<cfelse>
				<cfset local.thisrow["featuredimages"] = structNew()>
			</cfif>

			<cfset local.pageLink = "">
			<cfif local.thisRow.format eq "SWOD">
				<cfset local.pageLink = local.pageLink & "panel=showSWOD&seminarid=" & local.thisRow.contentid>
			<cfelse>
				<cfset local.pageLink = local.pageLink & "panel=showBundle&bundleid=" & local.thisRow.contentid>
			</cfif>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=semwebCatalog&" & local.pageLink>
			
			<cfquery name="local.arrGetLearningObjectives" datasource="#application.dsn.tlasites_seminarweb.dsn#" returnType="array">
				 EXEC dbo.sw_getLearningObjectivesByProgramID @programType=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.thisRow.format#">, @programID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.qryData.contentID#">;
			</cfquery>
			<cfset local.thisRow["learningobjectives"] = local.arrGetLearningObjectives.map(function(item) { return arguments.item.objective; })>
			
			<cfset local.thisRow["credits"] = []>
			<cfset local.qryAuthorities = local.objSWCredits.getCreditsGridBySeminar(seminarID=local.thisRow.contentid, siteCode=local.commonTagVars.sitecode).filter(function(row) { return arguments.row.wddxCreditsAvailable <> ''; }, true)>
			<cfloop query="local.qryAuthorities">
				<cfset local.qryCredits = local.objSWCredits.getCreditsFromWDDX(local.qryAuthorities.wddxCreditTypes,local.qryAuthorities.wddxCreditsAvailable,true)>
				<cfif local.qryCredits.recordcount>
					<cfset local.authorityStruct = { 
						"authorityname":local.qryAuthorities.authorityName, 
						"authoritycode":local.qryAuthorities.authorityCode, 
						"approvalnum":local.qryAuthorities.courseApproval, 
						"credittypes":[],
						"creditstatus":local.qryAuthorities.status, 
						"sponsorname":local.qryAuthorities.sponsorName,
						"datecreditstart":dateformat(local.qryAuthorities.creditOfferedStartDate,"m/d/yyyy"),
						"datecreditend":dateformat(local.qryAuthorities.creditOfferedEndDate,"m/d/yyyy"),
						"datecompleteby":dateformat(local.qryAuthorities.creditCompleteByDate,"m/d/yyyy")
					}>
					<cfloop query="local.qryCredits">
						<cfset local.authorityStruct.credittypes.append({ "type":local.qryCredits.displayName, "amount":local.qryCredits.numCredits })>
					</cfloop>
					<cfset arrayAppend(local.thisRow["credits"], local.authorityStruct)>
				</cfif>
			</cfloop>

			<cfset arrayAppend(local.dataStruct["SWODSeminars"], local.thisrow)>
		</cfloop>
		
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-SWODSeminarList">
					<cfloop array="#local.dataStruct.SWODSeminars#" index="local.thisRow">
						<li class="mc-mergeTagListItem">
							<a href="#local.commonTagVars.baseUrl#?pg=semwebCatalog&panel=<cfif local.thisRow.format eq "SWOD">showSWOD&seminarid=<cfelse>showBundle&bundleid=</cfif>#local.thisRow.contentid#">
								#local.thisRow.contentName#
							</a>
						</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-SWODSeminarList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="upcomingSWL" returntype="struct" access="public" output="false" hint="Displays a list of upcoming SWL.">
		<cfargument name="noresultstext" type="string" required="no" default="There are no upcoming SWL seminars to display.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["upcomingSWL"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

			arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>

		<cfset local.depoMemberDataID = 0>

		<cfif local.commonTagVars.memberid gt 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>

			<cfif local.depoMemberDataID LT 0>
				<cfset local.depoMemberDataID = 0>
			</cfif>
		</cfif>

		<cfset local.strAssociation = CreateObject("component","model.seminarweb.SWParticipants").getAssociationDetails(local.commonTagVars.siteCode)>
		<cfif structKeyExists(local.strAssociation, "qryAssociation") and local.strAssociation.qryAssociation.isSWL is 1>
			<cfset local.qrySWP = local.strAssociation.qryAssociation>
			<cfset local.swRegCart = CreateObject("component","model.semWebCatalog.semwebReg").swRegCartToQuery()>
			<cfset local.strFilters = { 
				"keywordsList":"",
				"formats":"swl",
				"sortoption":"date",
				"participantIDList":"",
				"maxrows":arguments.maxRows,
				"subjects":"",
				"authorIDList":""
			}>
			<cfset local.strPrograms = CreateObject("component","model.semWebCatalog.browse").getSWPrograms(
				siteID=local.commonTagVars.siteID, catalogOrgCode=local.commonTagVars.siteCode, memberID=local.commonTagVars.memberid, 
				depoMemberDataID=local.depoMemberDataID, qrySWP=local.qrySWP, strAssociation=local.strAssociation, swRegCart=local.swRegCart, 
				strFilters=local.strFilters, mode="mergecode")>

			<cfloop array="#local.strPrograms.arrPrograms#" index="local.thisProgram">
				<cfset local.thisrow = structNew()>
				<cfset local.thisrow["contentID"] = local.thisProgram.programID>
				<cfset local.thisrow["contentName"] = local.thisProgram.programTitle>			
				<cfset local.thisrow["format"] = local.thisProgram.ft>
				<cfset local.thisrow["isRegistered"] = local.thisProgram.isRegistered>
				<cfset local.thisrow["dateStart"] = local.thisProgram.dspStartDate>
				<cfset local.thisrow["dateEnd"] = local.thisProgram.dspEndDate>
				<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=semwebcatalog&panel=showLive&seminarid=#local.thisProgram.programID#">
				<cfset local.thisrow["featuredimagePath"] = local.thisProgram.featuredImagePath>
				<cfset arrayAppend(local.dataStruct["upcomingSWL"], local.thisrow)>
			</cfloop>
		</cfif>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif arrayLen(local.strPrograms.arrPrograms)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-upcomingSWLList">
					<cfloop array="#local.dataStruct.upcomingSWL#" index="local.thisRow">
						<li class="mc-mergeTagListItem"><a href="#local.thisRow.pageLink#">#local.thisRow.contentName#</a></li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-upcomingSWLList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="myStorePurchases" returntype="struct" access="public" output="false" hint="Displays a list of the member's store purchases.">
		<cfargument name="noresultstext" type="string" required="no" default="You have no recent purchases to display.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["storePurchases"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
	
	
			arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;


			declare @orgID int, @maxrows int;
			set @orgID = <cfqueryparam value="#local.commonTagVars.orgID#" cfsqltype="CF_SQL_INTEGER">;
			set @maxrows = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;

			IF OBJECT_ID('tempdb..##tmpOrders') IS NOT NULL 
				DROP TABLE ##tmpOrders;

			SELECT top (@maxrows) o.orderID, o.DateOfOrder, o.orderNumber 
			INTO ##tmpOrders
			FROM dbo.store_orders as o
			INNER JOIN dbo.store as s on s.storeID = o.storeID and s.siteID = #local.commonTagVars.siteID#
			INNER JOIN dbo.ams_members as m on m.memberid = o.memberID 
			WHERE o.orderCompleted = 1	
			and m.activeMemberID = <cfqueryparam value="#local.commonTagVars.memberid#" cfsqltype="CF_SQL_INTEGER">
			order by o.DateOfOrder desc;

			select orderID, DateOfOrder, orderNumber, isnull(ttl.totalFee,0) as totalFee
			from ##tmpOrders as tmp
			cross apply (
				select sum(ts.cache_amountAfterAdjustment) as totalFee
				from dbo.fn_store_orderTransactions(@orgID,tmp.orderid) as rt
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = rt.transactionID
			) as ttl
			ORDER BY tmp.DateOfOrder desc;

			IF OBJECT_ID('tempdb..##tmpOrders') IS NOT NULL 
				DROP TABLE ##tmpOrders;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfloop query="local.qryData">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["orderID"] = local.qryData.orderID>
			<cfset local.thisrow["DateOfOrder"] = dateformat(local.qryData.DateOfOrder,"m/d/yyyy") & " " & timeformat(local.qryData.DateOfOrder, "h:mm tt")>
			<cfset local.thisrow["orderNumber"] = local.qryData.orderNumber>
			<cfset local.thisrow["totalFee"] = local.qryData.totalFee>
			<cfset local.thisrow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=store&sa=viewReceipt&ordernumber=#local.qryData.orderNumber#">
			<cfset arrayAppend(local.dataStruct["storePurchases"], local.thisrow)>
		</cfloop>
		
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-storePurchaseList">
					<cfloop array="#local.dataStruct.storePurchases#" index="local.thisRow">
						<li class="mc-mergeTagListItem">
							#dateformat(local.thisRow.DateOfOrder,"mm/d/yyyy")# - <a href="#local.thisRow.pageLink#">#local.commonTagVars.siteCode##Numberformat(local.thisRow.orderID,"0000")#</a><br/>
							#dollarFormat(local.thisRow.totalFee)#
						</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-storePurchaseList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="myRecentPayments" returntype="struct" access="public" output="false" hint="Displays a list of the member's recent payments and associated invoices.">
		<cfargument name="noresultstext" type="string" required="no" default="You have no recent payments to display.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="paymentProfile" type="string" required="false" default="" hint="Pipe-delimited list of payment profile codes. Leave blank for all payment profiles.">
		
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["recentPayments"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
	
	
			arguments.maxRows = max(arguments.maxRows,1);
		</cfscript>
		
		<cfstoredproc procedure="cms_processMergeCode_MyRecentPayments" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(arguments.maxrows)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.orgID)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.paymentProfile#">
			<cfprocresult name="local.qryData" resultset="1">
		</cfstoredproc>	

		<cfoutput query="local.qryData" group="transactionID">
			<cfset local.thisrow = structNew()>
			<cfset local.thisrow["amount"] = local.qryData.amount>
			<cfset local.thisrow["detail"] = local.qryData.detail>
			<cfset local.thisrow["transactionDate"] = dateformat(local.qryData.transactionDate,"m/d/yyyy") & " " & timeformat(local.qryData.transactionDate, "h:mm tt")>
			<cfset local.thisrow["transactionID"] = local.qryData.transactionID>
			<cfset local.thisrow["invoices"] = arraynew(1)>
			<cfoutput>
				<cfset local.subRow = structNew()>
				<cfset local.stInvEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#local.qryData.invoiceNumber#|#right(GetTickCount(),5)#|#local.qryData.invoiceCode#","M3mbeR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.subRow["pageLink"] = "#local.commonTagVars.baseUrl#?pg=invoices&va=show&item=#local.stInvEnc#">
				<cfset local.subRow["invoiceNumber"] = local.qryData.invoiceNumber>
				<cfset arrayAppend(local.thisrow["invoices"], local.subRow)>
			</cfoutput>
			<cfset arrayAppend(local.dataStruct["recentPayments"], local.thisrow)>
		</cfoutput>
		
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryData.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<ul class="mc-mergeTagList mc-paymentsList">
					<cfloop array="#local.dataStruct.recentPayments#" index="local.thisRow">
						<li class="mc-mergeTagListItem">
							#dollarformat(local.thisRow.amount)# #local.thisRow.detail# on #dateformat(local.thisRow.transactionDate,"m/d/yyyy")#
							<cfif arrayLen(local.thisRow.invoices)>
								<div class="mc-invoiceList">
									<ul>
									<cfloop array="#local.thisRow.invoices#" index="local.subRow">
										<li class="mc-invoice"><a href="#local.subRow.pageLink#">#local.subRow.invoiceNumber#</a></li>
							        </cfloop>
									</ul>
								</div>
							</cfif>
						</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-paymentsList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="getSponsorAds" access="public" returntype="struct" output="false" hint="Displays Sponsor Ads for a site.">
		<cfargument name="zoneType" type="string" required="true" hint="zone type name for ads">
		<cfargument name="numberOfAds" type="numeric" required="false" default="5">
		<cfargument name="uniqueCode" type="string" required="false" default="" hint="helps select the correct zone for zonetypes with multiple zones per type">
		<cfargument name="target" type="string" required="false" default="_blank">
		<cfargument name="noresultstext" type="string" required="false" default="There are no ads to display.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["ads"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
		</cfscript>

		<cfset local.resultStruct = application.objCommon.getSponsorAds(zoneType=arguments.zoneType,numberOfAds=arguments.numberOfAds,uniqueCode=arguments.uniqueCode,siteCode=local.commonTagVars.siteCode,advanceRotation=true, baseurl=local.commonTagVars.baseUrl)>


		<cfif local.resultStruct.success eq true>
			<cfset local.dataStruct["ads"] = local.resultStruct.arrAds>

			<cfif arguments.format eq "json">
				<cfset local.data["dataStruct"] = local.dataStruct>
			<cfelseif arrayLen(local.dataStruct["ads"])>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-sponsorAdsList">
						<cfloop array="#local.dataStruct.ads#" index="local.thisAd">
							<div class="mc-mergeTagListItem" style="margin-bottom:8px;">
								<a href="#local.thisAd.adLink#" title="Click for more information" target="#arguments.target#">
									<img src="#replaceNoCase(local.thisAd.imageUrl,'*SITECODE*',local.commonTagVars.siteCode)#" style="width:#local.thisAd.width#px;height:#local.thisAd.height#px;" alt="#local.thisAd.adShortText#"/>
								</a>
							</div>
						</cfloop>
					</div>
					</cfoutput>
				</cfsavecontent>
			<cfelseif len(arguments.noresultstext)>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-sponsorAdsList mc-noDataMessageContainer">
						<span class="mc-noDataMessage">#arguments.noresultstext#</span>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="groupMembers" access="public" returntype="struct" hint="Displays members of a group.">
		<cfargument name="groupuid" type="string" required="true" default="" hint="Pipe-delimited list of Group UIDs.">
		<cfargument name="fieldSetuid" type="string" required="true" default="" hint="one fieldset UID">
		<cfargument name="groupSetuid" type="string" required="false" default="" hint="Pipe-delimited list of group set UIDs">
		<cfargument name="memberphoto" type="string" required="false" default="yes" hint="yes or no">
		<cfargument name="maxRows" type="string" required="false" default="">
		<cfargument name="sortmethod" type="string" required="false" default="lastname" hint="lastname or company">
		<cfargument name="order" type="string" required="false" default="ascending" hint="ascending or descending">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="noresultstext" type="string" required="false" default="There are no members.">
		
		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = arguments.format>
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = structNew()>
		<cfset local.dataStruct["groupMembers"] = arrayNew(1)>
		<cfset local.isValidParams = true>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>

		<cfif NOT application.objCommon.validateUIDList(inputString=arguments.groupuid) OR
			NOT isValid("guid", ListFirst(arguments.fieldSetuid,"|")) OR
			NOT application.objCommon.validateUIDList(inputString=arguments.groupSetuid)>
			<cfset local.isValidParams = false>
		</cfif>

		<cfset arguments.groupuid = replace(arguments.groupuid,"|",",", "ALL")>
		<cfset arguments.groupSetuid = replace(arguments.groupSetuid,"|",",", "ALL")>
		<cfset arguments.fieldSetuid = ListFirst(arguments.fieldSetuid,"|")>
		
		<cfif len(arguments.maxRows)>
			<cfset arguments.maxRows = max(arguments.maxRows,1)>
			<cfset arguments.maxRows = val(arguments.maxRows)>
		</cfif>
		<cftry>
			<cfif NOT local.isValidParams>
				<cfset local.qryGroupMembers = queryNew('')>
			<cfelse>
				<cfquery name="local.qryGroupMembers" datasource="#application.dsn.memberCentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						IF OBJECT_ID('tempdb..##tmpGroupMembersSearch') IS NOT NULL 
							DROP TABLE ##tmpGroupMembersSearch;
						CREATE TABLE ##tmpGroupMembersSearch (memberID int, autoID bigint, memberNumber varchar(50));
						IF OBJECT_ID('tempdb..##tmpGroups') IS NOT NULL 
							DROP TABLE ##tmpGroups;
						CREATE TABLE ##tmpGroups (groupID int);
						IF OBJECT_ID('tempdb..##tmpGroupsInGroupSets') IS NOT NULL 
							DROP TABLE ##tmpGroupsInGroupSets;
						CREATE TABLE ##tmpGroupsInGroupSets (groupID int, groupSetID int);

						DECLARE @orgID int, @fCompany varchar(200), @fAssociatedMemberID int, @fAssociatedGroupID int, @maxrows int,@groupid varchar(200);
						SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.orgID#">;
						<cfif len(arguments.maxRows) AND val(arguments.maxRows)>
							SET @maxrows = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.maxRows#">;
						</cfif>

						<cfif len(arguments.groupSetuid)>
							INSERT INTO ##tmpGroupsInGroupSets (groupID, groupSetID)
							SELECT mgsg.groupID, mgs.groupSetID 
							FROM dbo.ams_memberGroupSets mgs 
							inner join dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupsetid = mgs.groupSetid 
							inner join dbo.ams_groups as g on g.orgID = @orgID 
								and g.groupID = mgsg.groupID 
							WHERE mgs.uid IN (<cfqueryparam value="#arguments.groupSetuid#" cfsqltype="CF_SQL_VARCHAR" list="YES">);
						</cfif>

						<cfif len(arguments.groupuid)>
							INSERT INTO ##tmpGroups (groupID)
							SELECT groupID 
							FROM dbo.ams_groups
							WHERE orgID = @orgID
							AND [uid] IN (<cfqueryparam value="#arguments.groupuid#" cfsqltype="CF_SQL_VARCHAR" list="YES">);
						</cfif>

						INSERT INTO ##tmpGroupMembersSearch (memberID, autoID, memberNumber)
						SELECT distinct m.memberID, mg.autoID, m.memberNumber
						FROM dbo.cache_members_groups as mg
						INNER JOIN dbo.ams_members as m on m.orgID = @orgID 
							and m.memberid = mg.memberid 
							AND m.memberid = m.activememberID
							AND m.status = 'A'
							AND m.isProtected = 0
						INNER JOIN dbo.ams_groups as g on g.orgID = @orgID 
							AND g.groupID = mg.groupID
							<cfif len(arguments.groupuid)>
								AND g.groupid in (select groupid from ##tmpGroups)
							</cfif>;
						
						DECLARE @posStart int, @posStartAndCount int, @resultsFSID int, @outputFieldsXML xml, 
							@totalCount int, @isProtected bit, @canEditProtected bit;
						SELECT @resultsFSID = fieldSetID FROM ams_memberFieldSets WHERE UID IN ('#arguments.fieldsetUID#');

						SELECT @totalCount = count(*) from ##tmpGroupMembersSearch;

						IF OBJECT_ID('tempdb..##tmpGrpMembers') IS NOT NULL 
							DROP TABLE ##tmpGrpMembers;
						IF OBJECT_ID('tempdb..##tmpGrpMembersWithFS') IS NOT NULL 
							DROP TABLE ##tmpGrpMembersWithFS;
						CREATE TABLE ##tmpGrpMembers (memberID int , lastname varchar(75), firstname varchar(75), 
							membernumber varchar(50), MCAccountStatus char(1), hasMemberPhotoThumb bit, company varchar(200), groupIDs varchar(max), groups varchar(max));
						CREATE TABLE ##tmpGrpMembersWithFS (MFSAutoID int IDENTITY(1,1) not null);

						insert into ##tmpGrpMembers (memberID, lastname, firstname, membernumber, MCAccountStatus, hasMemberPhotoThumb, company, groupIDs, groups)
						select distinct
						<cfif len(arguments.maxRows) AND val(arguments.maxRows)>
							TOP(@maxRows)
						</cfif>
							m.memberid, m.lastname, m.firstname, m.membernumber, m.status, m.hasMemberPhotoThumb, m.company,
							<cfif len(arguments.groupSetuid)>
								ISNULL(STUFF((
									SELECT ',' + cast(g.groupID as varchar) 
									FROM dbo.ams_groups g
									INNER JOIN dbo.ams_memberGroupSetGroups gsg on gsg.groupID = g.groupid
										AND gsg.groupSetID in (select groupSetID from ##tmpGroupsInGroupSets)
									WHERE g.orgID = @orgID
									AND g.groupid in (select groupid from ##tmpGroupsInGroupSets) 
									AND g.groupid in (select groupid from dbo.cache_members_groups where memberID = tmp.memberID)
									FOR XML PATH('')), 1, 1, ''), '')
							<cfelse>''</cfif> as groupIDs,
							<cfif len(arguments.groupSetuid)>
								ISNULL(STUFF((
									SELECT ',' + case when gs.labelOverride is not null then gs.labelOverride else g.groupName end 
									FROM dbo.ams_groups g
									inner join dbo.ams_memberGroupSetGroups gs on gs.groupID = g.groupid
									WHERE g.orgID = @orgID
									AND g.groupid in (select groupid from ##tmpGroupsInGroupSets) 
									and gs.groupSetID in (select groupSetID from ##tmpGroupsInGroupSets)
									AND g.groupid in (select groupid from cache_members_groups where memberID = tmp.memberID)
									FOR XML PATH, TYPE).value(N'.[1]', N'nvarchar(max)'),1, 1, N''), '') 
							<cfelse>''</cfif> as groups
						from ##tmpGroupMembersSearch as tmp
						inner join dbo.cache_members_groups as mg on mg.orgID = @orgID and mg.autoID = tmp.autoID 
						inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = tmp.memberID;

						-- get fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company,m_status',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpGrpMembers', @membersResultTableName='##tmpGrpMembersWithFS',
							@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
						
						-- return @outputFieldsXML in only the first row to reduce the query payload
						SELECT *, CASE WHEN mc_row = 1 THEN @outputFieldsXML ELSE NULL END AS mc_outputFieldsXML
						FROM (
							SELECT DISTINCT tmp.firstname, tmp.lastname, tmp.membernumber, tmp.hasMemberPhotoThumb, tmp.groupIDs, tmp.groups, tmp.company, tmp.MCAccountStatus,
								tmpM.*, 
								ROW_NUMBER() OVER (
									ORDER BY
									<cfif len(arguments.sortmethod)>
										<cfif arguments.sortmethod EQ "lastname">
											tmp.lastname
										<cfelseif arguments.sortmethod EQ "company">
											tmp.company
										<cfelse>
											tmp.lastname
										</cfif>
									<cfelse>
										tmp.lastname
									</cfif>
									<cfif len(arguments.order)>
										<cfif arguments.order EQ "ascending">
											asc
										<cfelseif arguments.order EQ "descending">
											desc
										</cfif>
									</cfif>
								) AS mc_row
							FROM ##tmpGrpMembers as tmp
							INNER JOIN ##tmpGrpMembersWithFS as tmpM on tmpM.memberID = tmp.memberID
						) tmp
						ORDER BY mc_row;

						IF OBJECT_ID('tempdb..##tmpGrpMembers') IS NOT NULL 
							DROP TABLE ##tmpGrpMembers;
						IF OBJECT_ID('tempdb..##tmpGrpMembersWithFS') IS NOT NULL 
							DROP TABLE ##tmpGrpMembersWithFS;
						IF OBJECT_ID('tempdb..##tmpGroupMembersSearch') IS NOT NULL 
							DROP TABLE ##tmpGroupMembersSearch;
						IF OBJECT_ID('tempdb..##tmpGroupsInGroupSets') IS NOT NULL
							DROP TABLE ##tmpGroupsInGroupSets;
						IF OBJECT_ID('tempdb..##tmpGroups') IS NOT NULL
							DROP TABLE ##tmpGroups;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>

			<cfif local.qryGroupMembers.recordCount>
				<cfset local.xmlResultFields = local.qryGroupMembers.mc_outputFieldsXML[1]>
				<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>
				
				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					SELECT *
					FROM [local].qryOutputFields
					WHERE fieldcodeSect NOT IN ('mc','ma','mat','mp','mpt')
					AND fieldCode NOT IN ('m_firstname','m_lastname','m_company')
				</cfquery>

				<cfset local.companyInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_company']")>
				<cfset local.memberNumberInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membernumber']")>
				<cfset local.RecordTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_recordtypeid']")>
				<cfset local.memberTypeInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_membertypeid']")>
				<cfset local.memberStatusInFS = XMLSearch(local.xmlResultFields,"//field[@fieldCode='m_status']")>
				<cfset local.LastLoginDateInFS = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,17)='ml_datelastlogin_']")>
				
				<cfset local.orgPhoneTypes = application.objOrgInfo.getOrgPhoneTypes(orgID=local.commonTagVars.orgID, includeTags=1)>
				<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=local.commonTagVars.orgID, includeTags=1)>
				<cfset local.strOrgAddressTypes = structNew()>
				<cfloop query="local.orgAddressTypes">
					<cfif local.orgAddressTypes.isTag is 1>
						<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
					<cfelse>
						<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
					</cfif>
				</cfloop>
				<cfif arguments.format NEQ "json">
					<cfset local.mc_combinedAddresses = structNew()>
					<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
					<cfloop array="#local.tmp#" index="local.thisField">
						<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
						<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
							<cfset local.strKey = "t#local.thisATID#">
						<cfelse>
							<cfset local.strKey = local.thisATID>
						</cfif>
						<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
							<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
						</cfif>
					</cfloop>
				</cfif>

				<cfloop query="local.qryGroupMembers" group="memberid">
					<cfset local.thisrow = structNew('ordered')>

					<cfif arguments.format eq "json">
						<!--- combine address fields if there are any --->
						<cfset local.addressTypeStruct = StructNew()>
						<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
						<cfloop array="#local.tmp#" index="local.thisField">
							<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
							<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
								<cfset local.strKey = "t#local.thisATID#">
							<cfelse>
								<cfset local.strKey = local.thisATID>
							</cfif>
							<cfset local.strOrgAddressTypes[local.strKey] =  replace(local.strOrgAddressTypes[local.strKey]," ","_","ALL")>
							
							<cfset local.addressTypeStruct[local.strOrgAddressTypes[local.strKey]] = StructNew()>					
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>
		
							<cfloop list="address1,address2,address3,city,stateprov,postalcode,county,country" index="local.item">
								<cfset local.tmpArr = ArrayNew(1)>
								<cfset local.tmpArr = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix##local.item#']")>
								<cfif arrayLen(local.tmpArr) is 1 and len(local.qryGroupMembers[local.tmpArr[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
									<cfset local.addressTypeStruct[local.strOrgAddressTypes[local.strKey]][replace(local.tmpArr[1].xmlAttributes.FieldLabel," ","_","ALL")] = local.qryGroupMembers[local.tmpArr[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
								</cfif>
							</cfloop>
							
							<cfloop list="#valueList(local.orgPhoneTypes.phoneTypeID)#" index="local.item">
								<cfset local.tmpArr = ArrayNew(1)>
								<cfset local.tmpArr = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MPfcPrefix##local.item#']")>
								<cfif arrayLen(local.tmpArr) is 1 and len(local.qryGroupMembers[local.tmpArr[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
									<cfset local.addressTypeStruct[local.strOrgAddressTypes[local.strKey]][replace(local.tmpArr[1].xmlAttributes.FieldLabel," ","_","ALL")] = local.qryGroupMembers[local.tmpArr[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
								</cfif>
							</cfloop>
						</cfloop>
						<cfset local.thisrow['mc_combinedAddresses'] = local.addressTypeStruct>
					<cfelse>
						<!--- combine address fields if there are any --->
						<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
						<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
							<cfsavecontent variable="local.thisATFull">
								<cfoutput>
								<cfif left(local.thisATID,1) eq "t">
									<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
									<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfelse>
									<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
									<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
								</cfif>

								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>#local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#<br/> </cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>#local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#<br/> </cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>#local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>#local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#</cfif>
								<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
								<cfif arrayLen(local.tmp2) is 1 and len(local.qryGroupMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>, #local.qryGroupMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]# </cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])> #local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>#local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]# County<br/></cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
								<cfif arrayLen(local.tmp) is 1 and len(local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])> #local.qryGroupMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#<br/></cfif>
								<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
								<cfloop array="#local.tmp#" index="local.thisPT">
									<cfif len(local.qryGroupMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
										<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryGroupMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]#</div>
									</cfif>
								</cfloop>
								</cfoutput>
							</cfsavecontent>
							<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
							<cfif left(local.thisATfull,2) eq ", ">
								<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
							</cfif>
							<cfif len(local.thisATfull)>
								<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
							<cfelse>
								<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
							</cfif>
						</cfloop>
					</cfif>

					<cfif arguments.format eq "json" and len(local.qryGroupMembers["company"][local.qryGroupMembers.currentrow])>
						<cfset local.thisrow['mc_company'] = local.qryGroupMembers["company"][local.qryGroupMembers.currentrow]>
					<cfelse>
						<!--- get company if available --->
						<cfif arrayLen(local.companyInFS) is 1 and len(local.qryGroupMembers[local.companyInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
							<cfset local.thisrow['mc_company'] = local.qryGroupMembers[local.companyInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
						</cfif>
					</cfif>

					<cfif arguments.format eq "json">
						<cfset local.thisrow['mc_membernumber'] = local.qryGroupMembers["memberNumber"][local.qryGroupMembers.currentrow]>
					<cfelse>
						<!--- get membernumber if available --->
						<cfif arrayLen(local.memberNumberInFS) is 1 and len(local.qryGroupMembers[local.memberNumberInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
							<cfset local.thisrow['mc_membernumber'] = '<div>#local.memberNumberInFS[1].xmlAttributes.FieldLabel#: ' &
								local.qryGroupMembers[local.memberNumberInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow] & '</div>'>
						</cfif>
					</cfif>

					<!--- get recordtype if available --->
					<cfif arrayLen(local.RecordTypeInFS) is 1 and len(local.qryGroupMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
						<cfset local.thisrow['mc_recordType'] = local.qryGroupMembers[local.RecordTypeInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
					</cfif>
				
					<!--- get membertypeid if available --->
					<cfif arrayLen(local.memberTypeInFS) is 1 and len(local.qryGroupMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
						<cfset local.thisrow['mc_memberType'] = local.qryGroupMembers[local.memberTypeInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
					</cfif>
				
					<cfif arguments.format eq "json">
						<cfset local.thisrow['mc_memberStatus'] = local.qryGroupMembers["MCAccountStatus"][local.qryGroupMembers.currentrow]>
					<cfelse>
						<!--- get status if available --->
						<cfif arrayLen(local.memberStatusInFS) is 1 and len(local.qryGroupMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
							<cfset local.thisrow['mc_memberStatus'] = local.qryGroupMembers[local.memberStatusInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow]>
						</cfif>
					</cfif>

					<!--- get last login date if available --->
					<cfif arrayLen(local.LastLoginDateInFS) is 1>
						<cfif arguments.format neq "json">
							<cfif len(local.qryGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
								<cfset local.thisrow['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: #DateFormat(local.qryGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow],"m/d/yy")#'>
							<cfelse>
								<cfset local.thisrow['mc_lastlogin'] = '#local.LastLoginDateInFS[1].xmlAttributes.FieldLabel#: <span class="dim"><i>none</i></span>'>
							</cfif>
						<cfelse>
							<cfif len(local.qryGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow])>
								<cfset local.thisrow['mc_lastlogin'] = '#DateFormat(local.qryGroupMembers[local.LastLoginDateInFS[1].xmlAttributes.FieldLabel][local.qryGroupMembers.currentrow],"m/d/yy")#'>
							<cfelse>
								<cfset local.thisrow['mc_lastlogin'] = 'none'>
							</cfif>
						</cfif>
					</cfif>				

					<cfif arguments.format NEQ "json">
						<cfif StructCount(local.thisMem_mc_combinedAddresses)>
							<cfsavecontent variable="local.thisMemCombinedAddress">
								<cfoutput>
								<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
									<div>#local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#</div>
								</cfloop>
								</cfoutput>
							</cfsavecontent>
							<cfset local.thisrow['mc_combinedAddresses'] = rereplace(replace(replace(local.thisMemCombinedAddress,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
						</cfif>
					</cfif>
					
					<cfif local.qryOutputFieldsForLoop.recordCount>
						<cfif arguments.format eq "json">
							
							<cfset local.thisrow['mc_extraInfo'] = StructNew()>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfset local.currValue = local.qryGroupMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryGroupMembers.currentrow]>
								<cfset local.indExtraInfo = StructNew()>
								<cfsavecontent variable="local.indExtraInfo['#htmlEditFormat(replace(local.qryOutputFieldsForLoop.fieldLabel," ","_","all"))#']">
									<cfoutput>
										<cfif len(local.currValue)>
											<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
												#dollarFormat(local.currValue)#
											<cfelse>
												<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
													<cfcase value="DATE">
														#dateFormat(local.currValue,"m/d/yyyy")#
													</cfcase>
													<cfcase value="STRING,DECIMAL2,INTEGER">
														#local.currValue#
													</cfcase>
													<cfcase value="CONTENTOBJ">
														#application.objCommon.getCleanContentData(local.currValue)#
													</cfcase>
													<cfcase value="BIT">
														#YesNoFormat(local.currValue)#
													</cfcase>
												</cfswitch>
											</cfif>
										</cfif>
									</cfoutput>
								</cfsavecontent>
								<cfset local.indExtraInfo['#htmlEditFormat(replace(local.qryOutputFieldsForLoop.fieldLabel," ","_","all"))#'] = rereplace(replace(replace(local.indExtraInfo['#htmlEditFormat(replace(local.qryOutputFieldsForLoop.fieldLabel," ","_","all"))#'],'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
								<cfset StructAppend(local.thisrow['mc_extraInfo'],local.indExtraInfo) >
							</cfloop>
						<cfelse>
							<cfsavecontent variable="local.thisMemExtraInfo">
								<cfoutput>
								<cfloop query="local.qryOutputFieldsForLoop">
									<cfset local.currValue = local.qryGroupMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryGroupMembers.currentrow]>
									<cfif len(local.currValue)>
										<div>
											#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
											<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
												#dollarFormat(local.currValue)#
											<cfelse>
												<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
													<cfcase value="DATE">
														#dateFormat(local.currValue,"m/d/yyyy")#
													</cfcase>
													<cfcase value="STRING,DECIMAL2,INTEGER,CONTENTOBJ">
														#local.currValue#
													</cfcase>
													<cfcase value="BIT">
														#YesNoFormat(local.currValue)#
													</cfcase>
												</cfswitch>
											</cfif>
										</div>
									</cfif>
								</cfloop>
								</cfoutput>
							</cfsavecontent>
							<cfset local.thisrow['mc_extraInfo'] = rereplace(replace(replace(local.thisMemExtraInfo,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
						</cfif>
					</cfif>
					
					<cfif arguments.format EQ "json">
						<cfset local.thisrow['memberid'] = local.qryGroupMembers.memberID>
						<cfset local.thisrow["groups"] = ListToArray(HTMLEditFormat(local.qryGroupMembers.groups),',',false,true)>
						<cfset local.thisrow["firstname"] = HTMLEditFormat(local.qryGroupMembers.firstname)>
						<cfset local.thisrow["lastname"] = HTMLEditFormat(local.qryGroupMembers.lastname)>
						<cfset local.thisrow["hasMemberPhotoThumb"] = HTMLEditFormat(local.qryGroupMembers.hasMemberPhotoThumb)>
						<cfset local.thisrow["mc_accountStatus"] = HTMLEditFormat(local.qryGroupMembers.MCAccountStatus)>
					</cfif>
					
					<cfif local.qryGroupMembers.hasMemberPhotoThumb>
						<cfset local.thisrow['photourl'] = "/memberphotosth/" & LCASE(local.qryGroupMembers.membernumber) & ".jpg">
					<cfelse>
						<cfset local.thisrow['photourl'] = "/assets/common/images/directory/default.jpg">
					</cfif>

					<cfset local.thisrow['mc_combinedName'] = local.qryGroupMembers['Extended Name'][local.qryGroupMembers.currentrow]>
					
					<cfset arrayAppend(local.dataStruct["groupMembers"], local.thisrow)>
				</cfloop>
			</cfif>

			<cfif arguments.format eq "json">
				<cfset local.allGroupMemberStruct = StructNew("ordered")>
				<cfset local.allGroupMemberStruct["groupmembers"] = StructNew()>
				<cfset local.allGroupMemberStruct["groupmembers"] = local.dataStruct.groupMembers>
				<cfset local.data["dataStruct"] = local.allGroupMemberStruct>
			<cfelseif local.qryGroupMembers.recordCount>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
						<ul class="mc-mergeTagList mc-groupmembers" style="list-style:none;margin-left:5px;">
							<cfloop array="#local.dataStruct.groupMembers#" index="local.thisRow">
								<li class="mc-mergeTagListItem">
									<cfset local.otherFields = "">
									<cfset local.combinedName ="">
									<cfset local.company ="">
									<cfset local.photoUrl = "">
									<cfloop list="#StructKeyList(local.thisRow)#" index="local.item">
										<cfif local.item CONTAINS "mc_combinedName">
											<cfset local.combinedName = '<div>#local.thisRow['#local.item#']#</div>'>
										<cfelseif  local.item CONTAINS "mc_company">
											<cfset local.company = '<div>#local.thisRow['#local.item#']#</div>'>
										<cfelseif  local.item CONTAINS "photourl">
											<cfset local.photoUrl = '<img src="#local.thisRow['#local.item#']#">'>
										<cfelse>
											<cfset local.otherFields = local.otherFields & '<div>#local.thisRow['#local.item#']#</div>'>
										</cfif>
									</cfloop>

									<div style="display:table;margin:10px;width:100%">
										<cfif arguments.memberphoto EQ "yes">
											<div style="float:left;height:100px;width:80px;">#local.photoUrl#</div>
										</cfif>
										<div style="float:left;margin:5px;">												
											<div>
												#local.combinedName#
												#local.company#
												#local.otherFields#
											</div>
										</div>
									</div>
								</li>
							</cfloop>
						</ul>
					</cfoutput>
				</cfsavecontent>
				
			<cfelseif len(arguments.noresultstext)>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-groupmembers mc-noDataMessageContainer">
						<span class="mc-noDataMessage">#arguments.noresultstext#</span>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>
			<cfcatch type="any">	
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.data)>
			</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="DisplayReferralGrid" access="public" returntype="struct" hint="Displays Referral Grid." defaultJSONVariableName="referrals">
		<cfargument name="status" type="string" required="false" default="" hint="Pipe-delimited list of status codes. Leave blank for all statuses.">
		<cfargument name="maxRows" type="string" required="false" default="5">
		<cfargument name="referralDateOrder" type="string" required="false" default="descending" hint="ascending or descending">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="noresultstext" type="string" required="false" default="There are no referrals to display.">
		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";
			local.dataStruct = structNew();
			
			local.objAdminReferrals	= CreateObject("component","model.admin.referrals.referrals");
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
			local.qryReferralSettings = local.objAdminReferrals.getReferralSettings(local.commonTagVars.siteID);
			if(len(arguments.maxRows)){
				arguments.maxRows = max(arguments.maxRows,1);
				arguments.maxRows = val(arguments.maxRows);
			} else {
				arguments.maxRows = 5;
			}
			local.memberExternalLink = "#local.commonTagVars.baseUrl#?#application.objApplications.getAppBaseLink(applicationInstanceID=local.qryReferralSettings.applicationInstanceID, siteID=local.commonTagVars.siteID)#";
			local.dataStruct["referrals"] = arrayNew(1);
			local.tblName = "####tmpReferralCF#replace(createUUID(),'-','','ALL')#";
		</cfscript>

		<cfquery name="local.qryReferraldata" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @referralID int;
			select referralID
			from ref_referrals r
			inner join 	cms_applicationInstances ai on
				ai.applicationInstanceID = r.applicationInstanceID		
				and ai.siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.commonTagVars.siteID#" />
			inner join cms_applicationTypes at on 
				at.applicationTypeID = ai.applicationTypeID
			inner join sites s on
				s.siteID =  ai.siteID
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.order = 'desc'>
		<cfif len(arguments.referralDateOrder)>
			<cfif arguments.referralDateOrder EQ "ascending">
				<cfset local.order = 'asc'>
			<cfelseif arguments.referralDateOrder EQ "descending">
				<cfset local.order = 'desc'>
			</cfif>
		</cfif>

		<cfstoredproc procedure="cms_processMergeCode_displayReferralGrid" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qryReferraldata.referralID#">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" null="true">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#local.commonTagVars.siteCode#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
			<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.status#">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.maxRows#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.order#">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#local.tblName#">
			<cfprocresult name="local.qryGetMemberReferrals">
		</cfstoredproc>

		<cfset local.thisrow['reportMemberReferralGrid']  ="">
		<cfset local.tdStyle = "" />
		<cfset local.pageStyle = "" />
		<cfset local.testVariable = "">
		<cfset local.memberCount= 1>
		
		<cfoutput query="local.qryGetMemberReferrals" group="memberID">
			
			<cfif arguments.format EQ "json">
				<cfoutput>
					<cfset local.thisrow = structNew("ordered")>
					<cfset local.thisrow['clientName'] = clientName>
					<cfset local.thisrow['clientReferralID'] = clientReferralID>
					<cfset local.thisrow["statusName"] = statusName>
					<cfset local.thisrow["clientReferralDate"] = dateFormat(clientReferralDate,"m/d/yyyy")>
					<cfset local.thisrow["referralUrl"] = "#local.memberExternalLink#&ra=editReferral&clientReferralID=#clientReferralID#">
					<cfset local.thisrow["callDate"] = trim(dateformat(callDate,"m/d/yyyy") & " " & timeformat(callDate, "h:mm tt"))>
					<cfset local.thisrow["dateCaseOpened"] = trim(dateformat(dateCaseOpened,"m/d/yyyy") & " " & timeformat(dateCaseOpened, "h:mm tt"))>
					<cfset local.thisrow["dateCaseClosed"] = trim(dateformat(dateCaseClosed,"m/d/yyyy") & " " & timeformat(dateCaseClosed, "h:mm tt"))>
					<cfset local.thisrow["lastUpdatedDate"] = trim(dateformat(lastUpdatedDate,"m/d/yyyy") & " " & timeformat(lastUpdatedDate, "h:mm tt"))>
					

					<cfset local.thisClientID = val(clientID)>
					<cfif val(clientParentID)>
						<cfset local.thisClientID = val(clientParentID)>
					</cfif>
					<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID)>
					<cfset local.panelid1 = "">
					<cfloop query="local.qryGetReferralFilterData">
						<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
							<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
								<cfcase value="panelid1">
									<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue)>
									<cfset local.panelid1 = local.qryGetPanelInfo.name>
								</cfcase>
							</cfswitch>
						</cfif>
					</cfloop>
					<cfset local.thisrow["panelName"] = local.panelid1>
					
					<cfset local.clientCustomFields = structNew("ordered")>
					<cfloop list="#local.qryGetMemberReferrals.referralClientList#" index="local.referralClientItem">
						<cfif structKeyExists(local.qryGetMemberReferrals,local.referralClientItem)>
							<cfset local.clientCustomFields[local.referralClientItem] = local.qryGetMemberReferrals[local.referralClientItem][currentRow]>
						</cfif>
					</cfloop>
					<cfset local.thisrow["clientCustomFields"] = local.clientCustomFields>
					
					<cfset local.attorneyCustomFields = structNew("ordered")>
					<cfloop list="#local.qryGetMemberReferrals.referralAttorneyList#" index="local.referralAttorneyItem">
						<cfif structKeyExists(local.qryGetMemberReferrals,local.referralAttorneyItem)>
							<cfset local.attorneyCustomFields[local.referralAttorneyItem] = local.qryGetMemberReferrals[local.referralAttorneyItem][currentRow]>
						</cfif>
					</cfloop>
					<cfset local.thisrow["attorneyCustomFields"] = local.attorneyCustomFields>
					<cfset local.thisrow["feeDiscrepancyStatus"] = feeDiscrepancyStatus>					

					<cfset arrayAppend(local.dataStruct["referrals"], local.thisrow)>
				</cfoutput>
			<cfelse>
				<cfsavecontent variable="local.dataStruct['referrals'][#local.memberCount#]['grid']">
					<div>			
						<br/>
						<table width="100%" cellpadding="10" cellspacing="0" class="mc-referralGridtable table table-bordered">
							<thead>
								<th>Ref.##</th>
								<th>Ref.Date</th>
								<th>Client</th>
								<th>Ref Panel</th>
								<th>Status</th>	
							</thead>
							<tbody>
							<cfoutput>
							<cfset local.thisClientID = val(clientID)>
							<cfif val(clientParentID)>
								<cfset local.thisClientID = val(clientParentID)>
							</cfif>
							<cfset local.qryGetReferralFilterData = local.objAdminReferrals.getReferralFilterData(clientID=local.thisClientID)>
							<cfset local.panelid1 = "">
							<cfloop query="local.qryGetReferralFilterData">
								<cfif listFindNoCase("panelid1,panelid2,panelid3,subpanelid1,subpanelid2,subpanelid3",local.qryGetReferralFilterData.elementID)>
									<cfswitch expression="#local.qryGetReferralFilterData.elementID#">
										<cfcase value="panelid1">
											<cfset local.qryGetPanelInfo = local.objAdminReferrals.getPanelByID(panelID=local.qryGetReferralFilterData.elementValue)>
											<cfset local.panelid1 = local.qryGetPanelInfo.name>
										</cfcase>
									</cfswitch>
								</cfif>
							</cfloop>
							<tr valign="top">
								<td><a href="#local.memberExternalLink#&ra=editReferral&clientReferralID=#clientReferralID#">#clientReferralID#</a></td>
								<td>#dateFormat(clientReferralDate,"m/d/yyyy")#</td>
								<td>#clientName#</td>
								<td>#local.panelid1#</td>
								<td>#statusName#</td>
							</tr>
							</cfoutput>	
							</tbody>
						</table>
					<br/>
					</div>
					<div style="clear:both"></div>
				</cfsavecontent>
			</cfif>
			<cfset local.memberCount = local.memberCount + 1>
		</cfoutput>
			
		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif local.qryGetMemberReferrals.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
					<ul class="mc-mergeTagList mc-referrals" style="list-style:none;margin-left:5px;">
						<cfloop array="#local.dataStruct.referrals#" index="local.thisRow">
							<li class="mc-mergeTagListItem">
								<cfset local.fields = "">
								<cfloop list="#StructKeyList(local.thisRow)#" index="local.item">
									<cfset local.fields = local.fields & '<div>#local.thisRow['#local.item#']#</div>'>
								</cfloop>
								#local.fields#
							</li>
						</cfloop>
					</ul>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-referrals mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>
	
		<cfreturn local.data>
	</cffunction>

	<cffunction name="rssFeed" returntype="struct" access="public" output="false" hint="Displays the entries of a defined RSS feed.">
		<cfargument name="feedURL" type="string" required="true" default="" hint="The RSS feed URL needs to be defined in the RSS Feeds admin tool.">
		<cfargument name="noresultstext" type="string" required="no" default="None found">
		<cfargument name="sortmethod" type="string" required="false" default="publishdate" hint="publishdate or title">
		<cfargument name="order" type="string" required="false" default="descending" hint="ascending or descending">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["entries"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);

			if (arguments.format eq "json" and not len(arguments.jsonvariable))
				arguments.jsonvariable = "rssFeed";
		</cfscript>

		<cfquery name="local.qryRSSFeed" datasource="#application.dsn.customApps.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT TOP 1 f.rssID, f.isValid, f.rssURL, f.rssDescription, f.lastUpdated, c.rssCategoryDescription
			FROM dbo.rss_categories AS c
			INNER JOIN dbo.rss_feeds AS f ON f.rssCategoryID = c.rssCategoryID 
				AND f.rssURL = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.feedURL#">
			WHERE c.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryRSSFeed.recordcount and local.qryRSSFeed.isValid>
			<cfquery name="local.qryRSSEntries" datasource="#application.dsn.customApps.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT title, link, pubDate, guid, description, shortDesc
				FROM dbo.rss_entries
				WHERE rssID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryRSSFeed.rssID#">
				ORDER BY
				<cfif len(arguments.sortmethod)>
					<cfif arguments.sortmethod EQ "publishdate">
						pubDate
					<cfelseif arguments.sortmethod EQ "title">
						title
					<cfelse>
						pubDate
					</cfif>
				<cfelse>
					pubDate
				</cfif>
				<cfif len(arguments.order)>
					<cfif arguments.order EQ "ascending">
						ASC
					<cfelseif arguments.order EQ "descending">
						DESC
					</cfif>
				</cfif>;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
		</cfif>

		<cfif local.qryRSSFeed.recordCount is 0 OR NOT local.qryRSSFeed.isValid>
			<cfset local.dataStruct["error"] = "RSS feed is invalid.">
		<cfelse>
			<cfset local.dataStruct["feedURL"] = local.qryRSSFeed.rssURL>
			<cfset local.dataStruct["feedDescription"] = local.qryRSSFeed.rssDescription>
			<cfset local.dataStruct["lastUpdated"] = datetimeformat(local.qryRSSFeed.lastUpdated,"m/d/yyyy h:nn tt")>
			<cfset local.dataStruct["feedCategory"] = local.qryRSSFeed.rssCategoryDescription>
			<cfloop query="local.qryRSSEntries">
				<cfset local.thisrow = {}>
				<cfset local.thisrow["title"] = local.qryRSSEntries.title>
				<cfset local.thisrow["link"] = local.qryRSSEntries.link>
				<cfset local.thisrow["pubDate"] = dateFormat(local.qryRSSEntries.pubDate,"m/d/yyyy")>
				<cfset local.thisrow["guid"] = local.qryRSSEntries.guid>
				<cfset local.thisrow["description"] = local.qryRSSEntries.description>
				<cfset local.thisrow["shortdescription"] = local.qryRSSEntries.shortDesc>
				<cfset arrayAppend(local.dataStruct["entries"], local.thisrow)>
			</cfloop>
		</cfif>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif structKeyExists(local.dataStruct,"error")>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-rssFeedList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#local.dataStruct.error#</span>
				</div>
				</cfoutput>
			</cfsavecontent>			
		<cfelseif local.qryRSSEntries.recordCount>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
					<div class="mc-mergeTag">
						<h3><a href="#local.dataStruct.feedURL#">#local.dataStruct.feedDescription#</a></h3>
						<div class="mc-rssFeedList" style="margin-left:20px">
							<cfloop array="#local.dataStruct.entries#" index="local.thisRow">
								<div class="mc-rssFeedListItem">
									<b>#local.thisRow.pubDate#</b> 
									<h4><a href="#local.thisRow.link#">#local.thisRow.title#</a></h4>
									<p>#local.thisRow.shortdescription#</p>
								</div>
							</cfloop>
						</div>
					</div>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-rssFeedList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="jobBank" access="public" returntype="struct" output="false" hint="Displays Jobs of Specified Job Bank.">
		<cfargument name="pageName" type="string" required="true" hint="leave blank for all jobs">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="order" type="string" required="false" default="ascending" hint="ascending or descending by Post Date">
		<cfargument name="noresultstext" type="string" required="false" default="There are no recent job posts.">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["jsonvariable"] = arguments.jsonvariable;
			local.data["dataString"] = "";

			local.dataStruct = structNew();
			local.dataStruct["jobs"] = arrayNew(1);
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
		</cfscript>

		<cfquery name="local.arrJobs" datasource="#application.dsn.membercentral.dsn#" returntype="array">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.commonTagVars.siteID#">;
			DECLARE @maxrows int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.maxRows#">;
			DECLARE @environmentName varchar(50) = <cfqueryparam cfsqltype="cf_sql_varchar" value="#application.MCEnvironment#">;
			DECLARE @minAppInstanceID int;

			IF OBJECT_ID('tempdb..##tmpJobs') IS NOT NULL 
				DROP TABLE ##tmpJobs;
			IF OBJECT_ID('tempdb..##tmpJBAppBaseQueryString') IS NOT NULL 
				DROP TABLE ##tmpJBAppBaseQueryString;
			CREATE TABLE ##tmpJobs (applicationInstanceID int, jobbankname varchar(100), jobid int, jobbankid int, jobemail varchar(100), 
				jobtitle varchar(200), jobcompany varchar(200), jobcity varchar(100), jobstate varchar(2), jobphone varchar(25), 
				jobdateposted datetime, jobcategory varchar(50), jobtype varchar(50), joblink varchar(1000), rowID int);
			CREATE TABLE ##tmpJBAppBaseQueryString (mainhostname varchar(80), scheme varchar(10), queryString varchar(1000), applicationInstanceID int);

			INSERT INTO ##tmpJobs (applicationInstanceID, jobbankname, jobid, jobbankid, jobemail, jobtitle, jobcompany, jobcity, jobstate, 
				jobphone, jobdateposted, jobcategory, jobtype, joblink, rowID)
			SELECT TOP (@maxrows) ai.applicationInstanceID, ai.applicationInstanceName, jbj.jobID, jbj.jobBankID, jbj.emailAddress, 
				jbj.jobTitle, jbj.employer, jbj.city, jbj.state, jbj.phone, jbj.datePosted, jbc.description, jbe.description, '',
				ROW_NUMBER() OVER (ORDER BY jbj.datePosted<cfif arguments.order EQ "descending"> desc</cfif>)
			FROM dbo.jobBankJobs as jbj
			INNER JOIN dbo.jobBank as jb on jbj.jobBankID = jb.jobBankID
			INNER JOIN dbo.cms_applicationInstances AS ai ON ai.applicationInstanceID = jb.applicationInstanceID 
				AND ai.siteID = @siteID
			INNER JOIN dbo.cms_siteResources AS sr ON sr.siteID = @siteID AND ai.siteResourceID = sr.siteResourceID
				AND sr.siteResourceStatusID = 1
			INNER JOIN dbo.jobBankCategories as jbc on jbc.categoryID = jbj.categoryID
			INNER JOIN dbo.jobBankEmploymentTypes as jbe on jbe.employmentTypeID = jbj.employmentTypeID
			WHERE jbj.hasPaid = 1
			AND jbj.IsDeleted = 0
			AND jbj.status = 'A'
			AND getdate() <= jbj.removeOnDate
			<cfif len(arguments.pageName)>
				WHERE ai.applicationInstanceName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.pageName#">
			</cfif>;

			IF @@ROWCOUNT > 0 BEGIN
				-- get the appication Base Links and update tmpJobs with it
				SELECT @minAppInstanceID = MIN(applicationInstanceID) FROM ##tmpJobs;
				WHILE @minAppInstanceID IS NOT NULL BEGIN
					INSERT INTO ##tmpJBAppBaseQueryString (mainhostname, scheme, queryString)
					EXEC dbo.cms_getApplicationBaseLink @siteID=@siteID, @applicationInstanceID=@minAppInstanceID, @environmentName=@environmentName;

					UPDATE ##tmpJBAppBaseQueryString
					SET applicationInstanceID = @minAppInstanceID
					WHERE applicationInstanceID IS NULL;

					SELECT @minAppInstanceID = MIN(applicationInstanceID) FROM ##tmpJobs WHERE applicationInstanceID > @minAppInstanceID;
				END

				UPDATE tmp
				SET tmp.joblink = '#local.commonTagVars.baseUrl#?' + abl.queryString + '&action=SearchJobs&do=details&jobID=' + cast(tmp.jobid as varchar(10))
				FROM ##tmpJobs as tmp
				INNER JOIN ##tmpJBAppBaseQueryString as abl on abl.applicationInstanceID = tmp.applicationInstanceID;
			END

			SELECT jobbankname, jobid, jobbankid, jobemail, jobtitle, jobcompany, jobcity, jobstate, 
				jobphone, jobdateposted, jobcategory, jobtype, joblink
			FROM ##tmpJobs
			ORDER BY rowID;

			IF OBJECT_ID('tempdb..##tmpJobs') IS NOT NULL 
				DROP TABLE ##tmpJobs;
			IF OBJECT_ID('tempdb..##tmpJBAppBaseQueryString') IS NOT NULL 
				DROP TABLE ##tmpJBAppBaseQueryString;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.dataStruct["jobs"] = local.arrJobs>

		<cfif arguments.format eq "json">
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelseif arrayLen(local.dataStruct["jobs"])>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-jobList">
					<cfloop array="#local.dataStruct.jobs#" index="local.thisJob">
						<div class="mc-mergeTagListItem" style="margin-bottom:8px;">
							<p>
								<strong><a href="#local.thisJob.joblink#">#local.thisJob.jobtitle#</a></strong><br/>
								<cfif len(local.thisJob.jobcompany)>#local.thisJob.jobcompany#<br/></cfif>
								<cfif len(local.thisJob.jobcity)>#local.thisJob.jobcity#</cfif>
								<cfif len(local.thisJob.jobstate)> #local.thisJob.jobstate#</cfif>
								<cfif len(local.thisJob.jobcity) or len(local.thisJob.jobstate)> - </cfif>
								#DateFormat(local.thisJob.jobdateposted,'M/D/YYYY')#
							</p>
						</div>
					</cfloop>
				</div>
				</cfoutput>
			</cfsavecontent>
		<cfelseif len(arguments.noresultstext)>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput>
				<div class="mc-mergeTagList mc-jobList mc-noDataMessageContainer">
					<span class="mc-noDataMessage">#arguments.noresultstext#</span>
				</div>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="thisMember" access="public" returntype="struct" hint="Displays the current member.">
		<cfargument name="fieldSetuid" type="string" required="true" default="" hint="one fieldset UID">
		<cfargument name="groupSetuid" type="string" required="false" default="" hint="Pipe-delimited list of group set UIDs">
		<cfargument name="jsonvariable" type="string" required="false" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="noresultstext" type="string" required="false" default="Member not found.">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "json">
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = { "member": {} }>
		<cfset local.isValidParams = true>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>

		<cfif NOT isValid("guid", ListFirst(arguments.fieldSetuid,"|")) OR NOT application.objCommon.validateUIDList(inputString=arguments.groupSetuid)>
			<cfset local.isValidParams = false>
		</cfif>
		<cfset arguments.groupSetuid = replace(arguments.groupSetuid,"|",",","ALL")>
		<cfset arguments.fieldSetuid = ListFirst(arguments.fieldSetuid,"|")>

		<cfif local.isValidParams>
			<cftry>
				<cfquery name="local.qryMember" datasource="#application.dsn.memberCentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						IF OBJECT_ID('tempdb..##tmpThisMember') IS NOT NULL 
							DROP TABLE ##tmpThisMember;
						IF OBJECT_ID('tempdb..##tmpGroupsInGroupSets') IS NOT NULL 
							DROP TABLE ##tmpGroupsInGroupSets;
						IF OBJECT_ID('tempdb..##tmpThisMemberWithFS') IS NOT NULL 
							DROP TABLE ##tmpThisMemberWithFS;
						CREATE TABLE ##tmpThisMember (memberID int, prefix varchar(50), lastname varchar(75), middlename varchar(25), firstname varchar(75),
							suffix varchar(50), professionalsuffix varchar(100), membernumber varchar(50), MCAccountStatus char(1), hasmemberphotothumb bit, 
							company varchar(200), groupIDs varchar(max), groups varchar(max));
						CREATE TABLE ##tmpGroupsInGroupSets (groupID int, groupSetID int);
						CREATE TABLE ##tmpThisMemberWithFS (MFSAutoID int IDENTITY(1,1) NOT NULL);

						DECLARE @orgID int, @resultsFSID int, @outputFieldsXML xml, @memberID int;
						SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.orgID#">;
						SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">;

						SELECT @resultsFSID = fieldSetID 
						FROM dbo.ams_memberFieldSets 
						WHERE [uid] = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#arguments.fieldSetuid#">;

						<cfif len(arguments.groupSetuid)>
							INSERT INTO ##tmpGroupsInGroupSets (groupID, groupSetID)
							SELECT mgsg.groupID, mgs.groupSetID 
							FROM dbo.ams_memberGroupSets as mgs 
							INNER JOIN dbo.ams_memberGroupSetGroups as mgsg on mgsg.groupsetid = mgs.groupSetid 
							INNER JOIN dbo.ams_groups g on g.orgID = @orgID and g.groupID = mgsg.groupID 
							AND mgs.uid IN (<cfqueryparam cfsqltype="CF_SQL_IDSTAMP" list="true" value="#arguments.groupSetuid#">);
						</cfif>

						<cfif val(local.commonTagVars.memberid)>
							INSERT INTO ##tmpThisMember (memberID, prefix, lastname, middlename, firstname, suffix, professionalsuffix, 
								membernumber, MCAccountStatus, hasmemberphotothumb, company)
							SELECT memberID, prefix, lastname, middlename, firstname, suffix, professionalSuffix, 
								membernumber, status, hasMemberPhotoThumb, company
							FROM dbo.ams_members
							WHERE orgID = @orgID
							and memberID =@memberID;
						</cfif>
						
						-- get fieldset data and set back to snapshot because proc ends in read committed
						EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@resultsFSID, @existingFields='m_lastname,m_firstname,m_membernumber,m_company,m_status',
							@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmpThisMember', @membersResultTableName='##tmpThisMemberWithFS',
							@linkedMembers=0, @mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						SELECT TOP 1 tmp.prefix, tmp.firstname, tmp.middlename, tmp.lastname, tmp.suffix, tmp.professionalsuffix, tmp.membernumber, tmp.hasmemberphotothumb,
							<cfif len(arguments.groupSetuid)>
								ISNULL(STUFF((SELECT ',' + cast(g.groupID as varchar) 
									FROM ams_groups g					
									inner join ams_memberGroupSetGroups gs on gs.groupID = g.groupid
									WHERE g.orgID = @orgID
									AND g.groupid in (select groupid from ##tmpGroupsInGroupSets) and gs.groupSetID in (select groupSetID from ##tmpGroupsInGroupSets)
									AND g.groupid in (select groupid from cache_members_groups where memberID = tmp.memberID)
									FOR XML PATH('')), 1, 1, '')
								, '')
							<cfelse>''</cfif> as groupIDs,
							<cfif len(arguments.groupSetuid)>
								ISNULL(STUFF((SELECT ',' + case when gs.labelOverride is not null then gs.labelOverride else g.groupName end 
									FROM dbo.ams_groups g
									inner join dbo.ams_memberGroupSetGroups gs on gs.groupID = g.groupid
									WHERE g.orgID = @orgID
									AND g.groupid in (select groupid from ##tmpGroupsInGroupSets) and gs.groupSetID in (select groupSetID from ##tmpGroupsInGroupSets)
									AND g.groupid in (select groupid from dbo.cache_members_groups where memberID = tmp.memberID)
									FOR XML PATH, TYPE).value(N'.[1]', N'nvarchar(max)'),1, 1, N'')
								, '') 
							<cfelse>''</cfif> as groups,
							tmp.company, tmp.MCAccountStatus, tmpM.*, @outputFieldsXML AS mc_outputFieldsXML
						FROM ##tmpThisMember as tmp
						INNER JOIN ##tmpThisMemberWithFS as tmpM on tmpM.memberID = tmp.memberID;

						IF OBJECT_ID('tempdb..##tmpThisMemberWithFS') IS NOT NULL 
							DROP TABLE ##tmpThisMemberWithFS;
						IF OBJECT_ID('tempdb..##tmpThisMember') IS NOT NULL 
							DROP TABLE ##tmpThisMember;
						IF OBJECT_ID('tempdb..##tmpGroupsInGroupSets') IS NOT NULL
							DROP TABLE ##tmpGroupsInGroupSets;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfif local.qryMember.recordCount>
					<cfset local.xmlResultFields = local.qryMember.mc_outputFieldsXML>
					<cfset local.thisrow = structNew('ordered')>
					<cfset local.thisrow['memberid'] = local.qryMember.memberID>
					<cfset local.thisrow["groups"] = ListToArray(HTMLEditFormat(local.qryMember.groups),',',false,true)>
					<cfset local.thisrow["prefix"] = HTMLEditFormat(local.qryMember.prefix)>
					<cfset local.thisrow["firstname"] = HTMLEditFormat(local.qryMember.firstname)>
					<cfset local.thisrow["middlename"] = HTMLEditFormat(local.qryMember.middlename)>
					<cfset local.thisrow["lastname"] = HTMLEditFormat(local.qryMember.lastname)>
					<cfset local.thisrow["suffix"] = HTMLEditFormat(local.qryMember.suffix)>
					<cfset local.thisrow["professionalsuffix"] = HTMLEditFormat(local.qryMember.professionalsuffix)>
					<cfset local.thisrow["membernumber"] = HTMLEditFormat(local.qryMember.membernumber)>
					<cfset local.thisrow["company"] = HTMLEditFormat(local.qryMember.company)>
					<cfset local.thisrow["hasMemberPhotoThumb"] = HTMLEditFormat(local.qryMember.hasMemberPhotoThumb)>
					<cfset local.thisrow["mc_accountStatus"] = HTMLEditFormat(local.qryMember.MCAccountStatus)>
					<cfif local.qryMember.hasMemberPhotoThumb>
						<cfset local.thisrow['photourl'] = "/memberphotosth/" & LCASE(local.qryMember.membernumber) & ".jpg">
					<cfelse>
						<cfset local.thisrow['photourl'] = "/assets/common/images/directory/default.jpg">
					</cfif>

					<cfset local.columnList = local.qryMember.columnList()>
					<cfset local.columnList = listDeleteAt(local.columnList,listFindNoCase(local.columnList,"mc_outputFieldsXML"))>
					<cfset local.arrCustomFields = XMLSearch(local.xmlResultFields,"//field[not(substring(@fieldCode,1,3)='ma_') and not(substring(@fieldCode,1,3)='mp_') and not(substring(@fieldCode,1,4)='mat_') and not(substring(@fieldCode,1,4)='mpt_')]")>

					<cfloop array="#local.arrCustomFields#" index="local.thisField">
						<cfset local.currValue = local.qryMember[local.thisField.xmlattributes.fieldLabel]>
						<cfif len(local.currValue)>
							<cfif left(local.thisfield.xmlattributes.fieldCode,13) eq 'acct_balance_'>
								<cfset local.currValue = dollarFormat(local.currValue)>
							<cfelse>
								<cfswitch expression="#local.thisField.xmlattributes.dataTypeCode#">
									<cfcase value="DATE">
										<cfset local.currValue = dateFormat(local.currValue,"m/d/yyyy")>
									</cfcase>
									<cfcase value="STRING,DECIMAL2,INTEGER">
										<cfset local.currValue = local.currValue>
									</cfcase>
									<cfcase value="CONTENTOBJ">
										<cfset local.currValue = application.objCommon.getCleanContentData(local.currValue)>
									</cfcase>
									<cfcase value="BIT">
										<cfset local.currValue = YesNoFormat(local.currValue)>
									</cfcase>
								</cfswitch>
							</cfif>
						</cfif>
						<cfset local.thisrow['#htmlEditFormat(replace(local.thisfield.xmlattributes.fieldLabel," ","_","all"))#'] = rereplace(replace(replace(local.currValue,'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
					</cfloop>

					<cfloop list="#local.columnList#" index="local.thisCol">
						<cfset local.thisFieldKey = htmlEditFormat(replace(local.thisCol," ","_","all"))>
						<cfif NOT structKeyExists(local.thisrow, local.thisFieldKey)>
							<cfset local.thisrow[local.thisFieldKey] = rereplace(replace(replace(local.qryMember[local.thisCol],'  ',' ','ALL'),' ,',',','ALL'),'[\r\n\t]','','ALL')>
						</cfif>
					</cfloop>

					<cfset local.dataStruct["member"].append(local.thisrow)>
				<cfelse>
					<cfset local.dataStruct["err"] = arguments.noresultstext>
				</cfif>
			<cfcatch type="any">	
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.data)>
			</cfcatch>
			</cftry>
		</cfif>

		<cfset local.data["dataStruct"] = local.dataStruct>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="MyMemberHistory" access="public" returntype="struct" hint="Displays member history.">
		<cfargument name="category" type="string" required="false" default="" hint="Pipe-delimited list of category codes. Leave blank for all categories.">
		<cfargument name="Subcategory" type="string" required="false" default="" hint="Pipe-delimited list of subcategory codes. Leave blank for all categories.">
		<cfargument name="maxRows" type="numeric" required="false" default="5">
		<cfargument name="sortmethod" type="string" required="false" default="" hint="sort the result based on passed attribute. Values can be category or date">
		<cfargument name="order" type="string" required="false" default="" hint="sort order for the sortmethod passed. Values can be desc & asc">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested. JSON will be assigned to this global javascript variable">
		<cfargument name="noresultstext" type="string" required="false" default="There is no history to display.">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = arguments.format>
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = structNew()>
		<cfset local.dataStruct["memberHistory"] = arrayNew(1)>
		<cfset local.isValidParams = true>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>
		<cfset arguments.maxRows = max(arguments.maxRows,1)>

		<cftry>
			<cfquery name="local.qryMemberHistory" datasource="#application.dsn.memberCentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.orgID#">, 
						@siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">, 
						@memberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberid#">, 
						@maxrows int = <cfqueryparam value="#arguments.maxrows#" cfsqltype="CF_SQL_INTEGER">;
					
					SELECT TOP (@maxrows) cc.CategoryName as CategoryName, ccSub.CategoryName as SubCategoryName, mh.userDate as StartDate,
						mh.userEndDate as EndDate, mh.description as Description, mh.quantity as Quantity, mh.dollarAmt as Amount, 
						mLinkActive.memberID as linkMemberID, mLinkActive.firstName, mLinkActive.lastName
					FROM dbo.ams_memberHistory as mh					
					INNER JOIN dbo.cms_categories AS cc ON cc.CategoryID = mh.categoryID
					INNER JOIN dbo.cms_categories AS ccSub ON ccSub.categoryID = mh.subCategoryID
					<cfif len(arguments.category)>
						INNER JOIN dbo.fn_VarCharListToTable(<cfqueryparam value="#arguments.category#" cfsqltype="CF_SQL_VARCHAR">,'|') AS li ON li.listItem = cc.CategoryCode
					</cfif>
					<cfif len(arguments.Subcategory)>
						INNER JOIN dbo.fn_VarCharListToTable(<cfqueryparam value="#arguments.Subcategory#" cfsqltype="CF_SQL_VARCHAR">,'|') AS liSub ON liSub.listItem = ccSub.CategoryCode
					</cfif>
					INNER JOIN dbo.ams_members as m on m.orgID = @orgID and m.memberID = mh.memberID
						AND m.activeMemberID = @memberID
					LEFT OUTER JOIN dbo.ams_members as mLink 
						INNER JOIN dbo.ams_members as mLinkActive on mLinkActive.orgID = @orgID and mLinkActive.memberID = mLink.memberID
						ON mLink.orgID = @orgID and mLink.memberID = mh.linkMemberID
					WHERE mh.siteID = @siteID
					AND mh.[status] = 'A'
					ORDER BY
					<cfif len(arguments.sortmethod)>
						<cfif arguments.sortmethod EQ "category">
							cc.CategoryName
						<cfelseif arguments.sortmethod EQ "date">
							mh.userDate
						<cfelse>
							cc.CategoryName
						</cfif>
					<cfelse>
						cc.CategoryName
					</cfif>
					<cfif len(arguments.order)>
						<cfif arguments.order EQ "ascending">
							asc
						<cfelseif arguments.order EQ "descending">
							desc
						</cfif>
					</cfif>;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfloop query="local.qryMemberHistory">
				<cfset local.thisrow = structNew()>
				<cfset local.thisrow["CategoryName"] = local.qryMemberHistory.CategoryName>
				<cfset local.thisrow["SubCategoryName"] = local.qryMemberHistory.SubCategoryName>
				<cfset local.thisrow["StartDate"] = dateFormat(local.qryMemberHistory.StartDate,"m/d/yyyy")>
				<cfset local.thisrow["EndDate"] = dateFormat(local.qryMemberHistory.EndDate,"m/d/yyyy")>
				<cfset local.thisrow["Description"] = local.qryMemberHistory.Description>
				<cfset local.thisrow["Quantity"] = local.qryMemberHistory.Quantity>
				<cfset local.thisrow["Amount"] = local.qryMemberHistory.Amount>
				<cfset local.thisrow["linkMemberID"] = local.qryMemberHistory.linkMemberID>
				<cfset local.thisrow["firstName"] = local.qryMemberHistory.firstName>
				<cfset local.thisrow["lastName"] = local.qryMemberHistory.lastName>
				<cfset arrayAppend(local.dataStruct["memberHistory"], local.thisrow)>
			</cfloop>

			<cfif arguments.format eq "json">
				<cfset local.data["dataStruct"] = local.dataStruct>
			<cfelseif local.qryMemberHistory.recordCount>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<ul class="mc-mergeTagList mc-memberHistoryList" >
						<cfloop array="#local.dataStruct.memberHistory#" index="local.thisRow">
							<li class="mc-mergeTagListItem">Category: #local.thisRow.CategoryName#</li>
							<li class="mc-mergeTagListItem">Sub Category: #local.thisRow.SubCategoryName#</li>
							<li class="mc-mergeTagListItem" style="margin-bottom:10px;">Quantity: #local.thisRow.Quantity#</li>
						</cfloop>
					</ul>
					</cfoutput>
				</cfsavecontent>
			<cfelseif len(arguments.noresultstext)>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
					<div class="mc-mergeTagList mc-eventList mc-noDataMessageContainer">
						<span class="mc-noDataMessage">#arguments.noresultstext#</span>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfif>			
		<cfcatch type="any">	
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.data)>
		</cfcatch>
		</cftry>

		<cfset local.data["dataStruct"] = local.dataStruct>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="qrcode" access="public" returntype="struct" output="false" hint="Build the URL for QR code generation.">
		<cfargument name="text" type="string" required="true" hint="url link text">
		<cfargument name="width" type="numeric" required="false" default="250">
		<cfargument name="height" type="numeric" required="false" default="250">
		<cfargument name="additionalstyles" type="string" required="false" default="" hint="additional styles">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="Only needed when JSON format is requested.">

		<cfset var local = structNew()>
		<cfset local.text = arguments.text.encodeForURL().rereplacenocase('(?:%5B%5B)([\w\W]+?)(?:%5D%5D)','[[\1]]','all')>

		<cfif arguments.format eq "struct">
			<cfset local.url = "#application.paths.backendPlatform.internalUrl#?event=qrcode&text=#local.text#">
		<cfelse>
			<cfset local.url = "#application.paths.backendPlatform.url#?event=qrcode&text=#local.text#">
		</cfif>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = arguments.format>
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">
		<cfif len(arguments.additionalstyles) AND right(arguments.additionalstyles,1) NEQ ';'>
			<cfset arguments.additionalstyles = arguments.additionalstyles & ';'>
		</cfif>

		<cfif listFindNoCase("json,struct",arguments.format)>
			<cfset local.dataStruct = structNew()>
			<cfset local.dataStruct["qrcode"] = arrayNew(1)>
			<cfset local.jsondata = structNew()>
			<cfset local.jsondata["url"] = local.url>
			<cfset local.jsondata["width"] = arguments.width>
			<cfset local.jsondata["height"] = arguments.height>
			<cfset local.jsondata["additionalstyles"] = arguments.additionalstyles>
			<cfset arrayAppend(local.dataStruct["qrcode"], local.jsondata)>
			<cfset local.data["dataStruct"] = local.dataStruct>
		<cfelse>
			<cfsavecontent variable="local.data.dataString">
				<cfoutput><img src="#local.url#" width="#arguments.width#" height="#arguments.height#" <cfif len(arguments.additionalstyles)>style="#arguments.additionalstyles#"</cfif> alt=""/></cfoutput>
			</cfsavecontent>
			<cfset local.data.dataString = trim(local.data.dataString)>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="websiteCarousel" access="public" returntype="struct" output="false" hint="Displays website carousel.">
		<cfargument name="name" type="string" required="true">
		<cfargument name="format" type="string" required="false" default="html" hint="json or html">

		<cfscript>
			var local = structNew();
			local.data = structNew();
			local.data["format"] = arguments.format;
			local.data["dataString"] = "";			
			local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments);
		</cfscript>

		<cfif arguments.format eq "html">
			<cfquery name="local.qryWebsiteCarousel" datasource="#application.dsn.membercentral.dsn#">
				SELECT carouselID 
				FROM cms_carousels 
				WHERE carouselName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.name#"> 
				AND siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.commonTagVars.siteID#">
			</cfquery>

			<cfif local.qryWebsiteCarousel.recordCount>
				<cfset local.websiteCarouselHTML = createObject("component","model.carousel.carousel").renderWebsiteCarousel(siteID=local.commonTagVars.siteID, carouselID=local.qryWebsiteCarousel.carouselID).HTML>
				<cfsavecontent variable="local.data.dataString">
					<cfoutput>
						#local.websiteCarouselHTML#
					</cfoutput>
				</cfsavecontent>
			</cfif>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="tsDocumentCredits" access="public" output="false" returntype="struct" hint="Displays TrialSmith's member document credit info.">		
		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>

		<cfif local.commonTagVars.memberID gt 0>
			<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ams_getTLASITESDepoMemberDataIDByMemberID">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.memberID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.commonTagVars.siteID#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.depoMemberDataID">
			</cfstoredproc>

			<cfif local.depoMemberDataID GT 0>
				<!--- Get purchase credit balance --->
				<cfquery name="local.qPurchaseCredit" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					select firmplan 
					from dbo.fn_Documents_getPurchaseCredits(<cfqueryparam value="#local.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">);

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery name="local.qryPCAmount" datasource="#application.dsn.tlasites_trialsmith.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					SELECT SUM(PC.PurchaseCreditAmount) as PCTotal
					FROM dbo.PurchaseCredits as PC
					INNER JOIN dbo.depomemberdata as M ON PC.DepoMemberDataID = M.DepoMemberDataID
					WHERE 
					<cfif len(local.qPurchaseCredit.firmplan)>
						PC.depoMemberDataID IN (
							SELECT fpl2.depoMemberDataID
							FROM dbo.tlaFirmPlanLink AS fpl2 
							INNER JOIN dbo.tlaFirmPlanLink AS fpl ON fpl2.firmPlanID = fpl.firmPlanID
							WHERE fpl.depoMemberDataID = <cfqueryparam value="#local.DepoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
						)
					<cfelse>
						PC.DepoMemberDataID = <cfqueryparam value="#local.DepoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
					</cfif>;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfset local.data.dataString = dollarFormat(local.qryPCAmount.PCTotal)>
			</cfif>
		</cfif>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="recentApprovedTrialSmithDocuments" access="public" output="false" returntype="struct" hint="Displays TrialSmith's recently approved deposition count.">		
		<cfargument name="days" type="numeric" required="true">

		<cfset var local = structNew()>

		<cfset local.data = structNew()>
		<cfset local.data["format"] = "html">
		<cfset local.data["jsonvariable"] = "">
		<cfset local.data["dataString"] = "">

		<cfquery name="local.qryDocs" datasource="#application.dsn.tlasites_trialsmith.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @days int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.days#">;
			DECLARE @startDate datetime = DATEADD(DAY, @days*-1, GETDATE());

			SELECT COUNT(DISTINCT d.DocumentID) AS totalDocs
			FROM dbo.depoDocuments AS d
			INNER JOIN dbo.depomemberdata AS m ON m.depomemberdataID = d.DepomemberdataID
			INNER JOIN dbo.depoDocumentStatusHistory AS dsh ON dsh.depoDocumentHistoryID = d.currentStatusHistoryID
			INNER JOIN dbo.depoDocumentStatuses AS ds ON ds.statusID = dsh.statusID
				AND ds.statusName = 'Approved'
			WHERE dsh.dateEntered >= @startDate;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.data.dataString = numberFormat(local.qryDocs.totalDocs,',')>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="coupons" access="public" returntype="struct" hint="Displays coupon information.">
		<cfargument name="couponCode" type="string" required="true" default="" hint="one coupon code">
		<cfargument name="jsonvariable" type="string" required="false" default="" hint="JSON will be assigned to this global javascript variable">
		<cfargument name="noresultstext" type="string" required="false" default="You do not currently qualify for any coupons">

		<cfset var local = structNew()>
		<cfset local.data = structNew()>
		<cfset local.data["format"] = "json">
		<cfset local.data["jsonvariable"] = arguments.jsonvariable>
		<cfset local.data["dataString"] = "">

		<cfset local.dataStruct = { "coupon": {} }>
		<cfset local.commonTagVars = application.objMergeCodes.getCommonVariables(argumentCollection=arguments)>		

		<cftry>
			<cfset local.qualifyFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Coupon", functionName="qualifyForCoupon")>
			<cfstoredproc procedure="cms_processMergeCode_coupons" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#arguments.couponCode#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.memberid)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.commonTagVars.siteID)#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qualifyFID#">
				<cfprocresult name="local.qryData" resultset="1">
			</cfstoredproc>

			<cfif local.qryData.recordCount>
				<cfset local.thisrow = structNew('ordered')>
				<cfset local.thisrow['couponCode'] = local.qryData.couponCode>
				<cfset local.thisrow['availabilityStartDate'] = len(local.qryData.availabilityStartDate) ? DateTimeFormat(local.qryData.availabilityStartDate,"m/d/yyyy h:nn tt") : "">
				<cfset local.thisrow['availabilityEndDate'] = len(local.qryData.availabilityEndDate) ? DateTimeFormat(local.qryData.availabilityEndDate,"m/d/yyyy h:nn tt") : "">
				<cfset local.thisrow['percentageOff'] = local.qryData.percentageOff>
				<cfset local.thisrow['maxDiscountAmount'] = local.qryData.maxDiscountAmount>
				<cfset local.thisrow['dollarAmountOff'] = local.qryData.dollarAmountOff>
				<cfset local.thisrow['redemptionsAllowedMember'] = local.qryData.redemptionsAllowedMember>
				<cfset local.dataStruct["coupon"].append(local.thisrow)>
			<cfelse>
				<cfset local.dataStruct["err"] = arguments.noresultstext>
			</cfif>
		<cfcatch type="any">	
			<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local.data)>
		</cfcatch>
		</cftry>

		<cfset local.data["dataStruct"] = local.dataStruct>

		<cfreturn local.data>
	</cffunction>

</cfcomponent>